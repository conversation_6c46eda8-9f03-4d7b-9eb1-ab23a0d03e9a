#!/usr/bin/env python3
"""
运行时监控器

提供系统运行时的状态监控和定期报告功能，解决用户观察到的"系统卡住"问题。
通过定期输出系统活动日志，让用户了解系统正在正常工作。
"""

import asyncio
import contextlib
from datetime import UTC, datetime
from typing import TYPE_CHECKING

from ..core.base_component import BaseComponent, HealthCheckResult, HealthStatus

if TYPE_CHECKING:
    pass


class RuntimeMonitor(BaseComponent):
    """
    运行时监控器

    定期输出系统状态摘要，包括：
    - 组件运行状态
    - 数据流活动
    - 健康状况概览
    - 性能指标
    """

    def __init__(self, container=None):
        super().__init__("RuntimeMonitor")
        self.container = container
        self._monitor_task: asyncio.Task | None = None
        self._monitor_interval = 300  # 5分钟输出一次状态摘要
        self._last_report_time: datetime | None = None
        self._startup_time: datetime | None = None

    async def _initialize_impl(self) -> bool:
        """初始化监控器"""
        self._startup_time = datetime.now(UTC)
        await self.safe_log_info("运行时监控器初始化完成")
        return True

    async def _start_impl(self) -> bool:
        """启动监控器"""
        try:
            # 启动监控任务
            self._monitor_task = asyncio.create_task(self._monitor_loop())
            await self.safe_log_info("运行时监控器启动成功")
            return True
        except Exception as e:
            await self.safe_log_error(f"运行时监控器启动失败: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止监控器"""
        try:
            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                with contextlib.suppress(asyncio.CancelledError, asyncio.TimeoutError):
                    await asyncio.wait_for(self._monitor_task, timeout=5.0)

            await self.safe_log_info("运行时监控器已停止")
            return True
        except Exception as e:
            await self.safe_log_error(f"运行时监控器停止失败: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        if self._monitor_task and not self._monitor_task.done():
            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="运行时监控器正常运行"
            )
        else:
            return HealthCheckResult(
                status=HealthStatus.DEGRADED, message="监控任务未运行"
            )

    async def _monitor_loop(self):
        """监控循环"""
        await self.safe_log_info("🔍 运行时监控器开始工作...")

        # 等待系统完全启动后再开始监控
        await asyncio.sleep(30)  # 等待30秒让所有组件稳定

        while True:
            try:
                await self._generate_status_report()
                await asyncio.sleep(self._monitor_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                await self.safe_log_error(f"监控循环异常: {e}")
                await asyncio.sleep(60)  # 出错时等待1分钟再继续

    async def _generate_status_report(self):
        """生成状态报告"""
        try:
            current_time = datetime.now(UTC)

            # 计算运行时间
            if self._startup_time:
                uptime_seconds = (current_time - self._startup_time).total_seconds()
                uptime_minutes = uptime_seconds / 60
                uptime_hours = uptime_minutes / 60
            else:
                uptime_seconds = uptime_minutes = uptime_hours = 0

            # 采样 EventBus 指标 & 最近事件
            try:
                if (
                    self.container
                    and hasattr(self.container, "event_bus")
                    and self.container.event_bus
                ):
                    metrics = await self.container.event_bus.get_metrics()
                    await self.safe_log_info(
                        f"🚌 EventBus: published={metrics.get('total_published')}, processed={metrics.get('total_processed')}, failed={metrics.get('total_failed')}, queue={metrics.get('queue_size')}, subscribers={metrics.get('active_subscribers')}"
                    )
                    # 打印各类型事件计数的前几项
                    etc = metrics.get("event_type_counts") or {}
                    if etc:
                        top_items = list(etc.items())[:5]
                        await self.safe_log_info(
                            "🧮 Event types: "
                            + ", ".join(f"{k}={v}" for k, v in top_items)
                        )
                    # 订单拒绝诊断指标（若组件存在）
                    try:
                        diag = self.container.get_component("OrderDiagnosticsMonitor")
                        if diag:
                            health = await diag.health_check()
                            det = (
                                getattr(health, "details", {})
                                if hasattr(health, "details")
                                else {}
                            )
                            await self.safe_log_info(
                                f"🚫 Order rejects: rejected={det.get('rejected')}, details={det.get('rejected_details')}, samples={det.get('samples')}"
                            )
                    except Exception:
                        pass
                    # 策略审计事件计数
                    try:
                        audits = (
                            (etc.get("strategy_audit") or 0)
                            if isinstance(etc, dict)
                            else 0
                        )
                        await self.safe_log_info(f"🧾 Strategy audits: {audits}")
                    except Exception:
                        pass
            except Exception as e:
                await self.safe_log_warning(f"EventBus metrics sampling failed: {e}")

            await self.safe_log_info("=" * 60)
            await self.safe_log_info("系统运行状态报告")
            await self.safe_log_info(
                f"⏰ 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S UTC')}"
            )

            if uptime_hours >= 1:
                await self.safe_log_info(f"🕐 运行时间: {uptime_hours:.1f} 小时")
            else:
                await self.safe_log_info(f"🕐 运行时间: {uptime_minutes:.1f} 分钟")

            # 组件状态概览
            if self.container:
                all_components = self.container.get_all_components()
                running_count = sum(
                    1 for comp in all_components.values() if comp.is_running
                )

                await self.safe_log_info(
                    f"🏃 运行组件: {running_count}/{len(all_components)}"
                )

                # 分类统计组件状态
                status_counts = {}
                for component in all_components.values():
                    status = component.state.value
                    status_counts[status] = status_counts.get(status, 0) + 1

                for status, count in status_counts.items():
                    emoji = self._get_status_emoji(status)
                    await self.safe_log_info(f"  {emoji} {status}: {count}个")

                # 健康检查概览
                try:
                    health_result = await asyncio.wait_for(
                        self.container.health_check(), timeout=10.0
                    )
                    health_emoji = (
                        "💚"
                        if health_result.status == HealthStatus.HEALTHY
                        else "💛"
                        if health_result.status == HealthStatus.DEGRADED
                        else "❤️"
                    )
                    await self.safe_log_info(
                        f"{health_emoji} 系统健康: {health_result.status.value}"
                    )

                    if (
                        health_result.message
                        and health_result.status != HealthStatus.HEALTHY
                    ):
                        await self.safe_log_info(f"  详情: {health_result.message}")

                except TimeoutError:
                    await self.safe_log_info("⏰ 健康检查超时")
                except Exception as e:
                    await self.safe_log_info(f"❌ 健康检查异常: {e}")

            # 关键组件状态
            await self._report_key_components()

            # 数据流状态报告
            await self._report_data_flow_status()

            await self.safe_log_info("状态报告完成")
            await self.safe_log_info("=" * 60)

            self._last_report_time = current_time

        except Exception as e:
            await self.safe_log_error(f"生成状态报告失败: {e}")

    def _get_status_emoji(self, status: str) -> str:
        """获取状态对应的emoji"""
        emoji_map = {
            "running": "🏃",
            "stopped": "⏹️",
            "error": "❌",
            "failed": "💥",
            "starting": "🔧",
            "stopping": "🛑",
            "initialized": "✅",
            "initializing": "🔧",
            "uninitialized": "⚪",
        }
        return emoji_map.get(status, "❓")

    async def _report_key_components(self):
        """报告关键组件状态"""
        if not self.container:
            await self.safe_log_info("🔑 关键组件状态: 容器未可用")
            return

        key_components = [
            "BinanceClient",
            "DeribitClient",
            "IBKRClient",
            "TimescaleDBManager",
            "TelegramBotHandler",
            "DataEngine",
            "CausalEngine",
            "MicrostructureSignals",
        ]

        await self.safe_log_info("🔑 关键组件状态:")

        for comp_name in key_components:
            component = self.container.get_component(comp_name)
            if component:
                state = component.state.value
                emoji = self._get_status_emoji(state)

                # 检查是否有连接状态
                connection_info = ""
                if hasattr(component, "is_connected"):
                    try:
                        connected = getattr(component, "is_connected", False)
                        connection_info = (
                            f" ({'连接正常' if connected else '连接中断'})"
                        )
                    except (AttributeError, TypeError):
                        pass

                await self.safe_log_info(
                    f"  {emoji} {comp_name}: {state}{connection_info}"
                )
            else:
                await self.safe_log_info(f"  ⚪ {comp_name}: 未启动")

    async def _report_data_flow_status(self):
        """报告数据流状态"""
        await self.safe_log_info("📈 数据流状态:")

        if not self.container:
            await self.safe_log_info("  容器未可用，无法检查数据流状态")
            return

        # 检查TimescaleDB
        timescale = self.container.get_component("TimescaleDBManager")
        if timescale and hasattr(timescale, "get_recent_activity"):
            try:
                activity = await timescale.get_recent_activity()
                if activity:
                    await self.safe_log_info(
                        f"  💾 数据库: 最近接收到 {activity} 条数据"
                    )
                else:
                    await self.safe_log_info("  💾 数据库: 暂无数据流")
            except Exception:
                await self.safe_log_info("  💾 数据库: 状态未知")
        else:
            await self.safe_log_info("  💾 数据库: 未连接")

        # 检查交易所连接
        exchanges = ["BinanceClient", "DeribitClient", "IBKRClient"]
        for exchange_name in exchanges:
            exchange = self.container.get_component(exchange_name)
            if exchange and hasattr(exchange, "get_recent_messages"):
                try:
                    recent_count = await exchange.get_recent_messages()
                    if recent_count > 0:
                        await self.safe_log_info(
                            f"  📡 {exchange_name}: 接收到 {recent_count} 条消息"
                        )
                    else:
                        await self.safe_log_info(f"  📡 {exchange_name}: 暂无消息")
                except Exception:
                    pass

    async def generate_immediate_report(self):
        """立即生成一次状态报告"""
        await self.safe_log_info("🔍 手动触发状态报告...")
        await self._generate_status_report()

    async def set_monitor_interval(self, minutes: int):
        """设置监控间隔"""
        if minutes < 1:
            minutes = 1
        elif minutes > 60:
            minutes = 60

        self._monitor_interval = minutes * 60
        await self.safe_log_info(f"监控间隔已设置为 {minutes} 分钟")
