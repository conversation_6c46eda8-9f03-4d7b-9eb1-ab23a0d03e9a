#!/usr/bin/env python3
"""
基础组件类 - BaseComponent

定义系统中所有组件的统一生命周期接口和基础功能，包括：
- 异步初始化、启动、停止流程
- 组件状态管理和监控
- 统一错误处理机制
- 日志记录和指标收集
- 健康检查和自愈机制

设计要点：
- 提供标准的组件生命周期管理
- 支持优雅启动和关闭
- 集成异步日志和错误处理
- 支持组件状态监控和健康检查
"""

import asyncio
import contextlib
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import UTC, datetime
from enum import Enum
from typing import TYPE_CHECKING, Any

from .logger_mixin import ComponentLoggerMixin

if TYPE_CHECKING:
    from ..utils.async_logger import AsyncLogger
    from .dependency_injector import DependencyInjector
else:
    try:
        from ..utils.async_logger import AsyncLogger
        from .dependency_injector import DependencyInjector
    except ImportError:
        # 备用导入：使用绝对导入避免修改sys.path
        try:
            from src.core.dependency_injector import DependencyInjector
            from src.utils.async_logger import AsyncLogger
        except ImportError:
            # 最后备用：设置为None
            AsyncLogger = None
            DependencyInjector = None


class ComponentState(Enum):
    """组件状态枚举"""

    UNINITIALIZED = "uninitialized"  # 未初始化
    INITIALIZING = "initializing"  # 初始化中
    INITIALIZED = "initialized"  # 已初始化
    STARTING = "starting"  # 启动中
    RUNNING = "running"  # 运行中
    STOPPING = "stopping"  # 停止中
    STOPPED = "stopped"  # 已停止
    ERROR = "error"  # 错误状态
    FAILED = "failed"  # 失败状态


class HealthStatus(Enum):
    """健康状态枚举"""

    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class ComponentMetrics:
    """组件指标数据类"""

    start_time: datetime | None = None
    last_health_check: datetime | None = None
    uptime_seconds: float = 0.0
    error_count: int = 0
    warning_count: int = 0
    restart_count: int = 0
    custom_metrics: dict[str, Any] = field(default_factory=dict)


@dataclass
class HealthCheckResult:
    """健康检查结果"""

    status: HealthStatus
    message: str
    details: dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


class BaseComponent(ComponentLoggerMixin, ABC):
    """
    基础组件抽象类

    所有系统组件的基类，提供统一的生命周期管理、状态监控、
    错误处理和日志记录功能。

    子类需要实现：
    - _initialize_impl(): 具体初始化逻辑
    - _start_impl(): 具体启动逻辑
    - _stop_impl(): 具体停止逻辑
    - _health_check_impl(): 具体健康检查逻辑
    """

    def __init__(self, component_name: str, config: dict[str, Any] | None = None):
        self.component_name = component_name
        self.config = config or {}
        self.logger: AsyncLogger | None = None
        self.dependency_injector: DependencyInjector | None = None

        # 组件状态管理
        self._state = ComponentState.UNINITIALIZED
        self._state_lock = asyncio.Lock()

        # 指标收集
        self.metrics = ComponentMetrics()

        # 错误处理
        self._error_handlers: dict[type, callable] = {}
        self._last_error: Exception | None = None

        # 健康检查配置
        self._health_check_interval = self.config.get(
            "health_check_interval", 0
        )  # 默认禁用自动健康检查
        self._health_check_task: asyncio.Task | None = None
        self._last_health_status = HealthStatus.UNKNOWN

        # 生命周期钩子
        self._lifecycle_hooks: dict[str, list[callable]] = {
            "before_initialize": [],
            "after_initialize": [],
            "before_start": [],
            "after_start": [],
            "before_stop": [],
            "after_stop": [],
        }

    @property
    def state(self) -> ComponentState:
        """获取当前组件状态"""
        return self._state

    @property
    def is_running(self) -> bool:
        """检查组件是否正在运行"""
        return self._state == ComponentState.RUNNING

    @property
    def is_healthy(self) -> bool:
        """检查组件是否健康"""
        return self._last_health_status == HealthStatus.HEALTHY

    def set_logger(self, logger: "AsyncLogger"):
        """设置日志记录器"""
        self.logger = logger

    def set_dependency_injector(self, injector: "DependencyInjector"):
        """设置依赖注入器"""
        self.dependency_injector = injector

    def add_error_handler(self, exception_type: type, handler: callable):
        """添加错误处理器"""
        self._error_handlers[exception_type] = handler

    def add_lifecycle_hook(self, event: str, hook: callable):
        """添加生命周期钩子"""
        if event in self._lifecycle_hooks:
            self._lifecycle_hooks[event].append(hook)

    async def _execute_hooks(self, event: str):
        """执行生命周期钩子"""
        hooks = self._lifecycle_hooks.get(event, [])
        for hook in hooks:
            try:
                if asyncio.iscoroutinefunction(hook):
                    await hook(self)
                else:
                    hook(self)
            except Exception as e:
                await self.safe_log_warning(
                    f"Hook execution failed for {event}: {str(e)}"
                )

    async def _set_state(self, new_state: ComponentState):
        """安全地设置组件状态"""
        old_state = self._state
        self._state = new_state

        await self.log_state_change(old_state.value, new_state.value)

    async def _handle_error(self, error: Exception, context: str = ""):
        """统一错误处理"""
        self._last_error = error
        self.metrics.error_count += 1

        # 使用统一的错误日志记录
        await self.log_error_with_context(error, context)

        # 执行自定义错误处理器
        error_type = type(error)
        if error_type in self._error_handlers:
            try:
                handler = self._error_handlers[error_type]
                if asyncio.iscoroutinefunction(handler):
                    await handler(error, context)
                else:
                    handler(error, context)
            except Exception as handler_error:
                await self.safe_log_error(f"Error handler failed: {str(handler_error)}")

    async def initialize(self) -> bool:
        """
        初始化组件 - 从源头防止重复初始化

        Returns:
            bool: 初始化是否成功
        """
        async with self._state_lock:
            # 智能状态检查：已初始化的组件直接返回成功
            if self._state in (ComponentState.INITIALIZED, ComponentState.RUNNING):
                # 已经初始化完成，静默返回成功（源头防护）
                return True

            # 正在初始化中的处理
            if self._state == ComponentState.INITIALIZING:
                # 等待当前初始化完成，避免并发冲突
                await asyncio.sleep(0.1)  # 短暂等待
                return self._state == ComponentState.INITIALIZED

            # 只有未初始化状态才允许初始化
            if self._state != ComponentState.UNINITIALIZED:
                # 其他状态（如ERROR、FAILED）需要先重置
                await self.safe_log_info(
                    f"{self.component_name} resetting from {self._state.value} to allow initialization"
                )
                await self._set_state(ComponentState.UNINITIALIZED)

            await self._set_state(ComponentState.INITIALIZING)

            try:
                # 执行初始化前钩子
                await self._execute_hooks("before_initialize")

                # 自动注入依赖
                if self.dependency_injector:
                    try:
                        await self.dependency_injector.inject_dependencies(self)
                    except Exception as e:
                        await self.safe_log_warning(f"Dependency injection failed: {e}")

                # 执行具体初始化逻辑
                success = await self._initialize_impl()

                if success:
                    await self._set_state(ComponentState.INITIALIZED)
                    # 执行初始化后钩子
                    await self._execute_hooks("after_initialize")
                    await self.log_initialization(True)
                    return True
                else:
                    await self._set_state(ComponentState.FAILED)
                    await self.log_initialization(False, "initialization failed")
                    return False

            except Exception as e:
                await self._handle_error(e, "initialize")
                await self._set_state(ComponentState.ERROR)
                return False

    async def start(self) -> bool:
        """
        启动组件 - 从源头防止重复启动

        Returns:
            bool: 启动是否成功
        """
        async with self._state_lock:
            # 智能状态检查：已运行的组件直接返回成功
            if self._state == ComponentState.RUNNING:
                # 已经在运行状态，静默返回成功（源头防护）
                return True

            # 正在启动中的处理
            if self._state == ComponentState.STARTING:
                # 等待当前启动完成，避免并发冲突
                await asyncio.sleep(0.1)  # 短暂等待
                return self._state == ComponentState.RUNNING

            # 自动初始化未初始化的组件
            if self._state == ComponentState.UNINITIALIZED:
                await self.safe_log_info(
                    f"{self.component_name} auto-initializing before start"
                )
                init_success = await self.initialize()
                if not init_success:
                    return False

            # 允许从INITIALIZED和STOPPED状态启动
            if self._state not in [ComponentState.INITIALIZED, ComponentState.STOPPED]:
                await self.safe_log_warning(
                    f"{self.component_name} cannot start from state: {self._state.value}"
                )
                return False

            await self._set_state(ComponentState.STARTING)

            try:
                # 执行启动前钩子
                await self._execute_hooks("before_start")

                # 执行具体启动逻辑
                success = await self._start_impl()

                if success:
                    await self._set_state(ComponentState.RUNNING)
                    self.metrics.start_time = datetime.now(UTC)
                    self.metrics.restart_count += 1

                    # 启动健康检查任务
                    await self._start_health_check()

                    # 执行启动后钩子
                    await self._execute_hooks("after_start")
                    await self.log_startup(True)
                    return True
                else:
                    await self._set_state(ComponentState.FAILED)
                    await self.log_startup(False, "start failed")
                    return False

            except Exception as e:
                await self._handle_error(e, "start")
                await self._set_state(ComponentState.ERROR)
                return False

    async def stop(self) -> bool:
        """
        停止组件 - 从源头防止重复停止

        Returns:
            bool: 停止是否成功
        """
        async with self._state_lock:
            # 智能状态检查：已停止的组件直接返回成功
            if self._state == ComponentState.STOPPED:
                # 已经停止，静默返回成功（源头防护）
                return True

            # 未启动的组件无需停止
            if self._state in (
                ComponentState.UNINITIALIZED,
                ComponentState.INITIALIZED,
            ):
                # 未运行的组件，直接标记为停止状态
                await self._set_state(ComponentState.STOPPED)
                return True

            # 正在停止中的处理
            if self._state == ComponentState.STOPPING:
                # 等待当前停止完成，避免并发冲突
                await asyncio.sleep(0.1)  # 短暂等待
                return self._state == ComponentState.STOPPED

            # 只有运行中、错误或失败状态才需要停止
            if self._state not in [
                ComponentState.RUNNING,
                ComponentState.ERROR,
                ComponentState.FAILED,
                ComponentState.STARTING,
            ]:
                await self.safe_log_info(
                    f"{self.component_name} in state {self._state.value}, marking as stopped"
                )
                await self._set_state(ComponentState.STOPPED)
                return True

            await self._set_state(ComponentState.STOPPING)

            try:
                # 执行停止前钩子
                await self._execute_hooks("before_stop")

                # 停止健康检查任务
                await self._stop_health_check()

                # 执行具体停止逻辑
                success = await self._stop_impl()

                if success:
                    await self._set_state(ComponentState.STOPPED)

                    # 更新运行时间指标
                    if self.metrics.start_time:
                        self.metrics.uptime_seconds = (
                            datetime.now(UTC) - self.metrics.start_time
                        ).total_seconds()

                    # 执行停止后钩子
                    await self._execute_hooks("after_stop")
                    await self.log_shutdown(True)
                    return True
                else:
                    await self._set_state(ComponentState.ERROR)
                    await self.log_shutdown(False, "stop failed")
                    return False

            except Exception as e:
                await self._handle_error(e, "stop")
                await self._set_state(ComponentState.ERROR)
                return False

    async def restart(self) -> bool:
        """
        重启组件

        Returns:
            bool: 重启是否成功
        """
        await self.safe_log_info(f"Restarting {self.component_name}")

        # 先停止
        stop_success = await self.stop()
        if not stop_success:
            return False

        # 等待一小段时间
        await asyncio.sleep(1)

        # 再启动
        return await self.start()

    async def health_check(self) -> HealthCheckResult:
        """
        执行健康检查

        Returns:
            HealthCheckResult: 健康检查结果
        """
        try:
            # 基础状态检查
            if self._state != ComponentState.RUNNING:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message=f"Component not running, current state: {self._state.value}",
                )

            # 执行具体健康检查逻辑
            result = await self._health_check_impl()

            # 确保result是HealthCheckResult对象
            if isinstance(result, dict):
                # 如果返回字典，创建HealthCheckResult对象
                try:
                    status_raw = result.get("status", "unhealthy")
                    if isinstance(status_raw, HealthStatus):
                        status = status_raw
                    elif isinstance(status_raw, str):
                        # 先按名称匹配（HEALTHY），再按值匹配（healthy）
                        try:
                            status = HealthStatus[status_raw.upper()]
                        except KeyError:
                            try:
                                status = HealthStatus(status_raw.lower())
                            except ValueError:
                                status = HealthStatus.UNHEALTHY
                    else:
                        status = HealthStatus.UNHEALTHY

                    result = HealthCheckResult(
                        status=status,
                        message=result.get("message", "No message"),
                        details=result.get("details", {}),
                    )
                except Exception:
                    # 如果dict处理失败，创建默认的HealthCheckResult
                    result = HealthCheckResult(
                        status=HealthStatus.UNHEALTHY,
                        message="Failed to process health dict from _health_check_impl",
                        details={"original_result": str(result)},
                    )
            elif not hasattr(result, "status"):
                # 如果不是HealthCheckResult对象，创建默认的
                result = HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message=f"Invalid health check result type: {type(result)}",
                    details={"original_result": str(result)},
                )

            # 更新健康状态和指标
            self._last_health_status = result.status
            self.metrics.last_health_check = datetime.now(UTC)

            return result

        except Exception as e:
            await self._handle_error(e, "health_check")
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def get_metrics(self) -> dict[str, Any]:
        """
        获取组件指标

        Returns:
            Dict[str, Any]: 组件指标数据
        """
        current_time = datetime.now(UTC)

        # 计算当前运行时间
        current_uptime = 0.0
        if self.metrics.start_time and self._state == ComponentState.RUNNING:
            current_uptime = (current_time - self.metrics.start_time).total_seconds()

        return {
            "component_name": self.component_name,
            "state": self._state.value,
            "health_status": self._last_health_status.value,
            "start_time": self.metrics.start_time.isoformat()
            if self.metrics.start_time
            else None,
            "last_health_check": self.metrics.last_health_check.isoformat()
            if self.metrics.last_health_check
            else None,
            "uptime_seconds": current_uptime,
            "total_uptime_seconds": self.metrics.uptime_seconds,
            "error_count": self.metrics.error_count,
            "warning_count": self.metrics.warning_count,
            "restart_count": self.metrics.restart_count,
            "last_error": str(self._last_error) if self._last_error else None,
            "custom_metrics": self.metrics.custom_metrics.copy(),
        }

    async def _start_health_check(self):
        """启动健康检查任务"""
        if self._health_check_interval > 0:
            self._health_check_task = asyncio.create_task(self._health_check_loop())

    async def _stop_health_check(self):
        """停止健康检查任务"""
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._health_check_task

    async def _health_check_loop(self):
        """健康检查循环"""
        while self._state == ComponentState.RUNNING:
            try:
                await self.health_check()
                await asyncio.sleep(self._health_check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                await self._handle_error(e, "health_check_loop")
                await asyncio.sleep(self._health_check_interval)

    # 抽象方法 - 子类必须实现

    @abstractmethod
    async def _initialize_impl(self) -> bool:
        """
        具体初始化实现

        子类需要实现具体的初始化逻辑

        Returns:
            bool: 初始化是否成功
        """
        pass

    @abstractmethod
    async def _start_impl(self) -> bool:
        """
        具体启动实现

        子类需要实现具体的启动逻辑

        Returns:
            bool: 启动是否成功
        """
        pass

    @abstractmethod
    async def _stop_impl(self) -> bool:
        """
        具体停止实现

        子类需要实现具体的停止逻辑

        Returns:
            bool: 停止是否成功
        """
        pass

    @abstractmethod
    async def _health_check_impl(self) -> HealthCheckResult:
        """
        具体健康检查实现

        子类需要实现具体的健康检查逻辑

        Returns:
            HealthCheckResult: 健康检查结果
        """
        pass


class SimpleComponent(BaseComponent):
    """
    简单组件实现示例

    提供基础的组件实现，可用于测试或简单场景
    """

    def __init__(
        self,
        component_name: str = "SimpleComponent",
        config: dict[str, Any] | None = None,
    ):
        super().__init__(component_name, config)
        self._is_initialized = False
        self._is_started = False

    async def _initialize_impl(self) -> bool:
        """简单初始化实现"""
        await asyncio.sleep(0.1)  # 模拟初始化时间
        self._is_initialized = True
        return True

    async def _start_impl(self) -> bool:
        """简单启动实现"""
        if not self._is_initialized:
            return False
        await asyncio.sleep(0.1)  # 模拟启动时间
        self._is_started = True
        return True

    async def _stop_impl(self) -> bool:
        """简单停止实现"""
        await asyncio.sleep(0.1)  # 模拟停止时间
        self._is_started = False
        return True

    async def _health_check_impl(self) -> HealthCheckResult:
        """简单健康检查实现"""
        if self._is_started:
            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="Component is running normally"
            )
        else:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message="Component is not started"
            )
