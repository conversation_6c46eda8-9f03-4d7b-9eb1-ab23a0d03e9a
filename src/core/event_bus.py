#!/usr/bin/env python3
# NOTE: Added lightweight throughput sampling logs in publish/subscribe handling to aid debugging of event flow.

"""
异步事件总线 - EventBus

实现高性能的发布-订阅模式事件系统，支持：
- 异步事件发布和订阅
- 事件类型定义和路由机制
- 事件持久化和重放功能
- 事件监控和性能指标收集
- 并发安全的事件处理

设计要点：
- 基于asyncio的高性能异步处理
- 支持事件过滤和条件订阅
- 提供事件历史记录和重放
- 集成性能监控和错误处理
- 支持事件优先级和批量处理
"""

import asyncio
import os
import time
import uuid
from collections import defaultdict, deque
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from enum import Enum
from typing import Any

from .base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)

# 注意：Logger由BaseComponent统一处理，无需在此处导入


class EventPriority(Enum):
    """事件优先级枚举"""

    LOW = 0
    NORMAL = 1
    HIGH = 2
    CRITICAL = 3


class EventStatus(Enum):
    """事件状态枚举"""

    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"


@dataclass
class BaseEvent:
    """基础事件类"""

    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: str = field(default="")
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    priority: EventPriority = field(default=EventPriority.NORMAL)
    source: str = field(default="")
    metadata: dict[str, Any] = field(default_factory=dict)
    # 兼容历史调用：很多模块使用 data= 传递内容，这里提供兼容字段并在初始化时合并
    data: dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        if not self.event_type:
            self.event_type = self.__class__.__name__


@dataclass
class DataUpdateEvent(BaseEvent):
    """数据更新事件"""

    data: dict[str, Any] = field(default_factory=dict)
    data_type: str = ""
    exchange: str = ""


@dataclass
class SignalEvent(BaseEvent):
    """信号事件"""

    signal_data: dict[str, Any] = field(default_factory=dict)
    signal_type: str = ""
    confidence: float = 0.0


@dataclass
class FeaturesEvent(BaseEvent):
    """聚合特征事件（按时间对齐后的现货与期权特征）。"""

    features: dict[str, Any] = field(default_factory=dict)


@dataclass
class SystemEvent(BaseEvent):
    """系统事件"""

    component: str = ""
    action: str = ""
    status: str = ""
    details: dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorEvent(BaseEvent):
    """错误事件"""

    error_type: str = ""
    error_message: str = ""
    component: str = ""
    stack_trace: str = ""
    context: dict[str, Any] = field(default_factory=dict)


@dataclass
class TradingDecisionEvent(BaseEvent):
    """交易决策事件"""

    strategy_name: str = ""
    action: str = ""  # "buy", "sell", "hold", "adjust"
    instruments: list[str] = field(default_factory=list)
    risk_params: dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0
    decision_data: dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionUpdateEvent(BaseEvent):
    """仓位更新事件"""

    symbol: str = ""
    position_change: dict[str, Any] = field(default_factory=dict)
    current_position: dict[str, Any] = field(default_factory=dict)
    change_type: str = ""  # "open", "close", "adjust", "liquidate"
    pnl_data: dict[str, Any] = field(default_factory=dict)


@dataclass
class OrderExecutionEvent(BaseEvent):
    """订单执行事件"""

    order_id: str = ""
    symbol: str = ""
    side: str = ""  # "buy", "sell"
    quantity: float = 0.0
    price: float = 0.0
    execution_type: str = ""  # "fill", "partial_fill", "cancel"
    remaining_quantity: float = 0.0


# 统一事件类型（集中定义，避免分散魔法字符串）
class EventType(str, Enum):
    # 订单相关
    ORDER_EXECUTED = "order_executed"
    ORDER_FILLED = "order_filled"
    ORDER_REJECTED = "order_rejected"
    ORDER_REJECTED_DETAILS = "order_rejected_details"

    # 策略/数据/仓位
    TRADING_DECISION = "TradingDecisionEvent"
    DATA_UPDATE = "DataUpdateEvent"
    FEATURES = "FeaturesEvent"
    POSITION_UPDATE = "PositionUpdateEvent"
    RISK_EVENT = "risk_event"
    RISK_REJECTION = "RiskRejectionEvent"
    # 订单生命周期与外部订阅
    ORDER_SUBMITTED = "order_submitted"
    ORDER_CANCELLED = "order_cancelled"
    ORDER_FAILED = "order_failed"
    ORDER_CANCEL = "order_cancel"
    ORDER_FILL_FEED = "order_fill"
    # 策略与运行模式
    STRATEGY_MODE_SWITCH = "strategy_mode_switch"
    STRATEGY_ORDER = "strategy_order"
    STRATEGY_AUDIT = "strategy_audit"
    RISK_WARNING = "risk_warning"
    EXPIRY_REMINDER = "expiry_reminder"
    STRATEGY_SWITCH_REQUEST = "strategy_switch_request"
    EMERGENCY_STOP = "emergency_stop"
    SIGNAL = "SignalEvent"
    # 风险/紧急事件
    RISK_LIMIT_BREACH = "risk_limit_breach"
    EMERGENCY_POSITION_REDUCTION = "emergency_position_reduction"
    EMERGENCY_DELTA_HEDGING = "emergency_delta_hedging"
    EMERGENCY_MODE_ACTIVATED = "emergency_mode_activated"
    EMERGENCY_MODE_RESET = "emergency_mode_reset"
    EXPIRY_EMERGENCY_HANDLING = "expiry_emergency_handling"
    # 仓位/到期管理
    POSITION_ADDED = "position_added"
    POSITION_UPDATED = "position_updated"
    POSITION_ANOMALY = "position_anomaly"
    POSITION_CONCENTRATION_ALERT = "position_concentration_alert"
    POSITION_EXPIRY_ALERT = "position_expiry_alert"
    EXPIRY_ALERT = "expiry_alert"
    EXPIRY_ACTION_EXECUTE = "expiry_action_execute"
    EXPIRY_BACKUP_ACTION = "expiry_backup_action"
    # 行权流程
    REQUEST_OPTION_POSITIONS = "request_option_positions"
    EXERCISE_REQUEST_CREATED = "exercise_request_created"
    EXERCISE_COMPLETED = "exercise_completed"
    MANUAL_EXERCISE_REQUEST = "manual_exercise_request"
    # 分析/监控
    LIQUIDITY_ALERT = "liquidity_alert"
    PRICING_ALERT = "pricing_alert"
    # PnL 报表
    PNL_UPDATE = "pnl_update"
    DAILY_PNL_REPORT = "daily_pnl_report"
    PNL_RECORD_ADDED = "pnl_record_added"


"""统一使用 EventType.*，不再导出 EV_* 兼容别名。"""


@dataclass
class EventSubscription:
    """事件订阅信息"""

    subscriber_id: str
    event_types: set[str]
    callback: Callable
    filter_func: Callable | None = None
    max_retries: int = 3
    retry_delay: float = 1.0
    created_at: datetime = field(default_factory=lambda: datetime.now(UTC))
    is_active: bool = True


@dataclass
class EventMetrics:
    """事件指标"""

    total_published: int = 0
    total_processed: int = 0
    total_failed: int = 0
    total_retries: int = 0
    avg_processing_time: float = 0.0
    events_per_second: float = 0.0
    last_event_time: datetime | None = None
    event_type_counts: dict[str, int] = field(default_factory=dict)


@dataclass
class EventRecord:
    """事件记录"""

    event: BaseEvent
    status: EventStatus
    processing_time: float = 0.0
    retry_count: int = 0
    error_message: str = ""
    processed_at: datetime | None = None


class EventBus(BaseComponent):
    """
    异步事件总线

    提供高性能的发布-订阅模式事件系统，支持：
    - 异步事件发布和处理
    - 灵活的订阅和过滤机制
    - 事件持久化和重放
    - 性能监控和错误处理

    注意：EventBus现在通过依赖注入系统以单例模式管理
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("EventBus", config)

        # 订阅管理
        self._subscribers: dict[str, EventSubscription] = {}
        self._event_type_subscribers: dict[str, set[str]] = defaultdict(set)

        # 事件队列和处理
        self._event_queue: asyncio.Queue = asyncio.Queue(
            maxsize=self.config.get("max_queue_size", 20000)  # 增加默认队列大小
        )
        self._processing_tasks: set[asyncio.Task] = set()
        self._worker_count = self.config.get("worker_count", 8)  # 增加默认工作协程数量

        # 事件历史和重放
        self._event_history: deque = deque(
            maxlen=self.config.get("max_history_size", 1000)
        )
        self._enable_persistence = self.config.get("enable_persistence", True)

        # 性能监控 - 使用单独的事件指标
        self._event_metrics = EventMetrics()
        self._metrics_lock = asyncio.Lock()

        # 错误处理
        self._error_handlers: dict[str, Callable] = {}
        self._dead_letter_queue: deque = deque(
            maxlen=self.config.get("max_dead_letter_size", 100)
        )

        # 日志开关：通过环境变量 EVENTBUS_DEBUG 或配置 debug_logs 控制详细日志
        try:
            env_flag = os.getenv("EVENTBUS_DEBUG", "false").lower() == "true"
        except Exception:
            env_flag = False
        self._debug_logs = bool(self.config.get("debug_logs", False) or env_flag)

        # 结构化事件数据规范化配置
        self._field_aliases: dict[str, str] = {
            "ts": "timestamp",
            "time": "timestamp",
            "event_time": "timestamp",
            "sym": "symbol",
            "instrument": "symbol",
            "instrument_name": "symbol",
            "qty": "quantity",
            "description": "reason",
            "msg": "reason",
        }
        self._priority_order: list[str] = [
            "timestamp",
            "symbol",
            "reason",
            "action",
            "side",
            "severity",
            "confidence",
            "quantity",
            "size",
            "price",
            "strike",
            "option_type",
            "days_to_expiry",
            "account_id",
            "strategy_id",
            "order_id",
            "event_type",
        ]

    @staticmethod
    def _to_iso_timestamp(val: Any) -> str | Any:
        try:
            if isinstance(val, datetime):
                return val.astimezone(UTC).isoformat()
            if isinstance(val, (int | float)):
                sec = val / 1000.0 if val > 1e12 else float(val)
                return datetime.fromtimestamp(sec, UTC).isoformat()
            if isinstance(val, str):
                return val
        except Exception:
            return val
        return val

    def _normalize_event_data(self, payload: dict[str, Any] | None) -> dict[str, Any]:
        if not isinstance(payload, dict) or not payload:
            return {}
        data = dict(payload)
        for src, dst in self._field_aliases.items():
            if src in payload and dst not in data:
                data[dst] = payload[src]
        if "reason" not in data and "message" in payload:
            data["reason"] = payload["message"]
        if "timestamp" in data:
            data["timestamp"] = self._to_iso_timestamp(data["timestamp"])
        ordered: dict[str, Any] = {}
        for key in self._priority_order:
            if key in data:
                ordered[key] = data[key]
        for key in sorted(k for k in data if k not in ordered):
            ordered[key] = data[key]
        return ordered

    async def _initialize_impl(self) -> bool:
        """初始化事件总线"""
        try:
            # 初始化事件队列
            if self._event_queue.qsize() > 0:
                # 清空队列
                while not self._event_queue.empty():
                    try:
                        self._event_queue.get_nowait()
                    except asyncio.QueueEmpty:
                        break

            # 重置指标
            self._event_metrics = EventMetrics()

            await self.log_initialization(True, "EventBus initialized successfully")

            return True

        except Exception as e:
            await self.log_initialization(
                False, f"EventBus initialization failed: {str(e)}"
            )
            return False

    async def _start_impl(self) -> bool:
        """启动事件总线"""
        try:
            # 启动事件处理工作协程
            for i in range(self._worker_count):
                task = asyncio.create_task(self._event_worker(f"worker-{i}"))
                self._processing_tasks.add(task)

            await self.log_startup(
                True,
                f"EventBus started with {self._worker_count} workers",
            )

            return True

        except Exception as e:
            await self.log_startup(False, f"EventBus start failed: {str(e)}")
            return False

    async def _stop_impl(self) -> bool:
        """停止事件总线"""
        try:
            # 停止所有工作协程
            for task in self._processing_tasks:
                task.cancel()

            # 等待所有任务完成
            if self._processing_tasks:
                await asyncio.gather(*self._processing_tasks, return_exceptions=True)

            self._processing_tasks.clear()

            await self.log_shutdown(True, "EventBus stopped successfully")

            return True

        except Exception as e:
            await self.log_shutdown(False, f"EventBus stop failed: {str(e)}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            # 检查队列状态
            queue_size = self._event_queue.qsize()
            max_queue_size = self.config.get("max_queue_size", 10000)

            # 检查工作协程状态
            active_workers = sum(
                1 for task in self._processing_tasks if not task.done()
            )

            # 计算健康状态
            if queue_size > max_queue_size * 0.9:
                status = HealthStatus.DEGRADED
                message = f"Event queue nearly full: {queue_size}/{max_queue_size}"
            elif active_workers < self._worker_count:
                status = HealthStatus.DEGRADED
                message = (
                    f"Some workers inactive: {active_workers}/{self._worker_count}"
                )
            else:
                status = HealthStatus.HEALTHY
                message = "EventBus operating normally"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "queue_size": queue_size,
                    "max_queue_size": max_queue_size,
                    "active_workers": active_workers,
                    "total_workers": self._worker_count,
                    "total_subscribers": len(self._subscribers),
                    "events_processed": self._event_metrics.total_processed,
                    "events_failed": self._event_metrics.total_failed,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def subscribe(
        self,
        event_types: str | list[str],
        callback: Callable,
        subscriber_id: str | None = None,
        filter_func: Callable | None = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
    ) -> str:
        # 轻量吞吐采样：每100次订阅调用打印一次订阅总数
        self._subscribe_calls = getattr(self, "_subscribe_calls", 0) + 1
        if self._debug_logs and self._subscribe_calls % 100 == 1:
            await self.safe_log_debug(
                f"Throughput[subscribe]: calls={self._subscribe_calls}, total_subscribers={len(self._subscribers)}"
            )
        """
        订阅事件

        Args:
            event_types: 事件类型或类型列表
            callback: 回调函数
            subscriber_id: 订阅者ID（可选）
            filter_func: 事件过滤函数（可选）
            max_retries: 最大重试次数
            retry_delay: 重试延迟

        Returns:
            str: 订阅ID
        """
        if subscriber_id is None:
            subscriber_id = str(uuid.uuid4())

        # 标准化事件类型
        if isinstance(event_types, str):
            event_types = [event_types]

        event_types_set = set(event_types)

        # 创建订阅
        subscription = EventSubscription(
            subscriber_id=subscriber_id,
            event_types=event_types_set,
            callback=callback,
            filter_func=filter_func,
            max_retries=max_retries,
            retry_delay=retry_delay,
        )

        # 存储订阅
        self._subscribers[subscriber_id] = subscription

        # 更新事件类型索引
        for event_type in event_types_set:
            self._event_type_subscribers[event_type].add(subscriber_id)

        if self._debug_logs:
            await self.safe_log_debug(
                f"Subscription created: {subscriber_id} for {event_types}"
            )

        return subscriber_id

    async def unsubscribe(self, subscriber_id: str) -> bool:
        """
        取消订阅

        Args:
            subscriber_id: 订阅者ID

        Returns:
            bool: 是否成功取消订阅
        """
        if subscriber_id not in self._subscribers:
            return False

        subscription = self._subscribers[subscriber_id]

        # 从事件类型索引中移除
        for event_type in subscription.event_types:
            self._event_type_subscribers[event_type].discard(subscriber_id)

            # 如果该事件类型没有订阅者了，删除索引
            if not self._event_type_subscribers[event_type]:
                del self._event_type_subscribers[event_type]

        # 删除订阅
        del self._subscribers[subscriber_id]

        await self.safe_log_info(f"Subscription removed: {subscriber_id}")

        return True

    async def publish(self, event: BaseEvent) -> bool:
        """
        发布事件

        Args:
            event: 事件对象

        Returns:
            bool: 是否成功发布
        """
        # 立即记录方法被调用
        if self._debug_logs:
            await self.safe_log_debug(f"PUBLISH: {event.event_type}")

        try:
            # 轻量吞吐采样：每500条事件采样一次队列长度与速率
            self._publish_calls = getattr(self, "_publish_calls", 0) + 1
            if self._debug_logs and self._publish_calls % 500 == 1:
                await self.safe_log_debug(
                    f"Throughput[publish]: calls={self._publish_calls}, queue_size={self._event_queue.qsize()}"
                )

            # 验证事件
            if not isinstance(event, BaseEvent):
                raise ValueError("Event must be an instance of BaseEvent")

            # 设置事件元数据
            if not event.source:
                event.source = self.component_name

            # 检查队列状态
            queue_size_before = self._event_queue.qsize()
            queue_maxsize = self._event_queue.maxsize
            if self._debug_logs:
                await self.safe_log_debug(
                    f"QUEUE_BEFORE size={queue_size_before}/{queue_maxsize}"
                )

            # 在入队前规范化事件的结构化数据（仅补充规范键，不移除原键）
            try:
                if isinstance(event.data, dict):
                    event.data = self._normalize_event_data(event.data)
            except Exception:
                pass

            # 使用传统队列处理
            await self._event_queue.put(event)

            # 添加调试日志
            if self._debug_logs:
                await self.safe_log_debug(
                    f"EVENT_QUEUED: {event.event_type} q={self._event_queue.qsize()}"
                )

            # 更新指标
            async with self._metrics_lock:
                self._event_metrics.total_published += 1
                self._event_metrics.last_event_time = datetime.now(UTC)

                # 更新事件类型计数
                event_type = event.event_type
                self._event_metrics.event_type_counts[event_type] = (
                    self._event_metrics.event_type_counts.get(event_type, 0) + 1
                )

            # 使用简单的日志格式进行调试
            if self._debug_logs:
                await self.safe_log_debug(
                    f"EVENT_PUBLISHED: {event.event_type} id={event.event_id}"
                )

            return True

        except Exception as e:
            await self.safe_log_error(f"Failed to publish event: {str(e)}")
            return False

    async def get_event_history(
        self,
        event_type: str | None = None,
        limit: int | None = None,
        since: datetime | None = None,
    ) -> list[EventRecord]:
        """
        获取事件历史

        Args:
            event_type: 事件类型过滤
            limit: 返回数量限制
            since: 时间过滤

        Returns:
            List[EventRecord]: 事件记录列表
        """
        filtered_events = []

        for record in self._event_history:
            # 事件类型过滤
            if event_type and record.event.event_type != event_type:
                continue

            # 时间过滤
            if since and record.event.timestamp < since:
                continue

            filtered_events.append(record)

        # 按时间倒序排序
        filtered_events.sort(key=lambda x: x.event.timestamp, reverse=True)

        # 应用数量限制
        if limit:
            filtered_events = filtered_events[:limit]

        return filtered_events

    async def replay_events(
        self,
        event_type: str | None = None,
        since: datetime | None = None,
        target_subscriber: str | None = None,
    ) -> int:
        """
        重放事件

        Args:
            event_type: 事件类型过滤
            since: 时间过滤
            target_subscriber: 目标订阅者

        Returns:
            int: 重放的事件数量
        """
        events_to_replay = await self.get_event_history(event_type, since=since)
        replayed_count = 0

        for record in events_to_replay:
            event = record.event

            # 如果指定了目标订阅者，只发送给该订阅者
            if target_subscriber:
                if target_subscriber in self._subscribers:
                    subscription = self._subscribers[target_subscriber]
                    if event.event_type in subscription.event_types:
                        await self._process_event_for_subscriber(event, subscription)
                        replayed_count += 1
            else:
                # 重新发布事件
                await self.publish(event)
                replayed_count += 1

        await self.safe_log_info(f"Replayed {replayed_count} events")

        return replayed_count

    async def get_metrics(self) -> dict[str, Any]:
        """获取事件总线指标"""
        base_metrics = await super().get_metrics()

        async with self._metrics_lock:
            event_metrics = {
                "total_published": self._event_metrics.total_published,
                "total_processed": self._event_metrics.total_processed,
                "total_failed": self._event_metrics.total_failed,
                "total_retries": self._event_metrics.total_retries,
                "avg_processing_time": self._event_metrics.avg_processing_time,
                "events_per_second": self._event_metrics.events_per_second,
                "last_event_time": self._event_metrics.last_event_time.isoformat()
                if self._event_metrics.last_event_time
                else None,
                "event_type_counts": self._event_metrics.event_type_counts.copy(),
                "queue_size": self._event_queue.qsize(),
                "active_subscribers": len(self._subscribers),
                "dead_letter_queue_size": len(self._dead_letter_queue),
                "history_size": len(self._event_history),
            }

        base_metrics.update(event_metrics)
        return base_metrics

    async def _event_worker(self, worker_id: str):
        """事件处理工作协程"""
        if self._debug_logs:
            await self.safe_log_debug(f"Event worker {worker_id} started")

        try:
            while self.is_running:
                try:
                    # 从队列获取事件
                    event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)

                    if self._debug_logs:
                        await self.safe_log_debug(
                            f"WORKER_GOT_EVENT: {worker_id} -> {event.event_type}"
                        )

                    # 处理事件
                    await self._process_event(event)

                except TimeoutError:
                    # 超时是正常的，继续循环
                    continue
                except asyncio.CancelledError:
                    # 工作协程被取消
                    break
                except Exception as e:
                    await self.safe_log_error(f"Worker {worker_id} error: {str(e)}")

        except asyncio.CancelledError:
            pass
        finally:
            if self._debug_logs:
                await self.safe_log_debug(f"Event worker {worker_id} stopped")

    async def _process_event(self, event: BaseEvent):
        """处理单个事件"""
        # 立即记录方法被调用
        if self._debug_logs:
            await self.safe_log_debug(
                f"PROCESS_EVENT: {event.event_type} ({event.event_id})"
            )

        await self.safe_log_debug(
            f"Processing event: {event.event_type} ({event.event_id})"
        )
        start_time = time.time()
        event_record = EventRecord(event=event, status=EventStatus.PROCESSING)

        try:
            # 查找订阅者
            subscribers = self._event_type_subscribers.get(event.event_type, set())
            # 使用简单的日志格式进行调试
            if self._debug_logs:
                await self.safe_log_debug(
                    f"SUBSCRIBERS: n={len(subscribers)} for {event.event_type} ids={list(subscribers)}"
                )

                # 详细检查CausalEngine订阅状态
                if self._debug_logs:
                    if "CausalEngine" in self._subscribers:
                        causal_subscription = self._subscribers["CausalEngine"]
                        await self.safe_log_debug(
                            f"CausalEngine subscribed: active={causal_subscription.is_active}, types={causal_subscription.event_types}"
                        )
                    else:
                        await self.safe_log_debug(
                            f"CausalEngine not found. subscribers={list(self._subscribers.keys())}"
                        )

                # 检查DataUpdateEvent的订阅者
                if event.event_type == "DataUpdateEvent":
                    dataupdate_subscribers = self._event_type_subscribers.get(
                        "DataUpdateEvent", set()
                    )
                    if self._debug_logs:
                        await self.safe_log_debug(
                            f"DataUpdateEvent subscribers: {list(dataupdate_subscribers)}"
                        )

            if not subscribers:
                # 没有订阅者，记录并返回
                event_record.status = EventStatus.COMPLETED
                event_record.processing_time = time.time() - start_time
                await self._record_event(event_record)
                return

            # 并发处理所有订阅者
            tasks = []
            for subscriber_id in subscribers:
                if subscriber_id in self._subscribers:
                    subscription = self._subscribers[subscriber_id]
                    if subscription.is_active:
                        # 添加调试日志
                        if self._debug_logs:
                            await self.safe_log_debug(
                                f"TASK: {subscriber_id} for {event.event_type}"
                            )
                        task = asyncio.create_task(
                            self._process_event_for_subscriber(event, subscription)
                        )
                        tasks.append(task)
                    else:
                        if self._debug_logs:
                            await self.safe_log_debug(
                                f"INACTIVE_SUBSCRIBER: {subscriber_id}"
                            )
                else:
                    if self._debug_logs:
                        await self.safe_log_debug(
                            f"SUBSCRIBER_NOT_FOUND: {subscriber_id}"
                        )

            # 等待所有处理完成
            if tasks:
                results = await asyncio.gather(*tasks, return_exceptions=True)

                # 检查是否有失败
                failed_count = sum(
                    1 for result in results if isinstance(result, Exception)
                )

                if failed_count > 0:
                    event_record.status = EventStatus.FAILED
                    event_record.error_message = f"{failed_count} subscribers failed"

                    # 更新失败指标
                    async with self._metrics_lock:
                        self._event_metrics.total_failed += 1
                else:
                    event_record.status = EventStatus.COMPLETED
            else:
                event_record.status = EventStatus.COMPLETED

            # 更新指标
            async with self._metrics_lock:
                self._event_metrics.total_processed += 1
                processing_time = time.time() - start_time

                # 更新平均处理时间
                if self._event_metrics.total_processed == 1:
                    self._event_metrics.avg_processing_time = processing_time
                else:
                    self._event_metrics.avg_processing_time = (
                        self._event_metrics.avg_processing_time
                        * (self._event_metrics.total_processed - 1)
                        + processing_time
                    ) / self._event_metrics.total_processed

        except Exception as e:
            event_record.status = EventStatus.FAILED
            event_record.error_message = str(e)

            async with self._metrics_lock:
                self._event_metrics.total_failed += 1

            await self.safe_log_error(f"Event processing failed: {str(e)}")

        finally:
            event_record.processing_time = time.time() - start_time
            event_record.processed_at = datetime.now(UTC)
            await self._record_event(event_record)

    async def _process_event_for_subscriber(
        self, event: BaseEvent, subscription: EventSubscription
    ):
        """为特定订阅者处理事件"""
        try:
            # 添加调试日志
            if self.logger:
                await self.logger.info(
                    f"CALLING_SUBSCRIBER: {subscription.subscriber_id} for {event.event_type}"
                )

            # 应用过滤器
            if subscription.filter_func and not subscription.filter_func(event):
                return

            # 调用回调函数
            if asyncio.iscoroutinefunction(subscription.callback):
                await subscription.callback(event)
            else:
                subscription.callback(event)

            # 调用成功日志
            if self.logger:
                await self.logger.info(
                    f"CALLBACK_SUCCESS: {subscription.subscriber_id}"
                )

        except Exception as e:
            # 处理重试逻辑
            if subscription.max_retries > 0:
                await self._retry_event_processing(event, subscription, e)
            else:
                # 添加到死信队列
                self._dead_letter_queue.append(
                    {
                        "event": event,
                        "subscriber_id": subscription.subscriber_id,
                        "error": str(e),
                        "timestamp": datetime.now(UTC),
                    }
                )

            raise e

    async def _retry_event_processing(
        self, event: BaseEvent, subscription: EventSubscription, error: Exception
    ):
        """重试事件处理"""
        # 这里可以实现更复杂的重试逻辑
        # 记录错误信息
        async with self._metrics_lock:
            self._event_metrics.total_retries += 1

        await self.safe_log_warning(
            f"Event processing failed for subscriber {subscription.subscriber_id}: {str(error)}"
        )

    async def _record_event(self, event_record: EventRecord):
        """记录事件到历史"""
        if self._enable_persistence:
            self._event_history.append(event_record)

    def add_error_handler(self, event_type: str, handler: Callable):
        """添加错误处理器"""
        self._error_handlers[event_type] = handler

    def get_subscribers(
        self, event_type: str | None = None
    ) -> dict[str, EventSubscription]:
        """获取订阅者信息"""
        if event_type:
            subscriber_ids = self._event_type_subscribers.get(event_type, set())
            return {
                sid: self._subscribers[sid]
                for sid in subscriber_ids
                if sid in self._subscribers
            }
        else:
            return self._subscribers.copy()

    def get_dead_letter_queue(self) -> list[dict[str, Any]]:
        """获取死信队列"""
        return list(self._dead_letter_queue)
