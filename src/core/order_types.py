"""
统一的订单类型定义

提供整个系统使用的标准化订单相关枚举
"""

from enum import Enum


class OrderSide(str, Enum):
    """订单方向"""

    BUY = "BUY"
    SELL = "SELL"


class OrderType(str, Enum):
    """订单类型"""

    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP = "STOP"
    STOP_LIMIT = "STOP_LIMIT"
    CONDITIONAL = "CONDITIONAL"
    ICEBERG = "ICEBERG"
    TWO_WAY = "TWO_WAY"


class OrderStatus(Enum):
    """订单状态"""

    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    FAILED = "failed"


class TimeInForce(str, Enum):
    """订单有效期"""

    DAY = "DAY"
    GTC = "GTC"  # Good Till Cancel
    IOC = "IOC"  # Immediate or Cancel
    FOK = "FOK"  # Fill or Kill
