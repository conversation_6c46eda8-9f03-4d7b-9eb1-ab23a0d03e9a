"""
客户端配置工厂
统一处理各交易所客户端的配置加载和初始化逻辑
"""

from typing import Any

from ..utils.crypto_helper import CryptoHelper
from .config_manager import ConfigManager


class ClientConfigFactory:
    """客户端配置工厂类"""

    @staticmethod
    def load_client_config(
        config_manager: ConfigManager,
        exchange_name: str,
        env_api_key: str,
        env_api_secret: str,
        config_path: str,
    ) -> tuple[bool, dict[str, Any]]:
        """
        统一的客户端配置加载逻辑

        Args:
            config_manager: 配置管理器
            exchange_name: 交易所名称
            env_api_key: 环境变量中的API密钥名
            env_api_secret: 环境变量中的API密钥秘钥名
            config_path: 配置文件中的路径

        Returns:
            (is_valid, config_dict): 是否有效和配置字典
        """
        import os

        # 初始化加密工具
        crypto_helper = CryptoHelper()

        # 从环境变量读取配置（支持加密存储）
        encrypted_api_key = os.getenv(env_api_key)
        encrypted_api_secret = os.getenv(env_api_secret)

        # 解密API凭据
        api_key, api_secret = crypto_helper.decrypt_api_credentials(
            encrypted_api_key or "", encrypted_api_secret or ""
        )

        client_config = {
            "api_key": api_key,
            "api_secret": api_secret,
        }

        # 如果环境变量没有，则从配置文件读取
        if not client_config.get("api_key"):
            file_config = config_manager.get(config_path, {})
            client_config.update(file_config)

        # 检查 API 密钥是否有效（非空且非空字符串）
        api_key = client_config.get("api_key")
        api_secret = client_config.get("api_secret")

        is_valid = bool(
            api_key and api_secret and api_key.strip() and api_secret.strip()
        )

        return is_valid, client_config

    @staticmethod
    def create_binance_client(
        config_manager: ConfigManager, celery_manager, cache_manager, event_bus=None
    ):
        """创建Binance客户端"""
        from src.gateways.binance_client import BinanceClient

        is_valid, config = ClientConfigFactory.load_client_config(
            config_manager=config_manager,
            exchange_name="Binance",
            env_api_key="BINANCE_API_KEY",
            env_api_secret="BINANCE_API_SECRET",
            config_path="exchanges.binance",
        )

        if not is_valid:
            return None, "未检测到有效的 Binance API 配置"

        try:
            client = BinanceClient(
                api_key=config["api_key"],
                api_secret=config["api_secret"],
                celery_manager=celery_manager,
                config_manager=config_manager,
                cache_manager=cache_manager,
                event_bus=event_bus,
            )
            return client, "BinanceClient 初始化成功"
        except Exception as e:
            return None, f"BinanceClient 加载失败: {e}"

    @staticmethod
    def create_deribit_client(
        config_manager: ConfigManager, celery_manager, cache_manager, event_bus=None
    ):
        """创建Deribit客户端"""
        from src.gateways.deribit_client import DeribitClient

        is_valid, config = ClientConfigFactory.load_client_config(
            config_manager=config_manager,
            exchange_name="Deribit",
            env_api_key="DERIBIT_API_KEY",
            env_api_secret="DERIBIT_API_SECRET",
            config_path="exchanges.deribit",
        )

        if not is_valid:
            return None, "未检测到有效的 Deribit API 配置"

        try:
            client = DeribitClient(
                api_key=config["api_key"],
                api_secret=config["api_secret"],
                celery_manager=celery_manager,
                cache_manager=cache_manager,
                event_bus=event_bus,
            )
            return client, "DeribitClient 初始化成功"
        except Exception as e:
            return None, f"DeribitClient 加载失败: {e}"
