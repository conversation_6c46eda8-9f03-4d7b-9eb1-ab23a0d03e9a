#!/usr/bin/env python3
"""
应用程序容器 - ApplicationContainer

整合依赖注入、组件管理和应用程序生命周期，提供：
- 应用程序级别的依赖注入配置
- 组件自动发现和注册
- 应用程序启动和关闭流程
- 集中化的配置管理
- 应用程序健康监控

设计要点：
- 统一管理所有应用组件
- 自动化依赖注入配置
- 提供应用程序级别的生命周期管理
- 支持模块化组件注册
- 集成监控和健康检查
"""

import asyncio
import inspect
import pkgutil
from pathlib import Path

from .base_component import (
    BaseComponent,
    ComponentState,
    HealthCheckResult,
)
from .config_manager import ConfigManager
from .dependency_injector import DependencyInjector, DependencyScope


class ApplicationContainer(BaseComponent):
    """
    应用程序容器

    统一管理应用程序的依赖注入、组件生命周期和配置
    """

    def __init__(self, config_manager: ConfigManager | None = None):
        super().__init__("ApplicationContainer")
        self.config_manager = config_manager or ConfigManager()

        # 核心组件
        self.dependency_injector = DependencyInjector(config_manager)
        # EventBus现在通过依赖注入系统管理，不再作为容器属性

        # 组件注册
        self._registered_components: dict[str, type[BaseComponent]] = {}
        self._component_instances: dict[str, BaseComponent] = {}
        self._handled_components: set[str] = set()  # 记录已处理（包括跳过）的组件
        # 预先将容器级组件标记为已处理，避免尾部阶段重复尝试
        self._handled_components.update({"ApplicationContainer", "DependencyInjector"})
        self._startup_order: list[str] = []

        # 启动失败记录（组件名 -> 原因）
        self._failed_components: dict[str, str] = {}

        # 配置 - 按照分层顺序排列
        self.config = {
            "auto_discovery_packages": [
                # Infrastructure Layer (基础设施层) - 最先启动
                "core",
                "utils",
                "notifications",
                # Storage Layer (存储层)
                "data",
                "monitoring",
                # Services Layer (服务层)
                "services",
                # API Gateway Layer (API网关层)
                "gateways",
                # Data Layer (数据层) - 依赖网关层的数据
                "analysis",  # 重新启用分析模块 - 信号生成组件
                # Strategy Layer (策略层) - 依赖数据分析
                "strategy",
                # Risk Management & Execution Layer (风险管理和执行层) - 最后启动
                "execution",
            ],
            # 分层启动顺序 - 确保正确的组件依赖关系
            "component_startup_order": [
                # 1. Infrastructure Layer
                "ConfigManager",
                "AsyncLogger",
                "EventBus",
                "StateManager",
                "TelegramBotHandler",
                "CeleryWorkerPool",
                # 2. Storage Layer
                "TimescaleDBManager",
                "CacheManager",
                # 3. Services Layer
                "MarketDataService",
                "OptionSelectorService",
                "VaRCalculationService",
                # 4. API Gateway Layer
                "BinanceClient",
                "DeribitClient",
                "IBKRClient",
                "DataSynchronizer",
                # 5. Data Layer
                "DataEngine",
                "CausalEngine",
                "MicrostructureSignals",
                "GreeksCalculator",
                # 6. Strategy Layer
                "MarketDetector",
                "OptionGridStrategy",
                "BranchStrategy",
                "OptionLifecycleManager",
                # 7. Risk Management Layer
                "MarginCalculator",
                "ExpiryManager",
                "RiskEngine",
                "AlertManager",
                # 8. Execution Layer
                "OrderManager",
                "PositionManager",
                "PnLTracker",
                "ExerciseHandler",
                # 9. Coordination Layer (依赖所有上层组件)
                "StrategyCoordinator",
            ],
            "component_startup_timeout": 120.0,
            "parallel_startup": False,  # 改为顺序启动
            "health_check_interval": 60.0,
        }

        # 加载配置
        self._load_config()

    def _load_config(self):
        """加载配置"""
        try:
            container_config = self.config_manager.get_section("application_container")
            if container_config:
                self.config.update(container_config)
        except Exception:
            # 使用默认配置
            pass

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            await self.log_initialization(True, "Initializing ApplicationContainer")

            # 初始化依赖注入器
            if self.logger:
                await self.logger.info("🔧 初始化依赖注入器...")
            self.dependency_injector.set_logger(self.logger)
            if not await self.dependency_injector.initialize():
                return False

            # 注册核心依赖
            if self.logger:
                await self.logger.info("📋 注册核心依赖...")
            await self._register_core_dependencies()

            if self.logger:
                await self.logger.info("🔍 准备开始组件自动发现...")

            # 自动发现和注册组件
            try:
                await asyncio.wait_for(
                    self._auto_discover_components(),
                    timeout=self.config.get("component_discovery_timeout", 60.0),
                )
            except TimeoutError:
                if self.logger:
                    await self.logger.error("⏰ 组件自动发现超时，可能某个模块导入阻塞")
                return False

            await self.log_initialization(
                True, "ApplicationContainer initialized successfully"
            )
            return True

        except Exception as e:
            if self.logger:
                await self.log_initialization(
                    False, f"ApplicationContainer initialization failed: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            await self.log_startup(True, "Starting ApplicationContainer")

            # 启动依赖注入器
            if self.logger:
                await self.logger.info("🔧 启动依赖注入器...")
            if not await self.dependency_injector.start():
                return False

            # 创建和启动核心组件
            if self.logger:
                await self.logger.info("🏗️ 创建和启动核心组件...")
            await self._create_core_components()

            # 启动所有注册的组件
            if self.logger:
                await self.logger.info("准备启动所有已注册的组件...")
            await self._start_all_components()

            await self.log_startup(True, "ApplicationContainer started successfully")
            return True

        except Exception as e:
            await self.log_startup(False, f"ApplicationContainer start failed: {e}")
            import traceback

            if self.logger:
                await self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            await self.log_shutdown(True, "Stopping ApplicationContainer")

            # 停止所有组件（逆序）
            await self._stop_all_components()

            # 停止依赖注入器
            if not await self.dependency_injector.stop():
                await self.safe_log_warning("DependencyInjector stop failed")

            await self.log_shutdown(True, "ApplicationContainer stopped successfully")
            return True

        except Exception as e:
            await self.log_shutdown(False, f"ApplicationContainer stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        # 提前导入所需类，避免在异常处理中导入失败
        from src.core.base_component import HealthCheckResult, HealthStatus

        try:
            issues = []
            component_health = {}

            # 检查依赖注入器健康状态
            di_health = await self.dependency_injector.health_check()
            if di_health.status != HealthStatus.HEALTHY:
                issues.append(f"DependencyInjector: {di_health.message}")

            # 检查所有组件健康状态
            unhealthy_components = 0
            for name, component in self._component_instances.items():
                try:
                    health = await component.health_check()

                    # 确保health是HealthCheckResult对象而非字典
                    if isinstance(health, dict):
                        # 如果返回字典，创建HealthCheckResult对象
                        try:
                            status_raw = health.get("status", "unhealthy")
                            if isinstance(status_raw, HealthStatus):
                                status = status_raw
                            elif isinstance(status_raw, str):
                                try:
                                    status = HealthStatus[status_raw.upper()]
                                except KeyError:
                                    try:
                                        status = HealthStatus(status_raw.lower())
                                    except ValueError:
                                        status = HealthStatus.UNHEALTHY
                            else:
                                status = HealthStatus.UNHEALTHY

                            health = HealthCheckResult(
                                status=status,
                                message=health.get("message", "No message"),
                                details=health.get("details", {}),
                            )
                        except Exception as dict_error:
                            # 如果dict处理失败，创建默认的HealthCheckResult
                            health = HealthCheckResult(
                                status=HealthStatus.UNHEALTHY,
                                message=f"Failed to process health dict: {dict_error}",
                                details={"original_health": str(health)},
                            )

                    # 确保health是HealthCheckResult对象
                    if not hasattr(health, "status"):
                        health = HealthCheckResult(
                            status=HealthStatus.UNHEALTHY,
                            message=f"Invalid health check result: {type(health)}",
                            details={"original_health": str(health)},
                        )

                    component_health[name] = health.status.value
                    if health.status != HealthStatus.HEALTHY:
                        unhealthy_components += 1
                        if health.status == HealthStatus.UNHEALTHY:
                            issues.append(f"{name}: {health.message}")
                except Exception as e:
                    component_health[name] = "error"
                    unhealthy_components += 1
                    issues.append(f"{name}: Health check failed - {e}")
                    # 记录详细错误信息以便调试
                    if self.logger:
                        await self.logger.error(
                            f"health_check - 组件 {name} 错误: {e}",
                            extra={"component": name},
                        )

            # 确定整体健康状态
            total_components = len(self._component_instances)
            if unhealthy_components == 0:
                status = HealthStatus.HEALTHY
                message = f"All {total_components} components healthy"
            elif unhealthy_components <= total_components * 0.2:  # 20%以下不健康
                status = HealthStatus.DEGRADED
                message = (
                    f"{unhealthy_components}/{total_components} components unhealthy"
                )
            else:
                status = HealthStatus.UNHEALTHY
                message = (
                    f"{unhealthy_components}/{total_components} components unhealthy"
                )

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "total_components": total_components,
                    "unhealthy_components": unhealthy_components,
                    "component_health": component_health,
                    "issues": issues,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
            )

    async def _register_core_dependencies(self):
        """注册核心依赖"""
        # 注册配置管理器
        self.dependency_injector.register(
            ConfigManager,
            factory=lambda: self.config_manager,
            scope=DependencyScope.SINGLETON,
        )
        # 将ConfigManager添加到组件实例中以确保映射正确
        self._component_instances["ConfigManager"] = self.config_manager
        self._handled_components.add("ConfigManager")  # 标记为已处理

        # 注册依赖注入器自身
        self.dependency_injector.register(
            DependencyInjector,
            factory=lambda: self.dependency_injector,
            scope=DependencyScope.SINGLETON,
        )

        # 手动注册服务类 - 修复依赖注册错误
        try:
            from src.services.market_data_service import MarketDataService
            from src.services.option_selector import OptionSelectorService
            from src.services.var_calculation_service import VaRCalculationService

            # 直接注册服务类，依赖注入器会自动处理依赖
            self.dependency_injector.register(
                MarketDataService, scope=DependencyScope.SINGLETON
            )
            self.dependency_injector.register(
                OptionSelectorService, scope=DependencyScope.SINGLETON
            )
            self.dependency_injector.register(
                VaRCalculationService, scope=DependencyScope.SINGLETON
            )

            # 同时注册到组件注册表中，确保能被启动
            self._registered_components["MarketDataService"] = MarketDataService
            self._registered_components["OptionSelectorService"] = OptionSelectorService
            self._registered_components["VaRCalculationService"] = VaRCalculationService

            await self.safe_log_info("✅ 手动注册服务类成功")
        except Exception as e:
            await self.safe_log_error(f"❌ 手动注册服务类失败: {e}")

        # 手动注册策略模式组件 - 修复策略模式初始化失败
        try:
            from src.strategy.modes.accumulation_mode import AccumulationMode
            from src.strategy.modes.distribution_mode import DistributionMode
            from src.strategy.modes.sideways_mode import SidewaysMode

            self.dependency_injector.register(
                AccumulationMode, scope=DependencyScope.SINGLETON
            )
            self.dependency_injector.register(
                DistributionMode, scope=DependencyScope.SINGLETON
            )
            self.dependency_injector.register(
                SidewaysMode, scope=DependencyScope.SINGLETON
            )

            await self.safe_log_info("✅ 手动注册策略模式组件成功")
        except Exception as e:
            await self.safe_log_error(f"❌ 手动注册策略模式组件失败: {e}")

        # PerformanceMonitor 和 SystemMetricsIntegration 通过自动发现注册

        await self.safe_log_info("Core dependencies registered")

    async def _auto_discover_components(self):
        """自动发现组件"""
        try:
            if self.logger:
                await self.logger.info("🔍 开始自动发现组件...")

            base_path = Path(__file__).parent.parent
            discovered_count = 0

            # 自动发现包列表：如需调试可使用 logger.debug

            for package_name in self.config["auto_discovery_packages"]:
                if self.logger:
                    await self.logger.info(f"📂 扫描包: {package_name}")

                package_path = base_path / package_name
                if package_path.exists():
                    package_discovered = await self._scan_package(
                        package_path, package_name
                    )
                    discovered_count += package_discovered

                    if self.logger:
                        await self.logger.info(
                            f"✅ 包 {package_name} 扫描完成，发现 {package_discovered} 个组件"
                        )
                else:
                    if self.logger:
                        await self.logger.warning(f"⚠️ 包路径不存在: {package_path}")

            if self.logger:
                await self.logger.info(
                    f"🎉 自动发现完成，总共发现 {discovered_count} 个组件"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ 组件自动发现失败: {e}")
            import traceback
            if self.logger:
                await self.logger.error(f"错误详情: {traceback.format_exc()}")

    async def _scan_package(self, package_path: Path, package_name: str) -> int:
        """扫描包中的组件"""
        discovered_count = 0

        try:
            # 导入包
            import importlib

            if self.logger:
                await self.logger.info(f"  📦 导入包: src.{package_name}")

            package = importlib.import_module(f"src.{package_name}")

            # 扫描模块
            if self.logger:
                await self.logger.info(f"  🔍 开始扫描包 {package_name} 中的模块...")

            for _importer, modname, _ispkg in pkgutil.walk_packages(
                package.__path__, package.__name__ + "."
            ):
                try:
                    if self.logger:
                        await self.logger.info(f"    📄 扫描模块: {modname}")

                    module = importlib.import_module(modname)
                    count = await self._scan_module(module)
                    discovered_count += count

                    if self.logger:
                        await self.logger.info(
                            f"    ✅ 模块 {modname} 扫描完成，发现 {count} 个组件"
                        )

                except Exception as e:
                    if self.logger:
                        await self.logger.warning(f"    ⚠️ 扫描模块 {modname} 失败: {e}")

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"  ❌ 扫描包 src.{package_name} 失败: {e}")
            import traceback
            if self.logger:
                await self.logger.warning(f"  错误详情: {traceback.format_exc()}")

        return discovered_count

    async def _scan_module(self, module) -> int:
        """扫描模块中的组件"""
        discovered_count = 0

        # 读取配置中的排除列表，并补充默认需要排除的容器级组件
        excluded = set(self.config.get("excluded_components", []))
        # 兼容大小写/不同命名：统一处理为类名匹配
        excluded |= {"ApplicationContainer", "DependencyInjector"}

        for name in dir(module):
            try:
                obj = getattr(module, name)

                # 检查是否是BaseComponent的子类（通过类名检查以避免导入问题）
                if (
                    inspect.isclass(obj)
                    and obj != BaseComponent
                    and not obj.__name__.startswith("_")
                    and self._is_base_component_subclass(obj)
                ):
                    # 排除容器自身和核心注入器/总线，避免被当作普通组件启动
                    if obj.__name__ in excluded:
                        continue

                    # 排除抽象类
                    if inspect.isabstract(obj):
                        # 跳过抽象类
                        continue

                    await self._register_component(obj)
                    discovered_count += 1

            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"Error checking {name} in {module.__name__}: {e}"
                    )

        return discovered_count

    def _is_base_component_subclass(self, cls) -> bool:
        """检查类是否是BaseComponent的子类（通过MRO检查避免导入问题）"""
        try:
            # 排除BaseComponent本身
            if cls.__name__ == "BaseComponent":
                return False

            # 直接检查是否是BaseComponent子类
            if issubclass(cls, BaseComponent):
                return True

            # 通过MRO检查基类名称（处理不同导入路径的情况）
            for base in cls.__mro__:
                if base.__name__ == "BaseComponent" and hasattr(base, "initialize"):
                    return True

            return False
        except (TypeError, AttributeError):
            return False

    async def _register_component(self, component_class: type[BaseComponent]):
        """注册组件"""
        component_name = component_class.__name__

        # 强保护：永远不要把容器/注入器当作普通组件注册
        if component_name in {"ApplicationContainer", "DependencyInjector"}:
            return

        # 跳过ConfigManager，它需要特殊的factory函数在_register_core_dependencies()中注册
        if component_name == "ConfigManager":
            if self.logger:
                await self.logger.debug("跳过ConfigManager，使用特殊factory注册")
            return

        # 检查是否已经注册
        if component_name in self._registered_components:
            return

        self._registered_components[component_name] = component_class

        # 注册到依赖注入器
        self.dependency_injector.register(
            component_class,
            scope=getattr(
                component_class, "_dependency_scope", DependencyScope.SINGLETON
            ),
        )

        if self.logger:
            await self.logger.debug(f"Registered component: {component_name}")

    async def _create_core_components(self):
        """创建核心组件"""
        # EventBus现在通过依赖注入系统管理，不再手动创建
        # EventBus将在组件启动阶段自动创建和初始化
        pass

    async def _start_all_components(self):
        """按照分层顺序启动所有组件"""
        try:
            if self.logger:
                await self.logger.info(
                    f"🔄 准备启动 {len(self._component_instances)} 个组件"
                )
                await self.logger.info("🔄 采用分层顺序启动，确保依赖关系正确")
            else:
                # 无logger时不输出

            # 确保关键服务类已注册 - 修复依赖注册错误
            if self.logger:
                await self.logger.info("🔧 开始确保关键服务类已注册...")
            else:
                # 无logger时不输出

            await self._ensure_critical_services_registered()

            if self.logger:
                await self.logger.info("✅ 关键服务类注册完成")

            parallel_startup = self.config.get(
                "parallel_startup", True
            )  # 默认改为分层并行启动提升性能

            if self.logger:
                await self.logger.info(
                    f"🚀 启动模式: {'分层并行' if parallel_startup else '顺序'}"
                )
            else:
                # 无logger时不输出

            if parallel_startup:
                # 分层并行启动 - 新的默认推荐模式
                await self._start_components_layered_parallel()
            else:
                # 分层顺序启动
                await self._start_components_sequential()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ 启动所有组件失败: {e}")
            import traceback
            if self.logger:
                await self.logger.error(f"错误堆栈: {traceback.format_exc()}")
            raise

    async def _start_components_parallel(self):
        """并行启动组件"""
        startup_tasks = {}

        for component_name, component_class in self._registered_components.items():
            if component_name not in self._component_instances:
                task = asyncio.create_task(
                    self._start_component(component_name, component_class)
                )
                startup_tasks[component_name] = task

        if startup_tasks:
            # 等待所有任务完成，保持名称对应关系
            for component_name, task in startup_tasks.items():
                try:
                    await task
                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"Component {component_name} startup failed: {e}"
                        )

    def _get_component_startup_order(self) -> list[str]:
        """获取组件启动顺序（按分层架构依赖关系）"""
        # 基于设计文档的分层架构定义启动顺序
        startup_layers = [
            # 1. Infrastructure Layer（基础设施层）
            [
                "ConfigManager",
                "AsyncLogger",
                "EventBus",
                "StateManager",
                "TelegramBotHandler",
                "CeleryWorkerPool",
                "CeleryManager",
            ],
            # 2. Storage Layer（存储层）
            [
                "TimescaleDBManager",
                "CacheManager",
                "DatabaseCleaner",
            ],
            # 3. API Gateway Layer（API网关层）
            [
                "BinanceClient",
                "DeribitClient",
                "IBKRClient",
                "DataSynchronizer",
            ],
            # 4. Data Layer（数据层）
            [
                "DataEngine",
                "CausalEngine",
                "MicrostructureSignals",
                "GreeksCalculator",
            ],
            # 5. Strategy Layer（策略层）
            [
                "MarketDetector",
                "OptionGridStrategy",
                "BranchStrategy",
                "StrategyCoordinator",
                "OptionLifecycleManager",
            ],
            # 6. Risk Management Layer（风险管理层）
            [
                "MarginCalculator",
                "ExpiryManager",
                "RiskEngine",
                "AlertManager",
            ],
            # 7. Execution Layer（执行层）
            [
                "OrderManager",
                "PositionManager",
                "PnLTracker",
                "ExerciseHandler",
            ],
        ]

        # 展平层级结构为启动顺序列表
        startup_order = []
        for layer in startup_layers:
            startup_order.extend(layer)

        return startup_order

    async def _start_components_sequential(self):
        """顺序启动组件（按依赖关系分层启动）"""
        startup_order = self._get_component_startup_order()

        if self.logger:
            await self.logger.info(
                f"📋 按分层架构启动 {len(self._registered_components)} 个组件..."
            )

        # 按照启动顺序启动组件
        for component_name in startup_order:
            if (
                component_name in self._registered_components
                and component_name not in self._component_instances
            ):
                component_class = self._registered_components[component_name]
                try:
                    if self.logger:
                        await self.logger.info(f"⚡ 启动组件: {component_name}")
                    await self._start_component_with_timeout(
                        component_name,
                        component_class,
                        timeout=self.config.get("component_startup_timeout", 30.0),
                    )
                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"❌ 组件 {component_name} 启动失败: {e}"
                        )

        # 启动未在顺序列表中的其他组件（如果有）
        for component_name, component_class in self._registered_components.items():
            if component_name not in self._component_instances:
                try:
                    if self.logger:
                        await self.logger.info(f"⚡ 启动其他组件: {component_name}")
                    await self._start_component(component_name, component_class)
                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"❌ 组件 {component_name} 启动失败: {e}"
                        )

    async def _start_components_layered_parallel(self):
        """分层并行启动组件（在每层内并行，但保持层间依赖顺序）"""
        startup_layers = [
            # 1. Infrastructure Layer（基础设施层）
            [
                "ConfigManager",
                "AsyncLogger",
                "EventBus",
                "StateManager",
                "TelegramBotHandler",
                "CeleryWorkerPool",
                "CeleryManager",
            ],
            # 2. Storage Layer（存储层）
            [
                "TimescaleDBManager",
                "CacheManager",
                "DatabaseCleaner",
            ],
            # 3. API Gateway Layer（API网关层） - 这些可以并行启动
            [
                "BinanceClient",
                "DeribitClient",
                "IBKRClient",
                "DataSynchronizer",
            ],
            # 4. Data Layer（数据层）
            [
                "DataEngine",
                "CausalEngine",
                "MicrostructureSignals",
                "GreeksCalculator",
            ],
            # 5. Strategy Layer（策略层）
            [
                "MarketDetector",
                "OptionGridStrategy",
                "BranchStrategy",
                "StrategyCoordinator",
                "OptionLifecycleManager",
            ],
            # 6. Risk Management Layer（风险管理层）
            [
                "MarginCalculator",
                "ExpiryManager",
                "RiskEngine",
                "AlertManager",
            ],
            # 7. Execution Layer（执行层）
            [
                "OrderManager",
                "PositionManager",
                "PnLTracker",
                "ExerciseHandler",
            ],
            # 8. Monitoring Layer（监控层）
            [
                "PerformanceMonitor",
                "SystemMetricsIntegration",
                "CeleryTaskManager",
                "SmartDelayManager",
                "AuditTrail",
            ],
            # 9. Analysis Layer（分析层）
            [
                "GammaLiquidationAnalyzer",
                "VolatilityMismatchAnalyzer",
                "MonteCarloEngine",
                "PerformanceAnalyzer",
            ],
            # 10. Mode Layer（模式层）
            [
                "AccumulationMode",
                "DistributionMode",
                "SidewaysMode",
            ],
        ]

        if self.logger:
            await self.logger.info(f"开始分层并行启动 {len(startup_layers)} 层组件...")

        for layer_index, layer_components in enumerate(startup_layers, 1):
            if self.logger:
                await self.logger.info(
                    f"📋 启动第 {layer_index} 层: {len(layer_components)} 个组件"
                )

            # 根据配置选择并行或顺序启动
            if self.config.get("parallel_startup", True):
                layer_tasks = []
                for component_name in layer_components:
                    if (
                        component_name in self._registered_components
                        and component_name not in self._component_instances
                    ):
                        component_class = self._registered_components[component_name]
                        task = asyncio.create_task(
                            self._start_component_with_timeout(
                                component_name,
                                component_class,
                                timeout=self.config.get(
                                    "component_startup_timeout", 30.0
                                ),
                            )
                        )
                        layer_tasks.append((component_name, task))

                if layer_tasks:
                    # 等待当前层所有组件完成启动（有超时保护）
                    layer_start_time = asyncio.get_event_loop().time()

                    results = await asyncio.gather(
                        *[task for _, task in layer_tasks], return_exceptions=True
                    )

                    layer_duration = asyncio.get_event_loop().time() - layer_start_time

                    # 统计成功和失败的组件
                    success_count = 0
                    for (component_name, _), result in zip(
                        layer_tasks, results, strict=False
                    ):
                        is_success = result is True
                        if not is_success:
                            reason = self._failed_components.get(
                                component_name, str(result)
                            )
                            if self.logger:
                                await self.logger.error(
                                    f"❌ 层 {layer_index} 组件 {component_name} 启动失败: {reason}"
                                )
                        else:
                            success_count += 1

                    if self.logger:
                        await self.logger.info(
                            f"✅ 第 {layer_index} 层完成: {success_count}/{len(layer_tasks)} 个组件成功, 耗时 {layer_duration:.2f}秒"
                        )
            else:
                # 顺序启动，便于定位卡住组件
                for component_name in layer_components:
                    if (
                        component_name in self._registered_components
                        and component_name not in self._component_instances
                    ):
                        component_class = self._registered_components[component_name]
                        result = await self._start_component_with_timeout(
                            component_name,
                            component_class,
                            timeout=self.config.get("component_startup_timeout", 30.0),
                        )
                        if result is not True:
                            reason = self._failed_components.get(
                                component_name, "unknown"
                            )
                            if self.logger:
                                await self.logger.error(
                                    f"❌ 层 {layer_index} 组件 {component_name} 启动失败: {reason}"
                                )

        # 启动未在分层列表中的其他组件（如果有）——默认不启动，除非显式允许
        remaining_components = []
        safe_tail: set[str] = set(self.config.get("tail_components", []))
        for component_name, component_class in self._registered_components.items():
            # 排除已处理（已启动/已跳过/容器级）
            if (
                component_name in self._handled_components
                or component_name in self._component_instances
            ):
                continue
            # 仅启动显式允许的尾部组件
            if component_name in safe_tail:
                remaining_components.append((component_name, component_class))
            else:
                if self.logger:
                    await self.logger.info(f"⏭️ 跳过非白名单尾部组件: {component_name}")

        if remaining_components:
            if self.logger:
                await self.logger.info(
                    f"🔧 启动剩余 {len(remaining_components)} 个组件..."
                )

            remaining_tasks = [
                asyncio.create_task(self._start_component_with_timeout(name, cls))
                for name, cls in remaining_components
            ]

            if remaining_tasks:
                await asyncio.gather(*remaining_tasks, return_exceptions=True)

    async def _start_component_with_timeout(
        self,
        component_name: str,
        component_class: type[BaseComponent],
        timeout: float = 15.0,
    ):
        """带超时保护的组件启动"""
        try:
            return await asyncio.wait_for(
                self._start_component(component_name, component_class), timeout=timeout
            )
        except TimeoutError:
            if self.logger:
                await self.logger.error(
                    f"⏰ 组件 {component_name} 启动超时 ({timeout}秒)，跳过以避免阻塞整体启动"
                )
        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ 组件 {component_name} 启动异常: {e}")

    async def _start_component(
        self, component_name: str, component_class: type[BaseComponent]
    ):
        """启动单个组件"""
        try:
            # 严格防止重复处理 - 检查是否已经处理过此组件
            if component_name in self._handled_components:
                return True

            # 严格防止重复实例化 - 检查是否已经有实例
            if component_name in self._component_instances:
                existing_component = self._component_instances[component_name]
                # 检查现有实例的状态
                if existing_component.state in (
                    ComponentState.RUNNING,
                    ComponentState.INITIALIZED,
                ):
                    self._handled_components.add(component_name)
                    return True
                # 如果实例存在但状态是UNINITIALIZED，继续使用这个实例进行初始化
                elif existing_component.state == ComponentState.UNINITIALIZED:
                    # 使用现有实例，不删除
                    component = existing_component
                else:
                    # 如果实例存在但状态不正确，移除并重新创建
                    del self._component_instances[component_name]
                    component = None
            else:
                component = None

            # 永久排除不应被当作普通组件启动的类
            if component_name in {
                "ApplicationContainer",
                "DependencyInjector",
            }:
                self._handled_components.add(component_name)
                return True

            # 检查是否应该跳过此组件
            if self._should_skip_component(component_name):
                if self.logger:
                    await self.logger.info(f"跳过组件 {component_name} (已禁用)")
                # 跳过禁用组件
                self._handled_components.add(component_name)  # 记录为已处理
                return True

            # 启动组件
            # 从依赖注入器获取实例（如果还没有的话）
            if component is None:
                component = await self.dependency_injector.resolve(component_class)

            # 设置基础依赖
            component.set_logger(self.logger)
            component.set_dependency_injector(self.dependency_injector)

            # EventBus现在通过依赖注入系统自动管理，不再需要手动注入
            # 依赖注入系统会自动为需要EventBus的组件注入实例

            # 【修复】为需要特殊依赖注入的组件注入依赖
            if component_name == "MarketDataService":
                try:
                    # MarketDataService需要CacheManager
                    if hasattr(component, "cache_manager"):
                        component.cache_manager = self._component_instances.get(
                            "CacheManager"
                        )

                    if self.logger:
                        await self.logger.info("✅ 为 MarketDataService 注入依赖组件")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"MarketDataService依赖注入警告: {dep_inject_error}"
                        )

            # 【修复】为DataEngine注入所有必要依赖
            elif component_name == "DataEngine":
                try:
                    # 注入其他组件
                    if hasattr(component, "binance_client"):
                        component.binance_client = self._component_instances.get(
                            "BinanceClient"
                        )
                    if hasattr(component, "deribit_client"):
                        component.deribit_client = self._component_instances.get(
                            "DeribitClient"
                        )
                    if hasattr(component, "cache_manager"):
                        component.cache_manager = self._component_instances.get(
                            "CacheManager"
                        )
                    if hasattr(component, "timescale_manager"):
                        component.timescale_manager = self._component_instances.get(
                            "TimescaleDBManager"
                        )
                    if hasattr(component, "celery_manager"):
                        component.celery_manager = self._component_instances.get(
                            "CeleryTaskManager"
                        )
                    if hasattr(component, "data_synchronizer"):
                        component.data_synchronizer = self._component_instances.get(
                            "DataSynchronizer"
                        )

                    if self.logger:
                        await self.logger.info("✅ 为 DataEngine 注入所有依赖组件")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"DataEngine依赖注入警告: {dep_inject_error}"
                        )

            # 【修复】为OrderManager注入关键依赖，确保事件与执行链路打通
            elif component_name == "OrderManager":
                try:
                    # EventBus（必须）
                    try:
                        eb = await self.dependency_injector.resolve(EventBus)
                        if hasattr(component, "set_event_bus"):
                            component.set_event_bus(eb)
                        else:
                            component.event_bus = eb  # 回退
                    except Exception:
                        pass

                    # CacheManager（可选）
                    if hasattr(component, "cache_manager"):
                        component.cache_manager = self._component_instances.get(
                            "CacheManager"
                        )

                    # PositionManager（可选）
                    if hasattr(component, "position_manager"):
                        component.position_manager = self._component_instances.get(
                            "PositionManager"
                        )

                    # IBKRClient（可选，用于IBIT执行）
                    if hasattr(component, "ibkr_client"):
                        component.ibkr_client = self._component_instances.get(
                            "IBKRClient"
                        )

                    if self.logger:
                        await self.logger.info("✅ 为 OrderManager 注入 EventBus/依赖组件")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"OrderManager依赖注入警告: {dep_inject_error}"
                        )

            # 【修复】为RiskEngine注入关键依赖，确保对TradingDecision进行门控
            elif component_name == "RiskEngine":
                try:
                    # EventBus（必须）
                    try:
                        eb = await self.dependency_injector.resolve(EventBus)
                        if hasattr(component, "set_event_bus"):
                            component.set_event_bus(eb)
                        else:
                            component.event_bus = eb
                    except Exception:
                        pass

                    # CacheManager / MarketDataService / VaRCalculationService（可选）
                    if hasattr(component, "cache_manager"):
                        component.cache_manager = self._component_instances.get(
                            "CacheManager"
                        )
                    try:
                        from src.services.market_data_service import (
                            MarketDataService,
                        )

                        component.market_data_service = (
                            await self.dependency_injector.resolve(MarketDataService)
                        )
                    except Exception:
                        pass
                    try:
                        from src.services.var_calculation_service import (
                            VaRCalculationService,
                        )

                        component.var_calculation_service = (
                            await self.dependency_injector.resolve(
                                VaRCalculationService
                            )
                        )
                    except Exception:
                        pass

                    if self.logger:
                        await self.logger.info("✅ 为 RiskEngine 注入 EventBus/依赖组件")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"RiskEngine依赖注入警告: {dep_inject_error}"
                        )

            # 【修复】为PositionManager注入事件总线和缓存，确保订单回报更新仓位
            elif component_name == "PositionManager":
                try:
                    try:
                        eb = await self.dependency_injector.resolve(EventBus)
                        if hasattr(component, "set_event_bus"):
                            component.set_event_bus(eb)
                        else:
                            component.event_bus = eb
                    except Exception:
                        pass

                    if hasattr(component, "cache_manager"):
                        component.cache_manager = self._component_instances.get(
                            "CacheManager"
                        )

                    if self.logger:
                        await self.logger.info("✅ 为 PositionManager 注入 EventBus/CacheManager")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"PositionManager依赖注入警告: {dep_inject_error}"
                        )

            # 【修复】为PnLTracker注入事件总线和缓存，确保盈亏跟踪链路
            elif component_name == "PnLTracker":
                try:
                    try:
                        eb = await self.dependency_injector.resolve(EventBus)
                        if hasattr(component, "set_event_bus"):
                            component.set_event_bus(eb)
                        else:
                            component.event_bus = eb
                    except Exception:
                        pass

                    if hasattr(component, "cache_manager"):
                        component.cache_manager = self._component_instances.get(
                            "CacheManager"
                        )

                    if self.logger:
                        await self.logger.info("✅ 为 PnLTracker 注入 EventBus/CacheManager")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"PnLTracker依赖注入警告: {dep_inject_error}"
                        )

            # 【修复】为ExerciseHandler注入事件总线（处理到期行权流程）
            elif component_name == "ExerciseHandler":
                try:
                    try:
                        eb = await self.dependency_injector.resolve(EventBus)
                        if hasattr(component, "set_event_bus"):
                            component.set_event_bus(eb)
                        else:
                            component.event_bus = eb
                    except Exception:
                        pass
                    if self.logger:
                        await self.logger.info("✅ 为 ExerciseHandler 注入 EventBus")
                except Exception as dep_inject_error:
                    if self.logger:
                        await self.logger.warning(
                            f"ExerciseHandler依赖注入警告: {dep_inject_error}"
                        )

            # 严格的初始化和启动流程
            # 1. 首先检查组件当前状态
            current_state = component.state

            # 2. 根据状态决定是否需要初始化
            if current_state == ComponentState.UNINITIALIZED:
                init_success = await component.initialize()
                if not init_success:
                    if self.logger:
                        await self.logger.error(
                            f"Component {component_name} initialization failed"
                        )
                    return False
                if self.logger:
                    await self.logger.info(f"组件 {component_name} 初始化 成功")
            elif current_state == ComponentState.RUNNING:
                # 已经在运行，直接返回成功
                if self.logger:
                    await self.logger.info(f"组件 {component_name} 已在运行，跳过启动")
                return True
            elif current_state == ComponentState.INITIALIZED:
                # 已初始化但未启动，这是正常状态，继续启动流程
                if self.logger:
                    await self.logger.info(f"组件 {component_name} 已初始化，准备启动")
            elif current_state in (
                ComponentState.INITIALIZING,
                ComponentState.STARTING,
            ):
                # 正在初始化或启动中，等待完成
                if self.logger:
                    await self.logger.info(
                        f"组件 {component_name} 正在 {current_state.name}，等待完成"
                    )
                # 这里可以添加等待逻辑，但现在先跳过
                pass
            elif current_state in (
                ComponentState.STOPPING,
                ComponentState.STOPPED,
                ComponentState.ERROR,
                ComponentState.FAILED,
            ):
                # 这些状态下不应该启动组件
                if self.logger:
                    await self.logger.warning(
                        f"组件 {component_name} 处于状态 {current_state.name}，跳过启动"
                    )
                return True  # 返回True避免阻塞其他组件启动
            else:
                # 未知状态，但UNINITIALIZED已在上面处理，这里应该不会到达
                if current_state == ComponentState.UNINITIALIZED:
                    # 这种情况不应该发生，因为上面已经处理了
                    if self.logger:
                        await self.logger.debug(
                            f"组件 {component_name} UNINITIALIZED状态重复检查，继续初始化流程"
                        )
                    init_success = await component.initialize()
                    if not init_success:
                        return False
                else:
                    # 真正的未知状态
                    if self.logger:
                        await self.logger.warning(
                            f"组件 {component_name} 处于未知状态 {current_state.name if hasattr(current_state, 'name') else str(current_state)}，跳过启动"
                        )
                    return True

            # 3. 根据状态决定是否需要启动
            current_state = component.state
            if current_state == ComponentState.INITIALIZED:
                start_success = await component.start()
                if not start_success:
                    if self.logger:
                        await self.logger.error(
                            f"Component {component_name} start failed"
                        )
                    return False
                if self.logger:
                    await self.logger.info(f"组件 {component_name} 启动 成功")
            elif current_state == ComponentState.RUNNING:
                # 已经在运行，跳过启动步骤
                if self.logger:
                    await self.logger.info(
                        f"组件 {component_name} 已运行，跳过启动步骤"
                    )
            elif current_state in (
                ComponentState.STARTING,
                ComponentState.INITIALIZING,
            ):
                # 正在启动或初始化中，跳过
                if self.logger:
                    await self.logger.info(
                        f"组件 {component_name} 正在 {current_state.name}，跳过启动步骤"
                    )
            else:
                # 其他状态，记录警告但不阻塞
                if self.logger:
                    await self.logger.warning(
                        f"组件 {component_name} 状态为 {current_state.name if hasattr(current_state, 'name') else str(current_state)}，跳过启动"
                    )
                # 不返回False，避免阻塞其他组件

            # 4. 成功启动，记录实例和状态
            self._component_instances[component_name] = component
            if component_name not in self._startup_order:
                self._startup_order.append(component_name)
            self._handled_components.add(component_name)  # 记录为已处理

            if self.logger:
                await self.logger.info(
                    f"Component {component_name} started successfully"
                )
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to start component {component_name}: {e}"
                )
            # 不重新抛出异常，继续启动其他组件

    async def _stop_all_components(self):
        """停止所有组件"""
        # 按启动顺序的逆序停止
        stopped_components = set()

        for component_name in reversed(self._startup_order):
            if (
                component_name in self._component_instances
                and component_name not in stopped_components
            ):
                try:
                    component = self._component_instances[component_name]

                    # 检查组件状态，只停止需要停止的组件
                    if component.state in (
                        ComponentState.RUNNING,
                        ComponentState.ERROR,
                        ComponentState.FAILED,
                    ):
                        stop_success = await component.stop()
                        if stop_success:
                            stopped_components.add(component_name)
                            if self.logger:
                                await self.logger.info(
                                    f"Component {component_name} stopped"
                                )
                        else:
                            if self.logger:
                                await self.logger.warning(
                                    f"Component {component_name} stop returned False"
                                )
                    elif component.state == ComponentState.STOPPED:
                        # 已经停止，直接标记为已处理
                        stopped_components.add(component_name)
                    else:
                        # 其他状态，记录但不尝试停止
                        if self.logger:
                            await self.logger.info(
                                f"Component {component_name} in state {component.state.value}, skipping stop"
                            )
                        stopped_components.add(component_name)

                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"Failed to stop component {component_name}: {e}"
                        )
                    # 即使停止失败，也标记为已处理，避免重复尝试
                    stopped_components.add(component_name)

        # 清理实例和启动顺序
        self._component_instances.clear()
        self._startup_order.clear()
        self._handled_components.clear()

    def get_component(self, component_name: str) -> BaseComponent | None:
        """获取组件实例"""
        return self._component_instances.get(component_name)

    async def get_component_by_type(self, component_type: type) -> BaseComponent | None:
        """根据类型获取组件实例"""
        try:
            if self.dependency_injector is None:
                return None
            return await self.dependency_injector.resolve(component_type)
        except Exception:
            return None

    def get_all_components(self) -> dict[str, BaseComponent]:
        """获取所有组件实例"""
        return self._component_instances.copy()

    async def restart_component(self, component_name: str) -> bool:
        """重启指定组件"""
        if component_name not in self._component_instances:
            return False

        try:
            component = self._component_instances[component_name]
            success = await component.restart()

            if self.logger:
                if success:
                    await self.logger.info(
                        f"Component {component_name} restarted successfully"
                    )
                else:
                    await self.logger.error(
                        f"Component {component_name} restart failed"
                    )

            return success

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to restart component {component_name}: {e}"
                )
            return False

    def _should_skip_component(self, component_name: str) -> bool:
        """检查是否应该跳过组件启动"""
        import os

        from dotenv import load_dotenv

        # 确保加载环境变量
        load_dotenv()

        # 根据环境变量决定是否启动特定组件
        skip_rules = {
            "IBKRClient": os.getenv("ENABLE_IBKR", "true").lower() != "true",
            "BinanceClient": os.getenv("ENABLE_BINANCE", "true").lower() != "true",
            "DeribitClient": os.getenv("ENABLE_DERIBIT", "true").lower() != "true",
            "TelegramBotHandler": os.getenv("ENABLE_TELEGRAM", "false").lower()
            != "true",
            "TimescaleDBManager": os.getenv("ENABLE_TIMESCALEDB", "true").lower()
            != "true",
        }

        return skip_rules.get(component_name, False)

    async def _ensure_critical_services_registered(self):
        """确保关键服务类已注册 - 修复依赖注册错误"""
        try:
            if not self.dependency_injector:
                await self.safe_log_error("❌ DependencyInjector 未初始化")
                return

            from src.services.market_data_service import MarketDataService
            from src.services.option_selector import OptionSelectorService
            from src.services.var_calculation_service import VaRCalculationService

            # 强制重新注册关键服务类 - 确保它们在依赖注入器中可用
            self.dependency_injector.register(
                MarketDataService, scope=DependencyScope.SINGLETON
            )
            self._registered_components["MarketDataService"] = MarketDataService
            await self.safe_log_info("✅ 强制重新注册 MarketDataService")

            self.dependency_injector.register(
                OptionSelectorService, scope=DependencyScope.SINGLETON
            )
            self._registered_components["OptionSelectorService"] = OptionSelectorService
            await self.safe_log_info("✅ 强制重新注册 OptionSelectorService")

            self.dependency_injector.register(
                VaRCalculationService, scope=DependencyScope.SINGLETON
            )
            self._registered_components["VaRCalculationService"] = VaRCalculationService
            await self.safe_log_info("✅ 强制重新注册 VaRCalculationService")

            await self.safe_log_info("✅ 关键服务类强制注册完成")
        except Exception as e:
            await self.safe_log_error(f"❌ 确保关键服务类注册失败: {e}")
