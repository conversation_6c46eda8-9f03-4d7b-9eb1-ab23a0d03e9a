#!/usr/bin/env python3
"""
日志混入类 - LoggerMixin

统一日志处理，消除重复的 `if self.logger:` 检查模式
"""

from typing import Any


class LoggerMixin:
    """日志混入类

    提供统一的日志处理方法，自动检查logger是否存在
    消除代码中大量重复的日志检查逻辑
    """

    logger: Any | None = None  # 类型提示，实际由子类提供

    async def safe_log_info(self, message: str, *args, **kwargs):
        """安全记录信息日志"""
        if self.logger and hasattr(self.logger, "info"):
            try:
                await self.logger.info(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error in safe_log_info: {e}")

    async def safe_log_error(self, message: str, *args, **kwargs):
        """安全记录错误日志"""
        if self.logger and hasattr(self.logger, "error"):
            try:
                await self.logger.error(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    async def safe_log_warning(self, message: str, *args, **kwargs):
        """安全记录警告日志"""
        if self.logger and hasattr(self.logger, "warning"):
            try:
                await self.logger.warning(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    async def safe_log_debug(self, message: str, *args, **kwargs):
        """安全记录调试日志"""
        if self.logger and hasattr(self.logger, "debug"):
            try:
                await self.logger.debug(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    async def safe_log_critical(self, message: str, *args, **kwargs):
        """安全记录严重错误日志"""
        if self.logger and hasattr(self.logger, "critical"):
            try:
                await self.logger.critical(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    async def safe_log(self, level: str, message: str, *args, **kwargs):
        """安全记录指定级别的日志"""
        if self.logger and hasattr(self.logger, level):
            try:
                log_method = getattr(self.logger, level)
                await log_method(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    def sync_safe_log_info(self, message: str, *args, **kwargs):
        """同步版本的安全信息日志"""
        if self.logger and hasattr(self.logger, "info"):
            try:
                # 如果是异步logger，跳过同步调用
                if callable(self.logger.info):
                    # 检查是否是协程函数
                    import inspect

                    if not inspect.iscoroutinefunction(self.logger.info):
                        self.logger.info(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    def sync_safe_log_error(self, message: str, *args, **kwargs):
        """同步版本的安全错误日志"""
        if self.logger and hasattr(self.logger, "error"):
            try:
                import inspect

                if not inspect.iscoroutinefunction(self.logger.error):
                    self.logger.error(message, *args, **kwargs)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

    def has_logger(self) -> bool:
        """检查是否有有效的logger"""
        return self.logger is not None

    def get_logger_type(self) -> str:
        """获取logger类型"""
        if not self.logger:
            return "none"

        logger_type = type(self.logger).__name__
        return logger_type.lower()

    async def log_component_lifecycle(
        self, component_name: str, action: str, success: bool = True, details: str = ""
    ):
        """记录组件生命周期事件"""
        status = "成功" if success else "失败"
        message = f"组件 {component_name} {action} {status}"

        if details:
            message += f": {details}"

        if success:
            await self.safe_log_info(message)
        else:
            await self.safe_log_error(message)

    async def log_performance_metric(
        self, metric_name: str, value: float, unit: str = "", context: str = ""
    ):
        """记录性能指标"""
        message = f"性能指标 [{metric_name}]: {value}{unit}"

        if context:
            message += f" ({context})"

        await self.safe_log_debug(message)

    async def log_business_event(
        self, event_type: str, details: dict = None, level: str = "info"
    ):
        """记录业务事件"""
        message = f"业务事件 [{event_type}]"

        if details:
            detail_str = ", ".join([f"{k}={v}" for k, v in details.items()])
            message += f": {detail_str}"

        await self.safe_log(level, message)

    async def log_error_with_context(
        self, error: Exception, context: str = "", include_traceback: bool = False
    ):
        """记录带上下文的错误"""
        error_msg = f"错误: {str(error)}"

        if context:
            error_msg = f"{context} - {error_msg}"

        if include_traceback:
            import traceback

            error_msg += f"\n堆栈跟踪:\n{traceback.format_exc()}"

        await self.safe_log_error(error_msg)

    def create_child_logger(self, child_name: str):
        """创建子logger（如果支持）"""
        if not self.logger:
            return None

        # 如果logger支持子logger创建
        if hasattr(self.logger, "getChild"):
            try:
                return self.logger.getChild(child_name)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

        # 如果logger有bind方法（如loguru）
        if hasattr(self.logger, "bind"):
            try:
                return self.logger.bind(component=child_name)
            except Exception as e:
                # 日志记录失败 - 只在开发环境打印到标准输出
                import os

                if os.getenv("DEBUG", "").lower() in ("true", "1"):
                    print(f"Logger error: {e}")

        return self.logger


class ComponentLoggerMixin(LoggerMixin):
    """组件专用日志混入

    为BaseComponent及其子类提供更具体的日志方法
    """

    component_name: str = "UnknownComponent"  # 由子类设置

    async def log_initialization(self, success: bool = True, details: str = ""):
        """记录初始化事件"""
        await self.log_component_lifecycle(
            self.component_name, "初始化", success, details
        )

    async def log_startup(self, success: bool = True, details: str = ""):
        """记录启动事件"""
        await self.log_component_lifecycle(
            self.component_name, "启动", success, details
        )

    async def log_shutdown(self, success: bool = True, details: str = ""):
        """记录关闭事件"""
        await self.log_component_lifecycle(
            self.component_name, "关闭", success, details
        )

    async def log_health_check(self, status: str, message: str = ""):
        """记录健康检查结果"""
        log_message = f"健康检查 [{self.component_name}]: {status}"

        if message:
            log_message += f" - {message}"

        # 根据状态选择日志级别
        if status.lower() in ["healthy", "正常"]:
            await self.safe_log_debug(log_message)
        elif status.lower() in ["degraded", "下降"]:
            await self.safe_log_warning(log_message)
        else:
            await self.safe_log_error(log_message)

    async def log_state_change(self, from_state: str, to_state: str):
        """记录状态变更"""
        message = f"状态变更 [{self.component_name}]: {from_state} -> {to_state}"
        await self.safe_log_info(message)

    async def log_configuration_change(
        self, config_key: str, old_value: Any = None, new_value: Any = None
    ):
        """记录配置变更"""
        if old_value is not None and new_value is not None:
            message = f"配置变更 [{self.component_name}] {config_key}: {old_value} -> {new_value}"
        else:
            message = f"配置变更 [{self.component_name}] {config_key}: {new_value}"

        await self.safe_log_info(message)

    async def log_metric_update(self, metrics: dict):
        """记录指标更新"""
        metrics_str = ", ".join([f"{k}={v}" for k, v in metrics.items()])
        message = f"指标更新 [{self.component_name}]: {metrics_str}"
        await self.safe_log_debug(message)


class TradingLoggerMixin(ComponentLoggerMixin):
    """交易专用日志混入

    为交易相关组件提供专门的日志方法
    """

    async def log_trade_execution(
        self,
        trade_type: str,
        instrument: str,
        quantity: float,
        price: float,
        success: bool = True,
    ):
        """记录交易执行"""
        status = "成功" if success else "失败"
        message = f"交易执行 {status} [{trade_type}]: {instrument} 数量={quantity} 价格={price}"

        if success:
            await self.safe_log_info(message)
        else:
            await self.safe_log_error(message)

    async def log_order_update(self, order_id: str, status: str, details: dict = None):
        """记录订单更新"""
        message = f"订单更新 [{order_id}]: {status}"

        if details:
            detail_str = ", ".join([f"{k}={v}" for k, v in details.items()])
            message += f" ({detail_str})"

        await self.safe_log_info(message)

    async def log_risk_event(self, risk_type: str, severity: str, details: str = ""):
        """记录风险事件"""
        message = f"风险事件 [{risk_type}] 严重性={severity}"

        if details:
            message += f": {details}"

        # 根据严重性选择日志级别
        if severity.lower() in ["low", "低"]:
            await self.safe_log_info(message)
        elif severity.lower() in ["medium", "中"]:
            await self.safe_log_warning(message)
        else:
            await self.safe_log_error(message)

    async def log_market_data_update(
        self, instrument: str, data_type: str, timestamp: str = ""
    ):
        """记录市场数据更新"""
        message = f"市场数据更新 [{instrument}] 类型={data_type}"

        if timestamp:
            message += f" 时间={timestamp}"

        await self.safe_log_debug(message)

    async def log_strategy_signal(
        self,
        strategy_name: str,
        signal_type: str,
        confidence: float,
        details: dict = None,
    ):
        """记录策略信号"""
        message = (
            f"策略信号 [{strategy_name}] 类型={signal_type} 置信度={confidence:.2f}"
        )

        if details:
            detail_str = ", ".join([f"{k}={v}" for k, v in details.items()])
            message += f" ({detail_str})"

        await self.safe_log_info(message)
