"""
配置管理系统 - ConfigManager

支持YAML配置文件、热重载、参数验证、环境变量覆盖等功能
"""

import asyncio
import logging
import os
import re
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from pathlib import Path
from typing import Any

import yaml
from dotenv import load_dotenv
from pydantic import BaseModel, ValidationError, field_validator
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

from .base_component import BaseComponent, HealthCheckResult, HealthStatus

logger = logging.getLogger(__name__)


@dataclass
class ConfigChangeEvent:
    """配置变更事件"""

    config_path: str
    old_value: Any
    new_value: Any
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    change_type: str = "update"  # update, add, delete


class ConfigValidationError(Exception):
    """配置验证异常"""

    pass


class SystemConfig(BaseModel):
    """系统配置模型"""

    environment: str = "development"
    log_level: str = "INFO"
    max_workers: int = 4
    debug_mode: bool = False
    use_testnet: bool = True

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v):
        if v not in ["development", "testing", "production"]:
            raise ValueError(
                "environment must be one of: development, testing, production"
            )
        return v

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v):
        if v not in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]:
            raise ValueError("log_level must be a valid logging level")
        return v


class RateLimitsConfig(BaseModel):
    """交易所限频配置模型"""

    # Binance 限频配置
    orders_per_second: int | None = None
    requests_per_minute: int | None = None
    weight_per_minute: int | None = None

    # Deribit 限频配置
    requests_per_second: int | None = None

    # 通用限频配置
    max_requests_per_minute: int | None = None
    max_orders_per_second: int | None = None

    @field_validator(
        "orders_per_second",
        "requests_per_minute",
        "weight_per_minute",
        "requests_per_second",
        "max_requests_per_minute",
        "max_orders_per_second",
        mode="before",
    )
    @classmethod
    def validate_positive_rates(cls, v):
        if v is not None and v <= 0:
            raise ValueError("Rate limit values must be positive")
        return v


class ExchangeConfig(BaseModel):
    """交易所配置模型"""

    api_key: str
    api_secret: str
    testnet: bool = True
    rate_limits: RateLimitsConfig | dict[str, int] = RateLimitsConfig()
    endpoints: dict[str, dict[str, str]] = {}

    @field_validator("rate_limits", mode="before")
    @classmethod
    def validate_rate_limits(cls, v):
        if isinstance(v, dict):
            # 如果是字典，转换为 RateLimitsConfig
            return RateLimitsConfig(**v)
        return v


class RedisConfig(BaseModel):
    """Redis配置模型"""

    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: str | None = None
    cluster_mode: bool = False
    connection_pool: dict[str, Any] = {}


class DatabaseConfig(BaseModel):
    """数据库配置模型"""

    host: str = "localhost"
    port: int = 5432
    database: str
    username: str
    password: str
    connection_pool: dict[str, int] = {}
    timescaledb: dict[str, str] = {}


class StrategyConfig(BaseModel):
    """策略配置模型"""

    type: str
    capital_allocation: float
    is_active: bool = True
    parameters: dict[str, Any] = {}
    risk_limits: dict[str, Any] = {}

    @field_validator("capital_allocation")
    @classmethod
    def validate_capital_allocation(cls, v):
        if not 0 < v <= 1:
            raise ValueError("capital_allocation must be between 0 and 1")
        return v


class RiskLimitsConfig(BaseModel):
    """风险限制配置模型"""

    max_delta: float = 0.5
    max_gamma: float = 0.05
    max_margin_usage: float = 0.70
    max_daily_loss: float = 0.05
    min_liquidity_score: float = 0.20

    @field_validator(
        "max_delta",
        "max_gamma",
        "max_margin_usage",
        "max_daily_loss",
        "min_liquidity_score",
    )
    @classmethod
    def validate_positive(cls, v):
        if v <= 0:
            raise ValueError("Risk limit values must be positive")
        return v


class TelegramConfig(BaseModel):
    """Telegram配置模型"""

    bot_token: str
    authorized_users: list[int] = []


class ConfigFileHandler(FileSystemEventHandler):
    """配置文件变更监听器"""

    def __init__(self, config_manager: "ConfigManager"):
        self.config_manager = config_manager

    def on_modified(self, event):
        if not event.is_directory and str(event.src_path).endswith(".yaml"):
            logger.info(f"Config file modified: {event.src_path}")
            # 使用线程安全的方式调度协程
            try:
                loop = asyncio.get_running_loop()
                loop.create_task(self.config_manager._reload_config())
            except RuntimeError:
                # 如果没有运行的事件循环，记录日志但不执行重载
                logger.warning("No running event loop, config reload skipped")


class ConfigManager(BaseComponent):
    """
    配置管理系统

    功能特性：
    - 支持YAML配置文件
    - 环境变量替换
    - 配置热重载
    - 参数验证
    - 配置变更审计
    - 多环境支持
    """

    def __init__(self, config_path: str = "config/config.yaml"):
        # 先初始化config_data，避免在super().__init__中访问时出错
        self.config_data: dict[str, Any] = {}

        super().__init__("ConfigManager")

        # 统一使用config/config.yaml，智能路径检测以确保在不同执行环境下都能找到
        standard_config_paths = [
            "config/config.yaml",  # 从根目录执行时的标准路径
            "../config/config.yaml",  # 从src目录执行时的路径
        ]

        # 如果用户明确传入了路径，则严格使用该路径，不做回退
        # 只有在未传入自定义路径时，才在标准路径集合中寻找第一个存在的文件
        if config_path != "config/config.yaml":
            # 保留用户指定路径（即使不存在），以便后续明确抛出 FileNotFoundError
            self.config_path = Path(config_path)
        else:
            # 未指定自定义路径：尝试在标准路径中寻找可用的配置文件
            for path in standard_config_paths:
                test_path = Path(path)
                if test_path.exists():
                    self.config_path = test_path
                    break
            else:
                # 若均不存在，则默认指向标准路径，后续加载时抛出异常
                self.config_path = Path("config/config.yaml")
        self.change_callbacks: list[Callable] = []
        self.change_history: list[ConfigChangeEvent] = []
        self.observer = None
        self._is_watching = False

        # 配置模型映射
        self.config_models = {
            "system": SystemConfig,
            "redis": RedisConfig,
            "database": DatabaseConfig,
            "risk_limits": RiskLimitsConfig,
            "telegram": TelegramConfig,
        }

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载环境变量
            load_dotenv()

            await self.load_config()
            if self.logger:
                await self.logger.info("ConfigManager initialized successfully")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize ConfigManager: {e}")
            else:
                logger.error(f"Failed to initialize ConfigManager: {e}")
            return False

    async def load_config(self) -> dict[str, Any]:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Config file not found: {self.config_path}")

            with open(self.config_path, encoding="utf-8") as f:
                raw_config = yaml.safe_load(f)

            # 环境变量替换
            self.config_data = self._substitute_env_vars(raw_config)

            # 验证配置
            await self._validate_config()

            if self.logger:
                await self.logger.info(f"Configuration loaded from {self.config_path}")
            else:
                logger.info(f"Configuration loaded from {self.config_path}")
            return self.config_data

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to load config: {e}")
            else:
                logger.error(f"Failed to load config: {e}")
            raise ConfigValidationError(f"Config loading failed: {str(e)}") from e

    def _substitute_env_vars(self, config: Any) -> Any:
        """递归替换环境变量"""
        if isinstance(config, dict):
            return {k: self._substitute_env_vars(v) for k, v in config.items()}
        elif isinstance(config, list):
            return [self._substitute_env_vars(item) for item in config]
        elif isinstance(config, str):
            # 替换 ${VAR_NAME} 格式的环境变量
            pattern = r"\$\{([^}]+)\}"

            def replace_env_var(match):
                var_name = match.group(1)
                default_value = None

                # 支持默认值语法: ${VAR_NAME:default_value}
                if ":" in var_name:
                    var_name, default_value = var_name.split(":", 1)

                env_value = os.getenv(var_name, default_value)
                if env_value is None:
                    raise ValueError(
                        f"Environment variable {var_name} not found and no default provided"
                    )

                # 尝试转换数据类型
                return self._convert_env_value(env_value)

            return re.sub(pattern, replace_env_var, config)
        else:
            return config

    def _convert_env_value(self, value: str) -> str:
        """转换环境变量值的数据类型 - 保持为字符串，让YAML解析器处理类型转换"""
        return value

    async def _validate_config(self) -> bool:
        """验证配置数据"""
        try:
            for section_name, model_class in self.config_models.items():
                if section_name in self.config_data:
                    try:
                        # 对于exchanges部分，需要特殊处理
                        if section_name == "exchanges":
                            for exchange_config in self.config_data[
                                section_name
                            ].values():
                                ExchangeConfig(**exchange_config)
                        # 对于strategies部分，需要特殊处理
                        elif section_name == "strategies":
                            for strategy_config in self.config_data[
                                section_name
                            ].values():
                                StrategyConfig(**strategy_config)
                        else:
                            model_class(**self.config_data[section_name])
                    except ValidationError as e:
                        raise ConfigValidationError(
                            f"Validation failed for {section_name}: {e}"
                        ) from e

            if self.logger:
                await self.logger.info("Configuration validation passed")
            else:
                logger.info("Configuration validation passed")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Configuration validation failed: {e}")
            else:
                logger.error(f"Configuration validation failed: {e}")
            raise

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            await self.start_watching()
            if self.logger:
                await self.logger.info("ConfigManager started successfully")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start ConfigManager: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            await self.stop_watching()
            if self.logger:
                await self.logger.info("ConfigManager stopped successfully")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop ConfigManager: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查配置文件是否存在
            config_file_exists = self.config_path.exists()

            # 检查配置数据是否已加载
            config_loaded = bool(self.config_data)

            # 检查监听器状态
            watcher_running = self._is_watching

            # 检查变更历史记录
            recent_changes = len(self.get_change_history(hours=1))

            if config_file_exists and config_loaded:
                status = HealthStatus.HEALTHY
                message = "ConfigManager is working normally"
            elif config_loaded:
                status = HealthStatus.DEGRADED
                message = "ConfigManager loaded but config file missing"
            else:
                status = HealthStatus.UNHEALTHY
                message = "ConfigManager configuration not loaded"

            details = {
                "config_file_exists": config_file_exists,
                "config_loaded": config_loaded,
                "watcher_running": watcher_running,
                "recent_changes": recent_changes,
                "config_sections": list(self.config_data.keys())
                if config_loaded
                else [],
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def start_watching(self) -> bool:
        """开始监听配置文件变更"""
        try:
            if self._is_watching:
                return True

            self.observer = Observer()
            event_handler = ConfigFileHandler(self)

            # 监听配置文件目录
            config_dir = self.config_path.parent
            if self.observer is not None:
                self.observer.schedule(event_handler, str(config_dir), recursive=False)
                self.observer.start()

            self._is_watching = True
            if self.logger:
                await self.logger.info(
                    f"Started watching config directory: {config_dir}"
                )
            else:
                logger.info(f"Started watching config directory: {config_dir}")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start config watching: {e}")
            else:
                logger.error(f"Failed to start config watching: {e}")
            return False

    async def stop_watching(self) -> bool:
        """停止监听配置文件变更"""
        try:
            if self.observer and self._is_watching:
                self.observer.stop()
                self.observer.join()
                self._is_watching = False
                if self.logger:
                    await self.logger.info("Stopped watching config files")
                else:
                    logger.info("Stopped watching config files")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop config watching: {e}")
            else:
                logger.error(f"Failed to stop config watching: {e}")
            return False

    async def _reload_config(self) -> bool:
        """重新加载配置"""
        try:
            old_config = self.config_data.copy()
            await self.load_config()

            # 记录变更
            changes = self._detect_changes(old_config, self.config_data)
            for change in changes:
                self.change_history.append(change)
                if self.logger:
                    await self.logger.info(
                        f"Config changed: {change.config_path} = {change.new_value}"
                    )
                else:
                    logger.info(
                        f"Config changed: {change.config_path} = {change.new_value}"
                    )

            # 通知回调函数
            for callback in self.change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(changes)
                    else:
                        callback(changes)
                except Exception as e:
                    if self.logger:
                        await self.logger.error(f"Config change callback failed: {e}")
                    else:
                        logger.error(f"Config change callback failed: {e}")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to reload config: {e}")
            else:
                logger.error(f"Failed to reload config: {e}")
            return False

    def _detect_changes(
        self, old_config: dict, new_config: dict, path: str = ""
    ) -> list[ConfigChangeEvent]:
        """检测配置变更"""
        changes = []

        # 检查新增和修改
        for key, new_value in new_config.items():
            current_path = f"{path}.{key}" if path else key

            if key not in old_config:
                # 新增
                changes.append(
                    ConfigChangeEvent(
                        config_path=current_path,
                        old_value=None,
                        new_value=new_value,
                        change_type="add",
                    )
                )
            elif old_config[key] != new_value:
                if isinstance(new_value, dict) and isinstance(old_config[key], dict):
                    # 递归检查嵌套字典
                    changes.extend(
                        self._detect_changes(old_config[key], new_value, current_path)
                    )
                else:
                    # 修改
                    changes.append(
                        ConfigChangeEvent(
                            config_path=current_path,
                            old_value=old_config[key],
                            new_value=new_value,
                            change_type="update",
                        )
                    )

        # 检查删除
        for key, old_value in old_config.items():
            if key not in new_config:
                current_path = f"{path}.{key}" if path else key
                changes.append(
                    ConfigChangeEvent(
                        config_path=current_path,
                        old_value=old_value,
                        new_value=None,
                        change_type="delete",
                    )
                )

        return changes

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（支持点号路径和环境变量回退）"""
        try:
            keys = key.split(".")
            value = self.config_data

            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    # 如果配置文件中没有找到，尝试从环境变量获取
                    env_value = os.getenv(key, default)
                    return env_value

            return value
        except Exception:
            # 最后回退到环境变量
            return os.getenv(key, default)

    def get_section(self, section: str) -> dict[str, Any]:
        """获取配置段"""
        return self.config_data.get(section, {})

    def set(self, key: str, value: Any) -> bool:
        """设置配置值（运行时）"""
        try:
            keys = key.split(".")
            config = self.config_data

            # 导航到目标位置
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]

            old_value = config.get(keys[-1])
            config[keys[-1]] = value

            # 记录变更
            change = ConfigChangeEvent(
                config_path=key,
                old_value=old_value,
                new_value=value,
                change_type="update" if old_value is not None else "add",
            )
            self.change_history.append(change)

            if self.logger:
                asyncio.create_task(
                    self.logger.info(f"Config updated: {key} = {value}")
                )
            else:
                logger.info(f"Config updated: {key} = {value}")
            return True

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Failed to set config {key}: {e}")
                )
            else:
                logger.error(f"Failed to set config {key}: {e}")
            return False

    def register_change_callback(self, callback: Callable) -> bool:
        """注册配置变更回调"""
        try:
            self.change_callbacks.append(callback)
            return True
        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Failed to register callback: {e}")
                )
            else:
                logger.error(f"Failed to register callback: {e}")
            return False

    @property
    def config(self) -> dict[str, Any]:
        """获取配置数据"""
        return self.config_data

    @config.setter
    def config(self, value: dict[str, Any]) -> None:
        """设置配置数据（用于BaseComponent兼容性）"""
        # 这个setter主要是为了兼容BaseComponent的__init__方法
        # 实际的配置数据应该通过load_config方法加载
        _ = value  # 标记参数已使用，避免IDE警告
        pass

    def get_change_history(self, hours: int = 24) -> list[ConfigChangeEvent]:
        """获取配置变更历史"""
        cutoff_time = datetime.now(UTC) - timedelta(hours=hours)
        return [
            change for change in self.change_history if change.timestamp >= cutoff_time
        ]

    async def save_config(self, backup: bool = True) -> bool:
        """保存当前配置到文件"""
        try:
            if backup:
                # 创建备份
                backup_path = self.config_path.with_suffix(
                    f".backup.{datetime.now(UTC).strftime('%Y%m%d_%H%M%S')}.yaml"
                )
                if self.config_path.exists():
                    import shutil

                    shutil.copy2(self.config_path, backup_path)

            # 保存配置
            with open(self.config_path, "w", encoding="utf-8") as f:
                yaml.dump(
                    self.config_data, f, default_flow_style=False, allow_unicode=True
                )

            if self.logger:
                await self.logger.info(f"Configuration saved to {self.config_path}")
            else:
                logger.info(f"Configuration saved to {self.config_path}")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to save config: {e}")
            else:
                logger.error(f"Failed to save config: {e}")
            return False

    async def rollback_config(self, backup_file: str) -> bool:
        """从备份文件回滚配置"""
        try:
            backup_path = Path(backup_file)
            if not backup_path.exists():
                raise FileNotFoundError(f"Backup file not found: {backup_path}")

            # 备份当前配置
            current_backup = self.config_path.with_suffix(
                f".rollback.{datetime.now(UTC).strftime('%Y%m%d_%H%M%S')}.yaml"
            )
            import shutil

            shutil.copy2(self.config_path, current_backup)

            # 恢复备份
            shutil.copy2(backup_path, self.config_path)

            # 重新加载
            await self.load_config()

            if self.logger:
                await self.logger.info(f"Configuration rolled back from {backup_file}")
            else:
                logger.info(f"Configuration rolled back from {backup_file}")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to rollback config: {e}")
            else:
                logger.error(f"Failed to rollback config: {e}")
            return False

    def validate_strategy_config(
        self, strategy_name: str, config: dict[str, Any]
    ) -> bool:
        """验证策略配置"""
        try:
            StrategyConfig(**config)
            return True
        except ValidationError as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(
                        f"Strategy config validation failed for {strategy_name}: {e}"
                    )
                )
            else:
                logger.error(
                    f"Strategy config validation failed for {strategy_name}: {e}"
                )
            return False

    def get_environment_config(self) -> str:
        """获取当前环境配置"""
        return self.get("system.environment", "development")

    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.get_environment_config() == "production"

    def is_testnet(self) -> bool:
        """是否使用测试网"""
        return self.get("system.use_testnet", True)


# 全局配置管理器实例
config_manager = ConfigManager()


async def get_config() -> ConfigManager:
    """获取配置管理器实例"""
    if not config_manager.config_data:
        await config_manager.initialize()
        await config_manager.start()
    return config_manager
