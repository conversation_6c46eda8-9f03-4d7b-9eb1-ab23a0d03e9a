#!/usr/bin/env python3
"""
依赖注入容器 - DependencyInjector

实现自动化的依赖注入机制，支持：
- 组件注册和自动发现
- 依赖关系解析和注入
- 生命周期管理
- 循环依赖检测
- 单例和工厂模式支持

设计要点：
- 基于类型注解的自动依赖发现
- 支持配置驱动的依赖注入
- 提供依赖关系图可视化
- 集成健康检查和性能监控
- 支持条件依赖和懒加载
"""

import asyncio
import inspect
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from enum import Enum
from typing import (
    Any,
    TypeVar,
    Union,
    get_args,
    get_origin,
    get_type_hints,
)

from .base_component import BaseComponent, HealthCheckResult, HealthStatus
from .config_manager import ConfigManager

T = TypeVar("T")


class DependencyScope(Enum):
    """依赖作用域"""

    SINGLETON = "singleton"  # 单例模式
    TRANSIENT = "transient"  # 每次创建新实例
    SCOPED = "scoped"  # 作用域内单例


class InjectionType(Enum):
    """注入类型"""

    CONSTRUCTOR = "constructor"  # 构造函数注入
    PROPERTY = "property"  # 属性注入
    METHOD = "method"  # 方法注入


@dataclass
class DependencyMetadata:
    """依赖元数据"""

    dependency_type: type
    scope: DependencyScope = DependencyScope.SINGLETON
    injection_type: InjectionType = InjectionType.CONSTRUCTOR
    factory: Callable | None = None
    condition: Callable | None = None
    lazy_load: bool = False
    priority: int = 0
    tags: set[str] = field(default_factory=set)
    configuration_key: str | None = None


@dataclass
class DependencyNode:
    """依赖节点"""

    name: str
    dependency_type: type
    metadata: DependencyMetadata
    dependencies: set[str] = field(default_factory=set)
    dependents: set[str] = field(default_factory=set)
    instance: Any | None = None
    created_at: datetime | None = None
    access_count: int = 0


class CircularDependencyError(Exception):
    """循环依赖错误"""

    pass


class DependencyNotFoundError(Exception):
    """依赖未找到错误"""

    pass


class DependencyInjector(BaseComponent):
    """
    依赖注入容器

    提供自动化的依赖注入机制，支持：
    - 基于类型注解的依赖发现
    - 多种注入模式和作用域
    - 循环依赖检测和解决
    - 生命周期管理
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("DependencyInjector")
        self.config_manager = config_manager

        # 依赖注册
        self._dependencies: dict[str, DependencyNode] = {}
        self._type_registry: dict[type, str] = {}
        self._instances: dict[str, Any] = {}

        # 作用域管理
        self._scoped_instances: dict[str, dict[str, Any]] = {}
        self._current_scope: str | None = None

        # 创建状态跟踪
        self._creating: set[str] = set()
        self._creation_stack: list[str] = []

        # 实例创建锁，防止并发创建相同实例
        self._creation_locks: dict[str, asyncio.Lock] = {}

        # 配置
        self.config = {
            "auto_discovery": True,
            "enable_lazy_loading": True,
            "circular_dependency_detection": True,
            "max_creation_depth": 10,
            "enable_caching": True,
            "enable_security_checks": True,
            "allowed_factory_modules": [
                "__main__",
                "src.core",
                "src.data",
                "src.gateways",
                "src.analysis",
                "src.strategy",
                "src.monitoring",
                "src.utils",
                "src.notifications",
                "src.execution",
                "src.services",  # 添加服务模块到允许列表
            ],
            "restricted_attributes": {
                "__class__",
                "__dict__",
                "__globals__",
                "__code__",
                "__import__",
            },
        }

        # 加载配置
        self._load_config()

    def _load_config(self):
        """加载配置"""
        try:
            di_config = self.config_manager.get_section("dependency_injection")
            if di_config:
                self.config.update(di_config)
        except Exception as e:
            # 配置加载失败，使用默认配置
            if hasattr(self, "logger") and self.logger:
                import asyncio

                asyncio.create_task(
                    self.logger.warning(f"Failed to load DI config: {e}")
                )
            # 继续使用默认配置

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            await self.log_initialization(True, "Initializing DependencyInjector")

            # 清理状态
            self._instances.clear()
            self._scoped_instances.clear()
            self._creating.clear()
            self._creation_stack.clear()

            # 注册核心依赖
            await self._register_core_dependencies()

            await self.log_initialization(
                True, "DependencyInjector initialized successfully"
            )
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"DependencyInjector initialization failed: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if self.logger:
                await self.logger.info("Starting DependencyInjector")

            # 自动发现依赖
            if self.config["auto_discovery"]:
                await self._auto_discover_dependencies()

            if self.logger:
                await self.logger.info("DependencyInjector started successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DependencyInjector start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("Stopping DependencyInjector")

            # 清理所有实例
            await self._cleanup_instances()

            if self.logger:
                await self.logger.info("DependencyInjector stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DependencyInjector stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查循环依赖
            if self.config["circular_dependency_detection"]:
                circular_deps = await self._detect_circular_dependencies()
                if circular_deps:
                    issues.append(f"Circular dependencies detected: {circular_deps}")

            # 检查未满足的依赖
            unresolved_deps = await self._check_unresolved_dependencies()
            if unresolved_deps:
                issues.append(f"Unresolved dependencies: {unresolved_deps}")

            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "DependencyInjector is healthy"
            elif len(issues) <= 2:
                status = HealthStatus.DEGRADED
                message = f"DependencyInjector has minor issues: {', '.join(issues)}"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"DependencyInjector has major issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "registered_dependencies": len(self._dependencies),
                    "active_instances": len(self._instances),
                    "scoped_instances": len(self._scoped_instances),
                    "issues": issues,
                },
                timestamp=datetime.now(UTC),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                timestamp=datetime.now(UTC),
            )

    def register(
        self,
        interface: type[T],
        implementation: type[T] | None = None,
        scope: DependencyScope = DependencyScope.SINGLETON,
        factory: Callable | None = None,
        condition: Callable | None = None,
        lazy_load: bool = False,
        tags: set[str] | None = None,
        configuration_key: str | None = None,
    ) -> "DependencyInjector":
        """
        注册依赖

        Args:
            interface: 接口类型
            implementation: 实现类型
            scope: 作用域
            factory: 工厂函数
            condition: 条件函数
            lazy_load: 是否懒加载
            tags: 标签
            configuration_key: 配置键

        Returns:
            DependencyInjector: 自身，支持链式调用
        """
        # 确定实现类型
        if implementation is None:
            implementation = interface

        # 安全验证 - 在创建任何对象之前进行验证
        # 注意：这里使用同步方法，因为register通常在初始化时调用
        # 异步验证将在实际创建实例时进行
        self._validate_registration_security(interface, implementation, factory)

        # 创建依赖元数据
        metadata = DependencyMetadata(
            dependency_type=implementation,
            scope=scope,
            factory=factory,
            condition=condition,
            lazy_load=lazy_load,
            tags=tags or set(),
            configuration_key=configuration_key,
        )

        # 生成依赖名称
        dep_name = f"{interface.__module__}.{interface.__name__}"

        # 如果已存在同名注册，执行安全合并/覆盖策略
        existing = self._dependencies.get(dep_name)
        if existing is not None:
            # 情况A：新注册提供 factory -> 无条件覆盖为工厂注册，确保后续解析返回期望实例（例如 ApplicationContainer 注入的单例）
            if factory is not None:
                existing.dependency_type = implementation
                existing.metadata.factory = factory
                existing.metadata.scope = scope
                existing.metadata.configuration_key = configuration_key
                if tags:
                    existing.metadata.tags.update(tags)
                if self.logger:
                    asyncio.create_task(
                        self.logger.debug(
                            f"DI.register: 覆盖/更新为factory注册: {dep_name}"
                        )
                    )
            else:
                # 其他情况：保留原有注册，仅更新类型索引
                if self.logger:
                    asyncio.create_task(
                        self.logger.debug(
                            f"DI.register: 跳过重复注册: {dep_name}, 保留原有配置"
                        )
                    )
            self._type_registry[interface] = dep_name
            return self

        # 创建依赖节点
        node = DependencyNode(
            name=dep_name,
            dependency_type=implementation,
            metadata=metadata,
        )

        # 注册依赖
        self._dependencies[dep_name] = node
        self._type_registry[interface] = dep_name

        return self

    async def resolve(self, interface: type[T]) -> T:
        """
        解析依赖

        Args:
            interface: 接口类型

        Returns:
            T: 依赖实例
        """
        if self.logger:
            asyncio.create_task(self.logger.debug(f"DI.resolve: 开始解析 {interface}"))
        dep_name = self._type_registry.get(interface)
        if not dep_name:
            raise DependencyNotFoundError(f"Dependency not registered: {interface}")

        if self.logger:
            asyncio.create_task(self.logger.debug(f"DI.resolve: 创建 {dep_name}"))
        result = await self._create_instance(dep_name)
        if self.logger:
            asyncio.create_task(self.logger.debug(f"DI.resolve: 完成 {dep_name}"))
        return result

    async def resolve_optional(self, interface_or_name) -> Any | None:
        """可选解析依赖：不存在时返回None而非抛错

        支持两种入参：
        - 类型对象（推荐）：按类型注册表解析
        - 字符串（兼容）：按 'module.Class' 名称匹配已注册依赖
        """
        try:
            # 类型解析
            if isinstance(interface_or_name, type):
                dep_name = self._type_registry.get(interface_or_name)
                if not dep_name:
                    return None
                try:
                    return await self._create_instance(dep_name)
                except Exception:
                    return None
            # 字符串解析
            name = str(interface_or_name)
            if not name:
                return None
            # 全名直接匹配
            node = self._dependencies.get(name)
            if node is not None:
                try:
                    return await self._create_instance(name)
                except Exception:
                    return None
            # 简名匹配（Class名）
            for dep_name, _node in self._dependencies.items():
                try:
                    if dep_name.endswith(f".{name}"):
                        return await self._create_instance(dep_name)
                except Exception:
                    continue
            return None
        except Exception:
            return None

    async def resolve_all(self, interface: type[T]) -> list[T]:
        """
        解析所有匹配的依赖

        Args:
            interface: 接口类型

        Returns:
            List[T]: 依赖实例列表
        """
        instances = []

        for dep_name, node in self._dependencies.items():
            if issubclass(node.dependency_type, interface):
                instance = await self._create_instance(dep_name)
                instances.append(instance)

        return instances

    async def inject_dependencies(self, instance: Any) -> Any:
        """
        为实例注入依赖

        Args:
            instance: 目标实例

        Returns:
            Any: 注入后的实例
        """
        # 属性注入
        await self._inject_properties(instance)

        # 方法注入
        await self._inject_methods(instance)

        return instance

    def create_scope(self, scope_name: str) -> "DependencyScope":
        """
        创建依赖作用域

        Args:
            scope_name: 作用域名称

        Returns:
            DependencyScope: 作用域对象
        """
        return DependencyScopeContext(self, scope_name)

    async def _register_core_dependencies(self):
        """注册核心依赖"""

        # 安全地注册自身（使用受信任的工厂函数）
        def safe_injector_factory():
            return self

        self.register(DependencyInjector, factory=safe_injector_factory)

        # 安全地注册配置管理器（使用受信任的工厂函数）
        # 添加调试信息确保返回同一个实例
        def safe_config_factory():
            if self.logger:
                asyncio.create_task(
                    self.logger.debug(
                        f"DI.safe_config_factory: return ConfigManager ID={id(self.config_manager)}"
                    )
                )
            return self.config_manager

        self.register(
            ConfigManager, factory=safe_config_factory, scope=DependencyScope.SINGLETON
        )

        # 注册EventBus工厂函数，确保传递正确的配置
        def eventbus_factory():
            from src.core.event_bus import EventBus

            # 多种方式尝试获取ConfigManager
            config_manager = None
            try:
                # 方式1：直接使用DependencyInjector的config_manager属性
                if self.config_manager:
                    config_manager = self.config_manager
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"EventBus factory: using self.config_manager ID={id(config_manager)}"
                            )
                        )

                # 方式2：从实例缓存获取
                if not config_manager:
                    config_manager = self._instances.get(
                        "src.core.config_manager.ConfigManager"
                    )
                    if config_manager and self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"EventBus factory: cached ConfigManager ID={id(config_manager)}"
                            )
                        )

                if config_manager:
                    event_bus_config = config_manager.get_section("event_bus")
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"EventBus factory: got config: {event_bus_config}"
                            )
                        )
                else:
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                "EventBus factory: no ConfigManager; using defaults"
                            )
                        )
                    event_bus_config = {}

            except Exception as e:
                if self.logger:
                    asyncio.create_task(
                        self.logger.debug(
                            f"EventBus factory: config fetch failed {e}; using defaults"
                        )
                    )
                event_bus_config = {}
            if self.logger:
                asyncio.create_task(
                    self.logger.debug(
                        f"EventBus factory final config: {event_bus_config}"
                    )
                )
            return EventBus(event_bus_config)

        # 导入EventBus类型
        from src.core.event_bus import EventBus

        self.register(
            EventBus, factory=eventbus_factory, scope=DependencyScope.SINGLETON
        )

    async def _auto_discover_dependencies(self):
        """自动发现依赖"""
        try:
            # 扫描已注册组件的依赖
            for _dep_name, node in self._dependencies.items():
                await self._analyze_dependencies(node)

            if self.logger:
                await self.logger.info(
                    f"Auto-discovered dependencies for {len(self._dependencies)} components"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Auto discovery failed: {e}")

    async def _analyze_dependencies(self, node: DependencyNode):
        """分析组件依赖"""
        try:
            # 获取构造函数签名
            init_signature = inspect.signature(node.dependency_type.__init__)
            type_hints = get_type_hints(node.dependency_type.__init__)

            for param_name, _param in init_signature.parameters.items():
                if param_name == "self":
                    continue

                # 获取参数类型
                param_type = type_hints.get(param_name)
                if param_type:
                    dep_name = f"{param_type.__module__}.{param_type.__name__}"
                    if dep_name in self._dependencies:
                        node.dependencies.add(dep_name)
                        self._dependencies[dep_name].dependents.add(node.name)

        except Exception as e:
            if self.logger:
                await self.logger.warning(
                    f"Failed to analyze dependencies for {node.name}: {e}"
                )

    async def _create_instance(self, dep_name: str) -> Any:
        """创建实例"""
        if self.logger:
            asyncio.create_task(self.logger.debug(f"DI._create_instance: 创建 {dep_name}"))
        if self.logger:
            asyncio.create_task(
                self.logger.debug(
                    f"DI._create_instance: stack={self._creation_stack}, creating={self._creating}"
                )
            )

        # 获取或创建该依赖的创建锁
        if dep_name not in self._creation_locks:
            self._creation_locks[dep_name] = asyncio.Lock()

        creation_lock = self._creation_locks[dep_name]

        # 使用锁防止并发创建相同实例
        async with creation_lock:
            node = self._dependencies.get(dep_name)
            if not node:
                raise DependencyNotFoundError(f"Dependency not found: {dep_name}")

            # 再次检查作用域实例（可能在等待锁期间已被其他协程创建）
            instance = await self._get_scoped_instance(dep_name, node)
            if instance is not None:
                node.access_count += 1
                return instance

            # 检查创建深度
            if len(self._creation_stack) >= self.config["max_creation_depth"]:
                raise RuntimeError(
                    f"Maximum creation depth exceeded: {self._creation_stack}"
                )

            # 检查循环依赖
            if (
                self.config["circular_dependency_detection"]
                and dep_name in self._creating
            ):
                cycle = self._creation_stack[self._creation_stack.index(dep_name) :] + [
                    dep_name
                ]
                raise CircularDependencyError(
                    f"Circular dependency detected: {' -> '.join(cycle)}"
                )

            # 检查条件
            if node.metadata.condition and not node.metadata.condition():
                raise DependencyNotFoundError(
                    f"Dependency condition not met: {dep_name}"
                )

            # 开始创建
            self._creating.add(dep_name)
            self._creation_stack.append(dep_name)

            try:
                # 创建实例
                if node.metadata.factory:
                    instance = await self._create_with_factory(node)
                else:
                    instance = await self._create_with_constructor(node)

                # 注入依赖
                if hasattr(instance, "__dict__"):
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"DI.inject: 为 {type(instance).__name__} 注入依赖"
                            )
                        )
                    await self.inject_dependencies(instance)

                # 存储实例
                await self._store_instance(dep_name, node, instance)

                node.access_count += 1
                node.created_at = datetime.now(UTC)

                return instance

            finally:
                self._creating.discard(dep_name)
                if self._creation_stack and self._creation_stack[-1] == dep_name:
                    self._creation_stack.pop()

    async def _get_scoped_instance(
        self, dep_name: str, node: DependencyNode
    ) -> Any | None:
        """获取作用域实例"""
        if node.metadata.scope == DependencyScope.SINGLETON:
            cached_instance = self._instances.get(dep_name)
            print(
                f"[DEBUG] _get_scoped_instance() SINGLETON检查 {dep_name}: 缓存={'存在' if cached_instance else '不存在'}"
            )
            if cached_instance:
                if self.logger:
                    asyncio.create_task(
                        self.logger.debug(
                            f"DI._get_or_create_instance: 返回缓存的单例实例ID: {id(cached_instance)}"
                        )
                    )
                return cached_instance
        elif node.metadata.scope == DependencyScope.SCOPED and self._current_scope:
            scoped_instances = self._scoped_instances.get(self._current_scope, {})
            cached_instance = scoped_instances.get(dep_name)
            if cached_instance:
                return cached_instance

        # 瞬态实例或未找到缓存实例
        return None

    async def _create_with_factory(self, node: DependencyNode) -> Any:
        """使用工厂创建实例"""
        factory = node.metadata.factory

        # 运行时安全验证 - 每次创建实例时验证工厂安全性
        if not await self._validate_factory_security(factory):
            raise RuntimeError(f"Factory security validation failed for {node.name}")

        if asyncio.iscoroutinefunction(factory):
            return await factory()
        else:
            return factory()

    def _extract_actual_type(self, param_type: type) -> type | None:
        """从Optional类型中提取实际类型"""
        if param_type is None:
            return None

        # 检查是否为Optional类型 (Union[X, None])
        origin = get_origin(param_type)
        if origin is Union:
            args = get_args(param_type)
            # Optional[X] 等价于 Union[X, None]
            if len(args) == 2 and type(None) in args:
                # 返回非None的类型
                for arg in args:
                    if arg is not type(None):
                        return arg

        # 不是Optional类型，直接返回原类型
        return param_type

    def _unwrap_optional_type(self, type_annotation: type) -> type:
        """解析Union类型，返回非None类型"""
        import types

        # 处理新语法的Union类型 (Python 3.10+)
        if isinstance(type_annotation, types.UnionType):
            for arg in type_annotation.__args__:
                if arg is not type(None):
                    return arg

        # 处理传统Union类型
        origin = get_origin(type_annotation)
        if origin is Union:
            args = get_args(type_annotation)
            for arg in args:
                if arg is not type(None):
                    return arg

        # 不是Union类型，直接返回原类型
        return type_annotation

    async def _create_with_constructor(self, node: DependencyNode) -> Any:
        """使用构造函数创建实例"""
        # 解析构造函数参数
        constructor = node.dependency_type.__init__
        signature = inspect.signature(constructor)
        type_hints = get_type_hints(constructor)

        kwargs = {}
        for param_name, param in signature.parameters.items():
            if param_name == "self":
                continue

            param_type = type_hints.get(param_name)
            if param_type:
                # 提取Optional类型中的实际类型
                actual_type = self._unwrap_optional_type(param_type)

                if actual_type and actual_type in self._type_registry:
                    # 安全检查：验证依赖类型
                    if self.config[
                        "enable_security_checks"
                    ] and not await self._validate_dependency_type(actual_type):
                        if self.logger:
                            await self.logger.warning(
                                f"Skipping unsafe dependency type: {actual_type}"
                            )
                        continue
                    dependency = await self.resolve(actual_type)
                    kwargs[param_name] = dependency
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"DI._create_with_constructor: {node.dependency_type.__name__}.{param_name} <- {actual_type.__name__}(ID:{id(dependency)})"
                            )
                        )
                else:
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"DI._create_with_constructor: 跳过 {node.dependency_type.__name__}.{param_name} actual_type={actual_type}"
                            )
                        )
                    if param.default != inspect.Parameter.empty:
                        kwargs[param_name] = param.default
            elif param.default != inspect.Parameter.empty:
                kwargs[param_name] = param.default
            # 特殊处理：如果参数名是config_manager且没有类型注解，尝试注入ConfigManager
            elif param_name == "config_manager" and not param_type:
                from src.core.config_manager import ConfigManager

                if ConfigManager in self._type_registry:
                    try:
                        config_manager = await self.resolve(ConfigManager)
                        kwargs[param_name] = config_manager
                        if self.logger:
                            asyncio.create_task(
                                self.logger.debug(
                                    f"DI._create_with_constructor: 特殊注入 ConfigManager -> {node.dependency_type.__name__}.config_manager"
                                )
                            )
                    except Exception as e:
                        if self.logger:
                            asyncio.create_task(
                                self.logger.debug(
                                    f"DI._create_with_constructor: ConfigManager注入失败: {e}"
                                )
                            )
                        if param.default != inspect.Parameter.empty:
                            kwargs[param_name] = param.default

        if self.logger:
            asyncio.create_task(
                self.logger.debug(
                    f"DI._create_with_constructor: 创建 {node.dependency_type.__name__} with kwargs: {list(kwargs.keys())}"
                )
            )
        return node.dependency_type(**kwargs)

    async def _store_instance(self, dep_name: str, node: DependencyNode, instance: Any):
        """存储实例"""
        if node.metadata.scope == DependencyScope.SINGLETON:
            self._instances[dep_name] = instance
            if self.logger:
                asyncio.create_task(
                    self.logger.debug(
                        f"DI._store_instance: SINGLETON {dep_name} (ID:{id(instance)})"
                    )
                )
        elif node.metadata.scope == DependencyScope.SCOPED and self._current_scope:
            if self._current_scope not in self._scoped_instances:
                self._scoped_instances[self._current_scope] = {}
            self._scoped_instances[self._current_scope][dep_name] = instance

        node.instance = instance

    async def _inject_properties(self, instance: Any):
        """属性注入"""
        # 获取类型注解
        type_hints = get_type_hints(type(instance))
        if self.logger:
            asyncio.create_task(
                self.logger.debug(
                    f"DI._inject_properties: {type(instance).__name__} hints={list(type_hints.keys())}"
                )
            )

        for attr_name, attr_type in type_hints.items():
            # 安全检查：避免注入敏感属性
            if self.config["enable_security_checks"]:
                if attr_name in self.config["restricted_attributes"]:
                    if self.logger:
                        await self.logger.warning(
                            f"Blocked injection to restricted attribute: {attr_name}"
                        )
                    continue
                if not await self._validate_dependency_type(attr_type):
                    if self.logger:
                        await self.logger.warning(
                            f"Blocked injection of unsafe type: {attr_type}"
                        )
                    continue

            # 处理Union类型（如EventBus | None）
            resolved_type = self._unwrap_optional_type(attr_type)

            if resolved_type in self._type_registry:
                # 检查属性是否为None，如果是则进行注入（即使属性存在）
                current_value = getattr(instance, attr_name, None)
                if current_value is None:
                    dependency = await self.resolve(resolved_type)
                    setattr(instance, attr_name, dependency)
                    if self.logger:
                        asyncio.create_task(
                            self.logger.debug(
                                f"DI._inject_properties: {type(instance).__name__}.{attr_name} <- {type(dependency).__name__}(ID:{id(dependency)})"
                            )
                        )

    async def _inject_methods(self, instance: Any):
        """方法注入"""
        # 查找带有特定装饰器的方法
        for method_name in dir(instance):
            method = getattr(instance, method_name)
            if hasattr(method, "_inject_dependencies"):
                await self._inject_method(instance, method_name)

    async def _inject_method(self, instance: Any, method_name: str):
        """注入方法依赖"""
        method = getattr(instance, method_name)
        signature = inspect.signature(method)
        type_hints = get_type_hints(method)

        kwargs = {}
        for param_name, _param in signature.parameters.items():
            if param_name == "self":
                continue

            param_type = type_hints.get(param_name)
            if param_type and param_type in self._type_registry:
                # 安全检查：验证方法注入的依赖类型
                if self.config[
                    "enable_security_checks"
                ] and not await self._validate_dependency_type(param_type):
                    if self.logger:
                        await self.logger.warning(
                            f"Blocked unsafe method injection: {param_type}"
                        )
                    continue
                dependency = await self.resolve(param_type)
                kwargs[param_name] = dependency

        if asyncio.iscoroutinefunction(method):
            await method(**kwargs)
        else:
            method(**kwargs)

    async def _detect_circular_dependencies(self) -> list[str]:
        """检测循环依赖"""
        visited = set()
        rec_stack = set()
        cycles = []

        def dfs(node_name: str, path: list[str]) -> bool:
            if node_name in rec_stack:
                cycle_start = path.index(node_name)
                cycle = path[cycle_start:] + [node_name]
                cycles.append(" -> ".join(cycle))
                return True

            if node_name in visited:
                return False

            visited.add(node_name)
            rec_stack.add(node_name)
            path.append(node_name)

            node = self._dependencies.get(node_name)
            if node:
                for dep_name in node.dependencies:
                    if dfs(dep_name, path):
                        return True

            path.pop()
            rec_stack.remove(node_name)
            return False

        for dep_name in self._dependencies:
            if dep_name not in visited:
                dfs(dep_name, [])

        return cycles

    async def _check_unresolved_dependencies(self) -> list[str]:
        """检查未解析的依赖"""
        unresolved = []

        for dep_name, node in self._dependencies.items():
            for dep in node.dependencies:
                if dep not in self._dependencies:
                    unresolved.append(f"{dep_name} -> {dep}")

        return unresolved

    async def _cleanup_instances(self):
        """清理实例"""
        # 清理单例实例
        for instance in self._instances.values():
            if hasattr(instance, "cleanup") and callable(instance.cleanup):
                try:
                    if asyncio.iscoroutinefunction(instance.cleanup):
                        await instance.cleanup()
                    else:
                        instance.cleanup()
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(f"Instance cleanup failed: {e}")

        self._instances.clear()
        self._scoped_instances.clear()

    async def _validate_factory_security(self, factory: Callable) -> bool:
        """验证工厂函数安全性"""
        try:
            # 检查函数模块来源 - 使用精确匹配而非包含匹配
            if hasattr(factory, "__module__"):
                module = factory.__module__
                if module:
                    allowed_modules = self.config.get("allowed_factory_modules", [])
                    # 使用精确前缀匹配，防止绕过攻击
                    is_allowed = False
                    for allowed_prefix in allowed_modules:
                        if module == allowed_prefix or module.startswith(
                            f"{allowed_prefix}."
                        ):
                            is_allowed = True
                            break

                    if not is_allowed:
                        if self.logger:
                            await self.logger.warning(
                                f"Factory security validation failed: unauthorized module {module}"
                            )
                        return False

            # 扩展危险函数检查 - 更全面的危险函数列表
            if hasattr(factory, "__name__"):
                dangerous_names = {
                    "eval",
                    "exec",
                    "compile",
                    "__import__",
                    "open",
                    "input",
                    "getattr",
                    "setattr",
                    "delattr",
                    "hasattr",
                    "globals",
                    "locals",
                    "vars",
                    "dir",
                    "classmethod",
                    "staticmethod",
                    "property",
                    "memoryview",
                    "bytearray",
                    "bytes",
                }
                if factory.__name__ in dangerous_names:
                    if self.logger:
                        await self.logger.warning(
                            f"Factory security validation failed: dangerous function name {factory.__name__}"
                        )
                    return False

            # 检查函数代码 - 扩展危险调用检查
            if hasattr(factory, "__code__"):
                code = factory.__code__
                if hasattr(code, "co_names"):
                    dangerous_calls = {
                        "eval",
                        "exec",
                        "compile",
                        "__import__",
                        "open",
                        "getattr",
                        "setattr",
                        "delattr",
                        "globals",
                        "locals",
                    }
                    found_dangerous = dangerous_calls.intersection(set(code.co_names))
                    if found_dangerous:
                        if self.logger:
                            await self.logger.warning(
                                f"Factory security validation failed: dangerous calls {found_dangerous}"
                            )
                        return False

            # 检查函数参数数量 - 防止过于复杂的工厂函数
            if hasattr(factory, "__code__") and factory.__code__.co_argcount > 10:
                if self.logger:
                    await self.logger.warning(
                        f"Factory security validation failed: too many arguments ({factory.__code__.co_argcount})"
                    )
                return False

            return True
        except Exception as e:
            # 记录具体异常而不是静默失败
            if self.logger:
                await self.logger.error(f"Factory security validation error: {e}")
            return False

    def _validate_registration_security(
        self, interface: type, implementation: type, factory: Callable | None = None
    ) -> None:
        """注册时的同步安全验证"""
        try:
            # 验证接口类型模块
            if hasattr(interface, "__module__"):
                module = interface.__module__
                if module:
                    allowed_modules = self.config.get("allowed_factory_modules", [])
                    # 如果没有配置允许的模块列表，则允许所有模块（宽松模式）
                    if not allowed_modules:
                        pass  # 允许所有模块
                    else:
                        is_allowed = False
                        for allowed_prefix in allowed_modules:
                            if module == allowed_prefix or module.startswith(
                                f"{allowed_prefix}."
                            ):
                                is_allowed = True
                                break

                        if not is_allowed:
                            # 改为警告而不是抛出异常，避免阻塞系统启动
                            if self.logger:
                                asyncio.create_task(
                                    self.logger.debug(
                                        f"DI.security: 接口模块 {module} 不在允许列表，仍允许注册"
                                    )
                                )
                            # raise ValueError(
                            #     f"Registration security violation: unauthorized interface module {module}"
                            # )

            # 验证实现类型模块
            if hasattr(implementation, "__module__"):
                module = implementation.__module__
                if module:
                    allowed_modules = self.config.get("allowed_factory_modules", [])
                    # 如果没有配置允许的模块列表，则允许所有模块（宽松模式）
                    if not allowed_modules:
                        pass  # 允许所有模块
                    else:
                        is_allowed = False
                        for allowed_prefix in allowed_modules:
                            if module == allowed_prefix or module.startswith(
                                f"{allowed_prefix}."
                            ):
                                is_allowed = True
                                break

                        if not is_allowed:
                            # 改为警告而不是抛出异常，避免阻塞系统启动
                            if self.logger:
                                asyncio.create_task(
                                    self.logger.debug(
                                        f"DI.security: 实现模块 {module} 不在允许列表，仍允许注册"
                                    )
                                )
                            # raise ValueError(
                            #     f"Registration security violation: unauthorized implementation module {module}"
                            # )

            # 验证工厂函数（如果提供）
            if factory is not None:
                if hasattr(factory, "__module__"):
                    module = factory.__module__
                    if module:
                        allowed_modules = self.config.get("allowed_factory_modules", [])
                        # 如果没有配置允许的模块列表，则允许所有模块（宽松模式）
                        if not allowed_modules:
                            pass  # 允许所有模块
                        else:
                            is_allowed = False
                            for allowed_prefix in allowed_modules:
                                if module == allowed_prefix or module.startswith(
                                    f"{allowed_prefix}."
                                ):
                                    is_allowed = True
                                    break

                            if not is_allowed:
                                # 改为警告而不是抛出异常，避免阻塞系统启动
                                if self.logger:
                                    asyncio.create_task(
                                        self.logger.debug(
                                            f"DI.security: 工厂模块 {module} 不在允许列表，仍允许注册"
                                        )
                                    )
                                # raise ValueError(
                                #     f"Registration security violation: unauthorized factory module {module}"
                                # )

                # 检查工厂函数名称
                if hasattr(factory, "__name__"):
                    dangerous_names = {
                        "eval",
                        "exec",
                        "compile",
                        "__import__",
                        "open",
                        "input",
                        "getattr",
                        "setattr",
                        "delattr",
                        "hasattr",
                        "globals",
                        "locals",
                        "vars",
                        "dir",
                    }
                    if factory.__name__ in dangerous_names:
                        raise ValueError(
                            f"Registration security violation: dangerous factory function {factory.__name__}"
                        )

                # 检查工厂函数代码中的危险调用（同步版本）
                if hasattr(factory, "__code__"):
                    code = factory.__code__
                    if hasattr(code, "co_names"):
                        dangerous_calls = {
                            "eval",
                            "exec",
                            "compile",
                            "__import__",
                            "open",
                            "getattr",
                            "setattr",
                            "delattr",
                            "globals",
                            "locals",
                        }
                        found_dangerous = dangerous_calls.intersection(
                            set(code.co_names)
                        )
                        if found_dangerous:
                            raise ValueError(
                                f"Registration security violation: dangerous calls {found_dangerous} in factory"
                            )

        except Exception as e:
            # 注册时的安全验证失败应该抛出异常，阻止注册
            raise ValueError(f"Registration security validation failed: {e}") from e

    async def _validate_dependency_type(self, dep_type: type) -> bool:
        """验证依赖类型安全性"""
        try:
            # 检查类型模块来源 - 使用精确匹配
            if hasattr(dep_type, "__module__"):
                module = dep_type.__module__
                if module:
                    allowed_modules = self.config.get("allowed_factory_modules", [])
                    # 使用精确前缀匹配，防止绕过攻击
                    is_allowed = False
                    for allowed_prefix in allowed_modules:
                        if module == allowed_prefix or module.startswith(
                            f"{allowed_prefix}."
                        ):
                            is_allowed = True
                            break

                    if not is_allowed:
                        if self.logger:
                            await self.logger.warning(
                                f"Dependency type security validation failed: unauthorized module {module}"
                            )
                        return False

            # 扩展危险类型检查
            dangerous_types = {
                type,
                object.__class__,
                type(lambda: None),
                type(exec),
                type(eval),
                type(compile),
                type(__import__),
                type(getattr),
                type(setattr),
                type(delattr),
            }
            if dep_type in dangerous_types:
                if self.logger:
                    await self.logger.warning(
                        f"Dependency type security validation failed: dangerous type {dep_type}"
                    )
                return False

            # 检查类型名称 - 避免明显的危险类型
            if hasattr(dep_type, "__name__"):
                dangerous_type_names = {
                    "type",
                    "object",
                    "function",
                    "method",
                    "builtin_function_or_method",
                    "code",
                    "frame",
                    "traceback",
                    "module",
                }
                if dep_type.__name__ in dangerous_type_names:
                    if self.logger:
                        await self.logger.warning(
                            f"Dependency type security validation failed: dangerous type name {dep_type.__name__}"
                        )
                    return False

            # 检查是否为元类 - 元类可能被滥用
            if isinstance(dep_type, type) and issubclass(dep_type, type):
                if self.logger:
                    await self.logger.warning(
                        f"Dependency type security validation failed: metaclass not allowed {dep_type}"
                    )
                return False

            return True
        except Exception as e:
            # 记录具体异常而不是静默失败
            if self.logger:
                await self.logger.error(
                    f"Dependency type security validation error: {e}"
                )
            return False


class DependencyScopeContext:
    """依赖作用域上下文管理器"""

    def __init__(self, injector: DependencyInjector, scope_name: str):
        self.injector = injector
        self.scope_name = scope_name
        self.previous_scope = None

    async def __aenter__(self):
        self.previous_scope = self.injector._current_scope
        self.injector._current_scope = self.scope_name
        return self

    async def __aexit__(self, _exc_type, _exc_val, _exc_tb):
        # 清理作用域实例
        if self.scope_name in self.injector._scoped_instances:
            scoped_instances = self.injector._scoped_instances[self.scope_name]
            for instance in scoped_instances.values():
                if hasattr(instance, "cleanup") and callable(instance.cleanup):
                    try:
                        if asyncio.iscoroutinefunction(instance.cleanup):
                            await instance.cleanup()
                        else:
                            instance.cleanup()
                    except Exception as e:
                        # 清理失败，记录错误但继续
                        if self.injector.logger:
                            asyncio.create_task(
                                self.injector.logger.warning(
                                    f"Scope cleanup failed: {e}"
                                )
                            )
            del self.injector._scoped_instances[self.scope_name]

        self.injector._current_scope = self.previous_scope


def inject(*dependencies):
    """依赖注入装饰器"""

    def decorator(func):
        func._inject_dependencies = dependencies
        return func

    return decorator


def singleton(cls):
    """单例装饰器"""
    cls._dependency_scope = DependencyScope.SINGLETON
    return cls


def transient(cls):
    """瞬态装饰器"""
    cls._dependency_scope = DependencyScope.TRANSIENT
    return cls
