"""
期权网格主策略

实现期权版分批抄底/卖出策略，通过三种交易模式的智能切换：
1. 抄底模式 (AccumulationMode) - 分批卖出OTM Put期权
2. 卖出模式 (DistributionMode) - 分批卖出OTM Call期权
3. 震荡模式 (SidewaysMode) - Short Strangle/Iron Condor策略

核心功能：
- 三种交易模式的切换逻辑
- 资金分配和仓位管理
- 风险检查和止损机制
- 策略性能评估和优化
"""

import asyncio
import contextlib
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Any

import numpy as np

from src.analysis.common_types import StrategyMode
from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.config_manager import ConfigManager
from src.core.event_bus import (
    BaseEvent,
    EventBus,
    EventType,
    PositionUpdateEvent,
    TradingDecisionEvent,
)
from src.data.cache_manager import CacheManager
from src.execution.order_manager import OrderManager
from src.services.market_data_service import MarketDataService

from .btc_to_ibit_mapper import (
    BTCSideSignal,
    BTCToIBITMapper,
    IBITMappingParams,
)
from .modes.accumulation_mode import AccumulationMode
from .modes.distribution_mode import DistributionMode
from .modes.sideways_mode import SidewaysMode
from .order_adapters import convert_strategy_order_to_request


class MarketState(Enum):
    """市场状态"""

    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"


@dataclass
class MarketAnalysis:
    """市场分析结果"""

    price_momentum: float  # 价格动量 (-1 到 1)
    iv_rank: float  # IV排名 (0 到 1)
    funding_rate: float  # 资金费率
    volume_profile: float  # 成交量分布
    technical_signals: dict[str, float]  # 技术指标
    market_state: MarketState
    confidence: float  # 分析置信度
    timestamp: datetime
    support_level: Decimal | None = None  # 技术支撑位
    resistance_level: Decimal | None = None  # 技术阻力位
    current_price: Decimal | None = None  # 当前价格


@dataclass
class StrategyMetrics:
    """策略指标"""

    total_trades: int = 0
    successful_trades: int = 0
    total_pnl: Decimal = Decimal("0")
    max_drawdown: Decimal = Decimal("0")
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    avg_trade_duration: float = 0.0
    current_mode: StrategyMode | None = None
    mode_switch_count: int = 0
    last_trade_time: datetime | None = None
    error_count: int = 0


@dataclass
class PositionInfo:
    """仓位信息"""

    symbol: str
    position_type: str  # 'put', 'call', 'spot'
    size: Decimal
    entry_price: Decimal
    current_price: Decimal
    unrealized_pnl: Decimal
    delta: float
    gamma: float
    theta: float
    vega: float
    expiry: datetime
    strike: Decimal | None = None


@dataclass
class OrderRequest:
    """订单请求"""

    symbol: str
    side: str  # 'buy', 'sell'
    order_type: str  # 'market', 'limit'
    size: Decimal
    price: Decimal | None = None
    strike: Decimal | None = None
    expiry: datetime | None = None
    option_type: str | None = None  # 'call', 'put'
    reason: str = ""
    metadata: dict[str, Any] = field(default_factory=dict)


class OptionGridStrategy(BaseComponent):
    """
    期权网格主策略

    核心功能：
    1. 三种交易模式的切换逻辑
    2. 资金分配和仓位管理
    3. 风险检查和止损机制
    4. 策略性能评估和优化
    """

    # 类级别的类型注解（用于依赖注入）
    event_bus: EventBus
    cache_manager: CacheManager
    order_manager: OrderManager
    market_data_service: MarketDataService

    def __init__(self, config_manager: ConfigManager):
        super().__init__("OptionGridStrategy")
        self.config_manager = config_manager

        # 策略状态
        # 默认当前模式，便于测试断言
        self.current_mode: StrategyMode | None = StrategyMode.SIDEWAYS
        self.target_mode: StrategyMode | None = None
        self.mode_transition_time: datetime | None = None

        # 策略配置
        self.config = {
            "capital_allocation": Decimal("0.70"),  # 主策略资金分配比例
            "batch_count": 5,  # 分批数量
            "batch_sizes": [0.10, 0.15, 0.20, 0.25, 0.30],  # 递进式仓位配置
            "delta_range": [0.1, 0.2],  # Delta目标范围
            "iv_threshold": 0.7,  # IV阈值
            "mode_switch_cooldown": 1800,  # 模式切换冷却时间(秒)
            "risk_check_interval": 30,  # 风险检查间隔(秒)
            "performance_review_interval": 3600,  # 性能评估间隔(秒)
            "audit_interval": 600,  # 审计日志间隔(秒)
        }

        # 策略指标
        self.strategy_metrics = StrategyMetrics()

        # 仓位管理
        self.positions: dict[str, PositionInfo] = {}
        self.pending_orders: list[OrderRequest] = []

        # 市场分析缓存
        self.market_analysis_cache: MarketAnalysis | None = None
        self.last_analysis_time: datetime | None = None
        # 时间对齐聚合特征缓存（由 DataSynchronizer 发布的 FeaturesEvent）
        self._features_cache: dict[str, Any] | None = None
        self._features_ts: datetime | None = None

        # 任务管理
        self._strategy_task: asyncio.Task | None = None
        self._risk_monitor_task: asyncio.Task | None = None
        self._performance_task: asyncio.Task | None = None
        self._audit_task: asyncio.Task | None = None

        # 策略模式实例（通过依赖注入获取，避免重复创建）
        self.accumulation_mode: AccumulationMode | None = None
        self.distribution_mode: DistributionMode | None = None
        self.sideways_mode: SidewaysMode | None = None

        # BTC到IBIT映射器
        self.btc_to_ibit_mapper: BTCToIBITMapper | None = None

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载策略配置
            strategy_config = self.config_manager.get("strategies.option_grid") or {}
            if strategy_config:
                # 展平历史的 parameters 层级，避免代码读取不到
                params = strategy_config.pop("parameters", None)
                if isinstance(params, dict):
                    strategy_config.update(params)
                self.config.update(strategy_config)

            # 初始化策略模式实例
            await self._initialize_mode_instances()

            # 初始化BTC-IBIT映射器
            await self._initialize_btc_ibit_mapper()

            # 初始化模式为震荡模式
            self.current_mode = StrategyMode.SIDEWAYS
            self.strategy_metrics.current_mode = self.current_mode

            if self.logger:
                await self.logger.info(
                    f"OptionGridStrategy initialized with config: {self.config}"
                )
                # 关键阈值与参数快照，便于审计
                await self.logger.info(
                    f"OGS params: iv_threshold={self.config.get('iv_threshold')}, "
                    f"mode_switch_cooldown={self.config.get('mode_switch_cooldown')}s, "
                    f"batch_count={self.config.get('batch_count')}, "
                    f"delta_range={self.config.get('delta_range')}"
                )

            # 注册配置热更新回调
            try:
                if hasattr(self.config_manager, "register_change_callback"):
                    self.config_manager.register_change_callback(
                        self._on_config_changes
                    )
            except Exception:
                pass

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize OptionGridStrategy: {e}")
            return False

    async def _on_config_changes(self, changes):
        """处理配置热更新：关注 strategies.option_grid.* 变更并应用到运行时配置"""
        try:
            relevant = [
                c
                for c in changes
                if str(getattr(c, "config_path", "")).startswith(
                    "strategies.option_grid."
                )
            ]
            if not relevant:
                return
            for change in relevant:
                key = change.config_path.split(".")[-1]
                new_val = change.new_value
                old_val = self.config.get(key)
                self.config[key] = new_val
                if self.logger:
                    await self.logger.info(
                        f"OGS config hot-updated: {key} {old_val} -> {new_val}"
                    )
            from contextlib import suppress

            with suppress(Exception):
                # 记录热更新次数
                self.metrics.custom_metrics["config_hot_updates"] = (
                    self.metrics.custom_metrics.get("config_hot_updates", 0)
                    + len(relevant)
                )
            # 特殊联动：batch_count 与 batch_sizes 对齐
            try:
                if "batch_sizes" in [c.config_path.split(".")[-1] for c in relevant]:
                    sizes = self.config.get("batch_sizes")
                    if isinstance(sizes, list):
                        self.config["batch_count"] = len(sizes)
                        if self.logger:
                            await self.logger.info(
                                f"OGS derived update: batch_count -> {self.config['batch_count']}"
                            )
            except Exception:
                pass
        except Exception as e:
            if self.logger:
                await self.logger.error(f"OGS hot-update handler error: {e}")

    async def _initialize_mode_instances(self) -> None:
        """初始化策略模式实例"""
        try:
            # 通过依赖注入获取模式实例，避免重复创建
            if self.dependency_injector:
                if not self.accumulation_mode:
                    try:
                        self.accumulation_mode = await self.dependency_injector.resolve(
                            AccumulationMode
                        )
                        if self.logger:
                            await self.logger.info(
                                "✅ 通过依赖注入获取AccumulationMode"
                            )
                    except Exception as e:
                        if self.logger:
                            await self.logger.error(
                                f"Failed to resolve AccumulationMode: {e}"
                            )
                        raise

                if not self.distribution_mode:
                    try:
                        self.distribution_mode = await self.dependency_injector.resolve(
                            DistributionMode
                        )
                        if self.logger:
                            await self.logger.info(
                                "✅ 通过依赖注入获取DistributionMode"
                            )
                    except Exception as e:
                        if self.logger:
                            await self.logger.error(
                                f"Failed to resolve DistributionMode: {e}"
                            )
                        raise

                if not self.sideways_mode:
                    try:
                        self.sideways_mode = await self.dependency_injector.resolve(
                            SidewaysMode
                        )
                        if self.logger:
                            await self.logger.info("✅ 通过依赖注入获取SidewaysMode")
                    except Exception as e:
                        if self.logger:
                            await self.logger.error(
                                f"Failed to resolve SidewaysMode: {e}"
                            )
                        raise
            else:
                # 无依赖注入器：创建默认实例，便于单元测试覆盖
                if not self.accumulation_mode:
                    self.accumulation_mode = AccumulationMode(self.config_manager)
                if not self.distribution_mode:
                    self.distribution_mode = DistributionMode(self.config_manager)
                if not self.sideways_mode:
                    self.sideways_mode = SidewaysMode(self.config_manager)

            # 验证模式实例已正确获取
            modes = [
                ("AccumulationMode", self.accumulation_mode),
                ("DistributionMode", self.distribution_mode),
                ("SidewaysMode", self.sideways_mode),
            ]

            for mode_name, mode_instance in modes:
                if not mode_instance:
                    raise RuntimeError(f"Failed to obtain {mode_name} instance")

            # 初始化所有模式实例
            for mode_name, mode_instance in modes:
                if hasattr(mode_instance, "initialize"):
                    result = await mode_instance.initialize()
                    if not result:
                        raise RuntimeError(f"Failed to initialize {mode_name}")

            # 启动所有模式实例
            for mode_name, mode_instance in modes:
                if hasattr(mode_instance, "start"):
                    result = await mode_instance.start()
                    if not result:
                        raise RuntimeError(f"Failed to start {mode_name}")

            if self.logger:
                await self.logger.info("✅ 所有策略模式实例已成功获取、初始化并启动")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize mode instances: {e}")
            raise

    async def _initialize_btc_ibit_mapper(self) -> None:
        """初始化BTC-IBIT映射器"""
        try:
            # 从配置中获取映射参数
            ibkr_config = self.config_manager.get("exchanges.ibkr") or {}
            if ibkr_config and "btc_to_ibit_mapping" in ibkr_config:
                mapping_config = ibkr_config["btc_to_ibit_mapping"]
                mapping_params = IBITMappingParams(
                    a=Decimal(str(mapping_config.get("regression_a", 0.0))),
                    b=Decimal(str(mapping_config.get("regression_b", 0.000754))),
                    tracking_error_band_bps=mapping_config.get(
                        "tracking_error_band_bps", 300
                    ),
                )
            else:
                # 使用默认参数
                mapping_params = IBITMappingParams(
                    a=Decimal("0.0"), b=Decimal("0.000754"), tracking_error_band_bps=300
                )

            # 初始化映射器
            self.btc_to_ibit_mapper = BTCToIBITMapper(mapping_params)

            # 设置依赖项（如果可用）
            if hasattr(self, "cache_manager") and self.cache_manager:
                self.btc_to_ibit_mapper.cache_manager = self.cache_manager

            if self.logger:
                await self.logger.info(
                    f"✅ BTC-IBIT映射器已初始化，参数: a={mapping_params.a}, b={mapping_params.b}, "
                    f"error_band={mapping_params.tracking_error_band_bps}bps"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize BTC-IBIT mapper: {e}")
            raise

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            # 订阅仓位更新事件，用于出场信号生成（主策略不再订阅 SignalEvent）
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.subscribe(
                    event_types=[EventType.POSITION_UPDATE],
                    callback=self._handle_position_update,
                    subscriber_id=f"{self.component_name}_position_handler",
                )

                # 订阅时间对齐后的聚合特征（现货+期权面），用于主策略内部分析增强
                await self.event_bus.subscribe(
                    event_types=[EventType.FEATURES],
                    callback=self._handle_features_event,
                    subscriber_id=f"{self.component_name}_features_handler",
                )

                if self.logger:
                    await self.logger.info(
                        "✅ OptionGridStrategy订阅PositionUpdateEvent/FeaturesEvent成功"
                    )

            # 启动策略主循环
            self._strategy_task = asyncio.create_task(self._strategy_loop())

            # 启动风险监控
            self._risk_monitor_task = asyncio.create_task(self._risk_monitor_loop())

            # 启动性能评估
            self._performance_task = asyncio.create_task(self._performance_loop())

            # 启动审计日志循环
            self._audit_task = asyncio.create_task(self._audit_loop())

            if self.logger:
                await self.logger.info("OptionGridStrategy started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start OptionGridStrategy: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止所有任务
            tasks = [
                self._strategy_task,
                self._risk_monitor_task,
                self._performance_task,
                self._audit_task,
            ]
            for task in tasks:
                if task and not task.done():
                    task.cancel()
                    with contextlib.suppress(asyncio.CancelledError):
                        await task

            if self.logger:
                await self.logger.info("OptionGridStrategy stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop OptionGridStrategy: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            issues = []

            # 检查任务状态
            if not self._strategy_task or self._strategy_task.done():
                issues.append("Strategy task not running")

            if not self._risk_monitor_task or self._risk_monitor_task.done():
                issues.append("Risk monitor task not running")

            # 检查组件依赖
            if not self.cache_manager:
                issues.append("CacheManager not available")

            # 检查错误率
            if self.strategy_metrics.error_count > 10:
                issues.append(f"High error count: {self.strategy_metrics.error_count}")

            status = HealthStatus.HEALTHY if not issues else HealthStatus.DEGRADED

            return HealthCheckResult(
                status=status,
                message=f"Strategy running in {self.current_mode.value if self.current_mode else 'unknown'} mode",
                details={
                    "current_mode": self.current_mode.value
                    if self.current_mode
                    else None,
                    "total_trades": self.strategy_metrics.total_trades,
                    "win_rate": self.strategy_metrics.win_rate,
                    "error_count": self.strategy_metrics.error_count,
                    "issues": issues,
                    "features_age_sec": (
                        (datetime.now(UTC) - self._features_ts).total_seconds()
                        if self._features_ts
                        else None
                    ),
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
            )

    async def set_dependencies(
        self,
        event_bus: EventBus,
        cache_manager: CacheManager,
        market_data_service: MarketDataService | None = None,
    ):
        """设置组件依赖"""
        self.event_bus = event_bus
        self.cache_manager = cache_manager
        self.market_data_service = market_data_service or MarketDataService(
            cache_manager
        )

    async def _strategy_loop(self):
        """策略主循环"""
        while self.is_running:
            try:
                # 市场分析
                market_analysis = await self._analyze_market()

                # 模式选择
                target_mode = await self._select_mode(market_analysis)

                # 模式切换
                if target_mode != self.current_mode:
                    await self._switch_mode(target_mode, market_analysis)

                # 执行策略逻辑
                await self._execute_strategy_logic(market_analysis)

                # 等待下一次执行
                await asyncio.sleep(60)  # 1分钟执行一次

            except Exception as e:
                self.strategy_metrics.error_count += 1
                if self.logger:
                    await self.logger.error(f"Strategy loop error: {e}")
                await asyncio.sleep(30)  # 错误后等待30秒

    async def _analyze_market(self) -> MarketAnalysis:
        """分析市场状态"""
        try:
            # 检查缓存
            if (
                self.market_analysis_cache
                and self.last_analysis_time
                and datetime.now(UTC) - self.last_analysis_time < timedelta(minutes=5)
            ):
                return self.market_analysis_cache

            # 获取市场数据
            market_data = await self._get_market_data()

            # 如果有聚合特征缓存，合并到分析数据中（不覆盖原始键）
            if self._features_cache:
                try:
                    market_data = dict(market_data or {})
                    market_data.setdefault("features", self._features_cache)
                    # 基于特征尝试改进 iv_rank 估计（使用近月ATM call/put vwap 的均值做粗略代理）
                    opts = self._features_cache.get("options", {})
                    call_atm = opts.get("near_call_atm", {}).get("iv_vwap")
                    put_atm = opts.get("near_put_atm", {}).get("iv_vwap")
                    if call_atm is not None and put_atm is not None:
                        approx_atm_iv = max(0.0, float(call_atm) + float(put_atm)) / 2.0
                        if "iv_rank" not in market_data:
                            market_data["iv_rank"] = min(1.0, max(0.0, approx_atm_iv))
                except Exception:
                    # 特征合并失败不影响主流程
                    pass

            # 计算价格动量
            price_momentum = await self._calculate_price_momentum(market_data)

            # 计算IV排名
            iv_rank = await self._calculate_iv_rank(market_data)

            # 获取资金费率
            funding_rate = market_data.get("funding_rate", 0.0)

            # 分析成交量分布
            volume_profile = await self._analyze_volume_profile(market_data)

            # 技术指标分析
            technical_signals = await self._analyze_technical_signals(market_data)

            # 确定市场状态
            market_state = await self._determine_market_state(
                price_momentum, iv_rank, funding_rate, technical_signals
            )

            # 计算分析置信度
            confidence = await self._calculate_analysis_confidence(market_data)

            # 计算支撑阻力位
            current_price = Decimal(str(market_data.get("price", 50000)))
            support_level = await self._calculate_support_level(current_price)
            resistance_level = await self._calculate_resistance_level(current_price)

            # 创建市场分析结果
            analysis = MarketAnalysis(
                price_momentum=price_momentum,
                iv_rank=iv_rank,
                funding_rate=funding_rate,
                volume_profile=volume_profile,
                technical_signals=technical_signals,
                market_state=market_state,
                confidence=confidence,
                timestamp=datetime.now(UTC),
                support_level=support_level,
                resistance_level=resistance_level,
                current_price=current_price,
            )

            # 更新缓存
            self.market_analysis_cache = analysis
            self.last_analysis_time = analysis.timestamp

            return analysis

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market analysis failed: {e}")

            # 返回默认分析结果
            return MarketAnalysis(
                price_momentum=0.0,
                iv_rank=0.1,  # 错误情况下使用低IV排名
                funding_rate=0.0,
                volume_profile=0.5,
                technical_signals={},
                market_state=MarketState.SIDEWAYS,
                confidence=0.1,
                timestamp=datetime.now(UTC),
            )

    async def _handle_features_event(self, event: BaseEvent):
        """处理由 DataSynchronizer 发布的 FeaturesEvent（时间对齐聚合特征）。"""
        try:
            features = getattr(event, "features", None)
            if not isinstance(features, dict):
                return
            self._features_cache = features
            self._features_ts = getattr(event, "timestamp", datetime.now(UTC))
        except Exception:
            # 缓存失败不影响主策略
            pass

    async def _get_market_data(self) -> dict[str, Any]:
        """获取市场数据"""
        try:
            market_data = {}
            mds = await self.dependency_injector.resolve(MarketDataService)

            # 获取BTC价格数据（latest spot/ticker 汇总）
            btc_data = await mds.get_latest_by_source_symbol("binance_spot", "BTCUSDT")
            if btc_data:
                market_data.update(btc_data)

            # 获取期权数据
            option_data = await mds.get_option_chain("BTC")
            if option_data:
                market_data["option_chain"] = option_data
                if isinstance(option_data, dict) and "greeks" in option_data:
                    market_data["greeks"] = option_data["greeks"]

            # 历史数据
            price_history = await mds.get_price_history("BTC")
            if price_history:
                market_data["price_history"] = price_history

            # 资金费率
            funding_rate = await mds.get_funding_rate("BTCUSDT")
            if funding_rate is not None:
                market_data["funding_rate"] = funding_rate

            # 成交量分布
            volume_profile = await mds.get_volume_profile("BTCUSDT")
            if volume_profile is not None:
                market_data["volume_profile"] = volume_profile

            return market_data

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get market data: {e}")
            return {}

    async def _calculate_price_momentum(self, market_data: dict[str, Any]) -> float:
        """计算价格动量"""
        try:
            # 获取价格历史
            price_history = market_data.get("price_history", [])
            if len(price_history) < 20:
                return 0.0

            # 计算短期和长期移动平均
            short_ma = np.mean(price_history[-5:])
            long_ma = np.mean(price_history[-20:])

            # 计算动量 (-1 到 1)
            if long_ma > 0:
                momentum = (short_ma - long_ma) / long_ma
                return max(-1.0, min(1.0, momentum * 10))  # 放大并限制范围

            return 0.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Price momentum calculation failed: {e}")
            return 0.0

    async def _calculate_iv_rank(self, market_data: dict[str, Any]) -> float:
        """计算IV排名"""
        try:
            # 获取当前IV和历史IV
            current_iv = market_data.get("current_iv", 0.0)
            iv_history = market_data.get("iv_history", [])

            if not iv_history or len(iv_history) < 30:
                return 0.5  # 默认中等水平

            # 计算IV排名 (当前IV在历史中的百分位)
            # 计算当前IV在历史数据中的排名
            rank = sum(1 for iv in iv_history if iv <= current_iv) / len(iv_history)
            return max(0.0, min(1.0, float(rank)))

        except Exception as e:
            if self.logger:
                await self.logger.error(f"IV rank calculation failed: {e}")
            return 0.5

    async def _analyze_volume_profile(self, market_data: dict[str, Any]) -> float:
        """分析成交量分布"""
        try:
            volume_data = market_data.get("volume_profile", {})
            if not volume_data:
                return 0.5

            # 分析成交量集中度
            total_volume = sum(volume_data.values())
            if total_volume == 0:
                return 0.5

            # 计算成交量分布的集中度
            max_volume = max(volume_data.values())
            concentration = max_volume / total_volume

            return max(0.0, min(1.0, concentration))

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Volume profile analysis failed: {e}")
            return 0.5

    async def _analyze_technical_signals(
        self, market_data: dict[str, Any]
    ) -> dict[str, float]:
        """分析技术指标"""
        try:
            signals = {}

            # RSI指标
            rsi = market_data.get("rsi", 50.0)
            signals["rsi"] = (rsi - 50) / 50.0  # 标准化到 -1 到 1

            # MACD指标
            macd = market_data.get("macd", 0.0)
            signals["macd"] = max(-1.0, min(1.0, macd))

            # 布林带位置
            bb_position = market_data.get("bollinger_position", 0.5)
            signals["bollinger"] = (bb_position - 0.5) * 2  # 转换到 -1 到 1

            return signals

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Technical signals analysis failed: {e}")
            return {}

    async def _determine_market_state(
        self,
        price_momentum: float,
        iv_rank: float,
        funding_rate: float,
        technical_signals: dict[str, float],
    ) -> MarketState:
        """确定市场状态"""
        try:
            # 高波动率状态
            if iv_rank > 0.8:
                return MarketState.HIGH_VOLATILITY

            # 趋势状态判断
            trend_signals = [
                price_momentum,
                technical_signals.get("macd", 0.0),
                technical_signals.get("rsi", 0.0),
            ]

            avg_trend = np.mean(trend_signals)

            if avg_trend > 0.3:
                return MarketState.TRENDING_UP
            elif avg_trend < -0.3:
                return MarketState.TRENDING_DOWN
            else:
                return MarketState.SIDEWAYS

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market state determination failed: {e}")
            return MarketState.SIDEWAYS

    async def _calculate_analysis_confidence(
        self, market_data: dict[str, Any]
    ) -> float:
        """计算分析置信度"""
        try:
            confidence_factors = []

            # 数据完整性
            required_fields = ["price_history", "current_iv", "volume_profile"]
            data_completeness = sum(
                1 for field in required_fields if field in market_data
            ) / len(required_fields)
            if data_completeness > 0:  # 只有当有数据时才添加到confidence_factors
                confidence_factors.append(data_completeness)

            # 数据新鲜度
            last_update = market_data.get("last_update")
            if last_update:
                age_minutes = (datetime.now(UTC) - last_update).total_seconds() / 60
                freshness = max(0.0, 1.0 - age_minutes / 60)  # 1小时内为满分
                confidence_factors.append(freshness)

            # 市场活跃度
            volume = market_data.get("volume", 0)
            if volume > 0:
                # 假设正常成交量为基准
                activity = min(1.0, volume / 1000000)  # 100万为满分
                confidence_factors.append(activity)

            if confidence_factors:
                result = np.mean(confidence_factors)
                return float(result) if not np.isnan(result) else 0.1
            else:
                return 0.1

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Confidence calculation failed: {e}")
            return 0.1  # 错误情况下返回低置信度

    async def _calculate_support_level(self, current_price: Decimal) -> Decimal | None:
        """计算技术支撑位 - 基于历史价格低点分析（类似accumulation_mode中的方法）"""
        try:
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            # 获取历史价格数据
            price_history = await mds.get_price_history("BTC")
            if not price_history:
                return None

            # 支撑位计算：使用30天和90天低点
            recent_30d_lows = [
                Decimal(str(price.get("low", current_price)))
                for price in price_history[-30:]
                if "low" in price
            ]
            recent_90d_lows = [
                Decimal(str(price.get("low", current_price)))
                for price in price_history[-90:]
                if "low" in price
            ]

            support = None

            if recent_30d_lows:
                support_30d = min(recent_30d_lows)
                if recent_90d_lows:
                    support_90d = min(recent_90d_lows)
                    # 加权平均：30天权重70%，90天权重30%
                    support = support_30d * Decimal("0.7") + support_90d * Decimal(
                        "0.3"
                    )
                else:
                    support = support_30d

            if support is None:
                return None
            # 确保支撑位不低于当前价格的70%
            return max(support, current_price * Decimal("0.7"))

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Support level estimation failed: {e}")
            return current_price * Decimal("0.8")

    async def _calculate_resistance_level(
        self, current_price: Decimal
    ) -> Decimal | None:
        """计算技术阻力位 - 基于历史价格高点分析（镜像支撑位方法）"""
        try:
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            price_history = await mds.get_price_history("BTC")
            if not price_history:
                return None

            # 阻力位计算：使用30天和90天高点
            recent_30d_highs = [
                Decimal(str(price.get("high", current_price)))
                for price in price_history[-30:]
                if "high" in price
            ]
            recent_90d_highs = [
                Decimal(str(price.get("high", current_price)))
                for price in price_history[-90:]
                if "high" in price
            ]

            resistance = None

            if recent_30d_highs:
                resistance_30d = max(recent_30d_highs)
                if recent_90d_highs:
                    resistance_90d = max(recent_90d_highs)
                    # 加权平均：30天权重70%，90天权重30%
                    resistance = resistance_30d * Decimal(
                        "0.7"
                    ) + resistance_90d * Decimal("0.3")
                else:
                    resistance = resistance_30d

            # 确保阻力位不高于当前价格的130%
            return min(resistance, current_price * Decimal("1.30"))

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Resistance level estimation failed: {e}")
            return None

    async def _select_mode(self, market_analysis: MarketAnalysis) -> StrategyMode:
        """选择策略模式"""
        try:
            iv_rank = market_analysis.iv_rank
            market_state = market_analysis.market_state

            # 获取当前仓位信息
            has_spot_position = await self._has_spot_position()

            # 模式选择逻辑
            if market_state == MarketState.HIGH_VOLATILITY:
                # 高波动率时优先震荡模式
                return StrategyMode.SIDEWAYS

            elif market_state == MarketState.TRENDING_DOWN:
                # 下跌趋势 + 高IV，选择抄底模式
                if iv_rank > self.config["iv_threshold"]:
                    return StrategyMode.ACCUMULATION
                else:
                    return StrategyMode.SIDEWAYS

            elif market_state == MarketState.TRENDING_UP:
                # 上涨趋势 + 有现货仓位，选择卖出模式
                if has_spot_position:
                    return StrategyMode.DISTRIBUTION
                else:
                    return StrategyMode.SIDEWAYS

            else:
                # 震荡市场，选择震荡模式
                return StrategyMode.SIDEWAYS

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Mode selection failed: {e}")
            return StrategyMode.SIDEWAYS

    async def _has_spot_position(self) -> bool:
        """检查是否有现货仓位"""
        try:
            for position in self.positions.values():
                if position.position_type == "spot" and position.size > 0:
                    return True
            return False
        except Exception:
            return False

    async def _switch_mode(
        self, target_mode: StrategyMode, market_analysis: MarketAnalysis
    ):
        """切换策略模式"""
        try:
            # 检查冷却时间
            if self.mode_transition_time and datetime.now(
                UTC
            ) - self.mode_transition_time < timedelta(
                seconds=self.config["mode_switch_cooldown"]
            ):
                return

            old_mode = self.current_mode

            # 执行模式切换前的清理
            await self._cleanup_mode(old_mode)

            # 切换模式
            self.current_mode = target_mode
            self.target_mode = target_mode
            self.mode_transition_time = datetime.now(UTC)

            # 更新指标
            self.strategy_metrics.current_mode = target_mode
            self.strategy_metrics.mode_switch_count += 1

            # 发布模式切换事件
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.STRATEGY_MODE_SWITCH,
                        metadata={
                            "old_mode": old_mode.value if old_mode else None,
                            "new_mode": target_mode.value,
                            "market_analysis": {
                                "price_momentum": market_analysis.price_momentum,
                                "iv_rank": market_analysis.iv_rank,
                                "market_state": market_analysis.market_state.value,
                                "confidence": market_analysis.confidence,
                            },
                            "timestamp": self.mode_transition_time.isoformat(),
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Strategy mode switched: {old_mode.value if old_mode else 'None'} -> {target_mode.value}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Mode switch failed: {e}")

    async def _cleanup_mode(self, mode: StrategyMode | None):
        """清理模式相关资源"""
        try:
            if not mode:
                return

            # 取消待处理订单
            self.pending_orders.clear()

            # 记录模式切换
            if self.logger:
                await self.logger.info(f"Cleaning up mode: {mode.value}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Mode cleanup failed: {e}")

    async def _execute_strategy_logic(self, market_analysis: MarketAnalysis):
        """执行策略逻辑"""
        try:
            if not self.current_mode:
                return

            # 根据当前模式执行相应逻辑
            if self.current_mode == StrategyMode.ACCUMULATION:
                await self._execute_accumulation_mode(market_analysis)
            elif self.current_mode == StrategyMode.DISTRIBUTION:
                await self._execute_distribution_mode(market_analysis)
            elif self.current_mode == StrategyMode.SIDEWAYS:
                await self._execute_sideways_mode(market_analysis)

        except Exception as e:
            self.strategy_metrics.error_count += 1
            if self.logger:
                await self.logger.error(f"Strategy execution failed: {e}")

    async def _execute_accumulation_mode(self, market_analysis: MarketAnalysis):
        """执行抄底模式（渐进式建仓）"""
        try:
            if self.logger:
                await self.logger.info(
                    f"[OGS] Accumulation audit: iv_rank={market_analysis.iv_rank:.2f}, "
                    f"momentum={market_analysis.price_momentum:.2f}, state={market_analysis.market_state.value}, "
                    f"params={{batch_count={self.config.get('batch_count')}, batch_sizes={self.config.get('batch_sizes')}, "
                    f"delta_range={self.config.get('delta_range')}}}"
                )
            # 发布审计事件
            try:
                if self.event_bus:
                    from src.core.event_bus import EventType

                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.STRATEGY_AUDIT,
                            data={
                                "mode": "accumulation",
                                "iv_rank": float(market_analysis.iv_rank),
                                "momentum": float(market_analysis.price_momentum),
                                "state": market_analysis.market_state.value,
                                "params": {
                                    "batch_count": self.config.get("batch_count"),
                                    "batch_sizes": self.config.get("batch_sizes"),
                                    "delta_range": self.config.get("delta_range"),
                                },
                                "ts": datetime.now(UTC).isoformat(),
                            },
                        )
                    )
            except Exception:
                pass
            current_price = Decimal(str(market_analysis.current_price))
            market_analysis_dict = {
                "price_momentum": market_analysis.price_momentum,
                "iv_rank": market_analysis.iv_rank,
                "support_level": market_analysis.support_level,
                "resistance_level": market_analysis.resistance_level,
                "rsi": getattr(market_analysis, "rsi", 30),
                "market_state": market_analysis.market_state.value,
            }

            # 检查基本条件
            if not await self.accumulation_mode.should_accumulate(
                current_price, market_analysis_dict
            ):
                return

            # 渐进式建仓：检查是否有批次需要触发
            next_batch = await self.accumulation_mode.get_next_batch_to_trigger(
                current_price
            )
            if next_batch is None:
                # 没有需要触发的批次
                return

            # 生成单个批次的订单
            order = await self.accumulation_mode.generate_single_batch_order(
                next_batch, current_price, market_analysis_dict
            )

            if order:
                try:
                    # 转换并执行订单
                    order_request = convert_strategy_order_to_request(order)
                    await self._execute_order(order_request)

                    # 标记批次为已完成
                    await self.accumulation_mode.mark_batch_completed(next_batch)

                    if self.logger:
                        progress = self.accumulation_mode.get_progress_status()
                        await self.logger.info(
                            f"抄底进度: {progress['completed_batches']}/{progress['total_batches']} "
                            f"({progress['progress_ratio']:.1%})"
                        )

                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"Accumulation order execution failed: {e}"
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Progressive accumulation execution failed: {e}"
                )

    async def _execute_distribution_mode(self, market_analysis: MarketAnalysis):
        """执行卖出模式（继续使用一次性建仓，但使用新的Strike设计）"""
        try:
            if self.logger:
                await self.logger.info(
                    f"[OGS] Distribution audit: iv_rank={market_analysis.iv_rank:.2f}, "
                    f"momentum={market_analysis.price_momentum:.2f}, resistance={market_analysis.resistance_level}, "
                    f"params={{batch_sizes={self.config.get('batch_sizes')}, max_coverage={self.config.get('max_coverage_ratio')}}}"
                )
            # 发布审计事件
            try:
                if self.event_bus:
                    from src.core.event_bus import EventType

                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.STRATEGY_AUDIT,
                            data={
                                "mode": "distribution",
                                "iv_rank": float(market_analysis.iv_rank),
                                "momentum": float(market_analysis.price_momentum),
                                "resistance": float(market_analysis.resistance_level)
                                if market_analysis.resistance_level
                                else None,
                                "params": {
                                    "batch_sizes": self.config.get("batch_sizes"),
                                    "max_coverage": self.config.get(
                                        "max_coverage_ratio"
                                    ),
                                },
                                "ts": datetime.now(UTC).isoformat(),
                            },
                        )
                    )
            except Exception:
                pass
            current_price = Decimal(str(market_analysis.current_price))
            market_analysis_dict = {
                "price_momentum": market_analysis.price_momentum,
                "iv_rank": market_analysis.iv_rank,
                "support_level": market_analysis.support_level,
                "resistance_level": market_analysis.resistance_level,
                "rsi": getattr(market_analysis, "rsi", 70),
            }

            if not await self.distribution_mode.should_distribute(
                current_price, market_analysis_dict
            ):
                return

            # 生成分发订单（使用新的对称Strike设计）
            orders = await self.distribution_mode.generate_distribution_orders(
                current_price, market_analysis_dict
            )

            # 执行订单
            for strategy_order in orders:
                try:
                    order_request = convert_strategy_order_to_request(strategy_order)
                    await self._execute_order(order_request)
                except Exception as e:
                    if self.logger:
                        await self.logger.error(
                            f"Distribution order execution failed: {e}"
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Distribution mode execution failed: {e}")

    async def _execute_sideways_mode(self, market_analysis: MarketAnalysis):
        """执行震荡模式"""
        try:
            if self.logger:
                await self.logger.info(
                    f"[OGS] Sideways audit: iv_rank={market_analysis.iv_rank:.2f}, "
                    f"implied_vol={getattr(market_analysis, 'implied_volatility', 0.0)}, "
                    f"realized_vol={getattr(market_analysis, 'realized_volatility', 0.0)}"
                )
            # 发布审计事件
            try:
                if self.event_bus:
                    from src.core.event_bus import EventType

                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.STRATEGY_AUDIT,
                            data={
                                "mode": "sideways",
                                "iv_rank": float(market_analysis.iv_rank),
                                "implied_vol": float(
                                    getattr(market_analysis, "implied_volatility", 0.0)
                                    or 0.0
                                ),
                                "realized_vol": float(
                                    getattr(market_analysis, "realized_volatility", 0.0)
                                    or 0.0
                                ),
                                "ts": datetime.now(UTC).isoformat(),
                            },
                        )
                    )
            except Exception:
                pass
            # 检查是否满足震荡策略条件（委托给专业模式类）
            current_price = Decimal(str(market_analysis.current_price))
            market_analysis_dict = {
                "price_momentum": market_analysis.price_momentum,
                "iv_rank": market_analysis.iv_rank,
                "realized_volatility": getattr(
                    market_analysis, "realized_volatility", 0.6
                ),
                "implied_volatility": getattr(
                    market_analysis, "implied_volatility", 0.8
                ),
                "rsi": getattr(market_analysis, "rsi", 50),  # 默认中性RSI
            }

            if not await self.sideways_mode.should_trade_sideways(
                current_price, market_analysis_dict
            ):
                return

            # 生成震荡策略订单（委托给专业模式类）
            orders = await self.sideways_mode.generate_sideways_strategies(
                current_price, market_analysis_dict
            )

            # 执行订单 - 转换为标准OrderRequest格式
            for strategy_order in orders:
                try:
                    order_request = convert_strategy_order_to_request(strategy_order)
                    await self._execute_order(order_request)
                except Exception as e:
                    if self.logger:
                        await self.logger.error(f"Order conversion failed: {e}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Sideways mode execution failed: {e}")

    async def _execute_order(self, order: OrderRequest):
        """执行订单 - 集成BTC-IBIT映射器和OrderManager实现真实交易"""
        try:
            # 添加到待处理订单
            self.pending_orders.append(order)

            # 更新指标
            self.strategy_metrics.total_trades += 1
            self.strategy_metrics.last_trade_time = datetime.now(UTC)

            # 1. 使用BTC-IBIT映射器转换BTC信号为IBIT期权订单
            ibit_order = await self._convert_btc_order_to_ibit(order)
            if not ibit_order:
                if self.logger:
                    await self.logger.warning(
                        f"BTC-IBIT映射失败，跳过订单: {order.symbol}"
                    )
                return

            # 2. 发布TradingDecisionEvent，经由 RiskEngine → OrderManager 统一执行
            if self.event_bus:
                action = ibit_order.side
                decision_data = {
                    "quantity": float(ibit_order.size),
                    "reason": ibit_order.reason or order.reason,
                    "source": "OptionGridStrategy",
                }
                # 注入 Deribit 流动性代理（若可获取）
                try:
                    tenor_days = 30
                    if getattr(ibit_order, "expiry", None):
                        from datetime import date as _date

                        tenor_days = max(
                            0,
                            (ibit_order.expiry.date() - _date.today()).days,
                        )
                    opt_type = (
                        "call"
                        if (
                            getattr(ibit_order, "option_type", "c")
                            .lower()
                            .startswith("c")
                        )
                        else "put"
                    )
                    mds = await self.dependency_injector.resolve(MarketDataService)
                    from src.services.liquidity_service import (
                        build_deribit_liquidity_proxy,
                    )

                    proxy = await build_deribit_liquidity_proxy(
                        mds=mds,
                        tenor_days=tenor_days,
                        option_type=opt_type,
                        config=self.config.get("liquidity_proxy", {}),
                        spot_price=None,
                    )
                    if proxy:
                        decision_data["liquidity_proxy"] = proxy
                except Exception:
                    pass
                risk_params = {
                    "strategy_role": "main",
                    "max_position_size": float(
                        self.config.get("max_position_size", 1000000)
                    ),
                }

                decision = TradingDecisionEvent(
                    strategy_name=self.component_name,
                    action=action,
                    instruments=[ibit_order.symbol],
                    risk_params=risk_params,
                    confidence=0.8,
                    decision_data=decision_data,
                    source=self.component_name,
                    timestamp=datetime.now(UTC),
                )

                await self.event_bus.publish(decision)

            # 发布订单事件(保留原有功能)
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.STRATEGY_ORDER,
                        data={
                            "symbol": order.symbol,
                            "side": order.side,
                            "size": float(order.size),
                            "strike": float(order.strike) if order.strike else None,
                            "option_type": order.option_type,
                            "reason": order.reason,
                            "metadata": order.metadata,
                            "timestamp": datetime.now(UTC).isoformat(),
                        },
                    )
                )

            if self.logger:
                await self.logger.info(
                    f"Order executed: {order.reason} - {order.side} {order.size} {order.symbol}"
                )

        except Exception as e:
            self.strategy_metrics.error_count += 1
            if self.logger:
                await self.logger.error(f"Order execution failed: {e}")

    async def _risk_monitor_loop(self):
        """风险监控循环"""
        while self.is_running:
            try:
                await self._check_position_risk()
                await self._check_greeks_risk()
                await self._check_expiry_risk()

                await asyncio.sleep(self.config["risk_check_interval"])

            except Exception as e:
                self.strategy_metrics.error_count += 1
                if self.logger:
                    await self.logger.error(f"Risk monitor error: {e}")
                await asyncio.sleep(60)

    async def _check_position_risk(self):
        """检查仓位风险"""
        try:
            total_exposure = Decimal("0")
            for position in self.positions.values():
                total_exposure += abs(position.size * position.current_price)

            # 检查总仓位限制
            max_exposure = self.config["capital_allocation"] * Decimal(
                "10"
            )  # 10倍杠杆限制
            if total_exposure > max_exposure:
                if self.logger:
                    await self.logger.warning(
                        f"Position exposure exceeds limit: {total_exposure} > {max_exposure}"
                    )

                # 发布风险警告
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.RISK_WARNING,
                            data={
                                "type": "position_exposure",
                                "current": float(total_exposure),
                                "limit": float(max_exposure),
                                "timestamp": datetime.now(UTC).isoformat(),
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position risk check failed: {e}")

    async def _check_greeks_risk(self):
        """检查Greeks风险"""
        try:
            total_delta = 0.0
            total_gamma = 0.0
            total_theta = 0.0
            total_vega = 0.0

            for position in self.positions.values():
                if position.position_type in ["put", "call"]:
                    total_delta += position.delta * float(position.size)
                    total_gamma += position.gamma * float(position.size)
                    total_theta += position.theta * float(position.size)
                    total_vega += position.vega * float(position.size)

            # 检查Delta中性
            if abs(total_delta) > 0.5 and self.logger:  # Delta限制
                await self.logger.warning(f"Delta exposure too high: {total_delta}")

            # 检查Gamma风险
            if abs(total_gamma) > 0.1 and self.logger:  # Gamma限制
                await self.logger.warning(f"Gamma exposure too high: {total_gamma}")

            # 检查Vega风险
            if abs(total_vega) > 100 and self.logger:  # Vega限制
                await self.logger.warning(f"Vega exposure too high: {total_vega}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Greeks risk check failed: {e}")

    async def _check_expiry_risk(self):
        """检查到期风险"""
        try:
            near_expiry_positions = []
            current_time = datetime.now(UTC)

            for symbol, position in self.positions.items():
                if position.position_type in ["put", "call"]:
                    days_to_expiry = (position.expiry - current_time).days
                    if days_to_expiry <= 7:  # 7天内到期
                        near_expiry_positions.append((symbol, position, days_to_expiry))

            if near_expiry_positions:
                if self.logger:
                    await self.logger.info(
                        f"Found {len(near_expiry_positions)} positions expiring within 7 days"
                    )

                # 发布到期提醒
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.EXPIRY_REMINDER,
                            data={
                                "positions": [
                                    {
                                        "symbol": symbol,
                                        "days_to_expiry": days,
                                        "size": float(pos.size),
                                        "unrealized_pnl": float(pos.unrealized_pnl),
                                    }
                                    for symbol, pos, days in near_expiry_positions
                                ],
                                "timestamp": current_time.isoformat(),
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry risk check failed: {e}")

    # 已移除 SignalEvent 相关处理：主策略不消费因果信号

    async def _publish_trading_decision_event(
        self, action: str, decision_data: dict, confidence: float
    ):
        """发布交易决策事件"""
        try:
            if not self.event_bus:
                return

            event = TradingDecisionEvent(
                strategy_name=self.component_name,
                action=action,
                instruments=["BTC", "IBIT"],  # 基于策略支持的工具
                risk_params={
                    "max_position_size": self.config.get("max_position_size", 1000000),
                    "risk_tolerance": self.config.get("risk_tolerance", 0.02),
                },
                confidence=confidence,
                decision_data=decision_data,
                source=self.component_name,
                timestamp=datetime.now(UTC),
            )

            await self.event_bus.publish(event)
            from contextlib import suppress

            with suppress(Exception):
                self.metrics.custom_metrics["decisions_published"] = (
                    self.metrics.custom_metrics.get("decisions_published", 0) + 1
                )

            if self.logger:
                await self.logger.info(
                    f"发布TradingDecisionEvent: action={action}, confidence={confidence:.2f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"发布交易决策事件失败: {e}")

    async def _performance_loop(self):
        """性能评估循环"""
        while self.is_running:
            try:
                await self._update_performance_metrics()
                await self._optimize_strategy_parameters()

                await asyncio.sleep(self.config["performance_review_interval"])

            except Exception as e:
                self.strategy_metrics.error_count += 1
                if self.logger:
                    await self.logger.error(f"Performance loop error: {e}")
                await asyncio.sleep(300)

    async def _audit_loop(self):
        """审计日志循环：周期性输出当前模式与关键阈值，便于调参与审计"""
        while self.is_running:
            try:
                mode = self.current_mode.value if self.current_mode else "unknown"
                params = self.config
                msg = (
                    f"OGS audit: mode={mode}, "
                    f"iv_threshold={float(params.get('iv_threshold', 0.0)):.2f}, "
                    f"delta_range={params.get('delta_range')}, "
                    f"batch_count={params.get('batch_count')}, "
                    f"batch_sizes={params.get('batch_sizes')}, "
                    f"cooldown={params.get('mode_switch_cooldown')}s, "
                    f"risk_check={params.get('risk_check_interval')}s"
                )
                if self.logger:
                    await self.logger.info(msg)
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Audit loop error: {e}")
            finally:
                await asyncio.sleep(self.config.get("audit_interval", 600))

    async def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 计算总PnL
            total_pnl = Decimal("0")
            for position in self.positions.values():
                total_pnl += position.unrealized_pnl

            self.strategy_metrics.total_pnl = total_pnl

            # 计算胜率
            if self.strategy_metrics.total_trades > 0:
                self.strategy_metrics.win_rate = (
                    self.strategy_metrics.successful_trades
                    / self.strategy_metrics.total_trades
                )

            # 计算夏普比率（完整版本）
            if self.strategy_metrics.total_trades > 10 and hasattr(
                self, "trade_returns"
            ):
                # 获取历史交易回报率序列
                returns = self.trade_returns[-252:]  # 最近一年的交易数据

                if len(returns) >= 30:  # 至少需要30笔交易计算统计意义
                    daily_returns = np.array(returns)

                    # 计算年化回报率
                    avg_daily_return = np.mean(daily_returns)
                    annual_return = avg_daily_return * 252  # 年化

                    # 计算年化标准差
                    std_daily_return = np.std(daily_returns)
                    annual_volatility = std_daily_return * np.sqrt(252)

                    # 无风险利率（假设5%）
                    risk_free_rate = 0.02  # 2%适合加密货币市场

                    # 夏普比率 = (年化回报 - 无风险利率) / 年化波动率
                    if annual_volatility > 0:
                        self.strategy_metrics.sharpe_ratio = (
                            annual_return - risk_free_rate
                        ) / annual_volatility
                    else:
                        self.strategy_metrics.sharpe_ratio = 0.0

                    # 计算最大回撤
                    cumulative_returns = np.cumprod(1 + daily_returns)
                    peak = np.maximum.accumulate(cumulative_returns)
                    drawdown = (cumulative_returns - peak) / peak
                    max_drawdown = np.min(drawdown)

                    # 更新策略指标
                    self.strategy_metrics.max_drawdown = float(max_drawdown)

                    # 计算Calmar比率 (年化回报/最大回撤)
                    if abs(max_drawdown) > 0.001:
                        calmar_ratio = annual_return / abs(max_drawdown)
                        if hasattr(self.strategy_metrics, "calmar_ratio"):
                            self.strategy_metrics.calmar_ratio = calmar_ratio

                    # 计算胜率
                    winning_trades = len([r for r in returns if r > 0])
                    if len(returns) > 0:
                        win_rate = winning_trades / len(returns)
                        self.strategy_metrics.win_rate = win_rate

                    # 计算盈亏比
                    winning_returns = [r for r in returns if r > 0]
                    losing_returns = [r for r in returns if r < 0]

                    if winning_returns and losing_returns:
                        avg_win = np.mean(winning_returns)
                        avg_loss = abs(np.mean(losing_returns))
                        if avg_loss > 0:
                            profit_loss_ratio = avg_win / avg_loss
                            if hasattr(self.strategy_metrics, "profit_loss_ratio"):
                                self.strategy_metrics.profit_loss_ratio = (
                                    profit_loss_ratio
                                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Performance metrics update failed: {e}")

    async def _optimize_strategy_parameters(self):
        """优化策略参数"""
        try:
            # 基于历史表现调整参数
            if self.strategy_metrics.win_rate < 0.4:  # 胜率过低
                # 提高IV阈值，更保守
                self.config["iv_threshold"] = min(
                    0.8, self.config["iv_threshold"] + 0.05
                )

            elif self.strategy_metrics.win_rate > 0.7:  # 胜率很高
                # 降低IV阈值，更积极
                self.config["iv_threshold"] = max(
                    0.6, self.config["iv_threshold"] - 0.02
                )

            # 根据错误率调整
            if self.strategy_metrics.error_count > 20:
                # 增加检查间隔，降低频率
                self.config["risk_check_interval"] = min(
                    120, self.config["risk_check_interval"] + 10
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Parameter optimization failed: {e}")

    def get_strategy_status(self) -> dict[str, Any]:
        """获取策略状态"""
        return {
            "current_mode": self.current_mode.value if self.current_mode else None,
            "target_mode": self.target_mode.value if self.target_mode else None,
            "mode_transition_time": self.mode_transition_time.isoformat()
            if self.mode_transition_time
            else None,
            "metrics": {
                "total_trades": self.strategy_metrics.total_trades,
                "successful_trades": self.strategy_metrics.successful_trades,
                "total_pnl": float(self.strategy_metrics.total_pnl),
                "win_rate": self.strategy_metrics.win_rate,
                "sharpe_ratio": self.strategy_metrics.sharpe_ratio,
                "mode_switch_count": self.strategy_metrics.mode_switch_count,
                "error_count": self.strategy_metrics.error_count,
            },
            "positions": {
                symbol: {
                    "type": pos.position_type,
                    "size": float(pos.size),
                    "unrealized_pnl": float(pos.unrealized_pnl),
                    "delta": pos.delta,
                    "expiry": pos.expiry.isoformat() if pos.expiry else None,
                }
                for symbol, pos in self.positions.items()
            },
            "pending_orders": len(self.pending_orders),
            "config": self.config,
        }

    async def _convert_btc_order_to_ibit(
        self, order: OrderRequest
    ) -> OrderRequest | None:
        """将BTC期权订单转换为IBIT期权订单"""
        try:
            if not self.btc_to_ibit_mapper:
                if self.logger:
                    await self.logger.error("BTC-IBIT映射器未初始化")
                return None

            # 获取当前BTC价格
            btc_price = await self._get_current_btc_price()
            if not btc_price:
                if self.logger:
                    await self.logger.warning("无法获取BTC价格，跳过映射")
                return None

            # 获取当前IBIT价格
            ibit_price = await self._get_current_ibit_price()
            if not ibit_price:
                if self.logger:
                    await self.logger.warning("无法获取IBIT价格，跳过映射")
                return None

            # 构建BTC信号
            direction = (
                "bullish"
                if order.option_type == "call"
                else "bearish"
                if order.option_type == "put"
                else "neutral"
            )
            btc_signal = BTCSideSignal(
                direction=direction,
                target_delta=0.2,  # 基于现有策略的delta范围
                tenor_days=30,  # 默认30天期权
                btc_price=btc_price,
            )

            # 使用映射器转换
            # 使用真实期权日历计算近月到期
            try:
                from src.utils.option_calendar import option_calendar

                next_expiry = option_calendar.get_expiry_for_tenor(30)
            except Exception:
                from datetime import timedelta

                next_expiry = (datetime.now(UTC) + timedelta(days=30)).date()

            mapping_output = self.btc_to_ibit_mapper.map_to_ibit(
                btc_signal, ibit_price, next_expiry
            )

            if mapping_output.action == "SKIP":
                if self.logger:
                    await self.logger.info(f"映射器建议跳过交易: {order.symbol}")
                return None

            # 转换为IBIT期权订单
            if mapping_output.option:
                ibit_symbol = self._format_ibit_option_symbol(mapping_output.option)

                # 创建新的OrderRequest
                ibit_order = OrderRequest(
                    symbol=ibit_symbol,
                    side=order.side,
                    order_type=order.order_type,
                    size=order.size,
                    price=order.price,  # 可以进一步优化价格映射
                    strike=mapping_output.option.strike,
                    expiry=datetime.combine(
                        mapping_output.option.expiry, datetime.min.time()
                    ).replace(tzinfo=UTC),
                    option_type=mapping_output.option.right.value.lower(),
                    reason=f"BTC-IBIT映射: {order.reason}",
                    metadata={
                        **order.metadata,
                        "btc_original_symbol": order.symbol,
                        "mapping_action": mapping_output.action,
                        "btc_price": float(btc_price),
                        "ibit_price": float(ibit_price),
                    },
                )

                if self.logger:
                    await self.logger.info(
                        f"BTC订单映射成功: {order.symbol} -> {ibit_symbol} "
                        f"(动作: {mapping_output.action})"
                    )

                return ibit_order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"BTC-IBIT订单转换失败: {e}")

        return None

    # NOTE: liquidity proxy logic moved to src/services/liquidity_service.py

    async def _get_current_btc_price(self) -> Decimal | None:
        """获取当前BTC价格 - 使用统一的MarketDataService"""
        try:
            if self.market_data_service:
                return await self.market_data_service.get_btc_price()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取BTC价格失败: {e}")
        return None

    async def _get_current_ibit_price(self) -> Decimal | None:
        """获取当前IBIT价格"""
        try:
            # 设计：信号侧不依赖 IBKR 行情，优先从 MarketDataService 获取（缓存）；无则回退映射
            try:
                if self.dependency_injector:
                    mds = await self.dependency_injector.resolve(MarketDataService)
                    val = await mds.get_ibit_price(prefer_realtime=False)
                    if val is not None:
                        return val
            except Exception:
                pass

            # 回退：使用 BTC→IBIT 映射估算
            btc_price = await self._get_current_btc_price()
            if btc_price and self.btc_to_ibit_mapper:
                estimated_ibit_price = self.btc_to_ibit_mapper.estimate_ibit_price(
                    btc_price
                )
                if self.logger:
                    await self.logger.info(f"使用估算IBIT价格: {estimated_ibit_price}")
                return estimated_ibit_price

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取IBIT价格失败: {e}")
        return None

    def _format_ibit_option_symbol(self, option) -> str:
        """格式化IBIT期权符号"""
        try:
            # IBIT期权符号格式: IBIT 2025-03-21 50 C
            expiry_str = option.expiry.strftime("%Y-%m-%d")
            strike_str = str(int(option.strike))
            option_type = option.right.value  # 'C' 或 'P'

            return f"IBIT {expiry_str} {strike_str} {option_type}"

        except Exception:
            # 备选简单格式
            return f"IBIT_{option.expiry}_{option.strike}_{option.right.value}"

    async def _handle_position_update(self, event: PositionUpdateEvent):
        """处理仓位更新事件，生成出场信号"""
        try:
            symbol = event.symbol
            current_position = event.current_position
            change_type = event.change_type
            pnl_data = event.pnl_data

            if self.logger:
                await self.logger.info(
                    f"收到仓位更新: {symbol} {change_type}, "
                    f"size={current_position.get('size', 0)}, "
                    f"pnl={pnl_data.get('unrealized_pnl', 0):.2f}"
                )

            # 检查是否需要生成出场信号
            should_exit, exit_reason = await self._should_exit_position(
                symbol, current_position, pnl_data
            )

            if should_exit:
                await self._generate_exit_signal(symbol, current_position, exit_reason)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理仓位更新事件失败: {e}")

    async def _should_exit_position(
        self, symbol: str, position: dict[str, Any], pnl_data: dict[str, Any]
    ) -> tuple[bool, str]:
        """判断是否应该出场"""
        try:
            unrealized_pnl = pnl_data.get("unrealized_pnl", 0)
            position_size = position.get("size", 0)
            position.get("avg_price", 0)

            # 如果没有仓位，不需要出场
            if abs(position_size) < 0.001:
                return False, ""

            # 止损/止盈阈值从配置读取（fallback 到默认值）
            params = self.config.get("parameters", {}) if isinstance(self.config, dict) else {}
            loss_threshold = float(params.get("exit_loss_usd", -1000))
            if unrealized_pnl < loss_threshold:
                return True, f"止损出场: 亏损 ${abs(unrealized_pnl):.2f}"

            # 止盈检查
            profit_threshold = float(params.get("exit_profit_usd", 2000))
            if unrealized_pnl > profit_threshold:
                return True, f"止盈出场: 盈利 ${unrealized_pnl:.2f}"

            # 期权到期检查 (如果是期权)
            if "BTC-" in symbol or "IBIT" in symbol:
                # 这里可以添加期权到期逻辑
                pass

            # 风险管理检查
            # 可以根据市场条件、波动率等决定是否出场

            return False, ""

        except Exception as e:
            if self.logger:
                await self.logger.error(f"判断出场条件失败: {e}")
            return False, "error"

    async def _generate_exit_signal(
        self, symbol: str, position: dict[str, Any], exit_reason: str
    ):
        """生成出场信号"""
        try:
            position_side = position.get("side", "")
            position_size = abs(position.get("size", 0))

            # 确定出场方向（与持仓方向相反）
            exit_action = "sell" if position_side.lower() == "long" else "buy"

            # 生成交易决策事件
            exit_decision = TradingDecisionEvent(
                strategy_name=self.component_name,
                action=exit_action,
                instruments=[symbol],
                confidence=0.9,  # 出场信号通常置信度较高
                decision_data={
                    "quantity": position_size,
                    "reason": exit_reason,
                    "exit_type": "position_based",
                    "position_info": position,
                },
                risk_params={
                    "max_position_size": position_size,
                    "strategy_role": "main",
                    "urgency": "high",  # 出场信号通常具有高紧急性
                },
                source=self.component_name,
            )

            # 发布出场决策事件
            if self.event_bus:
                await self.event_bus.publish(exit_decision)

                if self.logger:
                    await self.logger.info(
                        f"🔄 发布出场信号: {symbol} {exit_action} {position_size}, "
                        f"原因: {exit_reason}"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"生成出场信号失败: {e}")

    # 公共接口方法
    async def get_strategy_stats(self) -> dict[str, Any]:
        """获取策略统计"""
        try:
            # 计算分配资金
            allocated_capital = float(
                self.config.get("capital_allocation", Decimal("0.0"))
            )

            # 构建统计数据
            stats = {
                "total_pnl": float(self.strategy_metrics.total_pnl),
                "allocated_capital": allocated_capital,
                "total_trades": self.strategy_metrics.total_trades,
                "successful_trades": self.strategy_metrics.successful_trades,
                "win_rate": self.strategy_metrics.win_rate,
                "max_drawdown": float(self.strategy_metrics.max_drawdown),
                "sharpe_ratio": self.strategy_metrics.sharpe_ratio,
                "avg_trade_duration": self.strategy_metrics.avg_trade_duration,
                "current_mode": self.current_mode.value if self.current_mode else None,
                "mode_switch_count": self.strategy_metrics.mode_switch_count,
                "last_trade_time": self.strategy_metrics.last_trade_time,
                "error_count": self.strategy_metrics.error_count,
                "active_positions": len(self.positions),
                "pending_orders": len(self.pending_orders),
            }

            return stats

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取策略统计失败: {e}")
            return {
                "total_pnl": 0.0,
                "allocated_capital": 0.0,
                "total_trades": 0,
                "successful_trades": 0,
                "win_rate": 0.0,
                "current_mode": None,
                "error_count": 1,
            }

    async def get_current_position(self) -> dict[str, Any] | None:
        """获取当前持仓信息"""
        try:
            if not self.positions:
                return None

            # 汇总所有持仓信息
            total_quantity = Decimal("0")
            total_value = Decimal("0")
            symbols = []

            for symbol, position in self.positions.items():
                symbols.append(symbol)
                total_quantity += abs(position.quantity)
                total_value += position.market_value

            # 构建持仓汇总
            position_summary = {
                "symbols": symbols,
                "total_positions": len(self.positions),
                "total_quantity": float(total_quantity),
                "total_value": float(total_value),
                "current_mode": self.current_mode.value if self.current_mode else None,
                "pending_orders": len(self.pending_orders),
            }

            return position_summary

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取当前持仓失败: {e}")
            return None
