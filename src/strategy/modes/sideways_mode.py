"""
震荡模式 (SidewaysMode)

实现Short Strangle和Iron Condor策略逻辑，通过同时卖出Put和Call期权收获时间价值：
1. 波动率环境判断和策略选择
2. 时间价值收益优化
3. Gamma风险控制和提前平仓
4. 对称性管理和Delta中性

核心策略：
- 震荡时构建Short Strangle：同时卖出OTM Put和Call
- 或构建Iron Condor：在Short Strangle基础上买入保护翼
- Strike选择：Put在下方10-15%，Call在上方10-15%
- 确保收入/风险比至少1:3
"""

import asyncio

# 现在使用common_types.py中的统一类型定义
from dataclasses import dataclass
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any

from src.analysis.common_types import (
    GreeksData,
    OptionDataWithGreeks,
    OptionLeg,
    OptionType,
    SidewaysMetrics,
    SidewaysOrder,
    StrategyMode,
    StrategyType,
)
from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.config_manager import ConfigManager
from src.core.event_bus import EventBus
from src.core.order_types import OrderSide
from src.data.cache_manager import CacheManager
from src.services.market_data_service import MarketDataService
from src.services.option_selector import OptionSelectionCriteria, OptionSelectorService
from src.strategy.utils import safe_parse_expiry


@dataclass
class SidewaysStrategy:
    """震荡策略组合"""

    strategy_id: str
    strategy_type: str  # 'short_strangle' or 'iron_condor'
    legs: list[OptionLeg]
    net_premium: Decimal
    max_risk: Decimal
    max_profit: Decimal
    break_even_lower: Decimal
    break_even_upper: Decimal
    delta_neutral: bool
    created_at: datetime
    expiry: datetime

    @property
    def profit_risk_ratio(self) -> float:
        """收益风险比"""
        if self.max_risk > 0:
            return float(self.max_profit / self.max_risk)
        return 0.0

    @property
    def days_to_expiry(self) -> int:
        """距离到期天数"""
        return (self.expiry.date() - datetime.now(UTC).date()).days


class SidewaysMode(BaseComponent):
    """
    震荡模式

    核心功能：
    1. Short Strangle和Iron Condor策略
    2. 波动率环境判断和策略选择
    3. 时间价值收益优化
    4. Gamma风险控制和提前平仓
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("SidewaysMode")
        self.config_manager = config_manager

        # 核心组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None
        self.option_selector = OptionSelectorService()

        # 震荡配置
        self.config = {
            "min_iv_rank": 0.6,  # 最小IV排名
            "delta_range": [0.10, 0.20],  # Delta目标范围
            "put_strike_range": [-0.15, -0.10],  # Put Strike相对价格范围（下方）
            "call_strike_range": [0.10, 0.15],  # Call Strike相对价格范围（上方）
            "min_profit_risk_ratio": 0.25,  # 最小收益风险比（1:4）
            "max_position_size": Decimal("500"),  # 最大仓位大小
            "min_expiry_days": 14,  # 最小到期天数
            "max_expiry_days": 45,  # 最大到期天数
            "min_volume": 50,  # 最小成交量
            "min_open_interest": 200,  # 最小持仓量
            "max_bid_ask_spread": 0.15,  # 最大买卖价差（适合加密货币期权）
            "delta_neutral_threshold": 0.05,  # Delta中性阈值
            "gamma_risk_limit": 1000,  # Gamma风险限制
            "profit_target": 0.5,  # 利润目标（50%最大利润）
            "loss_limit": 0.8,  # 止损限制（80%最大风险）
            "iron_condor_wing_distance": 500,  # Iron Condor翼距离
        }

        # 状态管理
        self.active_strategies: dict[str, SidewaysOrder] = {}
        self.metrics = SidewaysMetrics()
        self.last_analysis_time: datetime | None = None

        # 加载配置
        self._load_config()

        # 审计任务
        self._audit_task = None

    def _load_config(self):
        """加载配置"""
        try:
            strategy_config = self.config_manager.get("strategies.sideways_mode")
            if strategy_config:
                self.config.update(strategy_config)
        except Exception:
            pass

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("Initializing SidewaysMode")
                await self.logger.info(f"SidewaysMode config: {self.config}")

            # 初始化指标
            self.metrics = SidewaysMetrics()

            # 清理状态
            self.active_strategies.clear()

            # 注册配置热更新回调
            try:
                if hasattr(self.config_manager, "register_change_callback"):
                    self.config_manager.register_change_callback(
                        self._on_config_changes
                    )
            except Exception:
                pass

            if self.logger:
                await self.logger.info("SidewaysMode initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"SidewaysMode initialization failed: {e}")
                await self.logger.error(f"Exception type: {type(e).__name__}")
                import traceback

                await self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    async def _on_config_changes(self, changes):
        """处理配置热更新：关注 strategies.sideways_mode.* 变更并应用到运行时配置"""
        try:
            relevant = [
                c
                for c in changes
                if str(getattr(c, "config_path", "")).startswith(
                    "strategies.sideways_mode."
                )
            ]
            if not relevant:
                return
            for change in relevant:
                key = change.config_path.split(".")[-1]
                new_val = change.new_value
                old_val = self.config.get(key)
                self.config[key] = new_val
                if self.logger:
                    await self.logger.info(
                        f"SIDE config hot-updated: {key} {old_val} -> {new_val}"
                    )
            from contextlib import suppress

            with suppress(Exception):
                self.metrics.custom_metrics["config_hot_updates"] = (
                    self.metrics.custom_metrics.get("config_hot_updates", 0)
                    + len(relevant)
                )
        except Exception as e:
            if self.logger:
                await self.logger.error(f"SIDE hot-update handler error: {e}")

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if self.logger:
                await self.logger.info("Starting SidewaysMode")

            # 启动成功
            if self.logger:
                await self.logger.info("SidewaysMode started successfully")
            # 启动审计日志任务
            self._audit_task = asyncio.create_task(self._audit_loop())
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"SidewaysMode start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("Stopping SidewaysMode")

            # 清理资源
            self.active_strategies.clear()

            # 停止审计任务
            if self._audit_task and not self._audit_task.done():
                self._audit_task.cancel()
                from contextlib import suppress

                with suppress(asyncio.CancelledError):
                    await self._audit_task

            if self.logger:
                await self.logger.info("SidewaysMode stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"SidewaysMode stop failed: {e}")
            return False

    async def _audit_loop(self):
        """周期性输出震荡模式关键状态与阈值"""
        while self.is_running:
            try:
                params = self.config
                metrics = self.metrics
                msg = (
                    f"SIDE audit: min_iv_rank={float(params.get('min_iv_rank', 0.6)):.2f}, "
                    f"delta_range={params.get('delta_range')}, "
                    f"min_prr={float(params.get('min_profit_risk_ratio', 0.25)):.2f}, "
                    f"delta_neutral_th={float(params.get('delta_neutral_threshold', 0.05)):.3f}, "
                    f"gamma_limit={float(params.get('gamma_risk_limit', 1000)):.1f}, "
                    f"active_strategies={len(self.active_strategies)}, "
                    f"delta_exposure={float(getattr(metrics, 'current_delta_exposure', 0.0)):.2f}, "
                    f"max_gamma={float(getattr(metrics, 'max_gamma_exposure', 0.0)):.2f}, "
                    f"profit_target={float(params.get('profit_target', 0.5)):.2f}, "
                    f"loss_limit={float(params.get('loss_limit', 0.8)):.2f}"
                )
                if self.logger:
                    await self.logger.info(msg)
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Sideways audit error: {e}")
            finally:
                await asyncio.sleep(self.config.get("audit_interval", 600))

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查配置
            if not self.config:
                issues.append("Configuration not loaded")

            # 检查依赖组件
            if not self.cache_manager:
                issues.append("CacheManager not available")

            # 检查错误率
            if self.metrics.error_count > 10:
                issues.append(f"High error count: {self.metrics.error_count}")

            # 检查Gamma风险
            if self.metrics.max_gamma_exposure > self.config["gamma_risk_limit"]:
                issues.append(f"High Gamma exposure: {self.metrics.max_gamma_exposure}")

            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "SidewaysMode is healthy"
            elif len(issues) <= 2:
                status = HealthStatus.DEGRADED
                message = f"SidewaysMode has minor issues: {', '.join(issues)}"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"SidewaysMode has major issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "active_strategies": len(self.active_strategies),
                    "total_strategies": self.metrics.total_strategies_created,
                    "success_rate": self.metrics.success_rate,
                    "delta_exposure": self.metrics.current_delta_exposure,
                    "gamma_exposure": self.metrics.max_gamma_exposure,
                    "error_count": self.metrics.error_count,
                },
                timestamp=datetime.now(UTC),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                timestamp=datetime.now(UTC),
            )

    async def should_trade_sideways(
        self, current_price: Decimal, market_analysis: dict[str, Any]
    ) -> bool:
        """判断是否应该执行震荡策略"""
        try:
            # 输入验证
            if not current_price or current_price <= 0:
                if self.logger:
                    await self.logger.error(f"Invalid current price: {current_price}")
                return False

            if not isinstance(market_analysis, dict):
                if self.logger:
                    await self.logger.error(
                        "Invalid market_analysis: must be a dictionary"
                    )
                return False

            # 检查IV环境
            iv_rank = market_analysis.get("iv_rank", 0)
            if not isinstance(iv_rank, (int | float)) or iv_rank < 0 or iv_rank > 1:
                if self.logger:
                    await self.logger.warning(
                        f"Invalid IV rank: {iv_rank}, using default 0"
                    )
                iv_rank = 0

            if iv_rank < self.config["min_iv_rank"]:
                if self.logger:
                    await self.logger.debug(
                        f"IV rank too low: {iv_rank} < {self.config['min_iv_rank']}"
                    )
                return False

            # 检查价格趋势
            price_momentum = market_analysis.get("price_momentum", 0)
            if abs(price_momentum) > 0.03:  # 趋势太强
                if self.logger:
                    await self.logger.debug(
                        f"Price momentum too strong: {abs(price_momentum)} > 0.03"
                    )
                return False

            # 检查波动率环境
            realized_vol = market_analysis.get("realized_volatility", 0)
            implied_vol = market_analysis.get("implied_volatility", 0)
            if implied_vol <= realized_vol:  # IV不够高
                if self.logger:
                    await self.logger.debug(
                        f"IV not high enough: {implied_vol} <= {realized_vol}"
                    )
                return False

            # 检查技术指标
            rsi = market_analysis.get("rsi", 50)
            if rsi < 40 or rsi > 60:  # 不在震荡区间
                if self.logger:
                    await self.logger.debug(
                        f"RSI not in sideways range: {rsi} not in [40, 60]"
                    )
                return False

            # 检查当前仓位
            current_exposure = await self._calculate_current_exposure()
            if current_exposure >= self.config["max_position_size"]:
                if self.logger:
                    await self.logger.debug(
                        f"Position size limit reached: {current_exposure} >= {self.config['max_position_size']}"
                    )
                return False

            if self.logger:
                await self.logger.debug("All sideways conditions met")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Sideways condition check failed: {e}")
            self.metrics.error_count += 1
            return False

    async def generate_sideways_strategies(
        self, current_price: Decimal, market_analysis: dict[str, Any]
    ) -> list[SidewaysOrder]:
        """生成震荡策略"""
        try:
            orders = []

            # 获取可用期权
            options_data = await self._get_available_options(current_price)
            if not options_data:
                return orders

            # 根据IV环境和市场状态选择策略类型
            iv_rank = market_analysis.get("iv_rank", 0)
            realized_vol = market_analysis.get("realized_volatility", 0)
            implied_vol = market_analysis.get("implied_volatility", 0)

            # 计算IV溢价程度
            iv_premium = (
                (implied_vol - realized_vol) / realized_vol if realized_vol > 0 else 0
            )

            if iv_rank >= 0.8 and iv_premium > 0.2:
                # 高IV且IV明显高于RV：优先Short Strangle（收获更多时间价值）
                strategy = await self._create_short_strangle(
                    current_price, options_data, market_analysis
                )
                if self.logger:
                    await self.logger.debug(
                        f"选择Short Strangle：IV_rank={iv_rank:.2f}, IV溢价={iv_premium:.2f}"
                    )
            elif iv_rank >= 0.6:
                # 中高IV：先尝试Iron Condor（有限风险），不可用则使用Short Strangle
                strategy = await self._create_iron_condor(
                    current_price, options_data, market_analysis
                )
                if not strategy:
                    strategy = await self._create_short_strangle(
                        current_price, options_data, market_analysis
                    )
                    if self.logger:
                        await self.logger.debug(
                            "Iron Condor不可用，使用Short Strangle策略"
                        )
                elif self.logger:
                    await self.logger.debug(f"选择Iron Condor：IV_rank={iv_rank:.2f}")
            else:
                # 低IV：不适合震荡策略
                if self.logger:
                    await self.logger.debug(
                        f"IV过低不适合震荡策略：IV_rank={iv_rank:.2f}"
                    )
                return orders

            if strategy:
                order = await self._create_sideways_order(strategy)
                if order:
                    orders.append(order)

            return orders

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Sideways strategy generation failed: {e}")
            self.metrics.error_count += 1
            return []

    async def _create_short_strangle(
        self,
        current_price: Decimal,
        options_data: dict[str, list],
        market_analysis: dict[str, Any],
    ) -> SidewaysStrategy | None:
        """创建Short Strangle策略 - 使用Monte Carlo增强风险评估"""
        try:
            puts = options_data.get("puts", [])
            calls = options_data.get("calls", [])

            if not puts or not calls:
                return None

            # 使用统一VaR计算服务

            # 选择Put腿
            put_strike = current_price * (
                1 + Decimal(str(self.config["put_strike_range"][0]))
            )  # 下方10-15%
            best_put = await self._select_best_option(puts, put_strike, "put")

            if not best_put:
                if self.logger:
                    await self.logger.debug(
                        f"No suitable put found for strike {put_strike}"
                    )
                return None

            # 选择Call腿
            call_strike = current_price * (
                1 + Decimal(str(self.config["call_strike_range"][1]))
            )  # 上方10-15%
            best_call = await self._select_best_option(calls, call_strike, "call")

            if not best_call:
                if self.logger:
                    await self.logger.debug(
                        f"No suitable call found for strike {call_strike}"
                    )
                return None

            # 使用传统风险计算
            net_premium = best_put["premium"] + best_call["premium"]
            max_profit = net_premium

            avg_iv = (best_put["iv"] + best_call["iv"]) / 2
            expiry_datetime = safe_parse_expiry(best_put["expiry"])
            if expiry_datetime:
                days_to_expiry = (expiry_datetime - datetime.now(UTC)).days
            else:
                days_to_expiry = 0

            if days_to_expiry > 0:
                time_factor = (days_to_expiry / 365) ** 0.5
                price_std = (
                    current_price * Decimal(str(avg_iv)) * Decimal(str(time_factor))
                )
                extreme_move = price_std * Decimal("2.5")

                put_breakeven = best_put["strike"] - net_premium
                call_breakeven = best_call["strike"] + net_premium

                downside_loss = max(0, extreme_move - (current_price - put_breakeven))
                upside_loss = max(0, extreme_move - (call_breakeven - current_price))
                statistical_risk = max(downside_loss, upside_loss)

                risk_cap_price = current_price * Decimal("0.15")
                risk_cap_premium = net_premium * 5
                max_risk = min(statistical_risk, risk_cap_price, risk_cap_premium)
            else:
                put_intrinsic = max(0, best_put["strike"] - current_price)
                call_intrinsic = max(0, current_price - best_call["strike"])
                max_risk = max(put_intrinsic, call_intrinsic) - net_premium

            # 检查收益风险比
            profit_risk_ratio = float(max_profit / max_risk) if max_risk > 0 else 0
            if profit_risk_ratio < self.config["min_profit_risk_ratio"]:
                if self.logger:
                    await self.logger.debug(
                        f"Short Strangle profit/risk ratio too low: {profit_risk_ratio:.3f} < {self.config['min_profit_risk_ratio']}"
                    )
                return None

            # 构建策略腿
            # 解析expiry并确保不传递None
            put_expiry = safe_parse_expiry(best_put["expiry"])
            if not put_expiry:
                if self.logger:
                    await self.logger.error("Failed to parse put expiry date")
                return None

            put_option_data = OptionDataWithGreeks(
                symbol=best_put["symbol"],
                strike=Decimal(str(best_put["strike"])),
                expiry=put_expiry,
                option_type=OptionType.PUT,
                mark_price=Decimal(str(best_put["premium"])),
                bid=Decimal(str(best_put["bid"])),
                ask=Decimal(str(best_put["ask"])),
                volume=best_put["volume"],
                open_interest=best_put["open_interest"],
                greeks=GreeksData(
                    delta=best_put["delta"],
                    gamma=best_put["gamma"],
                    theta=best_put["theta"],
                    vega=best_put["vega"],
                    implied_vol=best_put["iv"],
                ),
            )

            call_expiry = safe_parse_expiry(best_call["expiry"])
            if not call_expiry:
                if self.logger:
                    await self.logger.error("Failed to parse call expiry date")
                return None

            call_option_data = OptionDataWithGreeks(
                symbol=best_call["symbol"],
                strike=Decimal(str(best_call["strike"])),
                expiry=call_expiry,
                option_type=OptionType.CALL,
                mark_price=Decimal(str(best_call["premium"])),
                bid=Decimal(str(best_call["bid"])),
                ask=Decimal(str(best_call["ask"])),
                volume=best_call["volume"],
                open_interest=best_call["open_interest"],
                greeks=GreeksData(
                    delta=best_call["delta"],
                    gamma=best_call["gamma"],
                    theta=best_call["theta"],
                    vega=best_call["vega"],
                    implied_vol=best_call["iv"],
                ),
            )

            legs = [
                OptionLeg(option_data=put_option_data, side=OrderSide.SELL, quantity=1),
                OptionLeg(
                    option_data=call_option_data, side=OrderSide.SELL, quantity=1
                ),
            ]

            # 创建策略
            strategy_id = f"strangle_{int(datetime.now(UTC).timestamp())}"
            strategy = SidewaysStrategy(
                strategy_id=strategy_id,
                strategy_type="short_strangle",
                legs=legs,
                net_premium=net_premium,
                max_risk=Decimal(str(max_risk)),
                max_profit=max_profit,
                break_even_lower=best_put["strike"] - net_premium,
                break_even_upper=best_call["strike"] + net_premium,
                delta_neutral=abs(best_put["delta"] + best_call["delta"])
                < self.config["delta_neutral_threshold"],
                created_at=datetime.now(UTC),
                expiry=put_expiry,
            )

            if self.logger:
                await self.logger.info(
                    f"Created Short Strangle strategy with profit/risk ratio: {profit_risk_ratio:.3f}"
                )

            return strategy

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Short Strangle creation failed: {e}")
            return None

    async def _create_iron_condor(
        self,
        current_price: Decimal,
        options_data: dict[str, list],
        market_analysis: dict[str, Any],
    ) -> SidewaysStrategy | None:
        """创建Iron Condor策略"""
        try:
            puts = options_data.get("puts", [])
            calls = options_data.get("calls", [])

            if not puts or not calls:
                return None

            # 选择Short腿
            short_put_strike = current_price * (
                1 + Decimal(str(self.config["put_strike_range"][0]))
            )
            short_call_strike = current_price * (
                1 + Decimal(str(self.config["call_strike_range"][1]))
            )

            short_put = await self._select_best_option(puts, short_put_strike, "put")
            short_call = await self._select_best_option(
                calls, short_call_strike, "call"
            )

            if not short_put or not short_call:
                if self.logger:
                    await self.logger.debug(
                        "No suitable short options found for Iron Condor"
                    )
                return None

            # 选择Long腿（保护翼）
            wing_distance = Decimal(str(self.config["iron_condor_wing_distance"]))
            long_put_strike = short_put["strike"] - wing_distance
            long_call_strike = short_call["strike"] + wing_distance

            long_put = await self._select_best_option(puts, long_put_strike, "put")
            long_call = await self._select_best_option(calls, long_call_strike, "call")

            if not long_put or not long_call:
                if self.logger:
                    await self.logger.debug(
                        "No suitable long options found for Iron Condor wings"
                    )
                return None

            # 构建策略腿
            short_put_data = OptionDataWithGreeks(
                symbol=short_put["symbol"],
                strike=Decimal(str(short_put["strike"])),
                expiry=safe_parse_expiry(short_put["expiry"]) or datetime.now(UTC),
                option_type=OptionType.PUT,
                mark_price=Decimal(str(short_put["premium"])),
                bid=Decimal(str(short_put["bid"])),
                ask=Decimal(str(short_put["ask"])),
                volume=short_put["volume"],
                open_interest=short_put["open_interest"],
                greeks=GreeksData(
                    delta=short_put["delta"],
                    gamma=short_put["gamma"],
                    theta=short_put["theta"],
                    vega=short_put["vega"],
                    implied_vol=short_put["iv"],
                ),
            )

            short_call_data = OptionDataWithGreeks(
                symbol=short_call["symbol"],
                strike=Decimal(str(short_call["strike"])),
                expiry=safe_parse_expiry(short_call["expiry"]) or datetime.now(UTC),
                option_type=OptionType.CALL,
                mark_price=Decimal(str(short_call["premium"])),
                bid=Decimal(str(short_call["bid"])),
                ask=Decimal(str(short_call["ask"])),
                volume=short_call["volume"],
                open_interest=short_call["open_interest"],
                greeks=GreeksData(
                    delta=short_call["delta"],
                    gamma=short_call["gamma"],
                    theta=short_call["theta"],
                    vega=short_call["vega"],
                    implied_vol=short_call["iv"],
                ),
            )

            long_put_data = OptionDataWithGreeks(
                symbol=long_put["symbol"],
                strike=Decimal(str(long_put["strike"])),
                expiry=safe_parse_expiry(long_put["expiry"]) or datetime.now(UTC),
                option_type=OptionType.PUT,
                mark_price=Decimal(str(long_put["premium"])),
                bid=Decimal(str(long_put["bid"])),
                ask=Decimal(str(long_put["ask"])),
                volume=long_put["volume"],
                open_interest=long_put["open_interest"],
                greeks=GreeksData(
                    delta=long_put["delta"],
                    gamma=long_put["gamma"],
                    theta=long_put["theta"],
                    vega=long_put["vega"],
                    implied_vol=long_put["iv"],
                ),
            )

            long_call_data = OptionDataWithGreeks(
                symbol=long_call["symbol"],
                strike=Decimal(str(long_call["strike"])),
                expiry=safe_parse_expiry(long_call["expiry"]) or datetime.now(UTC),
                option_type=OptionType.CALL,
                mark_price=Decimal(str(long_call["premium"])),
                bid=Decimal(str(long_call["bid"])),
                ask=Decimal(str(long_call["ask"])),
                volume=long_call["volume"],
                open_interest=long_call["open_interest"],
                greeks=GreeksData(
                    delta=long_call["delta"],
                    gamma=long_call["gamma"],
                    theta=long_call["theta"],
                    vega=long_call["vega"],
                    implied_vol=long_call["iv"],
                ),
            )

            legs = [
                OptionLeg(option_data=short_put_data, side=OrderSide.SELL, quantity=1),
                OptionLeg(option_data=short_call_data, side=OrderSide.SELL, quantity=1),
                OptionLeg(option_data=long_put_data, side=OrderSide.BUY, quantity=1),
                OptionLeg(option_data=long_call_data, side=OrderSide.BUY, quantity=1),
            ]

            # 计算策略指标
            net_premium = (
                Decimal(str(short_put["premium"]))
                + Decimal(str(short_call["premium"]))
                - Decimal(str(long_put["premium"]))
                - Decimal(str(long_call["premium"]))
            )
            max_profit = net_premium
            max_risk = wing_distance - net_premium

            # 检查收益风险比
            profit_risk_ratio = float(max_profit / max_risk) if max_risk > 0 else 0
            if profit_risk_ratio < self.config["min_profit_risk_ratio"]:
                if self.logger:
                    await self.logger.debug(
                        f"Iron Condor profit/risk ratio too low: {profit_risk_ratio} < {self.config['min_profit_risk_ratio']}"
                    )
                return None

            # 创建策略
            strategy_id = f"condor_{int(datetime.now(UTC).timestamp())}"
            strategy = SidewaysStrategy(
                strategy_id=strategy_id,
                strategy_type="iron_condor",
                legs=legs,
                net_premium=net_premium,
                max_risk=max_risk,
                max_profit=max_profit,
                break_even_lower=Decimal(str(short_put["strike"])) - net_premium,
                break_even_upper=Decimal(str(short_call["strike"])) + net_premium,
                delta_neutral=True,  # Iron Condor通常是Delta中性的
                created_at=datetime.now(UTC),
                expiry=safe_parse_expiry(short_put["expiry"]) or datetime.now(UTC),
            )

            return strategy

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Iron Condor creation failed: {e}")
            return None

    async def _get_available_options(self, current_price: Decimal) -> dict[str, list]:
        """获取可用期权"""
        try:
            # 从 MarketDataService 获取期权数据（回退缓存）
            mds = await self.dependency_injector.resolve(MarketDataService)
            options_block = await mds.get_option_chain("BTC")
            if isinstance(options_block, dict):
                options_data = options_block.get("options")
            else:
                options_data = options_block
            if not options_data:
                return {}

            puts = []
            calls = []

            # 计算Strike范围
            put_min_strike = current_price * (
                1 + Decimal(str(self.config["put_strike_range"][1]))
            )
            put_max_strike = current_price * (
                1 + Decimal(str(self.config["put_strike_range"][0]))
            )
            call_min_strike = current_price * (
                1 + Decimal(str(self.config["call_strike_range"][0]))
            )
            call_max_strike = current_price * (
                1 + Decimal(str(self.config["call_strike_range"][1]))
            )

            for option_data in options_data:
                strike = Decimal(str(option_data.get("strike", 0)))
                delta = abs(option_data.get("delta", 0))

                # 过滤条件
                if (
                    delta < self.config["delta_range"][0]
                    or delta > self.config["delta_range"][1]
                    or option_data.get("volume", 0) < self.config["min_volume"]
                    or option_data.get("open_interest", 0)
                    < self.config["min_open_interest"]
                ):
                    continue

                # 检查买卖价差
                bid = Decimal(str(option_data.get("bid", 0)))
                ask = Decimal(str(option_data.get("ask", 0)))
                mark_price = Decimal(str(option_data.get("mark_price", 0)))
                if (
                    mark_price > 0
                    and (ask - bid) / mark_price > self.config["max_bid_ask_spread"]
                ):
                    continue

                # 分类Put和Call
                if (
                    option_data.get("option_type") == "put"
                    and put_max_strike <= strike <= put_min_strike
                ):
                    puts.append(option_data)
                elif (
                    option_data.get("option_type") == "call"
                    and call_min_strike <= strike <= call_max_strike
                ):
                    calls.append(option_data)

            return {"puts": puts, "calls": calls}

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get available options: {e}")
            self.metrics.error_count += 1
            return {}

    async def _select_best_option(
        self, options: list[dict], target_strike: Decimal, option_type: str
    ) -> dict | None:
        """选择最佳期权"""
        try:
            if not options:
                return None

            # 创建选择标准
            criteria = OptionSelectionCriteria(
                option_type=option_type,
                target_strike=target_strike,
                min_expiry_days=self.config["min_expiry_days"],
                max_expiry_days=self.config["max_expiry_days"],
                min_volume=self.config["min_volume"],
                min_open_interest=self.config["min_open_interest"],
                max_spread_bps=self.config["max_bid_ask_spread"] * 10000,
            )

            # 使用统一的期权选择服务
            best_option = await self.option_selector.select_best_option(
                options, criteria
            )
            return best_option

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Option selection failed: {e}")
            return None

    async def _create_sideways_order(
        self, strategy: SidewaysStrategy
    ) -> SidewaysOrder | None:
        """创建震荡订单"""
        try:
            # 计算订单大小
            order_size = min(
                self.config["max_position_size"], strategy.net_premium * Decimal("10")
            )

            # 计算风险指标
            risk_metrics = {
                "max_profit": float(strategy.max_profit),
                "max_risk": float(strategy.max_risk),
                "profit_risk_ratio": strategy.profit_risk_ratio,
                "break_even_range": float(
                    strategy.break_even_upper - strategy.break_even_lower
                ),
                "days_to_expiry": strategy.days_to_expiry,
                "total_gamma": sum(abs(leg.option_data.gamma) for leg in strategy.legs),
                "total_theta": sum(leg.option_data.theta for leg in strategy.legs),
                "total_vega": sum(abs(leg.option_data.vega) for leg in strategy.legs),
            }

            # 生成订单ID
            order_id = f"sideways_{strategy.strategy_type}_{int(datetime.now(UTC).timestamp())}"

            # 创建订单
            order = SidewaysOrder(
                order_id=order_id,
                strategy_mode=StrategyMode.SIDEWAYS,
                size=order_size,
                target_premium=strategy.net_premium,
                batch_number=1,
                created_at=datetime.now(UTC),
                strategy_type=StrategyType.SHORT_STRANGLE
                if strategy.strategy_type == "short_strangle"
                else StrategyType.IRON_CONDOR,
                legs=strategy.legs,
                net_premium=strategy.net_premium,
                max_risk=strategy.max_risk,
                max_profit=strategy.max_profit,
                break_even_lower=strategy.break_even_lower,
                break_even_upper=strategy.break_even_upper,
                delta_neutral=strategy.delta_neutral,
                risk_metrics=risk_metrics,
                metadata={
                    "iv_rank": 0.7,  # 从market_analysis获取
                    "expected_theta_decay": risk_metrics["total_theta"]
                    * strategy.days_to_expiry,
                    "delta_neutral": strategy.delta_neutral,
                },
            )

            return order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Sideways order creation failed: {e}")
            return None

    async def _calculate_current_exposure(self) -> Decimal:
        """计算当前敞口"""
        try:
            total_exposure = Decimal("0")

            for order in self.active_strategies.values():
                total_exposure += order.size

            return total_exposure

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exposure calculation failed: {e}")
            return Decimal("0")

    async def should_close_early(
        self,
        order: SidewaysOrder,
        current_price: Decimal,
        market_analysis: dict[str, Any],
    ) -> bool:
        """判断是否应该提前平仓"""
        try:
            # 检查利润目标
            current_profit = await self._calculate_current_profit(order, current_price)
            profit_ratio = (
                current_profit / order.max_profit if order.max_profit > 0 else 0
            )

            if profit_ratio >= self.config["profit_target"]:
                return True

            # 检查止损
            loss_ratio = (
                abs(current_profit) / order.max_risk if order.max_risk > 0 else 0
            )
            if current_profit < 0 and loss_ratio >= self.config["loss_limit"]:
                return True

            # 检查Gamma风险
            gamma_exposure = sum(
                abs(leg.option_data.gamma) for leg in order.legs
            ) * float(order.size)
            if gamma_exposure > self.config["gamma_risk_limit"]:
                return True

            # 检查趋势突破
            price_momentum = market_analysis.get("price_momentum", 0)
            return abs(price_momentum) > 0.05  # 强趋势突破

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Early close check failed: {e}")
            return False

    async def _calculate_current_profit(
        self, order: SidewaysOrder, current_price: Decimal
    ) -> Decimal:
        """计算当前盈亏"""
        try:
            current_time = datetime.now(UTC)

            # 计算时间衰减因子 - 使用order的created_at作为开始时间
            # 假设到期时间为创建时间后30天
            days_passed = (current_time - order.created_at).days
            total_days = 30  # 假设30天到期
            time_decay_factor = days_passed / total_days if total_days > 0 else 1

            if order.strategy_type == StrategyType.SHORT_STRANGLE:
                # Short Strangle盈亏计算
                put_leg = next(
                    (leg for leg in order.legs if leg.option_type == OptionType.PUT),
                    None,
                )
                call_leg = next(
                    (leg for leg in order.legs if leg.option_type == OptionType.CALL),
                    None,
                )

                if not put_leg or not call_leg:
                    # 如果没有腿数据，使用简化的盈亏计算基于盈亏平衡点
                    if (
                        current_price <= order.break_even_lower
                        or current_price >= order.break_even_upper
                    ):
                        # 超出盈亏平衡点，计算损失
                        if current_price <= order.break_even_lower:
                            loss = order.break_even_lower - current_price
                        else:
                            loss = current_price - order.break_even_upper
                        # 损失受限于最大风险
                        total_pnl = -min(loss, order.max_risk)
                    else:
                        # 在盈亏平衡点内，计算时间价值收益
                        total_pnl = order.net_premium * Decimal(str(time_decay_factor))
                else:
                    # 计算内在价值损失
                    put_intrinsic = max(0, put_leg.strike - current_price)
                    call_intrinsic = max(0, current_price - call_leg.strike)
                    total_intrinsic = put_intrinsic + call_intrinsic

                    # 估算时间价值收益（卖方收益）
                    time_value_profit = order.net_premium * Decimal(
                        str(time_decay_factor)
                    )

                    # 总盈亏 = 时间价值收益 - 内在价值损失
                    total_pnl = time_value_profit - total_intrinsic

            elif order.strategy_type == StrategyType.IRON_CONDOR:
                # Iron Condor盈亏计算
                if (
                    current_price <= order.break_even_lower
                    or current_price >= order.break_even_upper
                ):
                    # 突破盈亏平衡点，计算实际损失
                    if current_price <= order.break_even_lower:
                        loss = order.break_even_lower - current_price
                    else:
                        loss = current_price - order.break_even_upper
                    total_pnl = order.net_premium - loss
                else:
                    # 在盈利区间，基于时间衰减计算利润
                    total_pnl = order.net_premium * Decimal(str(1 - time_decay_factor))
            else:
                # 其他策略类型的默认计算
                total_pnl = Decimal("0")

            return total_pnl

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Profit calculation failed: {e}")
            return Decimal("0")
