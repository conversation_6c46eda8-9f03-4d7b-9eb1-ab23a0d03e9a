"""
卖出模式 (DistributionMode)

实现Covered Call策略逻辑，通过卖出OTM Call期权实现期权版分批卖出：
1. 现货持仓检测和Call选择
2. 收益最大化的strike选择算法
3. 行权后的资金释放处理
4. Delta管理和风险控制

核心策略：
- 上涨时针对现货持仓分批卖出OTM Call期权（Delta 0.1-0.2）
- 若被行权：以预设高价卖出BTC现货（实现高位卖出）
- 若不被行权：保留现货同时获得权利金收益
"""

import asyncio
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any

from src.analysis.common_types import (
    DistributionMetrics,
    DistributionOrder,
    GreeksData,
    OptionDataWithGreeks,
    OptionType,
    StrategyMode,
)
from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.config_manager import ConfigManager
from src.core.dependency_injector import inject
from src.core.event_bus import EventBus
from src.data.cache_manager import CacheManager
from src.strategy.utils import safe_parse_expiry

# 现在使用common_types.py中的统一类型定义


class DistributionMode(BaseComponent):
    """
    卖出模式

    核心功能：
    1. Covered Call策略逻辑
    2. 现货持仓检测和Call选择
    3. 收益最大化的strike选择算法
    4. 行权后的资金释放处理
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("DistributionMode")
        self.config_manager = config_manager

        # 核心组件依赖 - 将通过依赖注入自动设置
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None

        # 卖出配置（对称抄底模式设计 - 优化版）
        self.config = {
            "min_spot_position": Decimal("0.1"),  # 最小现货持仓
            "delta_range": [0.08, 0.20],  # Delta目标范围（与抄底模式对称）
            "strike_range": [
                0.10,
                0.37,
            ],  # Strike相对价格范围（与抄底模式对称：10%-37%）
            "strike_levels": [
                0.10,
                0.17,
                0.26,
                0.37,
            ],  # 具体Strike水平：10%, 17%, 26%, 37%（扩大间距）
            "min_premium_rate": 0.015,  # 最小权利金收益率
            "max_coverage_ratio": 0.8,  # 最大覆盖比例
            "batch_sizes": [
                0.30,
                0.30,
                0.25,
                0.15,
            ],  # 分批比例（优化：减少深度OTM敞口）
            "min_expiry_days": 14,  # 最小到期天数
            "max_expiry_days": 45,  # 最大到期天数
            "min_volume": 100,  # 最小成交量
            "min_open_interest": 500,  # 最小持仓量
            "max_bid_ask_spread": 0.15,  # 最大买卖价差（适合加密货币期权）
            "exercise_handling": "auto_release",  # 行权处理方式
            "profit_target": 0.8,  # 利润目标（80%权利金）
        }

        # 状态管理
        self.active_orders: dict[str, DistributionOrder] = {}
        self.metrics = DistributionMetrics()
        self.last_analysis_time: datetime | None = None

        # 加载配置
        self._load_config()

        # 审计任务
        self._audit_task = None

    @inject(EventBus, CacheManager)
    async def set_dependencies(self, event_bus: EventBus, cache_manager: CacheManager):
        """设置组件依赖 - 通过依赖注入自动调用"""
        self.event_bus = event_bus
        self.cache_manager = cache_manager

    def _load_config(self):
        """加载配置"""
        try:
            strategy_config = self.config_manager.get("strategies.distribution_mode")
            if strategy_config:
                self.config.update(strategy_config)
        except Exception:
            pass

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("Initializing DistributionMode")
                await self.logger.info(f"DistributionMode config: {self.config}")

            # 初始化指标
            self.metrics = DistributionMetrics()

            # 清理状态
            self.active_orders.clear()

            # 注册配置热更新回调
            try:
                if hasattr(self.config_manager, "register_change_callback"):
                    self.config_manager.register_change_callback(
                        self._on_config_changes
                    )
            except Exception:
                pass

            if self.logger:
                await self.logger.info("DistributionMode initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DistributionMode initialization failed: {e}")
                await self.logger.error(f"Exception type: {type(e).__name__}")
                import traceback

                await self.logger.error(f"Traceback: {traceback.format_exc()}")
            return False

    async def _on_config_changes(self, changes):
        """处理配置热更新：关注 strategies.distribution_mode.* 变更并应用到运行时配置"""
        try:
            relevant = [
                c
                for c in changes
                if str(getattr(c, "config_path", "")).startswith(
                    "strategies.distribution_mode."
                )
            ]
            if not relevant:
                return
            for change in relevant:
                key = change.config_path.split(".")[-1]
                new_val = change.new_value
                old_val = self.config.get(key)
                self.config[key] = new_val
                if self.logger:
                    await self.logger.info(
                        f"DIST config hot-updated: {key} {old_val} -> {new_val}"
                    )
            from contextlib import suppress

            with suppress(Exception):
                self.metrics.custom_metrics["config_hot_updates"] = (
                    self.metrics.custom_metrics.get("config_hot_updates", 0)
                    + len(relevant)
                )
        except Exception as e:
            if self.logger:
                await self.logger.error(f"DIST hot-update handler error: {e}")

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if self.logger:
                await self.logger.info("Starting DistributionMode")

            # 启动成功
            if self.logger:
                await self.logger.info("DistributionMode started successfully")
            # 启动审计日志任务
            self._audit_task = asyncio.create_task(self._audit_loop())
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DistributionMode start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("Stopping DistributionMode")

            # 清理资源
            self.active_orders.clear()

            # 停止审计任务
            if self._audit_task and not self._audit_task.done():
                self._audit_task.cancel()
                from contextlib import suppress

                with suppress(asyncio.CancelledError):
                    await self._audit_task

            if self.logger:
                await self.logger.info("DistributionMode stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"DistributionMode stop failed: {e}")
            return False

    async def _audit_loop(self):
        """周期性输出分发模式关键状态与阈值"""
        while self.is_running:
            try:
                coverage = await self._calculate_current_coverage()
                max_cov = float(self.config.get("max_coverage_ratio", 0.8))
                available = max(0.0, max_cov - coverage)
                params = self.config
                msg = (
                    f"DIST audit: min_spot={float(params.get('min_spot_position', 0.0)):.3f}, "
                    f"delta_range={params.get('delta_range')}, "
                    f"strike_levels={params.get('strike_levels')}, "
                    f"max_coverage={max_cov:.2f}, coverage={float(coverage):.2f}, "
                    f"available={float(available):.2f}, batches={params.get('batch_sizes')}, "
                    f"profit_target={float(params.get('profit_target', 0.8)):.2f}"
                )
                if self.logger:
                    await self.logger.info(msg)
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Distribution audit error: {e}")
            finally:
                await asyncio.sleep(self.config.get("audit_interval", 600))

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查配置
            if not self.config:
                issues.append("Configuration not loaded")

            # 检查依赖组件
            if not self.cache_manager:
                issues.append("CacheManager not available")

            # 检查错误率
            if self.metrics.error_count > 10:
                issues.append(f"High error count: {self.metrics.error_count}")

            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "DistributionMode is healthy"
            elif len(issues) <= 2:
                status = HealthStatus.DEGRADED
                message = f"DistributionMode has minor issues: {', '.join(issues)}"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"DistributionMode has major issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "active_orders": len(self.active_orders),
                    "total_calls_sold": self.metrics.total_calls_sold,
                    "success_rate": self.metrics.success_rate,
                    "error_count": self.metrics.error_count,
                },
                timestamp=datetime.now(UTC),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                timestamp=datetime.now(UTC),
            )

    async def should_distribute(
        self, current_price: Decimal, market_analysis: dict[str, Any]
    ) -> bool:
        """判断是否应该执行卖出策略"""
        try:
            # 检查现货持仓
            spot_positions = await self._get_spot_positions()
            if (
                not spot_positions
                or sum(pos.get("size", 0) for pos in spot_positions)
                < self.config["min_spot_position"]
            ):
                return False

            # 检查价格上涨条件
            price_momentum = market_analysis.get("price_momentum", 0)
            if price_momentum < 0.03:  # 至少3%上涨
                return False

            # 检查IV环境
            iv_rank = market_analysis.get("iv_rank", 0)
            if iv_rank < 0.4:  # IV不能太低
                return False

            # 检查技术指标
            rsi = market_analysis.get("rsi", 50)
            if rsi < 60:  # RSI显示超买
                return False

            # 检查覆盖比例
            current_coverage = await self._calculate_current_coverage()
            return not current_coverage >= self.config["max_coverage_ratio"]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Distribution condition check failed: {e}")
            self.metrics.error_count += 1
            return False

    async def generate_distribution_orders(
        self, current_price: Decimal, market_analysis: dict[str, Any]
    ) -> list[DistributionOrder]:
        """生成卖出模式订单"""
        try:
            orders = []

            # 获取现货持仓
            spot_positions = await self._get_spot_positions()
            if not spot_positions:
                return orders

            # 获取可用的Call期权
            call_options = await self._get_available_call_options(current_price)
            if not call_options:
                return orders

            # 计算当前覆盖情况
            current_coverage = await self._calculate_current_coverage()
            available_coverage = self.config["max_coverage_ratio"] - current_coverage

            if available_coverage <= 0:
                return orders

            # 使用阻力位优化的动态Strike计算
            target_strikes = await self._calculate_target_strikes(current_price)

            # 为每个批次生成订单（使用阻力位优化的Strike）
            total_spot = sum(pos.get("size", 0) for pos in spot_positions)
            used_strikes = set()

            for i, batch_size in enumerate(self.config["batch_sizes"]):
                if available_coverage <= 0 or i >= len(target_strikes):
                    break

                # 计算本批次覆盖量
                batch_coverage = min(batch_size, available_coverage)
                batch_spot_size = Decimal(str(total_spot)) * Decimal(
                    str(batch_coverage)
                )

                # 为当前目标Strike选择最优Call期权
                target_strike = target_strikes[i]
                optimal_call = await self._select_optimal_call_for_target_strike(
                    call_options, target_strike, used_strikes
                )
                if not optimal_call:
                    # 如果找不到对应目标Strike的期权，使用通用选择
                    optimal_call = await self._select_optimal_call(
                        call_options, current_price, market_analysis
                    )

                if not optimal_call or optimal_call.strike in used_strikes:
                    continue

                # 创建订单
                order = await self._create_distribution_order(
                    optimal_call, batch_spot_size, i + 1
                )
                if order:
                    orders.append(order)
                    available_coverage -= batch_coverage
                    used_strikes.add(optimal_call.strike)

                    if self.logger:
                        await self.logger.info(
                            f"创建分发订单 批次{i + 1}: Strike={optimal_call.strike}, "
                            f"Delta={optimal_call.delta:.3f}, 比例={batch_size:.1%}, "
                            f"权利金={optimal_call.mark_price}, 覆盖{batch_spot_size} BTC"
                        )

            return orders

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Distribution order generation failed: {e}")
            self.metrics.error_count += 1
            return []

    async def handle_exercise(
        self, order: DistributionOrder, exercise_price: Decimal
    ) -> dict[str, Any]:
        """处理Call期权行权"""
        try:
            if self.logger:
                await self.logger.info(
                    f"Handling Call exercise for order {order.order_id}"
                )

            # 计算行权收益
            spot_sale_proceeds = order.covered_position * exercise_price
            premium_collected = order.filled_premium or order.premium_target
            total_proceeds = spot_sale_proceeds + premium_collected

            # 计算收益率
            original_cost = order.covered_position * (
                exercise_price - premium_collected / order.covered_position
            )
            profit_ratio = (
                (total_proceeds - original_cost) / original_cost
                if original_cost > 0
                else 0
            )

            # 更新指标
            self.metrics.exercised_calls += 1
            self.metrics.total_spot_sold += order.covered_position
            self.metrics.avg_sale_price = (
                self.metrics.avg_sale_price * (self.metrics.exercised_calls - 1)
                + exercise_price
            ) / self.metrics.exercised_calls

            # 释放资金
            released_funds = total_proceeds

            # 决定后续策略
            post_exercise_strategy = await self._determine_post_exercise_strategy(
                released_funds, exercise_price, profit_ratio, market_analysis={}
            )

            result = {
                "order_id": order.order_id,
                "exercise_price": exercise_price,
                "spot_sold": order.covered_position,
                "premium_collected": premium_collected,
                "total_proceeds": total_proceeds,
                "profit_ratio": profit_ratio,
                "released_funds": released_funds,
                "post_strategy": post_exercise_strategy,
            }

            if self.logger:
                await self.logger.info(f"Call exercise handled: {result}")

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exercise handling failed: {e}")
            self.metrics.error_count += 1
            return {}

    async def _calculate_target_strikes(
        self, current_price: Decimal, resistance_level: Decimal | None = None
    ) -> list[Decimal]:
        """基于当前价格和阻力位智能计算目标Strike价格（镜像抄底模式）"""
        try:
            # 获取默认Strike配置
            default_levels = self.config.get("strike_levels", [0.10, 0.17, 0.26, 0.37])
            target_strikes = []

            # 尝试获取阻力位信息进行智能优化
            if resistance_level is None:
                try:
                    from src.services.market_data_service import MarketDataService

                    mds = await self.dependency_injector.resolve(MarketDataService)
                    market_analysis = await mds.get_cached("latest_market_analysis")
                    if market_analysis and isinstance(market_analysis, dict):
                        resistance_level = market_analysis.get("resistance_level")
                        if resistance_level:
                            resistance_level = Decimal(str(resistance_level))
                except Exception:
                    resistance_level = None  # 获取失败时使用默认方案

            # 如果有可靠的阻力位信息，使用智能混合方案
            if resistance_level and resistance_level > current_price:
                optimized_strikes = self._calculate_resistance_optimized_strikes(
                    current_price, resistance_level, default_levels
                )
                target_strikes = optimized_strikes

                if self.logger:
                    # 使用异步日志记录
                    import asyncio

                    asyncio.create_task(
                        self.logger.info(
                            f"使用阻力位优化Strike计算: 阻力位={resistance_level}, 当前价格={current_price}"
                        )
                    )
            else:
                # 严格模式：缺少阻力位不生成目标
                from src.monitoring.strict_metrics import inc_skip

                inc_skip("DistributionMode", "no_resistance_level")
                target_strikes = []

                if self.logger:
                    # 使用异步日志记录
                    import asyncio

                    asyncio.create_task(
                        self.logger.info(
                            f"使用固定间距Strike计算: 阻力位={resistance_level}, 当前价格={current_price}"
                        )
                    )

            return target_strikes

        except Exception as e:
            if self.logger:
                # 使用异步日志记录
                import asyncio

                asyncio.create_task(
                    self.logger.error(f"Failed to calculate target strikes: {e}")
                )
            return []

    async def _get_spot_positions(self) -> list[Any]:
        """获取现货持仓"""
        try:
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            # 优先使用统一服务封装的读取
            positions_data = await mds.get_cached("spot_positions")
            if not positions_data:
                return []

            # 过滤BTC持仓
            btc_positions = [
                pos
                for pos in positions_data
                if pos.get("symbol", "").upper() == "BTC" and pos.get("size", 0) > 0
            ]

            return btc_positions

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get spot positions: {e}")
            self.metrics.error_count += 1
            return []

    async def _select_optimal_call_for_target_strike(
        self,
        call_options: list[OptionDataWithGreeks],
        target_strike: Decimal,
        used_strikes: set,
    ) -> OptionDataWithGreeks | None:
        """为指定目标Strike选择最优Call期权"""
        try:
            best_call = None
            best_score = -1

            for call in call_options:
                if call.strike in used_strikes:
                    continue

                # 计算与目标Strike的距离
                distance_to_target = abs(call.strike - target_strike) / target_strike

                # 如果距离超过10%，跳过
                if distance_to_target > 0.10:
                    continue

                # 计算综合评分：权利金 + 流动性 - 距离惩罚
                premium_score = float(call.mark_price) / 1000  # 权利金越高越好
                liquidity_score = min(1.0, call.volume / 100) * 0.3  # 流动性加分
                distance_penalty = distance_to_target * 10  # 距离惩罚
                delta_score = min(1.0, abs(call.delta) / 0.2) * 0.2  # Delta适合性

                combined_score = (
                    premium_score + liquidity_score + delta_score - distance_penalty
                )

                if combined_score > best_score:
                    best_score = combined_score
                    best_call = call

            return best_call

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Target strike selection failed: {e}")
            return None

    async def _select_optimal_call_for_strike_level(
        self,
        call_options: list[OptionDataWithGreeks],
        current_price: Decimal,
        target_strike_level: float,
        used_strikes: set,
    ) -> OptionDataWithGreeks | None:
        """为指定strike_level选择最优Call期权"""
        try:
            best_call = None
            best_score = -1

            for call in call_options:
                if call.strike in used_strikes:
                    continue

                # 计算与目标strike_level的距离
                actual_ratio = (call.strike - current_price) / current_price
                distance_to_target = abs(actual_ratio - target_strike_level)

                # 如果距离超过5%，跳过
                if distance_to_target > 0.05:
                    continue

                # 计算综合评分：权利金 + 流动性 - 距离惩罚
                premium_score = float(call.mark_price) / 1000  # 权利金越高越好
                liquidity_score = min(1.0, call.volume / 100) * 0.3  # 流动性加分
                distance_penalty = distance_to_target * 5  # 距离惩罚
                delta_score = min(1.0, abs(call.delta) / 0.2) * 0.2  # Delta适合性

                combined_score = (
                    premium_score + liquidity_score + delta_score - distance_penalty
                )

                if combined_score > best_score:
                    best_score = combined_score
                    best_call = call

            return best_call

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Strike level selection failed: {e}")
            return None

    async def _get_available_call_options(
        self, current_price: Decimal
    ) -> list[OptionDataWithGreeks]:
        """获取可用的Call期权（使用新的strike_range）"""
        try:
            if not self.cache_manager:
                return []

            # 从缓存获取期权数据
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            options_block = await mds.get_option_chain("BTC")
            options_data = (
                options_block
                if isinstance(options_block, list)
                else options_block.get("options")
                if options_block
                else None
            )
            if not options_data:
                return []

            call_options = []

            # 获取目标strike_levels用于优先筛选
            strike_levels = self.config.get("strike_levels", [0.08, 0.12, 0.18, 0.25])

            for option_data in options_data:
                if option_data.get("option_type") != "call":
                    continue

                strike = Decimal(str(option_data.get("strike", 0)))
                if strike <= current_price:  # Call期权Strike必须高于当前价格
                    continue

                # 计算strike比例
                strike_ratio = (strike - current_price) / current_price

                # 优先检查是否接近目标strike_levels，容差5%
                is_near_target = any(
                    abs(strike_ratio - target_level) <= 0.05
                    for target_level in strike_levels
                )

                # 如果不接近目标水平，检查是否在允许范围内
                if not is_near_target and not (
                    self.config["strike_range"][0]
                    <= strike_ratio
                    <= self.config["strike_range"][1]
                ):
                    continue

                # 检查Delta、流动性等条件
                delta = option_data.get("delta", 0)
                volume = option_data.get("volume", 0)
                open_interest = option_data.get("open_interest", 0)

                if (
                    self.config["delta_range"][0]
                    <= delta
                    <= self.config["delta_range"][1]
                    and volume >= self.config["min_volume"]
                    and open_interest >= self.config["min_open_interest"]
                ):
                    # 解析到期日期
                    expiry_date = safe_parse_expiry(option_data["expiry"])
                    if expiry_date is None:
                        continue  # 跳过无法解析到期日期的期权

                    call_option = OptionDataWithGreeks(
                        symbol=option_data["symbol"],
                        strike=Decimal(str(option_data["strike"])),
                        expiry=expiry_date,
                        option_type=OptionType.CALL,
                        mark_price=Decimal(str(option_data["mark_price"])),
                        bid=Decimal(str(option_data.get("bid", 0))),
                        ask=Decimal(str(option_data.get("ask", 0))),
                        volume=option_data["volume"],
                        open_interest=option_data["open_interest"],
                        greeks=GreeksData(
                            delta=option_data["delta"],
                            gamma=option_data.get("gamma", 0),
                            theta=option_data.get("theta", 0),
                            vega=option_data.get("vega", 0),
                            implied_vol=option_data.get("iv", 0),
                        ),
                    )

                    # 检查买卖价差
                    spread_ratio = (
                        float(call_option.bid_ask_spread / call_option.mark_price)
                        if call_option.mark_price > 0
                        else 0.0
                    )
                    if spread_ratio <= self.config["max_bid_ask_spread"]:
                        call_options.append(call_option)

            return call_options

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get call options: {e}")
            return []

    async def _select_optimal_call(
        self,
        call_options: list[OptionDataWithGreeks],
        current_price: Decimal,
        market_analysis: dict[str, Any],
    ) -> OptionDataWithGreeks | None:
        """选择最优Call期权 - 使用Monte Carlo增强定价"""
        try:
            if not call_options:
                return None

            # 导入Monte Carlo引擎
            from ...analysis.monte_carlo_engine import (
                MarketParameters,
                MonteCarloEngine,
                OptionContract,
                SimulationConfig,
            )

            # 初始化Monte Carlo引擎
            mc_engine = MonteCarloEngine()
            await mc_engine.initialize()

            # 市场参数
            market_params = MarketParameters(
                spot_price=float(current_price),
                risk_free_rate=0.02,  # 2%无风险利率（适合加密货币市场）
                volatility=market_analysis.get("implied_volatility", 0.5),
                dividend_yield=0.0,
            )

            # 模拟配置
            sim_config = SimulationConfig(
                num_simulations=5000,  # 适中的模拟次数以平衡精度和性能
                num_steps=50,
                antithetic_variates=True,
                parallel_execution=True,
                num_threads=2,
            )

            # 评分函数 - 集成Monte Carlo定价
            async def score_call_mc(call: OptionDataWithGreeks) -> float:
                score = 0.0

                try:
                    # 创建期权合约
                    time_to_expiry = (
                        call.expiry - datetime.now(UTC)
                    ).total_seconds() / (365.25 * 24 * 3600)
                    contract = OptionContract(
                        option_type="call",
                        strike=float(call.strike),
                        time_to_expiry=max(time_to_expiry, 0.001),  # 避免零时间
                        option_side="short",  # 我们在卖出Call
                        quantity=1.0,
                    )

                    # Monte Carlo定价
                    mc_result = await mc_engine.price_option(
                        contract, market_params, sim_config
                    )
                    theoretical_price = mc_result.option_price

                    # 市场价格与理论价格的比较
                    market_price = float(call.mark_price)
                    pricing_efficiency = (
                        market_price / theoretical_price if theoretical_price > 0 else 0
                    )

                    # 1. 定价效率评分（30%权重） - 市场价格高于理论价格更好（卖方优势）
                    if pricing_efficiency > 1.0:  # 市场价格高估
                        efficiency_score = min(2.0, pricing_efficiency)  # 最高2倍评分
                        score += 30 * (efficiency_score - 1.0)
                    elif pricing_efficiency > 0.8:  # 合理定价
                        score += 30 * 0.5

                    # 2. VaR风险评分（25%权重） - 低VaR风险更好
                    var_95 = abs(mc_result.var_95)
                    max_acceptable_var = float(current_price) * 0.1  # 10%最大VaR
                    if var_95 < max_acceptable_var:
                        var_score = 1.0 - (var_95 / max_acceptable_var)
                        score += 25 * var_score

                    # 3. 权利金收益率评分（20%权重）
                    premium_rate = market_price / float(call.strike)
                    if premium_rate >= self.config["min_premium_rate"]:
                        score += 20 * min(
                            2.0, premium_rate / self.config["min_premium_rate"]
                        )

                    # 4. Delta评分（15%权重）
                    target_delta = (
                        self.config["delta_range"][0] + self.config["delta_range"][1]
                    ) / 2
                    delta_score = 1 - abs(call.delta - target_delta) / target_delta
                    score += 15 * delta_score

                    # 5. 流动性评分（10%权重）
                    volume_score = min(call.volume / 1000, 1.0)
                    oi_score = min(call.open_interest / 5000, 1.0)
                    liquidity_score = (volume_score + oi_score) / 2
                    score += 10 * liquidity_score

                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Monte Carlo pricing failed for {call.symbol}: {e}"
                        )
                    # 使用简单评分
                    premium_rate = float(call.mark_price / call.strike)
                    if premium_rate >= self.config["min_premium_rate"]:
                        score += 40 * (premium_rate / self.config["min_premium_rate"])

                    target_delta = (
                        self.config["delta_range"][0] + self.config["delta_range"][1]
                    ) / 2
                    delta_score = 1 - abs(call.delta - target_delta) / target_delta
                    score += 30 * delta_score

                    volume_score = min(call.volume / 1000, 1.0)
                    oi_score = min(call.open_interest / 5000, 1.0)
                    liquidity_score = (volume_score + oi_score) / 2
                    score += 20 * liquidity_score

                    spread_score = max(
                        0, 1 - call.spread_ratio / self.config["max_bid_ask_spread"]
                    )
                    score += 10 * spread_score

                return score

            # 为每个期权计算评分
            scored_calls = []
            for call in call_options:
                try:
                    score = await score_call_mc(call)
                    scored_calls.append((call, score))
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Scoring failed for {call.symbol}: {e}"
                        )
                    continue

            if not scored_calls:
                return None

            # 选择评分最高的期权
            best_call, best_score = max(scored_calls, key=lambda x: x[1])

            # 验证最低要求
            premium_rate = float(best_call.mark_price / best_call.strike)
            if premium_rate < self.config["min_premium_rate"]:
                return None

            if self.logger:
                await self.logger.info(
                    f"Selected optimal call {best_call.symbol} with Monte Carlo score: {best_score:.2f}"
                )

            return best_call

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Monte Carlo call selection failed: {e}")

            # 使用统一的期权选择服务作为备选
            if (
                hasattr(self, "option_selector_service")
                and self.option_selector_service
            ):
                from src.services.option_selector import OptionSelectionCriteria

                criteria = OptionSelectionCriteria(
                    option_type="call",
                    target_delta=(
                        self.config["delta_range"][0] + self.config["delta_range"][1]
                    )
                    / 2,
                    max_spread_bps=self.config.get("max_bid_ask_spread", 0.05) * 10000,
                )

                option_dict_list = []
                for call in call_options:
                    option_dict = {
                        "strike": float(call.strike),
                        "option_type": "call",
                        "bid_price": float(call.bid_price),
                        "ask_price": float(call.ask_price),
                        "volume": call.volume,
                        "open_interest": call.open_interest,
                        "greeks": {"delta": call.delta},
                        "expiration_timestamp": call.expiry_timestamp,
                        "instrument_name": call.symbol,
                    }
                    option_dict_list.append(option_dict)

                best_option = await self.option_selector_service.select_best_option(
                    option_dict_list, criteria
                )

                if best_option:
                    best_call = next(
                        call
                        for call in call_options
                        if call.symbol == best_option["instrument_name"]
                    )
                else:
                    return None
            else:
                return None

            premium_rate = float(best_call.mark_price / best_call.strike)
            if premium_rate < self.config["min_premium_rate"]:
                return None

            return best_call

    async def _calculate_current_coverage(self) -> float:
        """计算当前覆盖比例"""
        try:
            # 获取现货总量
            spot_positions = await self._get_spot_positions()
            total_spot = sum(Decimal(str(pos.get("size", 0))) for pos in spot_positions)

            if total_spot == 0:
                return 0.0

            # 计算已覆盖的现货量
            covered_spot = sum(
                order.covered_position for order in self.active_orders.values()
            )

            return float(covered_spot / total_spot)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Coverage calculation failed: {e}")
            return 0.0

    async def _create_distribution_order(
        self, call: OptionDataWithGreeks, spot_size: Decimal, batch_number: int
    ) -> DistributionOrder | None:
        """创建卖出订单"""
        try:
            # 计算订单参数
            order_size = min(
                spot_size, call.strike * Decimal("0.1")
            )  # 限制单笔最大规模
            target_premium = call.mark_price * Decimal("0.95")  # 稍低于市价

            # 计算预期收益
            expected_pnl = target_premium * order_size / call.strike

            # 计算风险指标
            risk_metrics = {
                "max_opportunity_cost": float(
                    order_size * (call.strike - call.mark_price)
                ),  # 机会成本
                "break_even": float(call.strike + target_premium),
                "probability_exercise": self._estimate_exercise_probability(call),
                "gamma_risk": abs(call.gamma) * float(order_size),
                "vega_risk": abs(call.vega) * float(order_size),
            }

            # 生成订单ID
            order_id = f"dist_{int(datetime.now(UTC).timestamp())}_{batch_number}"

            # 创建订单
            order = DistributionOrder(
                order_id=order_id,
                strategy_mode=StrategyMode.DISTRIBUTION,
                size=order_size,
                target_premium=target_premium,
                batch_number=batch_number,
                created_at=datetime.now(UTC),
                call_option=call,
                covered_position=order_size,
                risk_metrics=risk_metrics,
                metadata={
                    "expected_pnl": float(expected_pnl),
                    "premium_rate": float(target_premium / call.strike),
                    "days_to_expiry": (call.expiry - datetime.now(UTC)).days,
                },
            )

            return order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order creation failed: {e}")
            return None

    def _estimate_exercise_probability(self, call: OptionDataWithGreeks) -> float:
        """估算行权概率"""
        try:
            # 基于Delta的简单估算
            # Delta近似等于行权概率
            return max(0.0, min(1.0, call.delta))

        except Exception:
            return 0.5  # 默认50%

    async def _determine_post_exercise_strategy(
        self,
        released_funds: Decimal,
        exercise_price: Decimal,
        profit_ratio: float,
        market_analysis: dict[str, Any],
    ) -> dict[str, Any]:
        """确定行权后策略"""
        try:
            strategy = {
                "type": "FUNDS_RELEASED",
                "released_amount": float(released_funds),
                "actions": [],
                "recommendations": [],
            }

            # 基于盈利情况决定策略
            if profit_ratio > 0.15:  # 高盈利（>15%）
                strategy["actions"].append(
                    {
                        "action": "PARTIAL_REINVEST",
                        "amount": float(released_funds * Decimal("0.6")),
                        "reasoning": "高盈利情况下部分资金再投资",
                    }
                )
                strategy["actions"].append(
                    {
                        "action": "PROFIT_TAKING",
                        "amount": float(released_funds * Decimal("0.4")),
                        "reasoning": "锁定部分利润",
                    }
                )
            elif profit_ratio > 0.05:  # 中等盈利（5-15%）
                strategy["actions"].append(
                    {
                        "action": "FULL_REINVEST",
                        "amount": float(released_funds),
                        "reasoning": "中等盈利情况下全部资金再投资",
                    }
                )
            else:  # 低盈利或亏损
                strategy["actions"].append(
                    {
                        "action": "CONSERVATIVE_REINVEST",
                        "amount": float(released_funds * Decimal("0.3")),
                        "reasoning": "低盈利情况下保守再投资",
                    }
                )

            return strategy

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Post-exercise strategy determination failed: {e}"
                )
            return {"type": "HOLD_FUNDS", "actions": []}

    def _calculate_resistance_optimized_strikes(
        self,
        current_price: Decimal,
        resistance_level: Decimal,
        default_levels: list[float],
    ) -> list[Decimal]:
        """基于阻力位优化Strike布局的智能混合算法（镜像抄底模式）"""
        try:
            # 1. 评估阻力位置信度和合理性
            resistance_ratio = (resistance_level - current_price) / current_price
            confidence = self._evaluate_resistance_confidence(
                current_price, resistance_level, resistance_ratio
            )

            # 2. 计算阻力位到当前价格的距离
            resistance_distance = abs(resistance_ratio)

            # 3. 基于置信度和距离选择优化策略
            if (
                confidence >= 0.7 and 0.05 <= resistance_distance <= 0.50
            ):  # 高置信度且合理距离
                return self._create_resistance_anchored_strikes(
                    current_price, resistance_level, default_levels, confidence
                )
            elif confidence >= 0.4:  # 中等置信度，部分优化
                return self._create_resistance_hybrid_strikes(
                    current_price, resistance_level, default_levels, confidence
                )
            else:  # 低置信度，使用默认间距
                return [
                    current_price * (1 + Decimal(str(ratio)))
                    for ratio in default_levels
                ]

        except Exception as e:
            if self.logger:
                import asyncio

                asyncio.create_task(
                    self.logger.warning(f"Resistance optimization failed: {e}")
                )
            # 出错时回退到默认方案
            return [
                current_price * (1 + Decimal(str(ratio))) for ratio in default_levels
            ]

    def _evaluate_resistance_confidence(
        self, current_price: Decimal, resistance_level: Decimal, resistance_ratio: float
    ) -> float:
        """评估阻力位的置信度（0-1范围）"""
        try:
            confidence = 0.5  # 基础置信度

            # 因素1：距离合理性 (阻力位应该在当前价格上方5%-50%范围内)
            abs_distance = abs(resistance_ratio)
            if 0.05 <= abs_distance <= 0.15:  # 5%-15%距离最佳
                confidence += 0.3
            elif 0.15 < abs_distance <= 0.30:  # 15%-30%距离较好
                confidence += 0.2
            elif 0.30 < abs_distance <= 0.50:  # 30%-50%距离一般
                confidence += 0.1
            else:
                confidence -= 0.2  # 距离过近或过远，降低置信度

            # 因素2：阻力位方向性 (阻力位应该在当前价格上方)
            if resistance_ratio > 0:  # 阻力位在上方
                confidence += 0.1
            else:
                confidence -= 0.3  # 阻力位在下方，严重异常

            # 因素3：价格水平合理性 (避免极端值)
            if resistance_level < current_price * Decimal("3.0"):  # 阻力不高于+200%
                confidence += 0.1
            else:
                confidence -= 0.2

            return max(0.0, min(1.0, confidence))

        except Exception:
            return 0.0  # 计算失败时返回最低置信度

    def _create_resistance_anchored_strikes(
        self,
        current_price: Decimal,
        resistance_level: Decimal,
        default_levels: list[float],
        confidence: float,
    ) -> list[Decimal]:
        """创建以阻力位为锚点的Strike布局"""
        try:
            strikes = []

            # 将最高的Strike锚定到阻力位下方5%（安全边界）
            anchor_offset = -0.05
            highest_strike = resistance_level * (1 + Decimal(str(anchor_offset)))

            # 根据阻力位调整其他Strike的分布
            relative_distances = [
                abs(level - default_levels[-1]) for level in default_levels[:-1]
            ]

            # 从阻力位向下按比例分布
            strikes.append(highest_strike)  # 最高Strike（锚定阻力位）

            current_level = float((highest_strike - current_price) / current_price)
            for _i, rel_dist in enumerate(relative_distances):
                # 按原有间距比例向下调整
                adjusted_distance = rel_dist * 0.8  # 略微压缩间距，增加安全性
                new_level = current_level - adjusted_distance
                new_strike = current_price * (1 + Decimal(str(new_level)))
                strikes.insert(0, new_strike)  # 插入到前面（低Strike）
                current_level = new_level

            return strikes

        except Exception:
            # 创建失败时返回默认Strike
            return [
                current_price * (1 + Decimal(str(ratio))) for ratio in default_levels
            ]

    def _create_resistance_hybrid_strikes(
        self,
        current_price: Decimal,
        resistance_level: Decimal,
        default_levels: list[float],
        confidence: float,
    ) -> list[Decimal]:
        """创建混合优化Strike布局（部分基于阻力位，部分基于固定间距）"""
        try:
            strikes = []
            resistance_ratio = float((resistance_level - current_price) / current_price)

            # 混合权重：基于置信度在阻力位优化和固定间距间插值
            resistance_weight = confidence  # 0.4-0.7范围
            default_weight = 1 - resistance_weight

            for i, default_level in enumerate(default_levels):
                # 对最高的两个Strike进行阻力位优化
                if i >= len(default_levels) - 2:
                    # 调整向阻力位方向
                    safety_margin = -0.03 if i == len(default_levels) - 1 else -0.02
                    target_level = resistance_ratio + safety_margin

                    # 加权平均
                    optimized_level = (
                        resistance_weight * target_level
                        + default_weight * default_level
                    )
                else:
                    # 低Strike保持相对固定间距
                    optimized_level = default_level

                strike_price = current_price * (1 + Decimal(str(optimized_level)))
                strikes.append(strike_price)

            return strikes

        except Exception:
            return [
                current_price * (1 + Decimal(str(ratio))) for ratio in default_levels
            ]

    async def _estimate_resistance_level(self, current_price: Decimal) -> Decimal:
        """估算技术阻力位 - 基于历史价格高点分析（类似accumulation_mode中的支撑位方法）"""
        try:
            if not self.cache_manager:
                return current_price * Decimal("1.2")  # 默认阻力在+20%

            # 获取历史价格数据
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            price_history = await mds.get_price_history("BTC")
            if not price_history:
                return current_price * Decimal("1.2")

            # 阻力位计算：使用30天和90天高点
            recent_30d_highs = [
                Decimal(str(price.get("high", current_price)))
                for price in price_history[-30:]
                if "high" in price
            ]
            recent_90d_highs = [
                Decimal(str(price.get("high", current_price)))
                for price in price_history[-90:]
                if "high" in price
            ]

            resistance = current_price * Decimal("1.2")  # 默认值

            if recent_30d_highs:
                resistance_30d = max(recent_30d_highs)
                if recent_90d_highs:
                    resistance_90d = max(recent_90d_highs)
                    # 加权平均：30天权重70%，90天权重30%
                    resistance = resistance_30d * Decimal(
                        "0.7"
                    ) + resistance_90d * Decimal("0.3")
                else:
                    resistance = resistance_30d

            # 确保阻力位不高于当前价格的130%
            return min(resistance, current_price * Decimal("1.30"))

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Resistance level estimation failed: {e}")
            return current_price * Decimal("1.2")

    async def _calculate_risk_reward_ratio(
        self, call: OptionDataWithGreeks, current_price: Decimal
    ) -> float:
        """计算风险回报比 - 基于技术分析的阻力位"""
        try:
            # 最大收益 = 权利金
            max_profit = call.mark_price

            # 估算最大风险 = (阻力位突破概率 × 无限损失风险) + 机会成本
            resistance_level = await self._estimate_resistance_level(current_price)

            # 基于阻力位距离评估突破概率
            resistance_distance = (resistance_level - current_price) / current_price
            breakthrough_probability = max(
                0.1, min(0.9, 1 - float(resistance_distance * 5))
            )

            # 机会成本：错过现货上涨的机会
            opportunity_cost = (
                call.strike - current_price
                if call.strike > current_price
                else Decimal("0")
            )

            # 综合风险评估
            expected_risk = float(opportunity_cost) * breakthrough_probability

            if expected_risk <= 0:
                return 0.0

            risk_reward_ratio = float(max_profit) / expected_risk
            return risk_reward_ratio

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Risk-reward calculation failed: {e}")
            return 0.0
