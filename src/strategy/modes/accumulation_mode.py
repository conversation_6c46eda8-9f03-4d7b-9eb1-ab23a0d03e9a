"""
抄底模式 (AccumulationMode) - 动态Strike计算版本

实现分批Sell Put策略逻辑，完全基于实时市场价格动态计算Strike：
1. 动态Strike价格计算（无硬编码）
2. 渐进式建仓策略
3. IV Rank条件判断和入场时机
4. 行权处理和现货转换逻辑
5. Delta管理和风险控制

核心策略：
- 下跌时分批卖出OTM Put期权（Delta 0.08-0.20）
- 若被行权：以预设低价获得BTC现货（实现抄底效果）
- 若不被行权：获得权利金收益（时间价值收益）
"""

import asyncio
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import Any

from src.analysis.common_types import (
    AccumulationMetrics,
    AccumulationOrder,
    ExerciseHandling,
    GreeksData,
    OptionDataWithGreeks,
    OptionType,
)
from src.analysis.pricing_validator import OptionPricingValidator
from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.config_manager import ConfigManager
from src.core.dependency_injector import inject
from src.core.event_bus import EventBus
from src.data.cache_manager import CacheManager
from src.services.market_data_service import MarketDataService
from src.strategy.utils import safe_parse_expiry


class AccumulationMode(BaseComponent):
    """
    抄底模式 - 动态Strike计算版本

    核心功能：
    1. 完全基于实时价格的动态Strike计算
    2. 渐进式建仓策略
    3. Strike价格选择和size计算
    4. IV Rank条件判断和入场时机
    5. 行权处理和现货转换逻辑
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("AccumulationMode")
        self.config_manager = config_manager

        # 核心组件依赖 - 将通过依赖注入自动设置
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None
        self.market_data_service: MarketDataService | None = None

        # Strike价格校验器
        self.pricing_validator: OptionPricingValidator | None = None

        # 抄底配置（优化版：扩大间距，提高触发偏移，优化风险分散）
        self.config = {
            "min_iv_rank": 0.7,  # 最小IV排名
            "delta_range": [0.08, 0.20],  # Delta目标范围（扩大以包含更多期权）
            "strike_range": [
                -0.37,
                -0.10,
            ],  # Strike相对价格范围（优化为10%-37%跌幅，扩大范围）
            "strike_levels": [
                -0.10,
                -0.17,
                -0.26,
                -0.37,
            ],  # 具体Strike水平：10%, 17%, 26%, 37%（扩大间距）
            "trigger_offset": 0.05,  # 触发价格偏移（比Strike高5%提前建仓，增加缓冲）
            "min_premium_rate": 0.02,  # 最小权利金收益率
            "max_position_size": Decimal("1000"),  # 最大仓位大小
            "batch_sizes": [
                0.30,
                0.30,
                0.25,
                0.15,
            ],  # 分批比例：30%→30%→25%→15%（减少深度OTM敞口）
            "min_expiry_days": 14,  # 最小到期天数
            "max_expiry_days": 45,  # 最大到期天数
            "min_volume": 100,  # 最小成交量
            "min_open_interest": 500,  # 最小持仓量
            "max_bid_ask_spread": 0.15,  # 最大买卖价差（适合加密货币期权）
            "exercise_handling": "auto_convert",  # 行权处理方式
        }

        # 抄底指标
        self.accumulation_metrics = AccumulationMetrics()

        # 活跃订单
        self.active_puts: dict[str, OptionDataWithGreeks] = {}
        self.pending_orders: list[AccumulationOrder] = []

        # 渐进式建仓管理
        self.current_batch = 0  # 当前批次索引
        self.max_batches = 4  # 最大批次数
        self.batch_history: list[dict[str, Any]] = []
        self.batch_completed: list[bool] = [
            False,
            False,
            False,
            False,
        ]  # 记录哪些批次已完成
        self.batch_trigger_prices: list[Decimal | None] = [
            None,
            None,
            None,
            None,
        ]  # 每个批次的触发价格

        # 动态价格计算（不再预计算绝对价格）
        self.last_trigger_price: Decimal | None = None
        self.last_order_time: datetime | None = None
        self.last_market_price: Decimal | None = None  # 缓存最后一次的市场价格

        # 审计任务
        self._audit_task: asyncio.Task | None = None

    def _convert_exercise_handling(self, value: str) -> ExerciseHandling:
        """转换行权处理方式字符串为枚举"""
        exercise_mapping = {
            "auto_convert": ExerciseHandling.AUTO_CONVERT,
            "auto_release": ExerciseHandling.AUTO_RELEASE,
            "manual": ExerciseHandling.MANUAL,
        }
        return exercise_mapping.get(value, ExerciseHandling.AUTO_CONVERT)

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            # 加载抄底模式配置
            accumulation_config = (
                self.config_manager.get("strategies.accumulation_mode") or {}
            )
            if accumulation_config:
                self.config.update(accumulation_config)
                # 同步最大批次数
                try:
                    sizes = self.config.get("batch_sizes")
                    if isinstance(sizes, list) and sizes:
                        self.max_batches = len(sizes)
                except Exception:
                    pass

            # 动态Strike计算不需要初始化价格水平
            # 所有Strike都将在订单生成时基于实时价格计算

            # 初始化Strike价格校验器
            try:
                self.pricing_validator = OptionPricingValidator(self.config)
                await self.pricing_validator.initialize()
                if self.logger:
                    await self.logger.info("Strike价格校验器初始化成功")
            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"Strike价格校验器初始化失败: {e}，将继续运行但缺少价格校验功能"
                    )
                self.pricing_validator = None

            if self.logger:
                await self.logger.info(
                    f"AccumulationMode initialized with config: {self.config}"
                )

            # 注册配置热更新回调
            try:
                if hasattr(self.config_manager, "register_change_callback"):
                    self.config_manager.register_change_callback(
                        self._on_config_changes
                    )
            except Exception:
                pass

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to initialize AccumulationMode: {e}")
            return False

    async def _on_config_changes(self, changes):
        """处理配置热更新：关注 strategies.accumulation_mode.* 变更并应用到运行时配置"""
        try:
            relevant = [
                c
                for c in changes
                if str(getattr(c, "config_path", "")).startswith(
                    "strategies.accumulation_mode."
                )
            ]
            if not relevant:
                return
            for change in relevant:
                key = change.config_path.split(".")[-1]
                new_val = change.new_value
                old_val = self.config.get(key)
                self.config[key] = new_val
                if key == "batch_sizes" and isinstance(new_val, list):
                    self.max_batches = len(new_val)
                if self.logger:
                    await self.logger.info(
                        f"ACC config hot-updated: {key} {old_val} -> {new_val}"
                    )
            from contextlib import suppress

            with suppress(Exception):
                self.metrics.custom_metrics["config_hot_updates"] = (
                    self.metrics.custom_metrics.get("config_hot_updates", 0)
                    + len(relevant)
                )
        except Exception as e:
            if self.logger:
                await self.logger.error(f"ACC hot-update handler error: {e}")

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            if self.logger:
                await self.logger.info("AccumulationMode started successfully")
            # 启动审计日志任务
            self._audit_task = asyncio.create_task(self._audit_loop())
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start AccumulationMode: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 清理待处理订单
            self.pending_orders.clear()

            # 停止审计任务
            if self._audit_task and not self._audit_task.done():
                self._audit_task.cancel()
                from contextlib import suppress

                with suppress(asyncio.CancelledError):
                    await self._audit_task

            if self.logger:
                await self.logger.info("AccumulationMode stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop AccumulationMode: {e}")
            return False

    async def _audit_loop(self):
        """周期性输出抄底模式的关键状态与阈值，便于审计和调参"""
        while self.is_running:
            try:
                # 当前价格与触发层级
                current_price = await self._get_current_btc_price()
                _trigger_levels = (
                    self._calculate_trigger_levels(current_price)
                    if current_price is not None
                    else []
                )
                next_batch = (
                    await self.get_next_batch_to_trigger(current_price)
                    if current_price is not None
                    else None
                )

                # 关键阈值与进度
                params = self.config
                completed = sum(1 for x in self.batch_completed if x)
                msg = (
                    f"ACC audit: min_iv_rank={float(params.get('min_iv_rank', 0.0)):.2f}, "
                    f"delta_range={params.get('delta_range')}, "
                    f"strike_levels={params.get('strike_levels')}, "
                    f"trigger_offset={float(params.get('trigger_offset', 0.0)):.3f}, "
                    f"batches={completed}/{self.max_batches}, "
                    f"next_batch={(next_batch + 1) if isinstance(next_batch, int) else None}, "
                    f"last_price={str(current_price) if current_price is not None else 'None'}"
                )
                if self.logger:
                    await self.logger.info(msg)
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Accumulation audit error: {e}")
            finally:
                # 审计间隔
                await asyncio.sleep(self.config.get("audit_interval", 600))

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            issues = []

            # 检查组件依赖
            if not self.cache_manager:
                issues.append("CacheManager not available")

            # 检查错误率
            if self.accumulation_metrics.error_count > 5:
                issues.append(
                    f"High error count: {self.accumulation_metrics.error_count}"
                )

            # 检查仓位风险
            if (
                self.accumulation_metrics.current_exposure
                > self.config["max_position_size"]
            ):
                issues.append("Position size exceeds limit")

            status = HealthStatus.HEALTHY if not issues else HealthStatus.DEGRADED

            return HealthCheckResult(
                status=status,
                message=f"AccumulationMode active with {len(self.active_puts)} puts",
                details={
                    "active_puts": len(self.active_puts),
                    "total_premium_collected": float(
                        self.accumulation_metrics.total_premium_collected
                    ),
                    "success_rate": self.accumulation_metrics.success_rate,
                    "current_exposure": float(
                        self.accumulation_metrics.current_exposure
                    ),
                    "issues": issues,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
            )

    @inject(EventBus, CacheManager)
    async def set_dependencies(self, event_bus: EventBus, cache_manager: CacheManager):
        """设置组件依赖 - 通过依赖注入自动调用"""
        self.event_bus = event_bus
        self.cache_manager = cache_manager

    def _calculate_target_strikes(
        self, current_price: Decimal, support_level: Decimal | None = None
    ) -> list[Decimal]:
        """基于当前价格和支撑位智能计算目标Strike价格（混合优化）"""
        try:
            # 获取默认Strike配置
            default_levels = self.config.get(
                "strike_levels", [-0.10, -0.17, -0.26, -0.37]
            )
            target_strikes = []

            # 如果有可靠的支撑位信息，使用智能混合方案
            if support_level and support_level > 0:
                optimized_strikes = self._calculate_support_optimized_strikes(
                    current_price, support_level, default_levels
                )
                target_strikes = optimized_strikes
            else:
                # 严格模式：缺少支撑位不生成目标
                from src.monitoring.strict_metrics import inc_skip

                inc_skip("AccumulationMode", "no_support_level")
                target_strikes = []

            return target_strikes

        except Exception as e:
            if self.logger:
                # 使用异步日志记录
                import asyncio

                asyncio.create_task(
                    self.logger.error(f"Failed to calculate target strikes: {e}")
                )
            return []

    def _calculate_support_optimized_strikes(
        self,
        current_price: Decimal,
        support_level: Decimal,
        default_levels: list[float],
    ) -> list[Decimal]:
        """基于支撑位优化Strike布局的智能混合算法（含动态风险权衡）"""
        try:
            # 1. 评估支撑位置信度和合理性
            support_ratio = (support_level - current_price) / current_price
            confidence = self._evaluate_support_confidence(
                current_price, support_level, support_ratio
            )

            # 2. 计算支撑位到当前价格的距离
            support_distance = abs(support_ratio)

            # 3. 生成候选优化Strike
            optimized_strikes = []
            if (
                confidence >= 0.7 and 0.05 <= support_distance <= 0.50
            ):  # 高置信度且合理距离
                optimized_strikes = self._create_support_anchored_strikes(
                    current_price, support_level, default_levels, confidence
                )
            elif confidence >= 0.4:  # 中等置信度，部分优化
                optimized_strikes = self._create_hybrid_strikes(
                    current_price, support_level, default_levels, confidence
                )

            # 4. 如果有候选优化Strike，进行风险收益权衡评估
            if optimized_strikes:
                default_strikes = [
                    current_price * (1 + Decimal(str(ratio)))
                    for ratio in default_levels
                ]

                risk_evaluation = self._evaluate_risk_reward_tradeoff(
                    optimized_strikes, default_strikes, current_price
                )

                # 5. 基于风险评估决定是否使用优化
                if risk_evaluation["should_optimize"]:
                    if self.logger:
                        import asyncio

                        asyncio.create_task(
                            self.logger.info(
                                f"启用支撑位优化: 权利金提升{risk_evaluation['premium_improvement']:.1%}, "
                                f"行权风险增加{risk_evaluation['exercise_risk_increase']:.1%}, "
                                f"风险调整收益{risk_evaluation['risk_adjusted_return']:.1%}"
                            )
                        )
                    return optimized_strikes
                else:
                    if self.logger:
                        import asyncio

                        asyncio.create_task(
                            self.logger.info(
                                f"跳过支撑位优化: 权利金提升{risk_evaluation['premium_improvement']:.1%}, "
                                f"行权风险增加{risk_evaluation['exercise_risk_increase']:.1%}, "
                                f"风险调整收益不足: {risk_evaluation['risk_adjusted_return']:.1%}"
                            )
                        )
                    return []

            # 6. 无有效优化或低置信度，使用默认间距
            return []

        except Exception as e:
            if self.logger:
                import asyncio

                asyncio.create_task(
                    self.logger.warning(f"Support optimization failed: {e}")
                )
            # 严格模式：计算失败即空
            return []

    def _evaluate_risk_reward_tradeoff(
        self,
        optimized_strikes: list[Decimal],
        default_strikes: list[Decimal],
        current_price: Decimal,
    ) -> dict:
        """评估风险收益权衡，决定是否启用优化"""
        try:
            # 1. 计算预期权利金收益提升
            premium_improvement = self._estimate_premium_improvement(
                optimized_strikes, default_strikes, current_price
            )

            # 2. 计算行权概率增加
            exercise_risk_increase = self._estimate_exercise_risk_change(
                optimized_strikes, default_strikes, current_price
            )

            # 3. 风险调整收益率
            risk_adjusted_return = premium_improvement / (1 + exercise_risk_increase)

            # 4. 决策规则
            should_optimize = (
                premium_improvement >= 0.5  # 权利金提升≥50%
                and exercise_risk_increase <= 0.3  # 行权风险增加≤30%
                and risk_adjusted_return >= 0.3  # 风险调整收益≥30%
            )

            return {
                "should_optimize": should_optimize,
                "premium_improvement": premium_improvement,
                "exercise_risk_increase": exercise_risk_increase,
                "risk_adjusted_return": risk_adjusted_return,
            }

        except Exception:
            # 评估失败时保守选择不优化
            return {
                "should_optimize": False,
                "premium_improvement": 0.0,
                "exercise_risk_increase": 1.0,
                "risk_adjusted_return": 0.0,
            }

    def _estimate_premium_improvement(
        self,
        optimized_strikes: list[Decimal],
        default_strikes: list[Decimal],
        current_price: Decimal,
    ) -> float:
        """估算权利金收益提升比例"""
        try:
            # 简化计算：基于Strike距离估算权利金差异
            def estimate_premium_rate(strike: Decimal) -> float:
                """基于距离估算权利金收益率"""
                distance = abs((strike - current_price) / current_price)
                # 经验公式：距离越近，权利金越高
                if distance <= 0.05:  # 0-5%距离
                    return 0.15  # 15%权利金率
                elif distance <= 0.10:  # 5-10%距离
                    return 0.08  # 8%权利金率
                elif distance <= 0.20:  # 10-20%距离
                    return 0.04  # 4%权利金率
                else:  # >20%距离
                    return 0.02  # 2%权利金率

            # 计算加权平均权利金收益率
            optimized_avg_premium = sum(
                estimate_premium_rate(strike) for strike in optimized_strikes
            ) / len(optimized_strikes)
            default_avg_premium = sum(
                estimate_premium_rate(strike) for strike in default_strikes
            ) / len(default_strikes)

            # 计算提升比例
            if default_avg_premium > 0:
                return (
                    optimized_avg_premium - default_avg_premium
                ) / default_avg_premium
            else:
                return 0.0

        except Exception:
            return 0.0

    def _estimate_exercise_risk_change(
        self,
        optimized_strikes: list[Decimal],
        default_strikes: list[Decimal],
        current_price: Decimal,
    ) -> float:
        """估算行权概率变化"""
        try:

            def estimate_exercise_probability(strike: Decimal) -> float:
                """基于距离估算行权概率"""
                distance = abs((strike - current_price) / current_price)
                # 经验公式：距离越近，行权概率越高
                if distance <= 0.05:  # 0-5%距离
                    return 0.60  # 60%行权概率
                elif distance <= 0.10:  # 5-10%距离
                    return 0.35  # 35%行权概率
                elif distance <= 0.20:  # 10-20%距离
                    return 0.15  # 15%行权概率
                else:  # >20%距离
                    return 0.05  # 5%行权概率

            # 计算加权平均行权概率
            optimized_avg_exercise = sum(
                estimate_exercise_probability(strike) for strike in optimized_strikes
            ) / len(optimized_strikes)
            default_avg_exercise = sum(
                estimate_exercise_probability(strike) for strike in default_strikes
            ) / len(default_strikes)

            # 计算风险增加比例
            if default_avg_exercise > 0:
                return (
                    optimized_avg_exercise - default_avg_exercise
                ) / default_avg_exercise
            else:
                return 0.0

        except Exception:
            return 1.0  # 出错时假设风险大幅增加

    def _evaluate_support_confidence(
        self, current_price: Decimal, support_level: Decimal, support_ratio: float
    ) -> float:
        """评估支撑位的置信度（0-1范围）"""
        try:
            confidence = 0.5  # 基础置信度

            # 因素1：距离合理性 (支撑位应该在当前价格下方5%-50%范围内)
            abs_distance = abs(support_ratio)
            if 0.05 <= abs_distance <= 0.15:  # 5%-15%距离最佳
                confidence += 0.3
            elif 0.15 < abs_distance <= 0.30:  # 15%-30%距离较好
                confidence += 0.2
            elif 0.30 < abs_distance <= 0.50:  # 30%-50%距离一般
                confidence += 0.1
            else:
                confidence -= 0.2  # 距离过近或过远，降低置信度

            # 因素2：支撑位方向性 (支撑位应该在当前价格下方)
            if support_ratio < 0:  # 支撑位在下方
                confidence += 0.1
            else:
                confidence -= 0.3  # 支撑位在上方，严重异常

            # 因素3：价格水平合理性 (避免极端值)
            if support_level > current_price * Decimal("0.3"):  # 支撑不低于-70%
                confidence += 0.1
            else:
                confidence -= 0.2

            return max(0.0, min(1.0, confidence))

        except Exception:
            return 0.0  # 计算失败时返回最低置信度

    def _create_support_anchored_strikes(
        self,
        current_price: Decimal,
        support_level: Decimal,
        default_levels: list[float],
        confidence: float,
    ) -> list[Decimal]:
        """创建以支撑位为锚点的Strike布局"""
        try:
            strikes = []
            support_ratio = float((support_level - current_price) / current_price)

            # 基于支撑位比例调整锚定策略
            # 支撑位越远，安全边界越大，越近则边界越小
            base_anchor_offset = 0.05
            distance_factor = min(abs(support_ratio) * 2, 0.03)  # 距离调整因子，最大3%
            anchor_offset = base_anchor_offset + distance_factor

            # 将最深的Strike锚定到支撑位上方（动态安全边界）
            deepest_strike = support_level * (1 + Decimal(str(anchor_offset)))

            # 计算默认间距
            default_intervals = []
            for i in range(len(default_levels) - 1):
                interval = default_levels[i] - default_levels[i + 1]
                default_intervals.append(interval)

            # 从最深Strike开始，向上按间距分布
            strikes = [deepest_strike]
            current_level = float((deepest_strike - current_price) / current_price)

            # 向上构建其他Strike，保持间距比例
            for i in range(len(default_levels) - 1):
                # 使用压缩的间距（80%原间距）
                interval = default_intervals[-(i + 1)] * 0.8
                current_level += interval  # 向上移动（减少负值）
                new_strike = current_price * (1 + Decimal(str(current_level)))
                strikes.insert(0, new_strike)  # 插入到前面（更浅的Strike）

            return strikes

        except Exception:
            # 创建失败时返回默认Strike
            return [
                current_price * (1 + Decimal(str(ratio))) for ratio in default_levels
            ]

    def _create_hybrid_strikes(
        self,
        current_price: Decimal,
        support_level: Decimal,
        default_levels: list[float],
        confidence: float,
    ) -> list[Decimal]:
        """创建混合优化Strike布局（部分基于支撑位，部分基于固定间距）"""
        try:
            strikes = []
            support_ratio = float((support_level - current_price) / current_price)

            # 混合权重：基于置信度在支撑位优化和固定间距间插值
            support_weight = confidence  # 0.4-0.7范围
            default_weight = 1 - support_weight

            for i, default_level in enumerate(default_levels):
                # 对最深的两个Strike进行支撑位优化
                if i >= len(default_levels) - 2:
                    # 调整向支撑位方向
                    safety_margin = 0.03 if i == len(default_levels) - 1 else 0.02
                    target_level = support_ratio + safety_margin

                    # 加权平均
                    optimized_level = (
                        support_weight * target_level + default_weight * default_level
                    )
                else:
                    # 浅Strike保持相对固定间距
                    optimized_level = default_level

                strike_price = current_price * (1 + Decimal(str(optimized_level)))
                strikes.append(strike_price)

            # 应用绝对安全边界保护
            strikes = self._apply_safety_boundaries(strikes, current_price)
            return strikes

        except Exception:
            return [
                current_price * (1 + Decimal(str(ratio))) for ratio in default_levels
            ]

    async def _validate_strike_price(
        self, option: OptionDataWithGreeks, current_price: Decimal
    ) -> dict[str, Any]:
        """
        校验Strike价格的合理性

        Args:
            option: 期权数据
            current_price: 当前BTC价格

        Returns:
            校验结果字典：包含是否有效、偏差百分比、校验详情等
        """
        try:
            validation_result = {
                "is_valid": True,
                "validation_score": 100.0,
                "price_deviation": 0.0,
                "warnings": [],
                "errors": [],
            }

            # 1. 基础Strike范围校验
            strike_price = Decimal(str(option.strike))
            strike_ratio = (strike_price - current_price) / current_price

            # 检查Strike是否在合理范围内
            expected_range = self.config.get("strike_range", [-0.25, -0.08])
            if strike_ratio < expected_range[0] or strike_ratio > expected_range[1]:
                validation_result["errors"].append(
                    f"Strike价格偏离预期范围: {float(strike_ratio) * 100:.1f}% "
                    f"(预期: {expected_range[0] * 100:.1f}% - {expected_range[1] * 100:.1f}%)"
                )
                validation_result["is_valid"] = False
                validation_result["validation_score"] -= 30

            # 2. Delta范围校验
            expected_delta_range = self.config.get("delta_range", [0.08, 0.20])
            if hasattr(option, "delta") and option.delta:
                delta = abs(option.delta)  # Put的Delta是负数，取绝对值
                if delta < expected_delta_range[0] or delta > expected_delta_range[1]:
                    validation_result["warnings"].append(
                        f"Delta偏离目标范围: {delta:.3f} "
                        f"(目标: {expected_delta_range[0]:.3f} - {expected_delta_range[1]:.3f})"
                    )
                    validation_result["validation_score"] -= 15

            # 3. 期权定价校验 (如果价格校验器可用)
            if (
                self.pricing_validator
                and hasattr(option, "mark_price")
                and option.mark_price
            ):
                try:
                    # 使用期权定价校验器验证价格合理性
                    price_validation = (
                        await self.pricing_validator.validate_option_price(
                            option.instrument_name, float(option.mark_price)
                        )
                    )

                    if price_validation.get("valid", True):
                        validation_result["pricing_validation"] = price_validation

                        # 根据价格偏差调整评分
                        deviation = price_validation.get("deviation_percentage", 0)
                        validation_result["price_deviation"] = deviation

                        if deviation > 20:  # 偏差超过20%
                            validation_result["errors"].append(
                                f"期权定价偏差过大: {deviation:.1f}%"
                            )
                            validation_result["is_valid"] = False
                            validation_result["validation_score"] -= 25
                        elif deviation > 10:  # 偏差超过10%
                            validation_result["warnings"].append(
                                f"期权定价偏差较大: {deviation:.1f}%"
                            )
                            validation_result["validation_score"] -= 10
                    else:
                        validation_result["errors"].append(
                            f"期权定价校验失败: {price_validation.get('reason', 'Unknown error')}"
                        )
                        validation_result["is_valid"] = False
                        validation_result["validation_score"] -= 40

                except Exception as e:
                    validation_result["warnings"].append(f"定价校验异常: {str(e)}")
                    validation_result["validation_score"] -= 5

            # 4. 流动性校验
            if hasattr(option, "volume") and hasattr(option, "open_interest"):
                min_volume = self.config.get("min_volume", 100)
                min_oi = self.config.get("min_open_interest", 500)

                if option.volume and option.volume < min_volume:
                    validation_result["warnings"].append(
                        f"成交量偏低: {option.volume} < {min_volume}"
                    )
                    validation_result["validation_score"] -= 10

                if option.open_interest and option.open_interest < min_oi:
                    validation_result["warnings"].append(
                        f"持仓量偏低: {option.open_interest} < {min_oi}"
                    )
                    validation_result["validation_score"] -= 10

            # 5. 买卖价差校验
            if (
                hasattr(option, "bid_price")
                and hasattr(option, "ask_price")
                and option.bid_price
                and option.ask_price
                and option.mark_price
            ):
                bid_ask_spread = (
                    option.ask_price - option.bid_price
                ) / option.mark_price
                max_spread = self.config.get("max_bid_ask_spread", 0.15)

                if bid_ask_spread > max_spread:
                    validation_result["warnings"].append(
                        f"买卖价差过大: {bid_ask_spread:.1%} > {max_spread:.1%}"
                    )
                    validation_result["validation_score"] -= 15

            # 确保评分不低于0
            validation_result["validation_score"] = max(
                validation_result["validation_score"], 0
            )

            return validation_result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Strike价格校验失败: {e}")
            return {
                "is_valid": False,
                "validation_score": 0.0,
                "price_deviation": 0.0,
                "warnings": [],
                "errors": [f"校验异常: {str(e)}"],
            }

    def _calculate_trigger_levels(self, current_price: Decimal) -> list[Decimal]:
        """基于当前价格动态计算触发水平（比Strike高5%提前触发，增加缓冲）"""
        try:
            strike_levels = self.config.get(
                "strike_levels", [-0.10, -0.17, -0.26, -0.37]
            )
            trigger_offset = self.config.get("trigger_offset", 0.05)
            trigger_levels = []

            for strike_ratio in strike_levels:
                # 触发价格比Strike高5%（提前建仓，增加缓冲空间）
                trigger_price = current_price * (
                    1 + Decimal(str(strike_ratio + trigger_offset))
                )
                trigger_levels.append(trigger_price)

            return trigger_levels

        except Exception as e:
            if self.logger:
                import asyncio

                asyncio.create_task(
                    self.logger.error(f"Failed to calculate trigger levels: {e}")
                )
            return []

    async def _get_current_btc_price(self) -> Decimal | None:
        """获取当前BTC价格 - 使用统一的MarketDataService"""
        try:
            if self.market_data_service:
                price = await self.market_data_service.get_btc_price()
                if price:
                    self.last_market_price = price
                return price or self.last_market_price

            if not self.cache_manager:
                return self.last_market_price

            return self.last_market_price

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get BTC price: {e}")
            return self.last_market_price

    async def should_accumulate(
        self, current_price: Decimal, market_analysis: dict[str, Any]
    ) -> bool:
        """判断是否应该执行抄底"""
        try:
            # 检查IV排名
            iv_rank = market_analysis.get("iv_rank", 0.0)
            if iv_rank < self.config["min_iv_rank"]:
                return False

            # 检查价格下跌
            price_momentum = market_analysis.get("price_momentum", 0.0)
            if price_momentum >= 0:
                return False

            # 检查市场状态
            market_state = market_analysis.get("market_state", "sideways")
            if market_state not in ["trending_down", "high_volatility"]:
                return False

            # 检查冷却时间
            if self.last_order_time and datetime.now(
                UTC
            ) - self.last_order_time < timedelta(hours=4):
                return False

            # 检查仓位限制
            return (
                not self.accumulation_metrics.current_exposure
                >= self.config["max_position_size"]
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Accumulation check failed: {e}")
            return False

    async def get_next_batch_to_trigger(self, current_price: Decimal) -> int | None:
        """获取下一个应该触发的批次（基于实时价格动态计算）"""
        try:
            # 基于当前价格动态计算触发水平
            trigger_levels = self._calculate_trigger_levels(current_price)
            if not trigger_levels:
                return None

            # 检查哪个批次应该触发（从第一批开始）
            for i in range(min(self.max_batches, len(trigger_levels))):
                if not self.batch_completed[i]:
                    trigger_price = trigger_levels[i]
                    if current_price <= trigger_price:
                        return i
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to check trigger batch: {e}")
            return None

    async def generate_single_batch_order(
        self, batch_index: int, current_price: Decimal, market_analysis: dict[str, Any]
    ) -> AccumulationOrder | None:
        """生成单个批次的抄底订单（基于实时价格动态计算Strike）"""
        try:
            if batch_index >= self.max_batches or self.batch_completed[batch_index]:
                return None

            # 获取可用期权
            available_puts = await self._get_available_puts(current_price)
            if not available_puts:
                return None

            # 选择最佳期权（基于动态计算的目标Strike）
            best_puts = await self._select_best_puts(available_puts, current_price)
            if not best_puts or batch_index >= len(best_puts):
                return None

            # 选择对应批次的期权
            put_option = best_puts[batch_index]
            batch_size = self.config["batch_sizes"][batch_index]

            # 创建订单
            order = await self._create_accumulation_order(
                put_option, batch_size, batch_index + 1
            )

            if order:
                # 标记批次为已触发
                self.batch_trigger_prices[batch_index] = current_price

                if self.logger:
                    await self.logger.info(
                        f"触发抄底批次{batch_index + 1}: 价格={current_price}, "
                        f"Strike={put_option.strike}, Delta={put_option.delta:.3f}, "
                        f"权利金={put_option.mark_price}"
                    )

            return order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Single batch order generation failed: {e}")
            return None

    async def get_progress_status(
        self, current_price: Decimal | None = None
    ) -> dict[str, Any]:
        """获取建仓进度状态（动态计算Strike水平）"""
        completed_batches = sum(self.batch_completed)
        status = {
            "completed_batches": completed_batches,
            "total_batches": self.max_batches,
            "progress_ratio": completed_batches / self.max_batches,
            "batch_status": self.batch_completed,
            "trigger_prices": [
                float(p) if p else None for p in self.batch_trigger_prices
            ],
        }

        # 如果提供了当前价格，计算动态Strike和触发水平
        if current_price:
            # 尝试获取支撑位信息进行智能优化
            support_level = None
            try:
                from src.services.market_data_service import MarketDataService

                mds = await self.dependency_injector.resolve(MarketDataService)
                market_analysis = await mds.get_cached("latest_market_analysis")
                if market_analysis and isinstance(market_analysis, dict):
                    support_level = market_analysis.get("support_level")
                    if support_level:
                        support_level = Decimal(str(support_level))
            except Exception:
                support_level = None  # 获取失败时使用默认方案

            target_strikes = self._calculate_target_strikes(
                current_price, support_level
            )
            trigger_levels = self._calculate_trigger_levels(current_price)

            status.update(
                {
                    "current_market_price": float(current_price),
                    "target_strikes": [float(s) for s in target_strikes],
                    "trigger_levels": [float(t) for t in trigger_levels],
                    "strike_levels_pct": self.config.get(
                        "strike_levels", [-0.10, -0.17, -0.26, -0.37]
                    ),
                    "support_level": float(support_level) if support_level else None,
                    "strike_optimization": "support_based"
                    if support_level
                    else "fixed_interval",
                }
            )

            # 添加校验统计信息（如果有校验器）
            if self.pricing_validator:
                try:
                    validation_stats = await self._get_validation_statistics()
                    status.update({"validation_statistics": validation_stats})
                except Exception as e:
                    if self.logger:
                        await self.logger.debug(
                            f"Failed to get validation statistics: {e}"
                        )

        return status

    async def _get_validation_statistics(self) -> dict[str, Any]:
        """获取Strike价格校验统计信息"""
        try:
            stats = {
                "total_validations": 0,
                "passed_validations": 0,
                "failed_validations": 0,
                "avg_validation_score": 0.0,
                "common_warnings": [],
                "validation_rate": 0.0,
            }

            if not self.pricing_validator:
                return stats

            # 获取所有活跃期权的校验统计
            validations = []
            for put in self.active_puts.values():
                if hasattr(put, "_validation_result"):
                    validation = put._validation_result
                    validations.append(validation)

            if validations:
                stats["total_validations"] = len(validations)
                stats["passed_validations"] = sum(
                    1 for v in validations if v["is_valid"]
                )
                stats["failed_validations"] = (
                    stats["total_validations"] - stats["passed_validations"]
                )
                stats["avg_validation_score"] = sum(
                    v["validation_score"] for v in validations
                ) / len(validations)
                stats["validation_rate"] = (
                    stats["passed_validations"] / stats["total_validations"] * 100
                )

                # 统计常见警告
                warning_counts = {}
                for validation in validations:
                    for warning in validation.get("warnings", []):
                        warning_counts[warning] = warning_counts.get(warning, 0) + 1

                stats["common_warnings"] = [
                    {"warning": warning, "count": count}
                    for warning, count in sorted(
                        warning_counts.items(), key=lambda x: x[1], reverse=True
                    )[:5]
                ]

            return stats

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get validation statistics: {e}")
            return {
                "total_validations": 0,
                "passed_validations": 0,
                "failed_validations": 0,
                "avg_validation_score": 0.0,
                "common_warnings": [],
                "validation_rate": 0.0,
                "error": str(e),
            }

    async def mark_batch_completed(self, batch_index: int) -> bool:
        """标记批次为已完成"""
        try:
            if 0 <= batch_index < self.max_batches:
                self.batch_completed[batch_index] = True
                if self.logger:
                    await self.logger.info(f"抄底批次{batch_index + 1}已完成")
                return True
            return False
        except Exception:
            return False

    # 继续包含其他必要方法的实现...
    async def _get_available_puts(
        self, current_price: Decimal
    ) -> list[OptionDataWithGreeks]:
        """获取可用的Put期权（基于动态Strike范围）"""
        try:
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            option_block = await mds.get_option_chain("BTC")
            option_chain = (
                option_block
                if isinstance(option_block, list)
                else option_block.get("options")
                if option_block
                else None
            )
            if not option_chain:
                return []

            available_puts = []
            min_expiry = datetime.now(UTC) + timedelta(
                days=self.config["min_expiry_days"]
            )
            max_expiry = datetime.now(UTC) + timedelta(
                days=self.config["max_expiry_days"]
            )

            # 获取目标strike_levels用于优先筛选
            strike_levels = self.config.get(
                "strike_levels", [-0.08, -0.12, -0.18, -0.25]
            )

            for option_data in option_chain:
                try:
                    # 过滤Put期权
                    if option_data.get("option_type") != "put":
                        continue

                    # 检查到期时间
                    expiry = safe_parse_expiry(option_data.get("expiry", ""))
                    if expiry is None:
                        continue
                    if expiry < min_expiry or expiry > max_expiry:
                        continue

                    # 检查Strike范围 (Put期权应该在当前价格下方)
                    strike = Decimal(str(option_data.get("strike", 0)))
                    if strike >= current_price:  # Put期权Strike必须低于当前价格
                        continue

                    strike_ratio = (
                        strike - current_price
                    ) / current_price  # 这会是负数

                    # 使用动态的strike_range检查
                    strike_range = self.config.get("strike_range", [-0.25, -0.08])

                    # 优先检查是否接近目标strike_levels，容差5%
                    is_near_target = any(
                        abs(strike_ratio - target_level) <= 0.05
                        for target_level in strike_levels
                    )

                    # 如果不接近目标水平，检查是否在允许范围内
                    if not is_near_target and not (
                        strike_range[0] <= strike_ratio <= strike_range[1]
                    ):
                        continue

                    # 检查流动性
                    volume = option_data.get("volume", 0)
                    open_interest = option_data.get("open_interest", 0)
                    if (
                        volume < self.config["min_volume"]
                        or open_interest < self.config["min_open_interest"]
                    ):
                        continue

                    # 检查买卖价差
                    bid_price = Decimal(str(option_data.get("bid_price", 0)))
                    ask_price = Decimal(str(option_data.get("ask_price", 0)))
                    if ask_price > 0:
                        spread = (ask_price - bid_price) / ask_price
                        if spread > self.config["max_bid_ask_spread"]:
                            continue

                    # 创建Put期权对象
                    put_option = OptionDataWithGreeks(
                        symbol=option_data.get("symbol", ""),
                        strike=strike,
                        expiry=expiry,
                        option_type=OptionType.PUT,
                        mark_price=bid_price,  # 卖出价格使用bid
                        bid=bid_price,
                        ask=ask_price,
                        volume=volume,
                        open_interest=open_interest,
                        greeks=GreeksData(
                            delta=option_data.get("delta", 0.0),
                            gamma=option_data.get("gamma", 0.0),
                            theta=option_data.get("theta", 0.0),
                            vega=option_data.get("vega", 0.0),
                            implied_vol=option_data.get("implied_vol", 0.0),
                        ),
                    )

                    available_puts.append(put_option)

                except Exception as e:
                    if self.logger:
                        await self.logger.warning(f"Failed to process option data: {e}")
                    continue

            return available_puts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get available puts: {e}")
            return []

    async def _select_best_puts(
        self, available_puts: list[OptionDataWithGreeks], current_price: Decimal
    ) -> list[OptionDataWithGreeks]:
        """选择最佳Put期权（基于动态目标Strike）"""
        try:
            if not available_puts:
                return []

            # 按Delta范围过滤（扩大范围以包含更多期权）
            delta_range = self.config["delta_range"]
            filtered_puts = [
                put
                for put in available_puts
                if delta_range[0] <= abs(put.delta) <= delta_range[1]
            ]

            # 如果按新Delta范围过滤后没有期权，放宽条件
            if not filtered_puts and available_puts:
                # 放宽到更大的Delta范围
                broader_range = [0.05, 0.25]
                filtered_puts = [
                    put
                    for put in available_puts
                    if broader_range[0] <= abs(put.delta) <= broader_range[1]
                ]

            if not filtered_puts:
                return []

            # 计算每个期权的评分并进行Strike价格校验
            scored_puts = []
            for put in filtered_puts:
                # 执行Strike价格校验
                validation_result = await self._validate_strike_price(
                    put, current_price
                )

                # 如果校验不通过且存在严重错误，跳过此期权
                if not validation_result["is_valid"] and validation_result["errors"]:
                    if self.logger:
                        await self.logger.warning(
                            f"期权 {getattr(put, 'instrument_name', 'Unknown')} Strike校验失败: "
                            f"{validation_result['errors']}"
                        )
                    continue

                # 计算基础评分
                base_score = await self._calculate_put_score(put, current_price)

                # 将校验评分纳入总评分（校验评分占30%权重）
                validation_score_factor = (
                    validation_result["validation_score"] / 100.0
                )  # 转换为0-1范围
                final_score = base_score * 0.7 + (
                    base_score * validation_score_factor * 0.3
                )

                # 记录校验结果到期权对象（供后续使用）
                if hasattr(put, "__dict__"):
                    put._validation_result = validation_result

                scored_puts.append((put, final_score))

                # 记录校验详情
                if self.logger and (
                    validation_result["warnings"] or not validation_result["is_valid"]
                ):
                    await self.logger.info(
                        f"期权 {getattr(put, 'instrument_name', 'Unknown')} "
                        f"Strike={put.strike}, 校验评分={validation_result['validation_score']:.1f}, "
                        f"最终评分={final_score:.2f}, "
                        f"警告={validation_result['warnings']}"
                    )

            # 按评分排序
            scored_puts.sort(key=lambda x: x[1], reverse=True)

            # 选择前几个最佳期权，优先选择接近目标strike_levels的期权
            batch_count = len(self.config["batch_sizes"])
            strike_levels = self.config.get(
                "strike_levels", [-0.08, -0.12, -0.18, -0.25]
            )

            best_puts = []
            used_strikes = set()

            # 优先为每个strike_level对应选择最佳期权
            for _i, target_strike in enumerate(strike_levels[:batch_count]):
                best_candidate = None
                best_score = -1

                for put, score in scored_puts:
                    if put.strike in used_strikes:
                        continue

                    # 计算与目标strike_level的距离（使用传入的实时价格）
                    actual_ratio = (put.strike - current_price) / current_price
                    distance_to_target = abs(actual_ratio - target_strike)

                    # 综合评分：原始评分 + 距离加分
                    combined_score = score - distance_to_target * 2  # 距离越近加分越高

                    if combined_score > best_score:
                        best_score = combined_score
                        best_candidate = put

                if best_candidate:
                    best_puts.append(best_candidate)
                    used_strikes.add(best_candidate.strike)

            # 如果没有找到足够的期权，使用原有逻辑填充
            if len(best_puts) < batch_count:
                for put, _score in scored_puts:
                    if put.strike not in used_strikes and len(best_puts) < batch_count:
                        best_puts.append(put)
                        used_strikes.add(put.strike)

            return best_puts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Put selection failed: {e}")
            return []

    # 其他方法保持不变...
    async def _calculate_put_score(
        self, put: OptionDataWithGreeks, current_price: Decimal
    ) -> float:
        """计算Put期权评分（0-1）

        维度与权重：
        - Delta贴合度（0.35）：越接近目标delta越好
        - 权利金收益率（0.25）：mark/当前价，相对min_premium_rate打分
        - 流动性（0.20）：volume/open_interest 归一化
        - 期限与价差（0.20）：到期窗口贴合 + 买卖价差越小越好
        """
        try:
            # 保护与取值
            mark = put.mark_price or put.bid or Decimal("0")
            bid = put.bid or Decimal("0")
            ask = put.ask or Decimal("0")
            oi = int(getattr(put, "open_interest", 0) or 0)
            vol = int(getattr(put, "volume", 0) or 0)
            delta = abs(float(getattr(put, "delta", 0.0) or 0.0))

            # 1) Delta贴合（目标为区间中点）
            dmin, dmax = self.config.get("delta_range", [0.08, 0.20])
            dmid = (float(dmin) + float(dmax)) / 2.0
            # 归一化距离（区间半宽）
            half = max(1e-6, (float(dmax) - float(dmin)) / 2.0)
            delta_score = max(0.0, 1.0 - abs(delta - dmid) / half)
            delta_score = min(1.0, delta_score)

            # 2) 权利金收益率（相对当前价）
            min_rate = float(self.config.get("min_premium_rate", 0.02))
            premium_rate = float(mark / current_price) if current_price > 0 else 0.0
            premium_score = min(1.0, premium_rate / max(1e-6, min_rate))

            # 3) 流动性（简单归一化）
            v_min = float(self.config.get("min_volume", 100))
            oi_min = float(self.config.get("min_open_interest", 500))
            vol_score = min(1.0, (vol / max(1.0, v_min)))
            oi_score = min(1.0, (oi / max(1.0, oi_min)))
            liq_score = (vol_score + oi_score) / 2.0

            # 4) 期限与价差
            # 到期贴合：位于[min_days,max_days]中点附近更好
            days = 0.0
            try:
                if getattr(put, "expiry", None):
                    days = (
                        (put.expiry.date() - datetime.now(UTC).date()).days
                    )
            except Exception:
                days = 0.0
            min_days = float(self.config.get("min_expiry_days", 14))
            max_days = float(self.config.get("max_expiry_days", 45))
            mid_days = (min_days + max_days) / 2.0
            half_days = max(1e-6, (max_days - min_days) / 2.0)
            days_score = 0.0
            if min_days <= days <= max_days:
                days_score = max(0.0, 1.0 - abs(days - mid_days) / half_days)

            # 价差：越小越好
            spread_ratio = 1.0
            if ask > 0:
                spread_ratio = float((ask - bid) / ask)
            max_spread = float(self.config.get("max_bid_ask_spread", 0.15))
            spread_score = max(0.0, 1.0 - (spread_ratio / max(1e-6, max_spread)))

            term_spread_score = (days_score + spread_score) / 2.0

            # 汇总权重
            score = (
                0.35 * delta_score
                + 0.25 * premium_score
                + 0.20 * liq_score
                + 0.20 * term_spread_score
            )
            return float(max(0.0, min(1.0, score)))

        except Exception:
            return 0.0

    async def _create_accumulation_order(
        self, put: OptionDataWithGreeks, batch_size: float, batch_number: int
    ) -> AccumulationOrder | None:
        """创建抄底订单（卖出OTM Put 收权利金）"""
        try:
            # 安全检查
            if not put or not put.strike or not put.expiry:
                return None

            mark = put.mark_price or put.bid or Decimal("0")
            if mark <= 0:
                return None

            # 资金与合约规模（简单按权利金金额近似控制）
            max_pos = Decimal(str(self.config.get("max_position_size", Decimal("1000"))))
            used = self.accumulation_metrics.current_exposure or Decimal("0")
            remaining = max(Decimal("0"), max_pos - used)
            if remaining <= 0:
                return None

            # 目标本批资金占用
            batch_capital = remaining * Decimal(str(batch_size))
            # 估算合约张数（以权利金为近似规模）
            qty = int(max(1, (batch_capital / mark).to_integral_value(rounding="ROUND_FLOOR")))

            # 目标限价：用中价（更易成交），回退到bid
            price = put.mid_price if getattr(put, "mid_price", None) else mark

            # 订单ID
            oid = f"ACC-{int(datetime.now(UTC).timestamp())}-{batch_number}"

            order = AccumulationOrder(
                order_id=oid,
                strategy_mode=None,  # 由 __post_init__ 自动设置
                size=Decimal(qty),
                target_premium=price,
                batch_number=batch_number,
                created_at=datetime.now(UTC),
                put_option=put,
                max_delta=float(self.config.get("delta_range", [0.08, 0.20])[1]),
                reason=f"accumulate_batch_{batch_number}",
                expected_pnl=price * Decimal(qty),
            )

            # 更新本地暴露（以权利金近似）
            try:
                self.accumulation_metrics.current_exposure = (
                    self.accumulation_metrics.current_exposure + price * Decimal(qty)
                )
            except Exception:
                pass

            return order

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Create accumulation order failed: {e}")
            return None

    async def generate_accumulation_orders(
        self, market_analysis: dict[str, Any]
    ) -> list[AccumulationOrder]:
        """生成抄底订单 - 已弃用，请使用渐进式建仓方法"""
        if self.logger:
            await self.logger.warning(
                "generate_accumulation_orders已弃用，请使用generate_single_batch_order实现渐进式建仓"
            )
        return []
