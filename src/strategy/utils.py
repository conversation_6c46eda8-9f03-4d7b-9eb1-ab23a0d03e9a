"""
策略模块通用工具函数
"""

from datetime import datetime


def safe_parse_expiry(expiry_data) -> datetime | None:
    """安全解析期权到期日期，兼容datetime对象和ISO字符串

    Args:
        expiry_data: 期权到期数据，可能是datetime对象或ISO字符串

    Returns:
        datetime: 解析后的datetime对象，失败则返回None
    """
    if isinstance(expiry_data, datetime):
        return expiry_data
    elif isinstance(expiry_data, str) and expiry_data:
        try:
            return datetime.fromisoformat(expiry_data)
        except (ValueError, TypeError):
            return None
    else:
        return None
