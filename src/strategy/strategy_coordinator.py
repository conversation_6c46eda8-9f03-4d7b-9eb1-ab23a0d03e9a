"""
策略协调器

负责协调主策略（期权网格）和分支策略（方向性期权）的资金分配、风险管理和策略切换。
主要功能：
1. 主/分支策略资金分配管理
2. Delta敞口监控和自动对冲
3. 策略冲突检测和保护机制
4. 动态资金调整和优化
5. 策略切换协调
"""

import asyncio
import contextlib
from dataclasses import dataclass
from datetime import UTC, datetime
from enum import Enum
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.dependency_injector import inject
from src.core.event_bus import BaseEvent, EventBus
from src.data.cache_manager import CacheManager
from src.gateways.deribit_client import DeribitClient
from src.risk.risk_engine import RiskEngine


class AllocationMode(Enum):
    """资金分配模式"""

    CONSERVATIVE = "conservative"  # 保守模式：主策略80%，分支策略20%
    BALANCED = "balanced"  # 平衡模式：主策略60%，分支策略40%
    AGGRESSIVE = "aggressive"  # 激进模式：主策略40%，分支策略60%
    MAIN_ONLY = "main_only"  # 仅主策略：主策略100%
    BRANCH_ONLY = "branch_only"  # 仅分支策略：分支策略100%


class CoordinatorState(Enum):
    """协调器状态"""

    IDLE = "idle"
    MONITORING = "monitoring"
    REBALANCING = "rebalancing"
    HEDGING = "hedging"
    EMERGENCY = "emergency"


@dataclass
class AllocationConfig:
    """资金分配配置"""

    mode: AllocationMode
    main_strategy_ratio: float
    branch_strategy_ratio: float
    max_delta_exposure: float
    rebalance_threshold: float

    def validate(self) -> bool:
        """验证配置有效性"""
        return (
            abs(self.main_strategy_ratio + self.branch_strategy_ratio - 1.0) < 0.001
            and 0.0 <= self.main_strategy_ratio <= 1.0
            and 0.0 <= self.branch_strategy_ratio <= 1.0
            and self.max_delta_exposure > 0
            and 0.0 < self.rebalance_threshold < 1.0
        )


@dataclass
class PortfolioMetrics:
    """投资组合指标"""

    total_capital: float
    main_strategy_allocation: float
    branch_strategy_allocation: float
    total_delta_exposure: float
    total_pnl: float
    main_strategy_pnl: float
    branch_strategy_pnl: float
    risk_utilization: float

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_capital": self.total_capital,
            "main_strategy_allocation": self.main_strategy_allocation,
            "branch_strategy_allocation": self.branch_strategy_allocation,
            "total_delta_exposure": self.total_delta_exposure,
            "total_pnl": self.total_pnl,
            "main_strategy_pnl": self.main_strategy_pnl,
            "branch_strategy_pnl": self.branch_strategy_pnl,
            "risk_utilization": self.risk_utilization,
        }


class StrategyCoordinator(BaseComponent):
    """
    策略协调器

    协调主策略和分支策略的资金分配、风险管理和策略切换。
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__(component_name="StrategyCoordinator", config=config)

        # 配置参数
        self.total_capital = self.config.get("total_capital", 1.0)  # 1 BTC
        self.delta_hedge_threshold = self.config.get(
            "delta_hedge_threshold", 0.1
        )  # 0.1 Delta
        self.rebalance_interval_hours = self.config.get("rebalance_interval_hours", 6)
        self.emergency_stop_loss_pct = self.config.get(
            "emergency_stop_loss_pct",
            0.3,  # 30%紧急止损（适合加密货币波动性）
        )  # 20%

        # 分配配置预设
        self.allocation_presets = {
            AllocationMode.CONSERVATIVE: AllocationConfig(
                mode=AllocationMode.CONSERVATIVE,
                main_strategy_ratio=0.8,
                branch_strategy_ratio=0.2,
                max_delta_exposure=0.05,
                rebalance_threshold=0.1,
            ),
            AllocationMode.BALANCED: AllocationConfig(
                mode=AllocationMode.BALANCED,
                main_strategy_ratio=0.6,
                branch_strategy_ratio=0.4,
                max_delta_exposure=0.1,
                rebalance_threshold=0.15,
            ),
            AllocationMode.AGGRESSIVE: AllocationConfig(
                mode=AllocationMode.AGGRESSIVE,
                main_strategy_ratio=0.4,
                branch_strategy_ratio=0.6,
                max_delta_exposure=0.2,
                rebalance_threshold=0.2,
            ),
            AllocationMode.MAIN_ONLY: AllocationConfig(
                mode=AllocationMode.MAIN_ONLY,
                main_strategy_ratio=1.0,
                branch_strategy_ratio=0.0,
                max_delta_exposure=0.02,
                rebalance_threshold=0.05,
            ),
            AllocationMode.BRANCH_ONLY: AllocationConfig(
                mode=AllocationMode.BRANCH_ONLY,
                main_strategy_ratio=0.0,
                branch_strategy_ratio=1.0,
                max_delta_exposure=0.3,
                rebalance_threshold=0.25,
            ),
        }

        # 当前配置
        self.current_allocation = self.allocation_presets[AllocationMode.BALANCED]

        # 协调器状态
        self._coordinator_state = CoordinatorState.IDLE
        self._last_rebalance_time = datetime.now(UTC)

        # 组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None
        self.deribit_client: DeribitClient | None = None
        self.position_manager: Any | None = None
        self.main_strategy: Any | None = None
        self.branch_strategy: Any | None = None
        self.sideways_mode: Any | None = None

        # 监控任务
        self._monitoring_task: asyncio.Task | None = None
        self._rebalancing_task: asyncio.Task | None = None

        # 性能统计
        self.stats = {
            "rebalance_count": 0,
            "hedge_operations": 0,
            "strategy_switches": 0,
            "emergency_stops": 0,
            "total_fees_paid": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
        }

        # 历史指标记录
        self.metrics_history: list[PortfolioMetrics] = []

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 验证核心依赖组件（更宽容的检查）
            required_deps = [self.event_bus, self.cache_manager]

            if not all(required_deps):
                if self.logger:
                    await self.logger.error(
                        "Missing core dependencies (event_bus, cache_manager)"
                    )
                return False

            # 记录缺失的可选依赖
            missing_optional = []
            if not self.deribit_client:
                missing_optional.append("deribit_client")
            if not self.position_manager:
                missing_optional.append("position_manager")
            if not self.main_strategy:
                missing_optional.append("main_strategy")
            if not self.branch_strategy:
                missing_optional.append("branch_strategy")

            if missing_optional and self.logger:
                await self.logger.warning(
                    f"Missing optional dependencies: {missing_optional}"
                )
                await self.logger.info("StrategyCoordinator will run in limited mode")

            # 订阅策略切换事件（如果事件总线可用）
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.subscribe(
                    event_types=[EventType.STRATEGY_SWITCH_REQUEST],
                    callback=self._handle_strategy_switch_request,
                    subscriber_id=f"{self.component_name}_switch_listener",
                )

            # 验证初始分配配置
            if not self.current_allocation.validate():
                if self.logger:
                    await self.logger.error("Invalid allocation configuration")
                return False

            if self.logger:
                await self.logger.info("StrategyCoordinator initialized successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to initialize StrategyCoordinator: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 启动监控任务
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            self._rebalancing_task = asyncio.create_task(self._rebalancing_loop())

            self._coordinator_state = CoordinatorState.MONITORING

            # 执行初始资金分配
            await self._initial_allocation()

            # 同步主/分配比到 RiskEngine（保持风控门控一致）
            try:
                if self.dependency_injector:
                    risk_engine: (
                        RiskEngine | None
                    ) = await self.dependency_injector.resolve(RiskEngine)
                    if risk_engine:
                        await risk_engine.update_allocation_ratios(
                            main=self.current_allocation.main_strategy_ratio,
                            branch=self.current_allocation.branch_strategy_ratio,
                        )
            except Exception:
                pass

            if self.logger:
                await self.logger.info("StrategyCoordinator started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start StrategyCoordinator: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 停止监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._monitoring_task

            if self._rebalancing_task and not self._rebalancing_task.done():
                self._rebalancing_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._rebalancing_task

            self._coordinator_state = CoordinatorState.IDLE

            if self.logger:
                await self.logger.info("StrategyCoordinator stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop StrategyCoordinator: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            # 计算当前投资组合指标
            metrics = await self._calculate_portfolio_metrics()

            details = {
                "coordinator_state": self._coordinator_state.value,
                "allocation_mode": self.current_allocation.mode.value,
                "portfolio_metrics": metrics.to_dict() if metrics else None,
                "monitoring_task_running": self._monitoring_task
                and not self._monitoring_task.done(),
                "rebalancing_task_running": self._rebalancing_task
                and not self._rebalancing_task.done(),
                "stats": self.stats,
            }

            # 检查紧急情况
            if (
                metrics
                and abs(metrics.total_pnl / self.total_capital)
                > self.emergency_stop_loss_pct
            ):
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Emergency stop loss triggered",
                    details=details,
                    timestamp=datetime.now(UTC),
                )

            # 检查Delta敞口
            if (
                metrics
                and abs(metrics.total_delta_exposure)
                > self.current_allocation.max_delta_exposure
            ):
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message="Delta exposure exceeds limit",
                    details=details,
                    timestamp=datetime.now(UTC),
                )

            # 检查依赖策略健康状态（如果策略存在）
            strategy_health_checks = []
            if self.main_strategy:
                strategy_health_checks.append(await self.main_strategy.is_healthy())
            if self.branch_strategy:
                strategy_health_checks.append(await self.branch_strategy.is_healthy())

            if strategy_health_checks and not all(strategy_health_checks):
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message="Strategy components are unhealthy",
                    details=details,
                    timestamp=datetime.now(UTC),
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="StrategyCoordinator is healthy",
                details=details,
                timestamp=datetime.now(UTC),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
                timestamp=datetime.now(UTC),
            )

    @inject(
        EventBus,
        CacheManager,
        DeribitClient,
        "PositionManager",
        "OptionGridStrategy",
        "BranchStrategy",
    )
    async def set_dependencies(
        self,
        event_bus: EventBus,
        cache_manager: CacheManager,
        deribit_client: DeribitClient | None = None,
        position_manager=None,
        main_strategy=None,
        branch_strategy=None,
        sideways_mode=None,
    ):
        """设置依赖组件 - 通过依赖注入自动调用"""
        self.event_bus = event_bus
        self.cache_manager = cache_manager
        if deribit_client:
            self.deribit_client = deribit_client
        if position_manager:
            self.position_manager = position_manager
        if main_strategy:
            self.main_strategy = main_strategy
        if branch_strategy:
            self.branch_strategy = branch_strategy
        if sideways_mode:
            self.sideways_mode = sideways_mode

    async def _initial_allocation(self):
        """执行初始资金分配"""
        try:
            main_allocation = (
                self.total_capital * self.current_allocation.main_strategy_ratio
            )
            branch_allocation = (
                self.total_capital * self.current_allocation.branch_strategy_ratio
            )

            # 设置策略资金限制
            if hasattr(self.main_strategy, "set_capital_limit"):
                await self.main_strategy.set_capital_limit(main_allocation)

            if hasattr(self.branch_strategy, "set_capital_limit"):
                await self.branch_strategy.set_capital_limit(branch_allocation)

            if self.logger:
                await self.logger.info(
                    f"Initial allocation completed: Main={main_allocation:.4f}, "
                    f"Branch={branch_allocation:.4f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in initial allocation: {e}")

    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await asyncio.sleep(30)  # 30秒检查一次

                # 计算投资组合指标
                metrics = await self._calculate_portfolio_metrics()
                if metrics:
                    self.metrics_history.append(metrics)

                    # 保留最近100个记录
                    if len(self.metrics_history) > 100:
                        self.metrics_history.pop(0)

                    # 存储到缓存
                    await self.cache_manager.set(
                        "strategy:portfolio_metrics", metrics.to_dict(), ttl=300
                    )

                # 检查Delta敞口
                await self._check_delta_exposure()

                # 检查紧急情况
                await self._check_emergency_conditions()

                # 检查策略冲突
                await self._check_strategy_conflicts()

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in monitoring loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(60)

    async def _rebalancing_loop(self):
        """资金再平衡循环"""
        while self.is_running:
            try:
                await asyncio.sleep(3600)  # 1小时检查一次

                # 检查是否需要再平衡
                if await self._should_rebalance():
                    await self._execute_rebalance()

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in rebalancing loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(1800)  # 错误后等待30分钟

    async def _calculate_portfolio_metrics(self) -> PortfolioMetrics | None:
        """计算投资组合指标"""
        try:
            # 检查依赖组件是否可用
            if not self.main_strategy or not self.branch_strategy:
                if self.logger:
                    await self.logger.warning(
                        "Strategy components not available for portfolio metrics calculation"
                    )
                return None

            # 获取主策略指标
            main_stats = await self.main_strategy.get_strategy_stats()
            main_pnl = main_stats.get("total_pnl", 0.0)
            main_allocation = main_stats.get("allocated_capital", 0.0)

            # 获取分支策略指标
            branch_stats = await self.branch_strategy.get_strategy_stats()
            branch_pnl = branch_stats.get("total_pnl", 0.0)
            branch_allocation = branch_stats.get("allocated_capital", 0.0)

            # 计算总Delta敞口
            total_delta = await self._calculate_total_delta_exposure()

            # 计算风险利用率
            risk_utilization = (
                abs(total_delta) / self.current_allocation.max_delta_exposure
            )

            return PortfolioMetrics(
                total_capital=self.total_capital,
                main_strategy_allocation=main_allocation,
                branch_strategy_allocation=branch_allocation,
                total_delta_exposure=total_delta,
                total_pnl=main_pnl + branch_pnl,
                main_strategy_pnl=main_pnl,
                branch_strategy_pnl=branch_pnl,
                risk_utilization=risk_utilization,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating portfolio metrics: {e}")
            return None

    async def _calculate_total_delta_exposure(self) -> float:
        """计算总Delta敞口"""
        try:
            total_delta = 0.0

            # 检查依赖组件是否可用
            if not self.position_manager:
                if self.logger:
                    await self.logger.warning(
                        "PositionManager not available for delta exposure calculation"
                    )
                return 0.0

            # 获取所有持仓
            positions = await self.position_manager.get_all_positions()

            for position in positions:
                # 获取期权Greeks
                if self.deribit_client:
                    greeks = await self.deribit_client.get_option_greeks(
                        position.get("instrument_name", "")
                    )
                    if greeks:
                        delta = greeks.get("delta", 0.0)
                        quantity = position.get("quantity", 0.0)
                        total_delta += delta * quantity

            return total_delta

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating total delta exposure: {e}")
            return 0.0

    async def _check_delta_exposure(self):
        """检查Delta敞口"""
        try:
            total_delta = await self._calculate_total_delta_exposure()

            if abs(total_delta) > self.delta_hedge_threshold:
                await self._execute_delta_hedge(total_delta)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking delta exposure: {e}")

    async def _execute_delta_hedge(self, delta_exposure: float):
        """执行Delta风险控制 - 通过仓位管理而非对冲（适应IBIT无法做空的限制）"""
        try:
            self._coordinator_state = CoordinatorState.HEDGING

            delta_abs = abs(delta_exposure)

            if delta_abs > 2.0:  # 紧急阈值
                # 紧急情况：强制减仓30%
                await self._emergency_position_reduction(0.3)
                self.stats["emergency_stops"] += 1

                if self.logger:
                    await self.logger.critical(
                        f"Emergency Delta control: reduced positions by 30%, "
                        f"original delta: {delta_exposure:.4f}"
                    )

            elif delta_abs > 1.2:  # 高风险阈值
                # 高风险：暂停新订单，减少仓位大小
                await self._pause_aggressive_strategies()
                await self._reduce_position_sizing(0.2)
                self.stats["hedge_operations"] += 1

                if self.logger:
                    await self.logger.warning(
                        f"Delta risk control: paused aggressive orders, reduced sizing by 20%, "
                        f"delta exposure: {delta_exposure:.4f}"
                    )
            else:
                # 正常范围：允许继续操作
                await self._resume_normal_operations()

                if self.logger:
                    await self.logger.info(
                        f"Delta exposure within acceptable range: {delta_exposure:.4f}"
                    )

            self._coordinator_state = CoordinatorState.MONITORING

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in delta risk control: {e}")
            self._coordinator_state = CoordinatorState.MONITORING

    async def _check_emergency_conditions(self):
        """检查紧急情况"""
        try:
            metrics = await self._calculate_portfolio_metrics()
            if not metrics:
                return

            # 检查总PnL是否触发紧急止损
            pnl_ratio = metrics.total_pnl / self.total_capital
            if pnl_ratio < -self.emergency_stop_loss_pct:
                await self._trigger_emergency_stop()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking emergency conditions: {e}")

    async def _trigger_emergency_stop(self):
        """触发紧急停止"""
        try:
            self._coordinator_state = CoordinatorState.EMERGENCY

            # 停止所有策略
            await self.main_strategy.emergency_stop()
            await self.branch_strategy.emergency_stop()

            # 平仓所有持仓
            await self.position_manager.close_all_positions("emergency_stop")

            self.stats["emergency_stops"] += 1

            if self.logger:
                await self.logger.critical(
                    "Emergency stop triggered - all positions closed"
                )

            # 发送紧急通知事件
            from src.core.event_bus import EventType

            emergency_event = BaseEvent(
                event_type=EventType.EMERGENCY_STOP,
                source=self.component_name,
                metadata={
                    "reason": "stop_loss_triggered",
                    "timestamp": datetime.now(UTC).isoformat(),
                },
            )
            await self.event_bus.publish(emergency_event)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error triggering emergency stop: {e}")

    async def _check_strategy_conflicts(self):
        """检查策略冲突"""
        try:
            # 检查依赖组件是否可用
            if not self.main_strategy or not self.branch_strategy:
                if self.logger:
                    await self.logger.warning(
                        "Strategy components not available for conflict checking"
                    )
                return

            # 获取主策略当前操作
            main_position = await self.main_strategy.get_current_position()
            branch_position = await self.branch_strategy.get_current_position()

            if (
                main_position
                and branch_position
                and self._positions_conflict(main_position, branch_position)
            ):
                # 检查是否存在相反方向的持仓
                await self._resolve_strategy_conflict(main_position, branch_position)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking strategy conflicts: {e}")

    def _positions_conflict(self, _main_pos: Any, _branch_pos: Any) -> bool:
        """判断持仓是否冲突"""
        try:
            # 检测策略间的持仓冲突
            return False

        except Exception:
            return False

    async def _resolve_strategy_conflict(self, _main_pos: Any, _branch_pos: Any):
        """解决策略冲突"""
        try:
            # 优先保留主策略持仓，关闭分支策略持仓
            await self.branch_strategy.force_close_position("conflict_resolution")

            if self.logger:
                await self.logger.warning(
                    "Strategy conflict resolved - branch position closed"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error resolving strategy conflict: {e}")

    async def _should_rebalance(self) -> bool:
        """判断是否需要资金再平衡"""
        try:
            # 检查时间间隔
            time_since_last = (
                datetime.now(UTC) - self._last_rebalance_time
            ).total_seconds() / 3600
            if time_since_last < self.rebalance_interval_hours:
                return False

            # 计算当前分配偏差
            metrics = await self._calculate_portfolio_metrics()
            if not metrics:
                return False

            target_main = (
                self.total_capital * self.current_allocation.main_strategy_ratio
            )
            target_branch = (
                self.total_capital * self.current_allocation.branch_strategy_ratio
            )

            main_deviation = (
                abs(metrics.main_strategy_allocation - target_main) / self.total_capital
            )
            branch_deviation = (
                abs(metrics.branch_strategy_allocation - target_branch)
                / self.total_capital
            )

            # 如果偏差超过阈值，需要再平衡
            return (
                max(main_deviation, branch_deviation)
                > self.current_allocation.rebalance_threshold
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking rebalance need: {e}")
            return False

    async def _execute_rebalance(self):
        """执行资金再平衡"""
        try:
            self._coordinator_state = CoordinatorState.REBALANCING

            # 计算目标分配
            target_main = (
                self.total_capital * self.current_allocation.main_strategy_ratio
            )
            target_branch = (
                self.total_capital * self.current_allocation.branch_strategy_ratio
            )

            # 获取当前分配
            metrics = await self._calculate_portfolio_metrics()
            if not metrics:
                return

            # 计算需要调整的金额
            main_adjustment = target_main - metrics.main_strategy_allocation
            branch_adjustment = target_branch - metrics.branch_strategy_allocation

            # 执行调整
            if abs(main_adjustment) > 0.001:  # 最小调整阈值
                await self._adjust_strategy_allocation("main", main_adjustment)

            if abs(branch_adjustment) > 0.001:
                await self._adjust_strategy_allocation("branch", branch_adjustment)

            self._last_rebalance_time = datetime.now(UTC)
            self.stats["rebalance_count"] += 1

            if self.logger:
                await self.logger.info(
                    f"Rebalance completed: Main adjustment={main_adjustment:.4f}, "
                    f"Branch adjustment={branch_adjustment:.4f}"
                )

            self._coordinator_state = CoordinatorState.MONITORING

            # 再平衡后同步配比到 RiskEngine
            try:
                if self.dependency_injector:
                    risk_engine: (
                        RiskEngine | None
                    ) = await self.dependency_injector.resolve(RiskEngine)
                    if risk_engine:
                        await risk_engine.update_allocation_ratios(
                            main=self.current_allocation.main_strategy_ratio,
                            branch=self.current_allocation.branch_strategy_ratio,
                        )
            except Exception:
                pass

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error executing rebalance: {e}")
            self._coordinator_state = CoordinatorState.MONITORING

    async def _adjust_strategy_allocation(self, strategy: str, adjustment: float):
        """调整策略资金分配"""
        try:
            if strategy == "main" and hasattr(self.main_strategy, "adjust_capital"):
                await self.main_strategy.adjust_capital(adjustment)
            elif strategy == "branch" and hasattr(
                self.branch_strategy, "adjust_capital"
            ):
                await self.branch_strategy.adjust_capital(adjustment)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error adjusting {strategy} strategy allocation: {e}"
                )

    async def _handle_strategy_switch_request(self, event: BaseEvent):
        """处理策略切换请求"""
        try:
            switch_data = event.metadata
            from_strategy = switch_data.get("from_strategy")
            to_strategy = switch_data.get("to_strategy")
            reason = switch_data.get("reason", "unknown")

            if from_strategy == "branch" and to_strategy == "volatility":
                # 分支策略请求切换到波动率策略
                await self._switch_to_volatility_strategy(reason)

            self.stats["strategy_switches"] += 1

            if self.logger:
                await self.logger.info(
                    f"Strategy switch processed: {from_strategy} -> {to_strategy}, "
                    f"reason: {reason}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling strategy switch request: {e}")

    async def _switch_to_volatility_strategy(self, reason: str):
        """切换到波动率策略"""
        try:
            if not self.sideways_mode:
                if self.logger:
                    await self.logger.warning(
                        "SidewaysMode not available for volatility strategy switch"
                    )
                return

            # 暂停分支策略
            await self.branch_strategy.pause()

            # 启动波动率策略（SidewaysMode）
            if not await self.sideways_mode.is_running():
                await self.sideways_mode.start()

            # 调整资金分配给波动率策略
            branch_allocation = (
                self.total_capital * self.current_allocation.branch_strategy_ratio
            )
            if hasattr(self.sideways_mode, "set_capital_limit"):
                await self.sideways_mode.set_capital_limit(branch_allocation)

            if self.logger:
                await self.logger.info(
                    f"Switched to volatility strategy, reason: {reason}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error switching to volatility strategy: {e}")

    # 公共接口方法

    async def set_allocation_mode(self, mode: AllocationMode) -> bool:
        """设置资金分配模式"""
        try:
            if mode not in self.allocation_presets:
                return False

            old_mode = self.current_allocation.mode
            self.current_allocation = self.allocation_presets[mode]

            # 触发重新分配
            await self._execute_rebalance()

            if self.logger:
                await self.logger.info(
                    f"Allocation mode changed: {old_mode.value} -> {mode.value}"
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error setting allocation mode: {e}")
            return False

    async def get_portfolio_metrics(self) -> PortfolioMetrics | None:
        """获取投资组合指标"""
        return await self._calculate_portfolio_metrics()

    async def get_allocation_status(self) -> dict[str, Any]:
        """获取资金分配状态"""
        try:
            metrics = await self._calculate_portfolio_metrics()

            return {
                "current_mode": self.current_allocation.mode.value,
                "target_allocation": {
                    "main_strategy": self.current_allocation.main_strategy_ratio,
                    "branch_strategy": self.current_allocation.branch_strategy_ratio,
                },
                "actual_allocation": {
                    "main_strategy": metrics.main_strategy_allocation
                    / self.total_capital
                    if metrics
                    else 0,
                    "branch_strategy": metrics.branch_strategy_allocation
                    / self.total_capital
                    if metrics
                    else 0,
                },
                "delta_exposure": metrics.total_delta_exposure if metrics else 0,
                "risk_utilization": metrics.risk_utilization if metrics else 0,
                "last_rebalance": self._last_rebalance_time.isoformat(),
                "coordinator_state": self._coordinator_state.value,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting allocation status: {e}")
            return {}

    async def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计"""
        try:
            stats = self.stats.copy()

            # 计算额外指标
            if self.metrics_history:
                recent_metrics = self.metrics_history[-20:]  # 最近20个记录

                # 计算最大回撤
                peak_pnl = max(m.total_pnl for m in recent_metrics)
                current_pnl = recent_metrics[-1].total_pnl
                drawdown = (
                    (peak_pnl - current_pnl) / self.total_capital
                    if peak_pnl > current_pnl
                    else 0
                )
                stats["current_drawdown"] = drawdown

                # 计算平均风险利用率
                avg_risk_util = sum(m.risk_utilization for m in recent_metrics) / len(
                    recent_metrics
                )
                stats["avg_risk_utilization"] = avg_risk_util

            return stats

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting performance stats: {e}")
            return self.stats.copy()

    async def emergency_shutdown(self) -> bool:
        """紧急关闭"""
        try:
            await self._trigger_emergency_stop()
            await self.stop()
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in emergency shutdown: {e}")
            return False

    # Delta风险控制支持方法（适应IBIT无对冲限制）

    async def _emergency_position_reduction(self, reduction_ratio: float):
        """紧急减仓"""
        try:
            # 通知所有策略减少仓位
            if self.main_strategy:
                await self._notify_strategy_reduce_position(
                    self.main_strategy, reduction_ratio
                )

            if self.branch_strategy:
                await self._notify_strategy_reduce_position(
                    self.branch_strategy, reduction_ratio
                )

            if self.logger:
                await self.logger.critical(
                    f"Emergency position reduction: {reduction_ratio:.1%}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Emergency position reduction failed: {e}")

    async def _pause_aggressive_strategies(self):
        """暂停激进策略"""
        try:
            # 暂停主策略的新订单
            if self.main_strategy and hasattr(self.main_strategy, "pause_new_orders"):
                await self.main_strategy.pause_new_orders()

            # 暂停分支策略的新订单
            if self.branch_strategy and hasattr(
                self.branch_strategy, "pause_new_orders"
            ):
                await self.branch_strategy.pause_new_orders()

            if self.logger:
                await self.logger.warning(
                    "Aggressive strategies paused due to high Delta exposure"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to pause aggressive strategies: {e}")

    async def _reduce_position_sizing(self, reduction_ratio: float):
        """减少仓位大小"""
        try:
            # 通知策略减少新订单大小
            size_multiplier = 1.0 - reduction_ratio

            if self.main_strategy and hasattr(
                self.main_strategy, "adjust_position_sizing"
            ):
                await self.main_strategy.adjust_position_sizing(size_multiplier)

            if self.branch_strategy and hasattr(
                self.branch_strategy, "adjust_position_sizing"
            ):
                await self.branch_strategy.adjust_position_sizing(size_multiplier)

            if self.logger:
                await self.logger.info(
                    f"Position sizing reduced by {reduction_ratio:.1%}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to reduce position sizing: {e}")

    async def _resume_normal_operations(self):
        """恢复正常操作"""
        try:
            # 恢复主策略
            if self.main_strategy and hasattr(
                self.main_strategy, "resume_normal_operations"
            ):
                await self.main_strategy.resume_normal_operations()

            # 恢复分支策略
            if self.branch_strategy and hasattr(
                self.branch_strategy, "resume_normal_operations"
            ):
                await self.branch_strategy.resume_normal_operations()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to resume normal operations: {e}")

    async def _notify_strategy_reduce_position(self, strategy, reduction_ratio: float):
        """通知策略减少仓位"""
        try:
            if hasattr(strategy, "emergency_position_reduction"):
                await strategy.emergency_position_reduction(reduction_ratio)
            elif hasattr(strategy, "reduce_positions"):
                await strategy.reduce_positions(reduction_ratio)
            else:
                if self.logger:
                    await self.logger.warning(
                        f"Strategy {strategy.__class__.__name__} does not support position reduction"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to notify strategy for position reduction: {e}"
                )
