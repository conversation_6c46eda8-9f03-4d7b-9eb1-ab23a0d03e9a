"""
订单类型转换适配器

在策略订单对象和执行订单对象之间提供转换功能

注意：此文件生成的订单使用BTC期权格式，但主策略会通过
BTC-IBIT映射器将这些订单转换为IBIT期权格式，最终通过IBKR执行
"""

# 避免循环导入，直接在此定义OrderRequest或使用TYPE_CHECKING
from typing import TYPE_CHECKING

from src.analysis.common_types import (
    AccumulationOrder,
    DistributionOrder,
    SidewaysOrder,
)

if TYPE_CHECKING:
    from src.strategy.option_grid_strategy import OrderRequest
else:
    # 运行时使用 - 避免循环导入
    OrderRequest = None


def accumulation_order_to_request(accumulation_order: AccumulationOrder):
    """将AccumulationOrder转换为OrderRequest"""
    # 运行时导入避免循环依赖
    from src.strategy.option_grid_strategy import OrderRequest

    # 从put_option获取基础信息
    put_option = accumulation_order.put_option
    if not put_option:
        raise ValueError("AccumulationOrder must have put_option data")

    # 构建期权合约符号 (Deribit格式)
    expiry_dt = getattr(put_option, "expiry", None) or getattr(
        put_option, "expiry_date", None
    )
    expiry_str = expiry_dt.strftime("%d%b%y").upper() if expiry_dt else "UNKNOWN"
    strike_val = getattr(put_option, "strike", None)
    strike_str = str(int(strike_val)) if strike_val else "0"
    symbol = f"BTC-{expiry_str}-{strike_str}-P"

    return OrderRequest(
        symbol=symbol,
        side="sell",  # 抄底模式卖出OTM看跌期权收取权利金
        order_type="limit",
        size=accumulation_order.size,
        price=accumulation_order.target_premium,
        strike=strike_val,
        expiry=expiry_dt,
        option_type="put",
        reason=accumulation_order.reason,
        metadata={
            "strategy_mode": "accumulation",
            "order_id": accumulation_order.order_id,
            "batch_number": accumulation_order.batch_number,
            "max_delta": getattr(accumulation_order, "max_delta", 0.2),
            "expected_pnl": str(accumulation_order.expected_pnl),
            "created_at": accumulation_order.created_at.isoformat(),
        },
    )


def distribution_order_to_request(distribution_order: DistributionOrder):
    """将DistributionOrder转换为OrderRequest"""
    # 运行时导入避免循环依赖
    from src.strategy.option_grid_strategy import OrderRequest

    # 从call_option获取基础信息
    call_option = distribution_order.call_option
    if not call_option:
        raise ValueError("DistributionOrder must have call_option data")

    # 构建期权合约符号 (Deribit格式)
    expiry_dt = getattr(call_option, "expiry", None) or getattr(
        call_option, "expiry_date", None
    )
    expiry_str = expiry_dt.strftime("%d%b%y").upper() if expiry_dt else "UNKNOWN"
    strike_val = getattr(call_option, "strike", None)
    strike_str = str(int(strike_val)) if strike_val else "0"
    symbol = f"BTC-{expiry_str}-{strike_str}-C"

    return OrderRequest(
        symbol=symbol,
        side="sell",  # 卖出模式卖出看涨期权
        order_type="limit",
        size=distribution_order.size,
        price=distribution_order.target_premium,
        strike=strike_val,
        expiry=expiry_dt,
        option_type="call",
        reason=getattr(distribution_order, "reason", "Distribution strategy execution"),
        metadata={
            "strategy_mode": "distribution",
            "order_id": distribution_order.order_id,
            "batch_number": distribution_order.batch_number,
            "covered_position": str(distribution_order.covered_position),
            "created_at": distribution_order.created_at.isoformat(),
        },
    )


def sideways_order_to_request(sideways_order: SidewaysOrder):
    """将SidewaysOrder转换为OrderRequest"""
    # 运行时导入避免循环依赖
    from src.strategy.option_grid_strategy import OrderRequest

    # SidewaysOrder可能包含call或put期权
    if hasattr(sideways_order, "call_option") and sideways_order.call_option:
        option = sideways_order.call_option
        option_type = "call"
        option_code = "C"
        side = "sell"  # 震荡模式通常卖出期权收取权利金
    elif hasattr(sideways_order, "put_option") and sideways_order.put_option:
        option = sideways_order.put_option
        option_type = "put"
        option_code = "P"
        side = "sell"
    else:
        raise ValueError(
            "SidewaysOrder must have either call_option or put_option data"
        )

    # 构建期权合约符号 (Deribit格式)
    expiry_dt = getattr(option, "expiry", None) or getattr(option, "expiry_date", None)
    expiry_str = expiry_dt.strftime("%d%b%y").upper() if expiry_dt else "UNKNOWN"
    strike_val = getattr(option, "strike", None)
    strike_str = str(int(strike_val)) if strike_val else "0"
    symbol = f"BTC-{expiry_str}-{strike_str}-{option_code}"

    return OrderRequest(
        symbol=symbol,
        side=side,
        order_type="limit",
        size=sideways_order.size,
        price=sideways_order.target_premium,
        strike=strike_val,
        expiry=expiry_dt,
        option_type=option_type,
        reason=getattr(sideways_order, "reason", "Sideways strategy execution"),
        metadata={
            "strategy_mode": "sideways",
            "order_id": sideways_order.order_id,
            "batch_number": sideways_order.batch_number,
            "created_at": sideways_order.created_at.isoformat(),
        },
    )


def convert_strategy_order_to_request(strategy_order):
    """通用转换函数 - 根据订单类型自动选择转换方法"""
    # 运行时导入避免循环依赖
    from src.strategy.option_grid_strategy import OrderRequest

    if isinstance(strategy_order, AccumulationOrder):
        return accumulation_order_to_request(strategy_order)
    elif isinstance(strategy_order, DistributionOrder):
        return distribution_order_to_request(strategy_order)
    elif isinstance(strategy_order, SidewaysOrder):
        return sideways_order_to_request(strategy_order)
    elif isinstance(strategy_order, OrderRequest):
        # 已经是OrderRequest，直接返回
        return strategy_order
    else:
        raise ValueError(f"Unsupported strategy order type: {type(strategy_order)}")
