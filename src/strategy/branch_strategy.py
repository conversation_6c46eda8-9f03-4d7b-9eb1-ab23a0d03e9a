"""
分支策略引擎

基于因果信号监听的方向性期权交易策略，集成微观结构信号进行精准出场。
主要功能：
1. 监听CausalEngine的因果信号作为入场触发
2. 集成MicrostructureSignals进行精准出场时机判断
3. 实现方向性期权交易逻辑（Buy OTM Call/Put）
4. 智能止盈机制基于微观结构信号
5. 趋势衰竭检测和自动切换到Iron Condor策略
"""

import asyncio
import contextlib
from dataclasses import dataclass
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from src.analysis.causal_engine import CausalEngine
from src.analysis.microstructure_signals import MicrostructureSignals, TrendStatus
from src.analysis.types import SignalType
from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.config_manager import ConfigManager
from src.core.event_bus import BaseEvent, EventBus, EventType
from src.data.cache_manager import CacheManager
from src.gateways.deribit_client import DeribitClient
from src.gateways.ibkr_client import IBKRClient
from src.services.market_data_service import MarketDataService
from src.services.option_selector import OptionSelectionCriteria, OptionSelectorService
from src.strategy.btc_to_ibit_mapper import (
    BTCSideSignal,
    BTCToIBITMapper,
    IBITMappingParams,
)
from src.utils.market_hours import MarketHoursManager, MarketHoursPolicy


class DirectionType(Enum):
    """方向类型枚举"""

    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"


class StrategyState(Enum):
    """策略状态枚举"""

    IDLE = "idle"
    MONITORING = "monitoring"
    POSITION_OPEN = "position_open"
    PROFIT_TAKING = "profit_taking"
    SWITCHING_TO_VOLATILITY = "switching_to_volatility"


@dataclass
class BranchPosition:
    """分支策略持仓信息"""

    instrument_name: str
    direction: DirectionType
    entry_price: float
    quantity: float
    entry_time: datetime
    target_profit: float
    stop_loss: float
    current_pnl: float = 0.0

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "instrument_name": self.instrument_name,
            "direction": self.direction.value,
            "entry_price": self.entry_price,
            "quantity": self.quantity,
            "entry_time": self.entry_time.isoformat(),
            "target_profit": self.target_profit,
            "stop_loss": self.stop_loss,
            "current_pnl": self.current_pnl,
        }


@dataclass
class SignalTrigger:
    """信号触发记录"""

    signal_type: SignalType
    direction: DirectionType
    confidence: float
    timestamp: datetime
    processed: bool = False


class BranchStrategy(BaseComponent):
    """
    分支策略引擎

    基于因果信号的方向性期权交易策略，集成微观结构信号进行精准出场。
    """

    # 类级别的类型注解（用于依赖注入）
    event_bus: EventBus
    causal_engine: CausalEngine
    microstructure_signals: MicrostructureSignals
    cache_manager: CacheManager
    deribit_client: DeribitClient
    ibkr_client: IBKRClient
    market_data_service: MarketDataService
    # position_manager 和 risk_engine 是可选的，不在依赖注入中自动注入

    # Injected by DI
    config_manager: ConfigManager | None = None

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__(component_name="BranchStrategy", config=config)

        # 配置参数
        self.max_position_size = self.config.get("max_position_size", 0.1)  # BTC
        self.profit_target_ratio = self.config.get(
            "profit_target_ratio", 2.0
        )  # 2倍盈利目标
        self.stop_loss_ratio = self.config.get(
            "stop_loss_ratio", 0.3
        )  # 30%止损（适合加密货币波动性）
        self.signal_confidence_threshold = self.config.get(
            "signal_confidence_threshold", 0.7
        )
        self.max_holding_hours = self.config.get("max_holding_hours", 24)

        # OTM期权选择参数
        self.otm_delta_range = self.config.get(
            "otm_delta_range", (0.15, 0.35)
        )  # Delta范围
        self.min_time_to_expiry_days = self.config.get("min_time_to_expiry_days", 7)
        self.max_time_to_expiry_days = self.config.get("max_time_to_expiry_days", 30)

        # 策略状态
        self._strategy_state = StrategyState.IDLE
        self._current_position: BranchPosition | None = None
        self._signal_triggers: list[SignalTrigger] = []

        # 组件依赖 - 这些将通过依赖注入自动设置
        # self.event_bus, self.causal_engine, self.microstructure_signals 等
        # 已在类级别定义类型注解，将由依赖注入器自动注入

        # BTC 到 IBIT 映射器
        mapping_params = IBITMappingParams(
            a=Decimal("0.5"),  # 回归常数项，需要根据实际数据校准
            b=Decimal("0.0015"),  # 回归斜率，需要根据实际数据校准
            tracking_error_band_bps=300,  # 3% 误差带
        )
        self.btc_to_ibit_mapper = BTCToIBITMapper(mapping_params)

        # 市场时段管理器
        self.market_hours_manager = MarketHoursManager()
        self.market_hours_policy = MarketHoursPolicy(
            self.config.get("market_hours_policy", "queue_until_open")
        )

        # 监控任务
        self._monitoring_task: asyncio.Task | None = None
        self._position_management_task: asyncio.Task | None = None

        # 期权选择服务
        self.option_selector = OptionSelectorService()

        # 性能统计
        self.stats = {
            "total_signals": 0,
            "positions_opened": 0,
            "profitable_exits": 0,
            "stop_loss_exits": 0,
            "structure_breakdown_exits": 0,
            "volatility_strategy_switches": 0,
            "total_pnl": 0.0,
        }

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # Load strategy config (liquidity_proxy etc.) from ConfigManager if available
            try:
                if getattr(self, "config_manager", None):
                    # prefer top-level branch_strategy for runtime tunables
                    bs_cfg = self.config_manager.get("branch_strategy") or {}
                    if bs_cfg:
                        # merge into self.config
                        if isinstance(self.config, dict):
                            self.config.update(bs_cfg)
                        else:
                            self.config = bs_cfg
                        if self.logger:
                            await self.logger.info(
                                f"BranchStrategy loaded config from ConfigManager: keys={list(bs_cfg.keys())}"
                            )
            except Exception:
                # non-fatal
                pass
            # 验证核心依赖组件
            required_deps = [
                self.event_bus,
                self.causal_engine,
                self.microstructure_signals,
                self.cache_manager,
                self.deribit_client,  # 用于数据分析
            ]

            if not all(required_deps):
                if self.logger:
                    await self.logger.error("Missing required dependencies")
                return False

            # PositionManager是可选的，如果没有则只进行信号分析不做仓位管理
            if (
                not hasattr(self, "position_manager") or not self.position_manager
            ) and self.logger:
                await self.logger.warning(
                    "PositionManager not available, running in analysis-only mode"
                )

            # RiskEngine是可选的，如果没有则使用基本风险检查
            if (
                not hasattr(self, "risk_engine") or not self.risk_engine
            ) and self.logger:
                await self.logger.warning(
                    "RiskEngine not available, using basic risk checks"
                )

            # IBKR 客户端是可选的，如果没有则只进行信号分析不执行交易
            if not self.ibkr_client:
                if self.logger:
                    await self.logger.warning(
                        "IBKR client not available, running in analysis-only mode"
                    )
            else:
                if self.logger:
                    await self.logger.info(
                        "IBKR client available, trading mode enabled"
                    )

            # 订阅因果信号事件
            from src.core.event_bus import EventType

            await self.event_bus.subscribe(
                event_types=[EventType.SIGNAL],
                callback=self._handle_causal_signal,
                subscriber_id=f"{self.component_name}_causal_listener",
            )

            if self.logger:
                await self.logger.info("BranchStrategy initialized successfully")

            return True

        except (ConnectionError, TimeoutError) as e:
            if self.logger:
                await self.logger.error(
                    f"Network error during BranchStrategy initialization: {e}"
                )
            return False
        except (ValueError, TypeError) as e:
            if self.logger:
                await self.logger.error(
                    f"Configuration error during BranchStrategy initialization: {e}"
                )
            return False
        except Exception as e:
            if self.logger:
                await self.logger.critical(
                    f"Unexpected error during BranchStrategy initialization: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 启动监控任务
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())
            self._position_management_task = asyncio.create_task(
                self._position_management_loop()
            )

            self._strategy_state = StrategyState.MONITORING

            if self.logger:
                await self.logger.info("BranchStrategy started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start BranchStrategy: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 停止监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._monitoring_task

            if (
                self._position_management_task
                and not self._position_management_task.done()
            ):
                self._position_management_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._position_management_task

            self._strategy_state = StrategyState.IDLE

            if self.logger:
                await self.logger.info("BranchStrategy stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop BranchStrategy: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            details = {
                "strategy_state": self._strategy_state.value,
                "current_position": self._current_position.to_dict()
                if self._current_position
                else None,
                "pending_signals": len(self._signal_triggers),
                "monitoring_task_running": self._monitoring_task
                and not self._monitoring_task.done(),
                "position_management_task_running": self._position_management_task
                and not self._position_management_task.done(),
                "stats": self.stats,
            }

            # 检查依赖组件健康状态
            if not all(
                [
                    self.causal_engine and await self.causal_engine.is_healthy(),
                    self.microstructure_signals
                    and await self.microstructure_signals.is_healthy(),
                ]
            ):
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message="Dependency components are unhealthy",
                    details=details,
                    timestamp=datetime.now(UTC),
                )

            # 检查持仓风险
            if self._current_position:
                holding_time = (
                    datetime.now(UTC) - self._current_position.entry_time
                ).total_seconds() / 3600
                if holding_time > self.max_holding_hours:
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message="Position held too long",
                        details=details,
                        timestamp=datetime.now(UTC),
                    )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="BranchStrategy is healthy",
                details=details,
                timestamp=datetime.now(UTC),
            )

        except (ConnectionError, TimeoutError) as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Network connection issue during health check: {e}",
                details={"error_type": "network", "error": str(e)},
                timestamp=datetime.now(UTC),
            )
        except (AttributeError, KeyError) as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Component configuration issue: {e}",
                details={"error_type": "configuration", "error": str(e)},
                timestamp=datetime.now(UTC),
            )
        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Unexpected health check failure: {e}",
                details={"error_type": "unexpected", "error": str(e)},
                timestamp=datetime.now(UTC),
            )

    def set_dependencies(
        self,
        event_bus: EventBus,
        causal_engine: CausalEngine,
        microstructure_signals: MicrostructureSignals,
        cache_manager: CacheManager,
        deribit_client: DeribitClient,
        market_data_service: MarketDataService,
        position_manager: Any,
    ):
        """设置依赖组件"""
        self.event_bus = event_bus
        self.causal_engine = causal_engine
        self.microstructure_signals = microstructure_signals
        self.cache_manager = cache_manager
        self.deribit_client = deribit_client
        self.market_data_service = market_data_service
        self.position_manager = position_manager

    def set_risk_engine(self, risk_engine):
        """设置风险引擎"""
        self.risk_engine = risk_engine

    async def _publish_trading_decision_event(
        self,
        action: str,
        instruments: list[str],
        decision_data: dict,
        risk_params: dict,
        confidence: float,
    ) -> None:
        """发布交易决策事件（经由 RiskEngine 门控后由 OrderManager 执行）。"""
        try:
            if not self.event_bus:
                return

            from src.core.event_bus import TradingDecisionEvent

            event = TradingDecisionEvent(
                strategy_name=self.component_name,
                action=action,
                instruments=instruments,
                risk_params=risk_params,
                confidence=confidence,
                decision_data=decision_data,
                source=self.component_name,
                timestamp=datetime.now(UTC),
            )

            await self.event_bus.publish(event)

            if self.logger:
                await self.logger.info(
                    f"BranchStrategy 发布TradingDecisionEvent: action={action}, instruments={instruments}, qty={decision_data.get('quantity')}"
                )
        except Exception as e:
            if self.logger:
                await self.logger.error(f"发布TradingDecisionEvent失败: {e}")

    async def _build_ibit_order_for_signal(
        self, signal: SignalTrigger
    ) -> tuple[str, str, int] | None:
        """根据信号构造 IBIT 期权下单信息: (action, instrument_symbol, quantity)。"""
        try:
            # 获取价格
            btc_price = await self._get_btc_price()
            if not btc_price:
                return None

            ibit_price = await self._get_ibit_price()
            if (
                not ibit_price
                and hasattr(self, "btc_to_ibit_mapper")
                and self.btc_to_ibit_mapper
            ):
                # 估算 IBIT 价格
                try:
                    from decimal import Decimal as _D

                    ibit_est = self.btc_to_ibit_mapper.estimate_ibit_price(
                        _D(str(btc_price))
                    )
                    ibit_price = float(ibit_est)
                except Exception:
                    ibit_price = None

            # 计算到期日（近月）
            try:
                from ..utils.option_calendar import option_calendar

                tenor_days = 30
                next_expiry = option_calendar.get_expiry_for_tenor(tenor_days)
            except Exception:
                from datetime import date, timedelta

                next_expiry = datetime.now(UTC).date() + timedelta(days=30)

            # 创建 BTC 侧信号
            side_sig = BTCSideSignal(
                direction=signal.direction.value,
                target_delta=0.3,
                tenor_days=30,
                btc_price=Decimal(str(btc_price)),
            )

            mapping_output = None
            if ibit_price is not None:
                try:
                    mapping_output = self.btc_to_ibit_mapper.map_to_ibit(
                        side_sig, Decimal(str(ibit_price)), next_expiry
                    )
                except Exception:
                    mapping_output = None

            # 支持的简化动作集：LONG_CALL / LONG_PUT / CASH_SECURED_PUT
            action = None
            contract = None
            if mapping_output and getattr(mapping_output, "action", "") in (
                "LONG_CALL",
                "LONG_PUT",
                "CASH_SECURED_PUT",
            ):
                action = mapping_output.action
                contract = getattr(mapping_output, "option", None)

            # 如果映射不可用，则构造一个保守合约
            if contract is None:
                # 兜底：根据估算价格选择 5% OTM Call 或 5% OTM Put
                if ibit_price is None:
                    return None
                from decimal import Decimal as _D

                from ..gateways.ibkr_client import OptionContract, Right

                if signal.direction == DirectionType.BULLISH:
                    strike = (_D(str(ibit_price)) * _D("1.05")).quantize(_D("0.01"))
                    contract = OptionContract(
                        underlying="IBIT",
                        expiry=next_expiry,
                        strike=strike,
                        right=Right.CALL,
                    )
                    action = "LONG_CALL"
                elif signal.direction == DirectionType.BEARISH:
                    strike = (_D(str(ibit_price)) * _D("0.95")).quantize(_D("0.01"))
                    contract = OptionContract(
                        underlying="IBIT",
                        expiry=next_expiry,
                        strike=strike,
                        right=Right.PUT,
                    )
                    action = "LONG_PUT"
                else:
                    return None

            # 格式化为 OrderManager 可识别的符号："IBIT YYYY-MM-DD STRIKE C/P"
            try:
                expiry_str = contract.expiry.isoformat()
            except Exception:
                from datetime import date

                expiry_val = (
                    contract.expiry
                    if isinstance(contract.expiry, date)
                    else datetime.now(UTC).date()
                )
                expiry_str = expiry_val.isoformat()

            right_letter = "C"
            try:
                right_name = getattr(
                    contract.right, "name", str(contract.right)
                ).upper()
                right_letter = (
                    "C" if "CALL" in right_name or right_name.startswith("C") else "P"
                )
            except Exception:
                pass

            strike_str = str(contract.strike)
            instrument_symbol = f"IBIT {expiry_str} {strike_str} {right_letter}"

            # 数量：保守设为1张（由 RiskEngine/Coordinator 统一管理规模）
            qty = 1

            # 将 LONG_* 映射为 buy，CASH_SECURED_PUT 映射为 sell
            if action in ("LONG_CALL", "LONG_PUT"):
                act = "buy"
            elif action in ("SELL_PUT_CSP", "CASH_SECURED_PUT"):
                act = "sell"
            else:
                return None

            return act, instrument_symbol, qty

        except Exception as e:
            if self.logger:
                await self.logger.error(f"构造IBIT订单失败: {e}")
            return None

    async def _handle_causal_signal(self, event: BaseEvent):
        """处理因果信号事件"""
        try:
            # 仅支持标准 SignalEvent
            signal_data = getattr(event, "signal_data", None)
            if not signal_data:
                return

            signal_type = SignalType(signal_data.get("signal_type"))
            # 优先从event.confidence获取，否则从signal_data获取
            confidence = getattr(
                event, "confidence", signal_data.get("confidence", 0.0)
            )

            # 检查信号置信度
            if confidence < self.signal_confidence_threshold:
                return

            # 确定交易方向
            direction = self._determine_direction_from_signal(signal_type, signal_data)
            if direction == DirectionType.NEUTRAL:
                return

            # 融合因果证据：p_value 与预测方向一致性
            adj_confidence = float(confidence)
            try:
                causal_support = signal_data.get("causal_support") or {}
                p_value = causal_support.get("p_value")
                pred_sign = causal_support.get("pred_sign")

                # 若事件中无pred_sign，尝试从因果引擎实时获取
                if (
                    not pred_sign
                    and hasattr(self, "causal_engine")
                    and self.causal_engine
                ):
                    pred_sign = await self.causal_engine.predict_next_return_sign()

                cond_ok = True
                if p_value is not None:
                    from contextlib import suppress

                    with suppress(Exception):
                        cond_ok = cond_ok and float(p_value) < 0.1

                if pred_sign:
                    # 将预测方向映射为 DirectionType
                    pred_dir = (
                        DirectionType.BULLISH
                        if pred_sign == "bullish"
                        else (
                            DirectionType.BEARISH
                            if pred_sign == "bearish"
                            else DirectionType.NEUTRAL
                        )
                    )
                    cond_ok = cond_ok and (pred_dir == direction)

                # 强证据：上调置信度；弱/冲突：降权
                if cond_ok:
                    adj_confidence = min(1.0, adj_confidence * 1.1)
                else:
                    adj_confidence = adj_confidence * 0.8

                # 如果降权后低于阈值则过滤
                if adj_confidence < self.signal_confidence_threshold:
                    return
            except Exception:
                # 任何异常都不阻断原有流程
                pass

            # 检查是否已有持仓
            if self._current_position:
                if self.logger:
                    await self.logger.info(
                        f"Ignoring signal {signal_type.value} - position already open"
                    )
                return

            # 记录信号触发
            trigger = SignalTrigger(
                signal_type=signal_type,
                direction=direction,
                confidence=adj_confidence,
                timestamp=datetime.now(UTC),
            )
            self._signal_triggers.append(trigger)
            self.stats["total_signals"] += 1

            if self.logger:
                await self.logger.info(
                    f"Causal signal received: {signal_type.value}, "
                    f"direction: {direction.value}, confidence: {adj_confidence:.3f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling causal signal: {e}")
            self.metrics.error_count += 1

    def _determine_direction_from_signal(
        self, signal_type: SignalType, signal_data: dict[str, Any]
    ) -> DirectionType:
        """根据信号类型确定交易方向"""
        if signal_type == SignalType.STRUCTURE_DIVERGENCE:
            # 结构背离信号 - 根据背离方向确定
            divergence_direction = signal_data.get("direction", "neutral")
            if divergence_direction == "bullish":
                return DirectionType.BULLISH
            elif divergence_direction == "bearish":
                return DirectionType.BEARISH

        elif signal_type == SignalType.VOLATILITY_MISMATCH:
            # 波动率错配信号 - 根据错配类型确定
            mismatch_type = signal_data.get("mismatch_type", "neutral")
            if mismatch_type == "underpriced":
                return DirectionType.BULLISH  # 波动率被低估，看涨
            elif mismatch_type == "overpriced":
                return DirectionType.BEARISH  # 波动率被高估，看跌

        elif signal_type == SignalType.GAMMA_LIQUIDATION_OVERLAP:
            # Gamma清算重叠信号 - 根据清算方向确定反向交易
            liquidation_direction = signal_data.get("liquidation_direction", "neutral")
            if liquidation_direction == "long_liquidation":
                return DirectionType.BULLISH  # 多头清算后反弹
            elif liquidation_direction == "short_liquidation":
                return DirectionType.BEARISH  # 空头清算后回调

        return DirectionType.NEUTRAL

    async def _monitoring_loop(self):
        """监控循环"""
        while self.is_running:
            try:
                await asyncio.sleep(5)  # 5秒检查一次

                # 处理待处理的信号
                await self._process_pending_signals()

                # 检查微观结构信号
                await self._check_microstructure_signals()

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in monitoring loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(10)

    async def _process_pending_signals(self):
        """处理待处理的信号"""
        if not self._signal_triggers or self._current_position:
            return

        # 获取最新的未处理信号
        unprocessed_signals = [s for s in self._signal_triggers if not s.processed]
        if not unprocessed_signals:
            return

        # 选择置信度最高的信号
        best_signal = max(unprocessed_signals, key=lambda s: s.confidence)

        # 尝试开仓
        success = await self._open_position(best_signal)
        if success:
            best_signal.processed = True
            self._strategy_state = StrategyState.POSITION_OPEN

    async def _open_position(self, signal: SignalTrigger) -> bool:
        """开仓操作"""
        try:
            # 通过 BTC→IBIT 映射构造 IBIT 期权合约符号，并发布 TradingDecisionEvent
            ibit_decision = await self._build_ibit_order_for_signal(signal)
            if not ibit_decision:
                if self.logger:
                    await self.logger.warning(
                        "IBIT mapping failed or unavailable; skip entry"
                    )
                return False

            action, instrument_symbol, qty = ibit_decision

            decision_data = {
                "quantity": qty,
                "reason": "causal_entry",
                "signal_type": signal.signal_type.value
                if hasattr(signal, "signal_type")
                else "causal",
                "direction": signal.direction.value,
            }
            # 注入 Deribit 流动性代理（不依赖 IBKR 行情）
            try:
                tenor_days = 30
                opt_type = "call" if action == "buy" else "put"
                mds = await self.dependency_injector.resolve(MarketDataService)
                from src.services.liquidity_service import build_deribit_liquidity_proxy

                proxy = await build_deribit_liquidity_proxy(
                    mds=mds,
                    tenor_days=tenor_days,
                    option_type=opt_type,
                    config=getattr(self, "config", {}).get("liquidity_proxy", {}),
                    spot_price=None,
                )
                if proxy:
                    decision_data["liquidity_proxy"] = proxy
            except Exception:
                pass

            # 风控参数提示：统一由 RiskEngine/Coordinator 进行比例约束
            risk_params = {
                "strategy_role": "branch",
                "max_position_size": float(self.max_position_size),
            }

            await self._publish_trading_decision_event(
                action=action,
                instruments=[instrument_symbol],
                decision_data=decision_data,
                risk_params=risk_params,
                confidence=float(signal.confidence),
            )

            # 本地标记（可选）：记录计划中的持仓，用于后续微观结构出场参考
            try:
                self._current_position = BranchPosition(
                    instrument_name=instrument_symbol,
                    direction=signal.direction,
                    entry_price=0.0,
                    quantity=float(qty),
                    entry_time=datetime.now(UTC),
                    target_profit=0.0,
                    stop_loss=0.0,
                )
                self._strategy_state = StrategyState.POSITION_OPEN
                self.stats["positions_opened"] += 1
            except Exception:
                pass

            return True

        except (ConnectionError, TimeoutError) as e:
            if self.logger:
                await self.logger.error(f"Network error opening position: {e}")
            self.metrics.error_count += 1
            return False
        except (ValueError, TypeError) as e:
            if self.logger:
                await self.logger.error(f"Invalid data error opening position: {e}")
            self.metrics.error_count += 1
            return False

        # NOTE: liquidity proxy logic moved to src/services/liquidity_service.py
        except Exception as e:
            if self.logger:
                await self.logger.critical(f"Unexpected error opening position: {e}")
            self.metrics.error_count += 1
            return False

    async def _plan_and_execute_ibit_option_trade(
        self,
        signal: SignalTrigger,
        target_profit: float,
        stop_loss: float,
    ) -> bool:
        """计划并执行 IBIT 期权交易（映射→时段策略→预交易风控→执行）"""
        try:
            market_analysis = type("MarketAnalysis", (), {"volatility": 0.5})()
            btc_price = await self._get_btc_price()
            if not btc_price:
                if self.logger:
                    await self.logger.error("Failed to get BTC price for mapping")
                return False

            btc_side_signal = BTCSideSignal(
                direction=signal.direction.value,
                target_delta=0.3,
                tenor_days=30,
                btc_price=Decimal(str(btc_price)),
            )

            ibit_price = await self._get_ibit_price()
            if not ibit_price:
                if self.logger:
                    await self.logger.error("Failed to get IBIT price for mapping")
                return False

            from ..utils.option_calendar import option_calendar

            tenor_days = 30
            next_expiry = option_calendar.get_expiry_for_tenor(tenor_days)

            mapping_output = self.btc_to_ibit_mapper.map_to_ibit(
                btc_side_signal, Decimal(str(ibit_price)), next_expiry
            )

            if mapping_output.action == "SKIP":
                if self.logger:
                    await self.logger.warning(
                        "Mapping suggests skipping trade due to tracking error"
                    )
                return False

            if (
                mapping_output.action in ["LONG_CALL", "SELL_PUT_CSP"]
                and mapping_output.option
            ):
                trading_allowed = self.market_hours_manager.is_trading_allowed(
                    self.market_hours_policy
                )
                if not trading_allowed:
                    current_session = self.market_hours_manager.get_current_session()
                    if self.market_hours_policy == MarketHoursPolicy.QUEUE_UNTIL_OPEN:
                        if self.logger:
                            await self.logger.info(
                                f"Market closed ({current_session.value}), queueing IBIT option trade until open"
                            )
                        queued_order = {
                            "order_type": "ibit_option",
                            "mapping_output": mapping_output,
                            "btc_price": btc_price,
                            "market_analysis": market_analysis,
                            "target_profit": target_profit,
                            "stop_loss": stop_loss,
                            "direction": signal.direction,
                            "timestamp": datetime.now(UTC),
                            "retry_count": 0,
                        }
                        await self._queue_order_for_market_open(queued_order)
                        return False
                    else:
                        if self.logger:
                            await self.logger.warning(
                                f"IBIT option trade rejected: market closed ({current_session.value})"
                            )
                        return False

                pretrade_result = await self._pretrade_check_ibit_option(
                    mapping_output, ibit_price
                )
                if not pretrade_result.get("ok"):
                    if self.logger:
                        await self.logger.warning(
                            f"IBIT option trade rejected by pretrade filters: {pretrade_result['reasons']}"
                        )
                    return False

                return await self._execute_ibit_option_core(
                    mapping_output=mapping_output,
                    underlying_btc_price=float(btc_price),
                    market_analysis=market_analysis,
                    target_profit=target_profit,
                    stop_loss=stop_loss,
                    direction=signal.direction,
                    apply_pretrade=False,
                )

            return False
        except Exception as e:
            if self.logger:
                await self.logger.error(f"IBIT option trade execution failed: {e}")
            return False

    async def _execute_ibit_option_core(
        self,
        mapping_output,
        underlying_btc_price: float,
        market_analysis,
        target_profit: float,
        stop_loss: float,
        direction,
        apply_pretrade: bool = True,
    ) -> bool:
        """核心执行：可选风控→下单→记录仓位"""
        try:
            from ..gateways.ibkr_client import OrderSide, OrderType

            if apply_pretrade:
                ibit_price = await self._get_ibit_price()
                if not ibit_price:
                    if self.logger:
                        await self.logger.error(
                            "Failed to get IBIT price for pretrade check"
                        )
                    return False
                pretrade_result = await self._pretrade_check_ibit_option(
                    mapping_output, ibit_price
                )
                if not pretrade_result.get("ok"):
                    if self.logger:
                        await self.logger.warning(
                            f"IBIT option trade rejected by pretrade filters: {pretrade_result['reasons']}"
                        )
                    return False

            side = (
                OrderSide.BUY
                if mapping_output.action == "LONG_CALL"
                else OrderSide.SELL
            )
            order_qty = await self._calculate_option_quantity(
                option_price=mapping_output.fair_value,
                underlying_price=float(underlying_btc_price),
                market_analysis=market_analysis,
            )

            order_result = await self.ibkr_client.place_order(
                contract=mapping_output.option,
                side=side,
                qty=order_qty,
                order_type=OrderType.MARKET,
            )

            if (
                order_result
                and hasattr(order_result, "order_id")
                and order_result.order_id
            ):
                self._current_position = BranchPosition(
                    instrument_name=f"IBIT-{mapping_output.option.strike}-{mapping_output.option.right.value}",
                    direction=direction,
                    entry_price=0.0,
                    quantity=order_qty,
                    entry_time=datetime.now(UTC),
                    target_profit=target_profit,
                    stop_loss=stop_loss,
                )
                if self.logger:
                    await self.logger.info(
                        f"IBIT option trade executed: {mapping_output.action} "
                        f"IBIT-{mapping_output.option.strike}-{mapping_output.option.right.value}"
                    )
                return True

            return False
        except Exception as e:
            if self.logger:
                await self.logger.error(f"IBIT option core execution failed: {e}")
            return False

    async def _get_ibit_price(self) -> float | None:
        """获取 IBIT 价格（信号侧不依赖 IBKR 行情）"""
        try:
            # 优先从 MarketDataService 获取缓存
            if self.dependency_injector:
                try:
                    mds = await self.dependency_injector.resolve(MarketDataService)
                    val = await mds.get_ibit_price(prefer_realtime=False)
                    if val is not None:
                        return float(val)
                except Exception:
                    pass
            # 回退映射
            btc_price = await self._get_btc_price()
            if btc_price and self.btc_to_ibit_mapper:
                from decimal import Decimal as _D

                est = self.btc_to_ibit_mapper.estimate_ibit_price(_D(str(btc_price)))
                if self.logger:
                    await self.logger.info(f"Estimated IBIT price via mapping: {est}")
                return float(est)
            return None
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error estimating IBIT price: {e}")
            return None

    async def _pretrade_check_ibit_option(
        self, mapping_output, ibit_price: float
    ) -> dict[str, Any]:
        """IBIT 期权预交易检查"""
        try:
            if not mapping_output.option:
                return {"ok": False, "reasons": ["No option contract"]}

            # 获取期权链数据进行流动性检查
            option_chain = await self.ibkr_client.get_option_chain("IBIT")
            if not option_chain:
                if self.logger:
                    await self.logger.warning(
                        "Cannot get option chain for liquidity check, proceeding with caution"
                    )
                # 在无法获取期权链时，使用基本检查
                return await self._basic_pretrade_check(mapping_output, ibit_price)

            # 查找匹配的期权合约
            matching_option = None
            for option in option_chain.get("options", []):
                if (
                    option["strike"] == float(mapping_output.option.strike)
                    and option["right"] == mapping_output.option.right.value
                ):
                    matching_option = option
                    break

            if not matching_option:
                return {"ok": False, "reasons": ["Option not found in chain"]}

            # 计算价差（基点）
            bid = matching_option.get("bid", 0)
            ask = matching_option.get("ask", 0)
            mid_price = (
                (bid + ask) / 2
                if bid > 0 and ask > 0
                else matching_option.get("price", 0)
            )

            spread_bps = None
            if mid_price > 0 and ask > bid:
                spread_bps = ((ask - bid) / mid_price) * 10000

            # 获取流动性数据
            volume = matching_option.get("volume", 0)
            open_interest = matching_option.get("open_interest", 0)

            # 计算所需资金（对于CSP）
            buying_power = None
            if mapping_output.action == "SELL_PUT_CSP":
                # CSP需要的现金担保 = 执行价 * 100 * 数量
                buying_power = float(mapping_output.option.strike) * 100 * 1

            # 计算到期天数
            from datetime import date

            days_to_expiry = (mapping_output.option.expiry - date.today()).days

            # 执行预交易检查
            if hasattr(self, "position_manager") and self.position_manager:
                return await self.position_manager.pretrade_check_ibit(
                    action=mapping_output.action,
                    strike=mapping_output.option.strike,
                    qty=1,
                    spread_bps=spread_bps,
                    oi=open_interest,
                    volume=volume,
                    buying_power=Decimal(str(buying_power)) if buying_power else None,
                    days_to_expiry=days_to_expiry,
                    iv=matching_option.get("iv"),
                )
            else:
                # 如果没有position_manager，使用基本检查
                return await self._basic_pretrade_check(mapping_output, ibit_price)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Pretrade check failed: {e}")
            return {"ok": False, "reasons": [f"Check failed: {str(e)}"]}

    async def _basic_pretrade_check(
        self, mapping_output, ibit_price: float
    ) -> dict[str, Any]:
        """基本的预交易检查（当无法获取详细数据时）"""
        reasons = []

        # 基本合理性检查
        if not mapping_output.option:
            reasons.append("No option contract")

        # 检查执行价是否合理（不能偏离现价太远）
        if mapping_output.option:
            strike = float(mapping_output.option.strike)
            price_diff_pct = abs(strike - ibit_price) / ibit_price

            if price_diff_pct > 0.5:  # 执行价偏离现价超过50%
                reasons.append(f"Strike too far from spot: {price_diff_pct:.1%}")

        # 检查到期日是否合理
        if mapping_output.option:
            from datetime import date

            days_to_expiry = (mapping_output.option.expiry - date.today()).days

            if days_to_expiry < 1:
                reasons.append("Option expires too soon")
            elif days_to_expiry > 365:
                reasons.append("Option expires too far in future")

        return {"ok": len(reasons) == 0, "reasons": reasons}

    async def _select_otm_option(
        self, direction: DirectionType
    ) -> dict[str, Any] | None:
        """选择合适的OTM期权"""
        try:
            # 获取期权链数据
            option_chain = await self.deribit_client.get_option_chain("BTC")
            if not option_chain:
                return None

            # 确定期权类型
            option_type = "call" if direction == DirectionType.BULLISH else "put"

            # 创建选择标准
            target_delta = sum(self.otm_delta_range) / 2
            criteria = OptionSelectionCriteria(
                option_type=option_type,
                min_expiry_days=self.min_time_to_expiry_days,
                max_expiry_days=self.max_time_to_expiry_days,
                min_delta=self.otm_delta_range[0],
                max_delta=self.otm_delta_range[1],
                target_delta=target_delta,
                min_bid=Decimal("0.01"),
                min_ask=Decimal("0.01"),
            )

            # 使用统一的期权选择服务
            best_option = await self.option_selector.select_best_option(
                option_chain, criteria
            )
            return best_option

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error selecting OTM option: {e}")
            return None

    async def _position_management_loop(self):
        """持仓管理循环"""
        while self.is_running:
            try:
                await asyncio.sleep(3)  # 3秒检查一次持仓

                if not self._current_position:
                    continue

                # 更新持仓PnL
                await self._update_position_pnl()

                # 检查出场条件
                exit_reason = await self._check_exit_conditions()
                if exit_reason:
                    await self._close_position(exit_reason)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in position management loop: {e}")
                self.metrics.error_count += 1
                await asyncio.sleep(10)

    async def _update_position_pnl(self):
        """更新持仓PnL"""
        if not self._current_position:
            return

        try:
            current_price = await self._get_option_price(
                self._current_position.instrument_name
            )
            if current_price:
                entry_price = self._current_position.entry_price
                quantity = self._current_position.quantity
                self._current_position.current_pnl = (
                    current_price - entry_price
                ) * quantity

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error updating position PnL: {e}")

    async def _check_exit_conditions(self) -> str | None:
        """检查出场条件"""
        if not self._current_position:
            return None

        try:
            current_price = await self._get_option_price(
                self._current_position.instrument_name
            )
            if not current_price:
                return None

            # 1. 止盈检查
            if current_price >= self._current_position.target_profit:
                return "profit_target"

            # 2. 止损检查
            if current_price <= self._current_position.stop_loss:
                return "stop_loss"

            # 3. 微观结构信号检查
            if await self.microstructure_signals.should_exit_due_to_structure_breakdown():
                return "structure_breakdown"

            # 4. 时间止损检查
            holding_time = (
                datetime.now(UTC) - self._current_position.entry_time
            ).total_seconds() / 3600
            if holding_time > self.max_holding_hours:
                return "time_stop"

            # 5. 趋势衰竭检查 - 切换到波动率策略
            if await self.microstructure_signals.should_switch_to_volatility_strategy():
                return "switch_to_volatility"

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking exit conditions: {e}")
            return None

    async def _close_position(self, reason: str):
        """平仓操作"""
        if not self._current_position:
            return

        try:
            # 检查是否是 IBIT 期权持仓
            if (
                self._current_position.instrument_name.startswith("IBIT-")
                and self.ibkr_client
            ):
                # 执行 IBIT 期权平仓
                success = await self._close_ibit_position(reason)
                if success:
                    return

            # 如果没有IBKR客户端或IBIT期权系统，记录错误
            if self.logger:
                await self.logger.error(
                    f"Cannot execute position closure: IBKR client unavailable. "
                    f"Position: {self._current_position.instrument_name}"
                )
                await self.logger.error(
                    "All executions must be done through IBIT options via IBKR"
                )
            return

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error closing position: {e}")
            self.metrics.error_count += 1

    async def _close_ibit_position(self, reason: str) -> bool:
        """平仓 IBIT 期权持仓（统一走 TradingDecisionEvent → RiskEngine → OrderManager）"""
        try:
            if not self._current_position:
                return False

            # 统一使用 OrderManager 识别格式："IBIT YYYY-MM-DD STRIKE C/P"
            instrument_symbol = self._current_position.instrument_name
            # 若为旧格式 IBIT-STRIKE-CALL/PUT，尝试转换（无到期信息则回退原样）
            if " " not in instrument_symbol and instrument_symbol.count("-") == 2:
                try:
                    underlying, strike, right = instrument_symbol.split("-")
                    right_letter = (
                        "C"
                        if right.upper().startswith("CALL")
                        or right.upper().startswith("C")
                        else "P"
                    )
                    from datetime import date

                    expiry_str = date.today().isoformat()  # 近似回退（到期未知）
                    instrument_symbol = (
                        f"{underlying} {expiry_str} {strike} {right_letter}"
                    )
                except Exception:
                    pass

            # 决策事件（反向动作：平仓卖出）
            action = "sell"
            qty = (
                int(self._current_position.quantity)
                if self._current_position.quantity
                else 1
            )

            decision_data = {
                "quantity": qty,
                "reason": f"close_{reason}",
                "close": True,
            }
            risk_params = {"strategy_role": "branch"}

            await self._publish_trading_decision_event(
                action=action,
                instruments=[instrument_symbol],
                decision_data=decision_data,
                risk_params=risk_params,
                confidence=0.95,  # 退出通常高度确定
            )

            # 更新本地状态（实际清算由 PositionManager/OrderManager 驱动）
            if reason == "profit_target":
                self.stats["profitable_exits"] += 1
            elif reason == "stop_loss":
                self.stats["stop_loss_exits"] += 1
            elif reason == "structure_breakdown":
                self.stats["structure_breakdown_exits"] += 1
            elif reason == "switch_to_volatility":
                self.stats["volatility_strategy_switches"] += 1
                await self._trigger_volatility_strategy_switch()

            # 清除本地持仓标记
            self._current_position = None
            self._strategy_state = StrategyState.IDLE
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error closing IBIT position: {e}")
            return False

    async def _trigger_volatility_strategy_switch(self):
        """触发切换到波动率策略"""
        try:
            # 发布切换事件给策略协调器
            switch_event = BaseEvent(
                event_type=EventType.STRATEGY_SWITCH_REQUEST,
                source=self.component_name,
                data={
                    "from_strategy": "branch",
                    "to_strategy": "volatility",
                    "reason": "trend_exhaustion",
                    "timestamp": datetime.now(UTC).isoformat(),
                },
            )

            if self.event_bus:
                await self.event_bus.publish(switch_event)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error triggering volatility strategy switch: {e}"
                )

    async def _check_microstructure_signals(self):
        """检查微观结构信号"""
        try:
            current_signal = await self.microstructure_signals.get_current_signal()
            if not current_signal:
                return

            # 如果没有持仓且趋势状态健康，可以考虑新的入场机会
            if (
                not self._current_position
                and current_signal.trend_status == TrendStatus.HEALTHY
            ):
                # 基于微观结构信号的额外入场逻辑
                try:
                    # 检查市场流动性条件
                    if current_signal.confidence > 0.7:  # 高置信度信号
                        # 检查当前市场状态是否适合入场
                        market_conditions = await self._assess_market_conditions()

                        if market_conditions.get("suitable_for_entry", False):
                            # 触发新的策略入场评估
                            if self.logger:
                                await self.logger.info(
                                    f"Microstructure signal suggests entry opportunity: "
                                    f"confidence={current_signal.confidence:.3f}"
                                )

                            # 可以设置标志位，在下次循环中评估入场
                            self._pending_entry_signal = {
                                "timestamp": datetime.now(UTC),
                                "signal": current_signal,
                                "trigger": "microstructure_healthy",
                            }

                except Exception as signal_error:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to process entry signal: {signal_error}"
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking microstructure signals: {e}")

    # 辅助方法

    async def _get_btc_price(self) -> float | None:
        """获取BTC当前价格"""
        try:
            if self.market_data_service:
                price_decimal = await self.market_data_service.get_btc_price()
                return float(price_decimal) if price_decimal else None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting BTC price: {e}")
            return None

    async def _get_option_price(self, instrument_name: str) -> float | None:
        """获取期权当前价格"""
        try:
            ticker = await self.deribit_client.get_ticker(instrument_name)
            if ticker:
                bid = ticker.get("bid_price", 0)
                ask = ticker.get("ask_price", 0)
                if bid > 0 and ask > 0:
                    return (bid + ask) / 2
            return None
        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error getting option price for {instrument_name}: {e}"
                )
            return None

    async def _calculate_position_size(
        self,
        instrument: dict[str, Any],
        signal_strength: float = 0.8,
        signal_confidence: float = 0.7,
    ) -> float:
        """智能仓位计算 - 基于凯利公式和风险调整的动态仓位管理"""
        try:
            # 基础参数获取
            option_price = await self._get_option_price(instrument["instrument_name"])
            if not option_price or option_price <= 0:
                return 0.0

            # 1. 基础仓位限制检查
            max_contracts_by_capital = self.max_position_size / option_price

            # 2. 风险引擎验证（如果可用）
            risk_multiplier = 1.0
            if hasattr(self, "risk_engine") and self.risk_engine:
                try:
                    # 获取当前风险指标
                    risk_metrics = await self.risk_engine.get_current_risk_metrics()
                    if risk_metrics:
                        # 根据保证金使用率调整
                        margin_usage = float(risk_metrics.margin_usage_ratio)
                        if margin_usage > 0.7:
                            risk_multiplier *= 0.5  # 高保证金使用率下减半
                        elif margin_usage > 0.5:
                            risk_multiplier *= 0.75  # 中等保证金使用率下减少25%

                        # 根据流动性评分调整
                        liquidity_score = risk_metrics.liquidity_score
                        if liquidity_score < 0.3:
                            risk_multiplier *= 0.6  # 低流动性下显著减仓
                        elif liquidity_score < 0.5:
                            risk_multiplier *= 0.8  # 中低流动性下适度减仓

                        if self.logger:
                            await self.logger.debug(
                                f"Risk adjustment: margin={margin_usage:.2%}, liquidity={liquidity_score:.2f}, multiplier={risk_multiplier:.2f}"
                            )
                except Exception as risk_error:
                    if self.logger:
                        await self.logger.warning(
                            f"Risk engine check failed: {risk_error}"
                        )

            # 3. 凯利公式仓位计算
            kelly_fraction = self._calculate_kelly_position_size(
                signal_strength, signal_confidence, option_price
            )

            # 4. 综合仓位大小计算
            # 取较小值：资金限制 vs 凯利公式建议
            optimal_contracts = min(max_contracts_by_capital, kelly_fraction)

            # 5. 信号质量调整
            signal_quality_multiplier = (
                signal_strength * 0.6 + signal_confidence * 0.4
            )  # 综合信号质量
            quality_adjusted_size = optimal_contracts * signal_quality_multiplier

            # 6. 风险调整
            final_size = quality_adjusted_size * risk_multiplier

            # 7. 最小单位和最终边界检查
            min_contract_size = instrument.get("min_trade_amount", 0.1)
            max_single_position = (
                max_contracts_by_capital * 0.5
            )  # 单仓位不超过资金限制的50%

            adjusted_size = max(min_contract_size, min(max_single_position, final_size))

            if self.logger:
                await self.logger.info(
                    f"Position sizing: base={max_contracts_by_capital:.2f}, "
                    f"kelly={kelly_fraction:.2f}, quality={signal_quality_multiplier:.2f}, "
                    f"risk={risk_multiplier:.2f}, final={adjusted_size:.2f}"
                )

            return round(adjusted_size, 2)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating position size: {e}")
            return 0.0

    def _calculate_kelly_position_size(
        self, signal_strength: float, signal_confidence: float, option_price: float
    ) -> float:
        """基于凯利公式计算最优仓位大小"""
        try:
            # 胜率估计 (基于信号置信度和强度)
            win_probability = min(
                0.85, signal_confidence * 0.7 + signal_strength * 0.3
            )  # 最高不超过85%

            # 赔率估计 (期权特性：有限损失，无限收益潜力)
            # 基于期权价格和目标收益比计算
            potential_gain_ratio = self.profit_target_ratio  # 来自配置的盈利目标倍数
            potential_loss_ratio = 1.0  # 期权最大损失是权利金

            # 调整后的赔率 (考虑期权时间价值衰减)
            adjusted_odds = (
                potential_gain_ratio / potential_loss_ratio * 0.7
            )  # 70%折扣考虑时间价值

            # 凯利公式: f = (bp - q) / b
            # 其中: b = 赔率, p = 胜率, q = 败率 = 1-p
            lose_probability = 1 - win_probability
            kelly_fraction_raw = (
                adjusted_odds * win_probability - lose_probability
            ) / adjusted_odds

            # 保守调整 (使用Kelly分数的25%以降低方差)
            kelly_fraction = max(0, kelly_fraction_raw * 0.25)

            # 转换为合约数量 (基于总资金)
            total_available_capital = (
                self.max_position_size * 5
            )  # 假设max_position_size是总资金的20%
            kelly_contracts = (kelly_fraction * total_available_capital) / option_price

            return kelly_contracts

        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                asyncio.create_task(
                    self.logger.warning(f"Kelly calculation failed: {e}")
                )
            return 0.0

    async def _validate_pre_trade_risk(self, trade_data: dict[str, Any]) -> bool:
        """预交易风险验证"""
        try:
            # 1. RiskEngine验证（如果可用）
            if hasattr(self, "risk_engine") and self.risk_engine:
                try:
                    risk_validation_result = await self.risk_engine.validate_order_risk(
                        trade_data
                    )
                    if not risk_validation_result[0]:  # (is_valid, message)
                        if self.logger:
                            await self.logger.warning(
                                f"RiskEngine validation failed: {risk_validation_result[1]}"
                            )
                        return False
                except Exception as risk_error:
                    if self.logger:
                        await self.logger.warning(
                            f"RiskEngine validation error: {risk_error}"
                        )
                    # 继续执行基本风险检查

            # 2. 基本风险检查
            instrument = trade_data.get("instrument", {})
            signal = trade_data.get("signal")

            # 检查信号置信度
            if signal and signal.confidence < self.signal_confidence_threshold:
                if self.logger:
                    await self.logger.info(
                        f"Signal confidence too low: {signal.confidence:.2f} < {self.signal_confidence_threshold}"
                    )
                return False

            # 3. 市场条件检查
            market_conditions = await self._analyze_market_conditions()
            if not market_conditions.get("suitable_for_entry", True):
                if self.logger:
                    await self.logger.warning(
                        f"Market conditions unfavorable: {market_conditions.get('reason', 'unknown')}"
                    )
                return False

            # 4. 仓位限制检查
            if self._current_position:
                if self.logger:
                    await self.logger.info(
                        "Already have an open position, skipping new entry"
                    )
                return False

            # 5. 期权合理性检查
            if instrument:
                option_price = await self._get_option_price(
                    instrument["instrument_name"]
                )
                if not option_price or option_price <= 0:
                    if self.logger:
                        await self.logger.warning(
                            "Invalid option price for risk validation"
                        )
                    return False

                # 检查期权是否过于深度价内/价外
                greeks = instrument.get("greeks", {})
                delta = abs(greeks.get("delta", 0))
                if delta < 0.1 or delta > 0.9:  # 过度价外或价内
                    if self.logger:
                        await self.logger.warning(
                            f"Option delta out of reasonable range: {delta}"
                        )
                    return False

            # 6. 流动性检查
            liquidity_check = await self._check_liquidity_conditions(instrument)
            if not liquidity_check:
                if self.logger:
                    await self.logger.warning("Liquidity conditions insufficient")
                return False

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Pre-trade risk validation error: {e}")
            return False  # 保守处理：验证失败则拒绝交易

    async def _check_liquidity_conditions(self, instrument: dict[str, Any]) -> bool:
        """检查流动性条件"""
        try:
            if not instrument:
                return False

            # 检查买卖价差
            bid_price = instrument.get("bid_price", 0)
            ask_price = instrument.get("ask_price", 0)

            if bid_price > 0 and ask_price > 0:
                spread_ratio = (ask_price - bid_price) / ((ask_price + bid_price) / 2)
                if spread_ratio > 0.15:  # 买卖价差超过15%
                    return False

            # 检查成交量和持仓量（如果有数据）
            volume = instrument.get("volume", 0)
            open_interest = instrument.get("open_interest", 0)

            return not (volume == 0 and open_interest == 0)  # 有流动性则返回True

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Liquidity check failed: {e}")
            return False

    # 公共接口方法

    async def get_current_position(self) -> BranchPosition | None:
        """获取当前持仓"""
        return self._current_position

    async def get_strategy_stats(self) -> dict[str, Any]:
        """获取策略统计"""
        stats = self.stats.copy()
        stats["strategy_state"] = self._strategy_state.value
        stats["current_position"] = (
            self._current_position.to_dict() if self._current_position else None
        )
        stats["pending_signals"] = len(
            [s for s in self._signal_triggers if not s.processed]
        )

        # 计算胜率
        total_exits = (
            stats["profitable_exits"]
            + stats["stop_loss_exits"]
            + stats["structure_breakdown_exits"]
        )
        stats["win_rate"] = (
            stats["profitable_exits"] / total_exits if total_exits > 0 else 0.0
        )

        return stats

    async def force_close_position(self, reason: str = "manual") -> bool:
        """强制平仓"""
        if not self._current_position:
            return False

        await self._close_position(reason)
        return True

    async def _calculate_option_quantity(
        self, option_price: float, underlying_price: float, market_analysis: Any
    ) -> int:
        """计算期权订单数量，基于风险管理和资金管理原则"""
        try:
            # 获取真实账户价值
            account_value = await self._get_real_account_value()

            # 风险参数配置
            max_risk_per_trade = 0.05  # 单笔交易最大风险5%（适合加密货币波动性）
            max_position_value = account_value * max_risk_per_trade

            # 基于期权价格计算最大数量
            if option_price <= 0:
                return 1  # 最小数量

            max_qty_by_value = int(max_position_value / option_price)
            max_qty_by_value = max(1, min(max_qty_by_value, 10))  # 限制在1-10之间

            # 基于波动率调整数量
            if hasattr(market_analysis, "volatility") and market_analysis.volatility:
                volatility = getattr(market_analysis, "volatility", 0.5)
                # 高波动率时减少数量
                volatility_adjustment = max(0.5, 1.0 - (volatility - 0.3) * 2)
                max_qty_by_value = int(max_qty_by_value * volatility_adjustment)

            # 基于资金管理的最终数量
            final_qty = max(1, min(max_qty_by_value, 5))  # 保守策略：最多5张

            if hasattr(self, "logger") and self.logger:
                await self.logger.info(
                    f"Calculated option quantity: {final_qty} "
                    f"(option_price=${option_price:.2f}, max_value=${max_position_value:.0f})"
                )

            return final_qty

        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.error(f"Error calculating option quantity: {e}")
            return 1  # 发生错误时返回最小数量

    async def _queue_order_for_market_open(self, order_data: dict):
        """将订单加入市场开盘队列"""
        try:
            # 初始化订单队列（如果不存在）
            if not hasattr(self, "_pending_orders_queue"):
                self._pending_orders_queue = []

            # 添加到队列
            self._pending_orders_queue.append(order_data)

            # 限制队列大小，防止内存泄漏
            max_queue_size = 100
            if len(self._pending_orders_queue) > max_queue_size:
                # 移除最老的订单
                removed_order = self._pending_orders_queue.pop(0)
                if hasattr(self, "logger") and self.logger:
                    await self.logger.warning(
                        f"Removed old queued order due to queue size limit: {removed_order['order_type']}"
                    )

            if hasattr(self, "logger") and self.logger:
                await self.logger.info(
                    f"Queued {order_data['order_type']} order for market open. Queue size: {len(self._pending_orders_queue)}"
                )

            # 启动市场开盘监控任务（如果尚未启动）
            await self._start_market_open_monitor()

        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.error(f"Failed to queue order: {e}")

    async def _start_market_open_monitor(self):
        """启动市场开盘监控任务"""
        try:
            # 检查是否已有监控任务在运行
            if (
                hasattr(self, "_market_monitor_task")
                and not self._market_monitor_task.done()
            ):
                return

            # 创建并启动监控任务
            self._market_monitor_task = asyncio.create_task(
                self._market_open_monitor_loop()
            )

        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.error(f"Failed to start market open monitor: {e}")

    async def _market_open_monitor_loop(self):
        """市场开盘监控循环"""
        try:
            while hasattr(self, "_pending_orders_queue") and self._pending_orders_queue:
                # 检查市场状态
                try:
                    from src.utils.market_hours import MarketHoursChecker

                    market_checker = MarketHoursChecker()
                    current_session = market_checker.get_current_session()

                    # 如果市场开盘，处理队列中的订单
                    if current_session.name in ["REGULAR", "EXTENDED"]:
                        if hasattr(self, "logger") and self.logger:
                            await self.logger.info(
                                f"Market is open ({current_session.value}), processing {len(self._pending_orders_queue)} queued orders"
                            )

                        # 处理队列中的所有订单
                        orders_to_process = self._pending_orders_queue.copy()
                        self._pending_orders_queue.clear()

                        for order_data in orders_to_process:
                            try:
                                await self._process_queued_order(order_data)
                                # 在订单之间添加小延迟，避免过快执行
                                await asyncio.sleep(1)
                            except Exception as order_error:
                                if hasattr(self, "logger") and self.logger:
                                    await self.logger.error(
                                        f"Failed to process queued order: {order_error}"
                                    )

                        break  # 处理完成后退出监控循环

                except Exception as check_error:
                    if hasattr(self, "logger") and self.logger:
                        await self.logger.warning(
                            f"Market status check failed: {check_error}"
                        )

                # 每5分钟检查一次市场状态
                await asyncio.sleep(300)

        except asyncio.CancelledError:
            if hasattr(self, "logger") and self.logger:
                await self.logger.info("Market open monitor cancelled")
        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.error(f"Market open monitor error: {e}")

    async def _process_queued_order(self, order_data: dict):
        """处理队列中的订单"""
        try:
            order_type = order_data["order_type"]

            if order_type == "ibit_option":
                # 重新执行IBIT期权交易
                mapping_output = order_data["mapping_output"]
                btc_price = order_data["btc_price"]
                market_analysis = order_data["market_analysis"]

                # 重新获取当前价格（因为队列中的价格可能已过时）
                try:
                    current_btc_price = await self._get_current_btc_price()
                    if current_btc_price:
                        btc_price = current_btc_price
                except Exception:
                    pass  # 使用原价格

                # 执行交易
                success = await self._execute_queued_ibit_option_trade(
                    mapping_output=mapping_output,
                    btc_price=btc_price,
                    market_analysis=market_analysis,
                    target_profit=order_data.get("target_profit"),
                    stop_loss=order_data.get("stop_loss"),
                    direction=order_data.get("direction"),
                )

                if hasattr(self, "logger") and self.logger:
                    status = "succeeded" if success else "failed"
                    await self.logger.info(f"Queued IBIT option order {status}")

        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.error(f"Failed to process queued order: {e}")

    async def _execute_queued_ibit_option_trade(
        self,
        mapping_output,
        btc_price,
        market_analysis,
        target_profit: float | None = None,
        stop_loss: float | None = None,
        direction: DirectionType | None = None,
    ) -> bool:
        """执行IBIT期权交易（从队列处理中调用，保留风控与仓位记录）"""
        try:
            # 队列执行也保留风控，并记录仓位
            return await self._execute_ibit_option_core(
                mapping_output=mapping_output,
                underlying_btc_price=float(btc_price),
                market_analysis=market_analysis,
                target_profit=target_profit if target_profit is not None else 0.0,
                stop_loss=stop_loss if stop_loss is not None else 0.0,
                direction=direction
                if direction is not None
                else getattr(self, "_last_signal_direction", None),
                apply_pretrade=True,
            )
        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.error(
                    f"IBIT queued option trade execution failed: {e}"
                )
            return False

    # 新增缺失的辅助方法

    async def _analyze_market_conditions(self) -> dict[str, Any]:
        """分析市场条件 - 用于风险验证"""
        return await self._assess_market_conditions()

    async def _assess_market_conditions(self) -> dict[str, Any]:
        """评估市场条件"""
        try:
            conditions = {
                "suitable_for_entry": True,
                "volatility_level": "normal",
                "liquidity_adequate": True,
                "market_session": "active",
                "confidence": 0.5,
            }

            # 检查市场时间
            from ..utils.market_hours import is_market_open

            if not is_market_open():
                conditions["suitable_for_entry"] = False
                conditions["market_session"] = "closed"
                conditions["reason"] = "market_closed"
                return conditions

            # 检查波动率水平
            if hasattr(self, "volatility_analyzer") and self.volatility_analyzer:
                try:
                    vol_analysis = (
                        await self.volatility_analyzer.get_current_volatility_analysis()
                    )
                    if vol_analysis:
                        vol_level = vol_analysis.get("volatility_level", "normal")
                        conditions["volatility_level"] = vol_level

                        # 极端波动率情况下不适合入场
                        if vol_level in ["extremely_high", "extremely_low"]:
                            conditions["suitable_for_entry"] = False
                            conditions["reason"] = f"extreme_volatility_{vol_level}"

                except Exception as vol_error:
                    if self.logger:
                        await self.logger.debug(f"Volatility check failed: {vol_error}")

            # 检查流动性状况
            if hasattr(self, "order_manager") and self.order_manager:
                try:
                    order_stats = await self.order_manager.get_statistics()
                    recent_fill_rate = order_stats.get("fill_rate", 1.0)

                    if recent_fill_rate < 0.8:  # 最近成交率过低
                        conditions["liquidity_adequate"] = False
                        conditions["reason"] = "poor_liquidity"
                        conditions["suitable_for_entry"] = False

                except Exception as liquidity_error:
                    if self.logger:
                        await self.logger.debug(
                            f"Liquidity check failed: {liquidity_error}"
                        )

            # 计算综合置信度
            confidence_factors = []
            if conditions["market_session"] == "active":
                confidence_factors.append(0.3)
            if conditions["volatility_level"] == "normal":
                confidence_factors.append(0.4)
            if conditions["liquidity_adequate"]:
                confidence_factors.append(0.3)

            conditions["confidence"] = sum(confidence_factors)

            return conditions

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market condition assessment failed: {e}")
            return {"suitable_for_entry": False, "error": str(e), "confidence": 0.0}

    async def _get_real_account_value(self) -> float:
        """获取真实账户价值，优先从交易所API获取"""
        try:
            # 方法1: 从PositionManager获取
            if hasattr(self, "position_manager") and self.position_manager:
                try:
                    # 获取所有持仓信息
                    positions = await self.position_manager.get_all_positions()
                    if positions:
                        total_value = sum(
                            pos.get("market_value", 0) + pos.get("unrealized_pnl", 0)
                            for pos in positions.values()
                        )
                        if total_value > 1000:  # 合理的账户价值下限
                            if self.logger:
                                await self.logger.info(
                                    f"从PositionManager获取账户价值: ${total_value:.2f}"
                                )
                            return float(total_value)
                except Exception as e:
                    if self.logger:
                        await self.logger.debug(
                            f"从PositionManager获取账户价值失败: {e}"
                        )

            # 方法2: 从交易所客户端获取
            account_info = await self._get_account_info_from_exchanges()
            if account_info and account_info.get("total_equity", 0) > 0:
                total_equity = float(account_info["total_equity"])
                if self.logger:
                    await self.logger.info(
                        f"从交易所API获取账户价值: ${total_equity:.2f}"
                    )
                return total_equity

            # 方法3: 从数据库查询最近的账户快照
            if hasattr(self, "timescale_manager") and self.timescale_manager:
                try:
                    recent_value = await self._get_recent_account_value_from_db()
                    if recent_value and recent_value > 1000:
                        if self.logger:
                            await self.logger.info(
                                f"从数据库获取最近账户价值: ${recent_value:.2f}"
                            )
                        return recent_value
                except Exception as e:
                    if self.logger:
                        await self.logger.debug(f"从数据库获取账户价值失败: {e}")

            # 方法4: 使用配置中的初始账户价值
            from src.monitoring.strict_metrics import inc_missing

            inc_missing("BranchStrategy", "account_value")
            if self.logger:
                await self.logger.warning("无法获取实时账户价值（严格模式）：返回None")
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取账户价值失败: {e}")
            return None

    async def _get_account_info_from_exchanges(self) -> dict[str, Any] | None:
        """从交易所API获取账户信息"""
        try:
            # 尝试从各个交易所客户端获取账户信息
            exchange_clients = []

            # Deribit客户端
            if hasattr(self, "deribit_client") and self.deribit_client:
                exchange_clients.append(("deribit", self.deribit_client))

            # Binance客户端
            if hasattr(self, "binance_client") and self.binance_client:
                exchange_clients.append(("binance", self.binance_client))

            total_equity = 0
            total_balance = 0
            currencies_found = []

            for exchange_name, client in exchange_clients:
                try:
                    # 获取账户余额
                    if hasattr(client, "get_account_summary"):
                        account_summary = await client.get_account_summary()
                        if account_summary:
                            equity = account_summary.get("equity", 0)
                            balance = account_summary.get("balance", 0)
                            currency = account_summary.get("currency", "USD")

                            total_equity += float(equity) if equity else 0
                            total_balance += float(balance) if balance else 0
                            currencies_found.append(f"{currency}@{exchange_name}")

                            if self.logger:
                                await self.logger.debug(
                                    f"{exchange_name} 账户 - 权益: {equity}, 余额: {balance}"
                                )

                except Exception as e:
                    if self.logger:
                        await self.logger.debug(
                            f"从 {exchange_name} 获取账户信息失败: {e}"
                        )
                    continue

            if total_equity > 0:
                return {
                    "total_equity": total_equity,
                    "total_balance": total_balance,
                    "currencies": currencies_found,
                    "timestamp": datetime.now(UTC).isoformat(),
                }

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从交易所获取账户信息失败: {e}")

        return None

    async def _get_recent_account_value_from_db(self) -> float | None:
        """从数据库获取最近的账户价值快照"""
        try:
            if not self.timescale_manager:
                return None

            # 查询最近24小时内的账户价值快照
            query = """
            SELECT equity, balance FROM account_snapshots
            WHERE time >= NOW() - INTERVAL '24 hours'
            ORDER BY time DESC
            LIMIT 1
            """

            async with self.timescale_manager.get_connection(read_only=True) as conn:
                result = await conn.fetchrow(query)
                if result:
                    equity = result.get("equity", 0)
                    balance = result.get("balance", 0)
                    return float(max(equity, balance)) if (equity or balance) else None

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"数据库查询账户价值失败: {e}")

        return None
