from __future__ import annotations

from collections import deque
from dataclasses import dataclass
from datetime import date, datetime, timedelta
from decimal import Decimal

try:
    from ..gateways.ibkr_client import OptionContract, Right
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from gateways.ibkr_client import OptionContract, Right

"""
BTC→IBIT 信号映射模块

将BTC侧信号转换为IBIT交易建议的映射层
"""


@dataclass
class BTCSideSignal:
    direction: str  # 'bullish' / 'bearish' / 'neutral'
    target_delta: float  # 0.6~0.8 for call 等
    tenor_days: int  # 30~90
    btc_price: Decimal  # 最新 BTCUSD


@dataclass
class IBITMappingParams:
    a: Decimal  # 回归常数项
    b: Decimal  # 回归斜率
    tracking_error_band_bps: int = 300  # 允许的误差带（bps）


@dataclass
class MappingOutput:
    action: str  # 'LONG_CALL' / 'SELL_PUT_CSP' / 'BUY_SPOT' / 'SKIP'
    option: OptionContract | None = None
    spot_qty: int | None = None


class BTCToIBITMapper:
    def __init__(self, params: IBITMappingParams):
        self.params = params
        self.volatility_analyzer = None
        self.cache_manager = None
        # 新增: 模型校准相关属性
        self.last_calibration_time: datetime | None = None
        self.calibration_window_hours = 24  # 24小时重新校准一次
        self.recent_prediction_errors = deque(maxlen=50)  # 保存最近的预测误差

    def estimate_ibit_price(self, btc_price: Decimal) -> Decimal:
        # 检查是否需要重新校准模型
        self._check_and_calibrate_model()
        return self.params.a + self.params.b * btc_price

    def within_band(self, ibit_est: Decimal, ibit_quote: Decimal) -> bool:
        if ibit_est <= 0:
            return False

        # 计算预测误差并记录
        diff = abs(ibit_quote - ibit_est) / ibit_est
        self.recent_prediction_errors.append(float(diff))

        # 动态调整误差容忍带宽
        adjusted_band = self._get_dynamic_error_band()

        return diff <= adjusted_band

    def pick_option(
        self, underlying: str, expiry: date, strike: Decimal, right: Right
    ) -> OptionContract:
        return OptionContract(
            underlying=underlying, expiry=expiry, strike=strike, right=right
        )

    def map_to_ibit(
        self, sig: BTCSideSignal, ibit_quote: Decimal, next_expiry: date
    ) -> MappingOutput:
        ibit_est = self.estimate_ibit_price(sig.btc_price)
        if not self.within_band(ibit_est, ibit_quote):
            return MappingOutput(action="SKIP")

        # 完整策略映射：根据信号强度、波动率、市场条件决定策略
        try:
            # 获取当前波动率环境 - 从市场数据计算
            volatility_level = self._get_current_volatility_level(ibit_quote)
            if volatility_level is None:
                from src.monitoring.strict_metrics import inc_skip

                inc_skip("BTCToIBITMapper", "no_volatility_level")
                return MappingOutput(action="SKIP")

            # 分析信号强度和确信度
            signal_strength = getattr(sig, "confidence", 0.5)  # 信号置信度
            abs(getattr(sig, "expected_move", 0.05))  # 预期变动幅度

            if sig.direction == "bullish":
                # 看涨信号的策略选择
                if signal_strength > 0.8 and volatility_level == "low":
                    # 高确信度 + 低波动率 → Long Call
                    strike = (ibit_quote * Decimal("1.05")).quantize(Decimal("0.01"))
                    return MappingOutput(
                        action="LONG_CALL",
                        option=self.pick_option(
                            "IBIT", next_expiry, strike, Right.CALL
                        ),
                        confidence=signal_strength,
                    )
                elif signal_strength > 0.6 and volatility_level == "high":
                    # 中等确信度 + 高波动率 → Bull Call Spread
                    long_strike = (ibit_quote * Decimal("1.03")).quantize(
                        Decimal("0.01")
                    )
                    short_strike = long_strike * Decimal("1.10")
                    return MappingOutput(
                        action="BULL_CALL_SPREAD",
                        option=self.pick_option(
                            "IBIT", next_expiry, long_strike, Right.CALL
                        ),
                        hedge_option=self.pick_option(
                            "IBIT", next_expiry, short_strike, Right.CALL
                        ),
                        confidence=signal_strength,
                    )
                else:
                    # 保守策略 → Cash Secured Put (收益增强)
                    strike = (ibit_quote * Decimal("0.95")).quantize(Decimal("0.01"))
                    return MappingOutput(
                        action="CASH_SECURED_PUT",
                        option=self.pick_option("IBIT", next_expiry, strike, Right.PUT),
                        confidence=signal_strength,
                    )

            elif sig.direction == "bearish":
                # 看跌信号的策略选择
                if signal_strength > 0.8 and volatility_level == "low":
                    # 高确信度 + 低波动率 → Long Put
                    strike = (ibit_quote * Decimal("0.95")).quantize(Decimal("0.01"))
                    return MappingOutput(
                        action="LONG_PUT",
                        option=self.pick_option("IBIT", next_expiry, strike, Right.PUT),
                        confidence=signal_strength,
                    )
                elif signal_strength > 0.6 and volatility_level == "high":
                    # 中等确信度 + 高波动率 → Bear Put Spread
                    long_strike = (ibit_quote * Decimal("0.97")).quantize(
                        Decimal("0.01")
                    )
                    short_strike = long_strike * Decimal("0.90")
                    return MappingOutput(
                        action="BEAR_PUT_SPREAD",
                        option=self.pick_option(
                            "IBIT", next_expiry, long_strike, Right.PUT
                        ),
                        hedge_option=self.pick_option(
                            "IBIT", next_expiry, short_strike, Right.PUT
                        ),
                        confidence=signal_strength,
                    )
                else:
                    # 保守策略 → Covered Call (如有持仓) 或观望
                    return MappingOutput(
                        action="SKIP", reason="bearish_signal_low_confidence"
                    )

            else:  # neutral
                # 中性信号 → 波动率策略
                if volatility_level == "high":
                    # 高波动率 → Short Straddle 或 Iron Condor
                    atm_strike = ibit_quote.quantize(Decimal("0.01"))
                    return MappingOutput(
                        action="SHORT_STRADDLE",
                        option=self.pick_option(
                            "IBIT", next_expiry, atm_strike, Right.CALL
                        ),
                        hedge_option=self.pick_option(
                            "IBIT", next_expiry, atm_strike, Right.PUT
                        ),
                        confidence=signal_strength,
                    )
                else:
                    # 低波动率 → Long Straddle 等待波动率上升
                    atm_strike = ibit_quote.quantize(Decimal("0.01"))
                    return MappingOutput(
                        action="LONG_STRADDLE",
                        option=self.pick_option(
                            "IBIT", next_expiry, atm_strike, Right.CALL
                        ),
                        hedge_option=self.pick_option(
                            "IBIT", next_expiry, atm_strike, Right.PUT
                        ),
                        confidence=signal_strength,
                    )

        except Exception:
            # 严格模式：映射失败直接跳过
            from src.monitoring.strict_metrics import inc_skip

            inc_skip("BTCToIBITMapper", "mapping_exception")
            return MappingOutput(action="SKIP")

    # 新增辅助方法

    def _get_current_volatility_level(self, current_price: Decimal) -> str | None:
        """获取当前波动率环境 - 同步方法，从市场数据计算"""
        try:
            # 1. 优先从波动率分析器获取实时数据
            if self.volatility_analyzer:
                try:
                    vol_analysis = (
                        self.volatility_analyzer.get_current_volatility_sync()
                    )
                    if vol_analysis:
                        return vol_analysis.get("volatility_level")
                except Exception:
                    pass

            # 无法获得波动率环境则返回None
            return None

        except Exception:
            return None

    # 移除简化回退波动率环境计算，严格依赖分析器或返回None

    async def _get_volatility_environment(self, current_price: Decimal) -> str | None:
        """获取当前波动率环境"""
        try:
            # 尝试从波动率分析器获取数据
            if hasattr(self, "volatility_analyzer") and self.volatility_analyzer:
                vol_analysis = (
                    await self.volatility_analyzer.get_current_volatility_analysis()
                )
                if vol_analysis:
                    return vol_analysis.get("volatility_level")

            return None

        except Exception:
            return None

    async def _calculate_optimal_call_strike(
        self, spot_price: Decimal, expected_move: float, style: str
    ) -> Decimal:
        """计算最优Call执行价格"""
        try:
            move_factor = Decimal(str(expected_move))

            if style == "aggressive":
                # 激进策略：更接近ATM，目标Delta 0.6-0.7
                strike_multiplier = Decimal("1.02") + (move_factor * Decimal("0.5"))
            elif style == "conservative":
                # 保守策略：更OTM，目标Delta 0.3-0.4
                strike_multiplier = Decimal("1.05") + (move_factor * Decimal("0.8"))
            else:
                # 中性策略
                strike_multiplier = Decimal("1.03") + (move_factor * Decimal("0.6"))

            strike = (spot_price * strike_multiplier).quantize(Decimal("0.01"))

            # 确保执行价格合理范围
            min_strike = spot_price * Decimal("1.01")
            max_strike = spot_price * Decimal("1.15")

            return max(min_strike, min(max_strike, strike))

        except Exception:
            # 失败时返回简单的5%溢价
            return (spot_price * Decimal("1.05")).quantize(Decimal("0.01"))

    async def _calculate_optimal_put_strike(
        self, spot_price: Decimal, expected_move: float, style: str
    ) -> Decimal:
        """计算最优Put执行价格"""
        try:
            move_factor = Decimal(str(expected_move))

            if style == "aggressive":
                # 激进策略：更接近ATM，目标Delta -0.6 到 -0.7
                strike_multiplier = Decimal("0.98") - (move_factor * Decimal("0.5"))
            elif style == "conservative":
                # 保守策略：更OTM，目标Delta -0.3 到 -0.4
                strike_multiplier = Decimal("0.95") - (move_factor * Decimal("0.8"))
            else:
                # 中性策略
                strike_multiplier = Decimal("0.97") - (move_factor * Decimal("0.6"))

            strike = (spot_price * strike_multiplier).quantize(Decimal("0.01"))

            # 确保执行价格合理范围
            max_strike = spot_price * Decimal("0.99")
            min_strike = spot_price * Decimal("0.85")

            return max(min_strike, min(max_strike, strike))

        except Exception:
            # 失败时返回简单的5%折价
            return (spot_price * Decimal("0.95")).quantize(Decimal("0.01"))

    def _calculate_multi_timeframe_volatility(self, prices) -> dict:
        """计算多时间框架波动率指标"""
        import numpy as np

        vol_metrics = {}

        # 1. 短期波动率 (5天)
        if len(prices) >= 5:
            short_returns = np.diff(np.log(prices[-5:]))
            vol_metrics["short_vol"] = np.std(short_returns) * np.sqrt(252)

        # 2. 中期波动率 (20天)
        if len(prices) >= 20:
            medium_returns = np.diff(np.log(prices[-20:]))
            vol_metrics["medium_vol"] = np.std(medium_returns) * np.sqrt(252)

        # 3. 长期波动率 (50天)
        if len(prices) >= 50:
            long_returns = np.diff(np.log(prices[-50:]))
            vol_metrics["long_vol"] = np.std(long_returns) * np.sqrt(252)

        # 4. 波动率趋势 (波动率的波动率)
        if len(prices) >= 20:
            rolling_vols = []
            for i in range(5, 21):  # 计算滚动5天波动率
                if i <= len(prices):
                    window_returns = np.diff(np.log(prices[-i:]))
                    rolling_vol = np.std(window_returns) * np.sqrt(252)
                    rolling_vols.append(rolling_vol)

            if len(rolling_vols) >= 10:
                vol_metrics["vol_of_vol"] = np.std(rolling_vols)
                vol_metrics["vol_trend"] = (
                    (rolling_vols[-1] - rolling_vols[0]) / rolling_vols[0]
                    if rolling_vols[0] > 0
                    else 0
                )

        return vol_metrics

    def _classify_volatility_environment(self, vol_metrics: dict) -> str:
        """基于多重指标综合判断波动率环境"""

        # 获取各时间框架波动率
        short_vol = vol_metrics.get("short_vol", 0.5)
        medium_vol = vol_metrics.get("medium_vol", 0.5)
        long_vol = vol_metrics.get("long_vol", 0.5)
        vol_trend = vol_metrics.get("vol_trend", 0)

        # 动态阈值 - 基于历史波动率分布
        high_threshold = 0.8  # 可以根据历史数据调整
        low_threshold = 0.3

        # 综合评分机制
        vol_score = 0

        # 1. 当前波动率水平 (40%权重)
        avg_vol = (short_vol + medium_vol + long_vol) / 3
        if avg_vol > high_threshold:
            vol_score += 0.4
        elif avg_vol < low_threshold:
            vol_score -= 0.4

        # 2. 短期vs长期波动率比较 (30%权重)
        if short_vol > long_vol * 1.2:  # 短期波动率显著高于长期
            vol_score += 0.3
        elif short_vol < long_vol * 0.8:  # 短期波动率显著低于长期
            vol_score -= 0.3

        # 3. 波动率趋势 (30%权重)
        if vol_trend > 0.1:  # 波动率上升趋势
            vol_score += 0.3
        elif vol_trend < -0.1:  # 波动率下降趋势
            vol_score -= 0.3

        # 根据综合评分分类
        if vol_score > 0.3:
            return "high"
        elif vol_score < -0.3:
            return "low"
        else:
            return "normal"

    def _check_and_calibrate_model(self) -> None:
        """检查并校准价格映射模型"""
        now = datetime.now()
        if (
            self.last_calibration_time is None
            or now - self.last_calibration_time
            > timedelta(hours=self.calibration_window_hours)
        ):
            # 获取最近的BTC-IBIT价格数据进行模型校准
            if self.cache_manager:
                recent_data = self._get_recent_price_pairs()
                if len(recent_data) >= 10:  # 至少需要10个数据点
                    self._recalibrate_model(recent_data)

            self.last_calibration_time = now

    def _get_recent_price_pairs(self) -> list:
        """从缓存获取最近的BTC-IBIT价格对"""
        try:
            btc_history = self.cache_manager.get_sync("btc_price_history") or []
            ibit_history = self.cache_manager.get_sync("ibit_price_history") or []

            # 确保数据对齐和时间戳匹配
            min_len = min(len(btc_history), len(ibit_history))
            return list(
                zip(btc_history[-min_len:], ibit_history[-min_len:], strict=False)
            )
        except Exception:
            return []

    def _recalibrate_model(self, price_pairs: list) -> None:
        """重新校准线性回归模型参数"""
        try:
            import numpy as np

            # 使用numpy进行简单的线性回归，避免引入sklearn依赖
            btc_prices = np.array([float(pair[0]) for pair in price_pairs])
            ibit_prices = np.array([float(pair[1]) for pair in price_pairs])

            # 简单的最小二乘法
            X = np.vstack([btc_prices, np.ones(len(btc_prices))]).T
            coeffs = np.linalg.lstsq(X, ibit_prices, rcond=None)[0]

            # 更新模型参数
            self.params.b = Decimal(str(coeffs[0]))
            self.params.a = Decimal(str(coeffs[1]))

        except Exception:
            # 校准失败时保持原参数
            pass

    def _get_dynamic_error_band(self) -> Decimal:
        """基于最近预测误差动态调整容错带宽"""
        if len(self.recent_prediction_errors) < 5:
            return Decimal(self.params.tracking_error_band_bps) / Decimal(10000)

        # 计算最近误差的标准差
        import numpy as np

        recent_std = np.std(self.recent_prediction_errors)

        # 动态调整: 基础带宽 + 2倍标准差
        base_band = Decimal(self.params.tracking_error_band_bps) / Decimal(10000)
        dynamic_adjustment = Decimal(str(recent_std * 2))

        return min(
            base_band + dynamic_adjustment, base_band * Decimal("2")
        )  # 最大不超过2倍基础带宽
