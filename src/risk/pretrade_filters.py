from __future__ import annotations

from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal

"""
Pre-trade 过滤器（IBIT/IBKR 专用最小实现）
- 资金校验：CSP BuyingPower 足额
- 名义规模：日内 CSP 名义上限
- 流动性：价差/OI/成交量阈值
- Long Call θ 风险：简单禁入条件占位（MVP）
"""


@dataclass
class CSPCheck:
    required_cash: Decimal
    ok: bool
    reason: str | None = None


@dataclass
class LiquidityCheck:
    spread_bps_ok: bool
    oi_ok: bool
    vol_ok: bool
    reason: str | None = None


class PretradeFilters:
    def __init__(self, config: dict | None = None):
        # 从配置加载参数，提供默认值
        config = config or {}

        # 基础参数
        self.base_max_spread_bps = config.get("max_spread_bps", 1500)
        self.base_min_oi = config.get("min_oi", 200)
        self.base_min_volume = config.get("min_volume", 50)
        self.daily_csp_notional_cap = Decimal(
            str(config.get("daily_csp_notional_cap", "100000"))
        )

        # 动态调整参数
        self.volatility_adjustment_enabled = config.get("volatility_adjustment", True)
        self.market_hours_adjustment_enabled = config.get(
            "market_hours_adjustment", True
        )

        # 状态变量
        self._today_csp_notional: Decimal = Decimal("0")
        self._current_market_regime = "normal"  # normal, high_vol, low_vol
        self._last_parameter_update: datetime | None = None

    def reset_daily_counters(self) -> None:
        self._today_csp_notional = Decimal("0")

    # 新增: 动态参数获取方法
    @property
    def max_spread_bps(self) -> int:
        """动态调整的最大价差阈值"""
        base_spread = self.base_max_spread_bps

        if not self.volatility_adjustment_enabled:
            return base_spread

        # 根据市场波动率调整价差容忍度
        if self._current_market_regime == "high_vol":
            return int(base_spread * 1.3)  # 高波动率时放宽30%
        elif self._current_market_regime == "low_vol":
            return int(base_spread * 0.8)  # 低波动率时收紧20%

        return base_spread

    @property
    def min_oi(self) -> int:
        """动态调整的最小持仓量阈值"""
        base_oi = self.base_min_oi

        # 根据市场环境调整流动性要求
        if self._current_market_regime == "high_vol":
            return int(base_oi * 1.5)  # 高波动率时要求更高流动性
        elif self._current_market_regime == "low_vol":
            return int(base_oi * 0.7)  # 低波动率时可以适当放松

        return base_oi

    @property
    def min_volume(self) -> int:
        """动态调整的最小成交量阈值"""
        base_volume = self.base_min_volume

        # 类似的动态调整逻辑
        if self._current_market_regime == "high_vol":
            return int(base_volume * 1.4)
        elif self._current_market_regime == "low_vol":
            return int(base_volume * 0.6)

        return base_volume

    def update_market_regime(
        self, volatility_level: str, current_time: datetime = None
    ) -> None:
        """更新市场状态，调整过滤参数"""
        if current_time is None:
            current_time = datetime.now()

        self._current_market_regime = volatility_level
        self._last_parameter_update = current_time

    def csp_buying_power(
        self, strike: Decimal, multiplier: int, qty: int, buying_power: Decimal
    ) -> CSPCheck:
        required = strike * Decimal(multiplier) * Decimal(qty)
        if buying_power < required:
            return CSPCheck(required_cash=required, ok=False, reason="INSUFFICIENT_BP")
        # 名义上限检查
        if (self._today_csp_notional + required) >= self.daily_csp_notional_cap:
            return CSPCheck(required_cash=required, ok=False, reason="DAILY_CAP")
        return CSPCheck(required_cash=required, ok=True)

    def add_csp_notional(self, strike: Decimal, multiplier: int, qty: int) -> None:
        self._today_csp_notional += strike * Decimal(multiplier) * Decimal(qty)

    def liquidity(
        self, spread_bps: float | None, oi: int | None, volume: int | None
    ) -> LiquidityCheck:
        """使用动态参数进行流动性检查"""
        if spread_bps is None:
            return LiquidityCheck(False, False, False, reason="NO_SPREAD_DATA")

        # 使用动态调整后的参数
        spread_ok = spread_bps <= self.max_spread_bps
        oi_ok = (oi or 0) >= self.min_oi
        vol_ok = (volume or 0) >= self.min_volume

        ok = spread_ok and oi_ok and vol_ok
        return LiquidityCheck(
            spread_ok,
            oi_ok,
            vol_ok,
            None
            if ok
            else f"LIQUIDITY_FAIL(spread:{spread_bps:.0f}/{self.max_spread_bps}, oi:{oi}/{self.min_oi}, vol:{volume}/{self.min_volume})",
        )

    def long_call_theta_guard(self, days_to_expiry: int, iv: float | None) -> bool:
        # MVP：到期太近且 IV 偏高时禁入，避免 θ 侵蚀过快
        return not (days_to_expiry <= 7 and (iv or 0) > 0.9)
