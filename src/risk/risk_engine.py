"""
风险控制引擎

负责实时风险监控、告警和自动化风险处理，专门针对期权交易的特有风险
"""

import asyncio
import contextlib
import time
from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import BaseEvent, EventBus, EventType, TradingDecisionEvent
from src.data.cache_manager import CacheManager
from src.services.market_data_service import MarketDataService
from src.data.timescale_manager import TimescaleDBManager
from src.services.var_calculation_service import VaRCalculationService, VaRParameters


class RiskLevel(Enum):
    """风险级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RiskType(Enum):
    """风险类型"""

    MARGIN = "margin"
    DELTA = "delta"
    GAMMA = "gamma"
    THETA = "theta"
    VEGA = "vega"
    LIQUIDITY = "liquidity"
    CONCENTRATION = "concentration"
    EXPIRY = "expiry"
    VAR = "var"


@dataclass
class RiskMetrics:
    """风险指标"""

    total_delta: Decimal = Decimal("0")
    total_gamma: Decimal = Decimal("0")
    total_theta: Decimal = Decimal("0")
    total_vega: Decimal = Decimal("0")
    margin_usage_ratio: Decimal = Decimal("0")
    margin_used: Decimal = Decimal("0")
    margin_available: Decimal = Decimal("0")
    portfolio_var: Decimal = Decimal("0")
    max_single_position_risk: Decimal = Decimal("0")
    liquidity_score: float = 1.0
    concentration_risk: float = 0.0
    days_to_nearest_expiry: int = 999
    portfolio_value: Decimal = Decimal("0")
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


@dataclass
class RiskEvent:
    """风险事件"""

    event_id: str
    risk_type: RiskType
    risk_level: RiskLevel
    message: str
    current_value: Decimal | float
    threshold: Decimal | float
    suggested_action: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    metadata: dict[str, Any] = field(default_factory=dict)


@dataclass
class RiskLimits:
    """风险限制配置"""

    # Greeks风险限制（适应IBIT期权无法做空对冲的限制）
    max_delta_exposure: Decimal = Decimal(
        "1.2"
    )  # 最大Delta敞口（提高到1.2适应抄底策略）
    emergency_delta_threshold: Decimal = Decimal("2.0")  # 紧急Delta阈值
    max_gamma_exposure: Decimal = Decimal("0.05")  # 最大Gamma敞口
    max_vega_exposure: Decimal = Decimal("1000")  # 最大Vega敞口

    # 保证金风险限制
    max_margin_usage: Decimal = Decimal("0.7")  # 最大保证金使用率70%
    margin_warning_level: Decimal = Decimal("0.5")  # 保证金警告水平50%

    # 组合风险限制
    max_portfolio_var: Decimal = Decimal("0.05")  # 最大VaR 5%
    max_daily_loss: Decimal = Decimal("0.08")  # 最大日损失8%
    max_single_position: Decimal = Decimal("0.08")  # 单仓位最大占比8%

    # 流动性风险限制
    min_liquidity_score: float = 0.2  # 最小流动性评分
    max_bid_ask_spread: Decimal = Decimal("0.15")  # 最大买卖价差15%（适合加密货币期权）

    # 到期风险限制
    min_days_to_expiry: int = 1  # 最小到期天数
    expiry_warning_days: int = 7  # 到期预警天数


class RiskEngine(BaseComponent):
    """
    风险控制引擎

    实时监控期权交易的特有风险，包括：
    - Greeks风险（Delta、Gamma、Theta、Vega）
    - 保证金风险
    - 流动性风险
    - 到期风险
    - 集中度风险
    """

    def __init__(
        self,
        config: dict[str, Any] | None = None,
        market_data_service: MarketDataService | None = None,
    ):
        super().__init__("RiskEngine", config)

        # 组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None
        self.market_data_service: MarketDataService = market_data_service
        self.var_calculation_service: VaRCalculationService | None = None
        self.db_manager: TimescaleDBManager | None = None

        # 风险配置
        self.risk_limits = RiskLimits()
        self._load_risk_limits_from_config()

        # 风险监控状态
        self.current_metrics: RiskMetrics | None = None
        self.risk_events: list[RiskEvent] = []
        self.last_risk_check: datetime | None = None

        # 监控任务
        self._monitoring_task: asyncio.Task | None = None
        self._monitoring_interval = 30  # 30秒监控间隔

        # 风险历史
        self._risk_history: list[RiskMetrics] = []
        self._max_history_size = 1000

        # 紧急处理标志
        self._emergency_mode = False
        self._emergency_actions_enabled = True

        # 计算优化配置（用于降级方法）
        self._adaptive_simulations = True  # 启用自适应模拟次数
        self._min_simulations = 1000  # 最小模拟次数
        self._max_simulations = 10000  # 最大模拟次数

        # 统一仓位比例控制（主/分支），可通过配置覆盖
        alloc_cfg = (self.config or {}).get("allocation_ratios", {})
        try:
            self.allocation_ratios: dict[str, float] = {
                "main": float(alloc_cfg.get("main", 0.7)),
                "branch": float(alloc_cfg.get("branch", 0.3)),
            }
        except Exception:
            self.allocation_ratios = {"main": 0.7, "branch": 0.3}

    async def update_allocation_ratios(
        self, main: float | None = None, branch: float | None = None
    ) -> None:
        """更新主/分策略仓位比例（供协调器同步）。"""
        try:
            if main is not None:
                self.allocation_ratios["main"] = float(main)
            if branch is not None:
                self.allocation_ratios["branch"] = float(branch)
            if self.logger:
                await self.logger.info(
                    f"RiskEngine allocation ratios updated: main={self.allocation_ratios['main']:.3f}, branch={self.allocation_ratios['branch']:.3f}"
                )
        except Exception:
            pass

    def _load_risk_limits_from_config(self):
        """从配置加载风险限制"""
        if not self.config:
            return

        risk_config = self.config.get("risk_limits", {})

        # 更新风险限制
        for attr_name in dir(self.risk_limits):
            if not attr_name.startswith("_"):
                config_value = risk_config.get(attr_name)
                if config_value is not None:
                    if isinstance(getattr(self.risk_limits, attr_name), Decimal):
                        setattr(self.risk_limits, attr_name, Decimal(str(config_value)))
                    else:
                        setattr(self.risk_limits, attr_name, config_value)

    async def _initialize_impl(self) -> bool:
        """初始化风险引擎"""
        try:
            # 初始化风险指标
            self.current_metrics = RiskMetrics()

            # 注入 TimescaleDBManager（用于落库）
            try:
                if hasattr(self, "dependency_injector") and self.dependency_injector:
                    self.db_manager = await self.dependency_injector.resolve(
                        TimescaleDBManager
                    )
                    if self.logger:
                        await self.logger.info("RiskEngine resolved TimescaleDBManager")
            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"RiskEngine could not resolve TimescaleDBManager: {e}"
                    )

            if self.logger:
                await self.logger.info("RiskEngine initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"RiskEngine initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动风险监控"""
        try:
            # 订阅交易决策事件
            if self.event_bus:
                await self.event_bus.subscribe(
                    event_types=[EventType.TRADING_DECISION],
                    callback=self._handle_trading_decision,
                    subscriber_id=f"{self.component_name}_decision_handler",
                )
                if self.logger:
                    await self.logger.info("✅ RiskEngine订阅TradingDecisionEvent成功")

            # 启动监控任务
            self._monitoring_task = asyncio.create_task(self._monitoring_loop())

            if self.logger:
                await self.logger.info("RiskEngine monitoring started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"RiskEngine start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止风险监控"""
        try:
            # 停止监控任务
            if self._monitoring_task and not self._monitoring_task.done():
                self._monitoring_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._monitoring_task

            if self.logger:
                await self.logger.info("RiskEngine stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"RiskEngine stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查监控任务状态
            if not self._monitoring_task or self._monitoring_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Risk monitoring task not running",
                )

            # 检查最近的风险检查时间
            if self.last_risk_check:
                time_since_check = datetime.now(UTC) - self.last_risk_check
                if time_since_check.total_seconds() > self._monitoring_interval * 2:
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message=f"Risk check delayed by {time_since_check.total_seconds():.1f}s",
                    )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="Risk monitoring operational"
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _handle_trading_decision(self, event: TradingDecisionEvent):
        """处理交易决策事件并进行风险评估"""
        try:
            # 避免处理自身增强后的事件，防止事件风暴/循环
            if event.source and "RiskEngine" in str(event.source):
                return
            if self.logger:
                await self.logger.info(
                    f"收到TradingDecisionEvent: strategy={event.strategy_name}, action={event.action}"
                )

            # 进行风险预检查
            risk_assessment = await self._assess_trading_decision_risk(event)

            if risk_assessment["approved"]:
                # 风险通过，继续发布给订单管理器
                await self._forward_to_order_manager(event, risk_assessment)
                from contextlib import suppress

                with suppress(Exception):
                    self.metrics.custom_metrics["decisions_approved"] = (
                        self.metrics.custom_metrics.get("decisions_approved", 0) + 1
                    )
            else:
                # 风险阻止
                if self.logger:
                    await self.logger.warning(
                        f"交易决策被风险管理拒绝: {risk_assessment['reason']}"
                    )
                from contextlib import suppress

                with suppress(Exception):
                    self.metrics.custom_metrics["decisions_rejected"] = (
                        self.metrics.custom_metrics.get("decisions_rejected", 0) + 1
                    )
                await self._publish_risk_rejection_event(event, risk_assessment)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理TradingDecisionEvent失败: {e}")

    async def _assess_trading_decision_risk(self, event: TradingDecisionEvent) -> dict:
        """评估交易决策的风险"""
        try:
            decision_data = event.decision_data
            risk_params = event.risk_params

            # 基础风险检查
            risk_params.get("max_position_size", 1000000)
            risk_params.get("risk_tolerance", 0.02)

            # 检查当前风险状态（接入实时指标）
            current_risks = await self._get_current_risk_status()
            try:
                # 使用最新的风险指标（若可用）覆盖静态状态
                cm = getattr(self, "current_metrics", None)
                rl = "low"
                if cm is not None:
                    # 保证金使用率
                    if cm.margin_usage_ratio >= self.risk_limits.max_margin_usage:
                        rl = "critical"
                    elif cm.margin_usage_ratio >= self.risk_limits.margin_warning_level:
                        rl = "high"
                    # 到期风险
                    try:
                        if (
                            cm.days_to_nearest_expiry
                            <= self.risk_limits.min_days_to_expiry
                        ):
                            rl = "critical"
                        elif (
                            cm.days_to_nearest_expiry
                            <= self.risk_limits.expiry_warning_days
                            and rl != "critical"
                        ):
                            rl = "high"
                    except Exception:
                        pass
                    # 流动性评分
                    try:
                        if (
                            cm.liquidity_score < self.risk_limits.min_liquidity_score
                            and rl == "low"
                        ):
                            rl = "medium"
                    except Exception:
                        pass
                current_risks["system_risk_level"] = rl
            except Exception:
                pass

            # 风险评估逻辑
            assessment = {
                "approved": True,
                "reason": "",
                "risk_level": "low",
                "additional_limits": {},
            }

            # 检查系统风险状态
            if current_risks.get("system_risk_level", "low") in ["high", "critical"]:
                assessment["approved"] = False
                assessment["reason"] = (
                    f"系统风险等级过高: {current_risks['system_risk_level']}"
                )
                assessment["risk_level"] = "critical"
                return assessment

            # 检查信号可信度
            if event.confidence < 0.5:
                assessment["approved"] = False
                assessment["reason"] = f"信号可信度不足: {event.confidence:.2f} < 0.5"
                assessment["risk_level"] = "medium"
                return assessment

            # 根据信号类型调整风险评估
            signal_type = decision_data.get("signal_type", "")
            if signal_type == "volatility_spike":
                volatility_level = decision_data.get("volatility_level", "normal")
                if volatility_level == "high":
                    assessment["additional_limits"]["position_reduction"] = 0.5
                    assessment["risk_level"] = "medium"

            if self.logger:
                await self.logger.info(f"风险评估结果: {assessment}")

            # 计算统一仓位比例下的建议数量（按主/分支分配），并应用上限
            try:
                orig_qty = float(decision_data.get("quantity", 1))
                role = str(risk_params.get("strategy_role", "main")).lower()
                ratio = float(self.allocation_ratios.get(role, 1.0))
                # 基于实时风险指标的缩放（仓位压降）
                scale = 1.0
                cm = getattr(self, "current_metrics", None)
                if cm is not None:
                    # 保证金使用率压降
                    try:
                        mur = float(cm.margin_usage_ratio)
                        if mur >= float(self.risk_limits.max_margin_usage):
                            scale *= 0.0  # 不应增加新仓位
                        elif mur >= float(self.risk_limits.margin_warning_level):
                            scale *= 0.6
                        elif mur >= 0.4:
                            scale *= 0.8
                    except Exception:
                        pass
                    # 流动性评分压降
                    try:
                        if (
                            cm.liquidity_score
                            < float(self.risk_limits.min_liquidity_score) + 0.05
                        ):
                            scale *= 0.8
                    except Exception:
                        pass
                    # 到期风险压降
                    try:
                        if (
                            cm.days_to_nearest_expiry
                            <= self.risk_limits.expiry_warning_days
                        ):
                            scale *= 0.8
                    except Exception:
                        pass
                sized_qty = max(1, int(round(orig_qty * ratio * scale)))
                max_pos = risk_params.get("max_position_size")
                if max_pos is not None:
                    from contextlib import suppress

                    with suppress(Exception):
                        sized_qty = min(sized_qty, int(float(max_pos)))
                assessment["additional_limits"]["sized_quantity"] = sized_qty
                assessment["additional_limits"]["position_scale"] = scale
            except Exception:
                pass

            return assessment

        except Exception as e:
            if self.logger:
                await self.logger.error(f"风险评估失败: {e}")
            return {
                "approved": False,
                "reason": f"风险评估异常: {str(e)}",
                "risk_level": "critical",
            }

    async def _get_current_risk_status(self) -> dict:
        """获取当前风险状态"""
        try:
            # 模拟风险状态检查
            return {
                "system_risk_level": "low",
                "position_usage": 0.3,
                "volatility_level": "normal",
            }
        except Exception:
            return {"system_risk_level": "unknown"}

    async def _forward_to_order_manager(
        self, decision_event: TradingDecisionEvent, risk_assessment: dict
    ):
        """将通过风险检查的交易决策转发给订单管理器"""
        try:
            if not self.event_bus:
                return

            # 应用数量限制（若存在）
            sized_qty = None
            from contextlib import suppress

            with suppress(Exception):
                sized_qty = risk_assessment.get("additional_limits", {}).get(
                    "sized_quantity"
                )

            # 合并/覆盖决策数据
            new_decision_data = {
                **decision_event.decision_data,
                "risk_approved": True,
                "risk_level": risk_assessment["risk_level"],
            }
            if sized_qty is not None:
                new_decision_data["quantity"] = sized_qty

            # 创建一个增强的交易决策事件，包含风险评估信息
            enhanced_event = TradingDecisionEvent(
                strategy_name=decision_event.strategy_name,
                action=decision_event.action,
                instruments=decision_event.instruments,
                risk_params={
                    **decision_event.risk_params,
                    **risk_assessment.get("additional_limits", {}),
                },
                confidence=decision_event.confidence,
                decision_data=new_decision_data,
                source=f"{self.component_name}",  # 标记为经过风险检查
                timestamp=datetime.now(UTC),
            )

            await self.event_bus.publish(enhanced_event)

            if self.logger:
                await self.logger.info(
                    f"交易决策已转发给订单管理器: {decision_event.action}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"转发交易决策失败: {e}")

    async def _publish_risk_rejection_event(
        self, decision_event: TradingDecisionEvent, risk_assessment: dict
    ):
        """发布风险拒绝事件"""
        try:
            if not self.event_bus:
                return

            rejection_event = BaseEvent(
                event_type=EventType.RISK_REJECTION,
                source=self.component_name,
                timestamp=datetime.now(UTC),
                data={
                    "original_strategy": decision_event.strategy_name,
                    "rejected_action": decision_event.action,
                    "rejection_reason": risk_assessment["reason"],
                    "risk_level": risk_assessment["risk_level"],
                },
            )

            await self.event_bus.publish(rejection_event)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"发布风险拒绝事件失败: {e}")

    async def _monitoring_loop(self):
        """风险监控循环"""
        while True:
            try:
                await self._perform_risk_check()
                await asyncio.sleep(self._monitoring_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Risk monitoring error: {e}")
                await asyncio.sleep(5)  # 错误后短暂等待

    async def _perform_risk_check(self):
        """执行风险检查"""
        try:
            # 计算当前风险指标
            await self._calculate_risk_metrics()

            # 检查风险限制
            risk_events = await self._check_risk_limits()

            # 处理风险事件
            if risk_events:
                await self._handle_risk_events(risk_events)

            # 更新检查时间
            self.last_risk_check = datetime.now(UTC)

            # 保存风险历史
            if self.current_metrics:
                self._risk_history.append(self.current_metrics)
                if len(self._risk_history) > self._max_history_size:
                    self._risk_history.pop(0)

            # 周期计算完成后落库
            await self._persist_risk_metrics()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk check failed: {e}")

    async def _calculate_risk_metrics(self):
        """计算风险指标"""
        try:
            if not self.cache_manager:
                return

            # 获取当前仓位数据
            positions_data = await self.market_data_service.get_positions()
            if not positions_data:
                return

            # 初始化指标
            total_delta = Decimal("0")
            total_gamma = Decimal("0")
            total_theta = Decimal("0")
            total_vega = Decimal("0")
            max_single_position = Decimal("0")
            total_portfolio_value = Decimal("0")

            # 计算Greeks敞口
            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict):
                    size = Decimal(str(position_data.get("size", 0)))
                    market_value = Decimal(str(position_data.get("market_value", 0)))

                    # 获取Greeks数据
                    greeks_data = await self.market_data_service.get_greeks(symbol)
                    if greeks_data:
                        delta = Decimal(str(greeks_data.get("delta", 0)))
                        gamma = Decimal(str(greeks_data.get("gamma", 0)))
                        theta = Decimal(str(greeks_data.get("theta", 0)))
                        vega = Decimal(str(greeks_data.get("vega", 0)))

                        # 计算仓位Greeks
                        position_delta = size * delta
                        position_gamma = size * gamma
                        position_theta = size * theta
                        position_vega = size * vega

                        total_delta += position_delta
                        total_gamma += position_gamma
                        total_theta += position_theta
                        total_vega += position_vega

                    total_portfolio_value += market_value
                    max_single_position = max(max_single_position, market_value)

            # 计算保证金使用率
            margin_data = await self.market_data_service.get_margin_info()
            margin_usage_ratio = Decimal("0")
            used_margin = Decimal("0")
            available_margin = Decimal("0")
            if margin_data:
                used_margin = Decimal(str(margin_data.get("used_margin", 0)))
                available_margin = Decimal(
                    str(margin_data.get("available_margin", 0))
                )
                total_margin = used_margin + available_margin
                if total_margin > 0:
                    margin_usage_ratio = used_margin / total_margin

            # 计算流动性评分
            liquidity_score = await self._calculate_liquidity_score()

            # 计算集中度风险
            concentration_risk = 0.0
            if total_portfolio_value > 0:
                concentration_risk = float(max_single_position / total_portfolio_value)

            # 计算最近到期天数
            days_to_nearest_expiry = await self._calculate_nearest_expiry()

            # 计算组合VaR
            current_btc_price = await self._get_current_btc_price()
            current_volatility = await self._get_current_volatility()
            portfolio_var = await self._calculate_portfolio_var(
                positions_data,
                {"current_price": current_btc_price, "volatility": current_volatility},
            )

            # 更新风险指标
            self.current_metrics = RiskMetrics(
                total_delta=total_delta,
                total_gamma=total_gamma,
                total_theta=total_theta,
                total_vega=total_vega,
                margin_usage_ratio=margin_usage_ratio,
                margin_used=used_margin,
                margin_available=available_margin,
                portfolio_var=portfolio_var,
                max_single_position_risk=concentration_risk,
                liquidity_score=liquidity_score,
                concentration_risk=concentration_risk,
                days_to_nearest_expiry=days_to_nearest_expiry,
                portfolio_value=total_portfolio_value,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk metrics calculation failed: {e}")

    async def _calculate_liquidity_score(self) -> float:
        """计算流动性评分"""
        try:
            # 获取期权链数据
            option_chain = await self.market_data_service.get_option_chain("BTC")
            if not option_chain:
                return 1.0

            total_score = 0.0
            count = 0

            for option_data in option_chain:
                if isinstance(option_data, dict):
                    bid_ask_spread = option_data.get("bid_ask_spread", 0)
                    volume = option_data.get("volume", 0)
                    open_interest = option_data.get("open_interest", 0)

                    # 计算单个期权的流动性评分
                    spread_score = max(0, 1 - bid_ask_spread / 0.05)  # 5%为最差
                    volume_score = min(1, volume / 100)  # 100为满分
                    oi_score = min(1, open_interest / 500)  # 500为满分

                    option_score = (spread_score + volume_score + oi_score) / 3
                    total_score += option_score
                    count += 1

            return total_score / count if count > 0 else 1.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Liquidity score calculation failed: {e}")
            return 1.0

    async def _calculate_nearest_expiry(self) -> int:
        """计算最近到期天数"""
        try:
            positions_data = await self.market_data_service.get_positions()
            if not positions_data:
                return 999

            nearest_expiry = None
            current_time = datetime.now(UTC)

            for _symbol, position_data in positions_data.items():
                if isinstance(position_data, dict):
                    expiry_str = position_data.get("expiry")
                    if expiry_str:
                        try:
                            expiry_date = datetime.fromisoformat(
                                expiry_str.replace("Z", "+00:00")
                            )
                            if nearest_expiry is None or expiry_date < nearest_expiry:
                                nearest_expiry = expiry_date
                        except Exception:
                            continue

            if nearest_expiry:
                days_to_expiry = (nearest_expiry - current_time).days
                return max(0, days_to_expiry)

            return 999

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Nearest expiry calculation failed: {e}")
            return 999

    async def _check_risk_limits(self) -> list[RiskEvent]:
        """检查风险限制"""
        risk_events = []

        if not self.current_metrics:
            return risk_events

        try:
            # 检查Delta敞口 - 分级风险管理（适应IBIT无法对冲的现实）
            delta_abs = abs(self.current_metrics.total_delta)

            if delta_abs > self.risk_limits.emergency_delta_threshold:
                # 紧急级别：强制减仓
                risk_events.append(
                    RiskEvent(
                        event_id=f"delta_emergency_{int(time.time())}",
                        risk_type=RiskType.DELTA,
                        risk_level=RiskLevel.CRITICAL,
                        message=f"Emergency Delta exposure: {self.current_metrics.total_delta}",
                        current_value=delta_abs,
                        threshold=self.risk_limits.emergency_delta_threshold,
                        suggested_action="Emergency position reduction required",
                    )
                )
            elif delta_abs > self.risk_limits.max_delta_exposure:
                # 高风险级别：暂停新订单
                risk_events.append(
                    RiskEvent(
                        event_id=f"delta_risk_{int(time.time())}",
                        risk_type=RiskType.DELTA,
                        risk_level=RiskLevel.HIGH,
                        message=f"Delta exposure exceeded: {self.current_metrics.total_delta}",
                        current_value=delta_abs,
                        threshold=self.risk_limits.max_delta_exposure,
                        suggested_action="Pause new orders and reduce position sizing",
                    )
                )

            # 检查Gamma敞口
            if (
                abs(self.current_metrics.total_gamma)
                > self.risk_limits.max_gamma_exposure
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"gamma_risk_{int(time.time())}",
                        risk_type=RiskType.GAMMA,
                        risk_level=RiskLevel.HIGH,
                        message=f"Gamma exposure exceeded: {self.current_metrics.total_gamma}",
                        current_value=abs(self.current_metrics.total_gamma),
                        threshold=self.risk_limits.max_gamma_exposure,
                        suggested_action="Close high gamma positions or adjust strikes",
                    )
                )

            # 检查保证金使用率
            if (
                self.current_metrics.margin_usage_ratio
                > self.risk_limits.max_margin_usage
            ):
                risk_level = (
                    RiskLevel.CRITICAL
                    if self.current_metrics.margin_usage_ratio > Decimal("0.8")
                    else RiskLevel.HIGH
                )
                risk_events.append(
                    RiskEvent(
                        event_id=f"margin_risk_{int(time.time())}",
                        risk_type=RiskType.MARGIN,
                        risk_level=risk_level,
                        message=f"Margin usage exceeded: {self.current_metrics.margin_usage_ratio:.2%}",
                        current_value=self.current_metrics.margin_usage_ratio,
                        threshold=self.risk_limits.max_margin_usage,
                        suggested_action="Reduce positions or add margin",
                    )
                )
            elif (
                self.current_metrics.margin_usage_ratio
                > self.risk_limits.margin_warning_level
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"margin_warning_{int(time.time())}",
                        risk_type=RiskType.MARGIN,
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Margin usage warning: {self.current_metrics.margin_usage_ratio:.2%}",
                        current_value=self.current_metrics.margin_usage_ratio,
                        threshold=self.risk_limits.margin_warning_level,
                        suggested_action="Monitor margin usage closely",
                    )
                )

            # 检查流动性风险
            if (
                self.current_metrics.liquidity_score
                < self.risk_limits.min_liquidity_score
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"liquidity_risk_{int(time.time())}",
                        risk_type=RiskType.LIQUIDITY,
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Low liquidity score: {self.current_metrics.liquidity_score:.2f}",
                        current_value=self.current_metrics.liquidity_score,
                        threshold=self.risk_limits.min_liquidity_score,
                        suggested_action="Avoid new positions in illiquid options",
                    )
                )

            # 检查集中度风险
            if self.current_metrics.concentration_risk > float(
                self.risk_limits.max_single_position
            ):
                risk_events.append(
                    RiskEvent(
                        event_id=f"concentration_risk_{int(time.time())}",
                        risk_type=RiskType.CONCENTRATION,
                        risk_level=RiskLevel.MEDIUM,
                        message=f"Position concentration too high: {self.current_metrics.concentration_risk:.2%}",
                        current_value=self.current_metrics.concentration_risk,
                        threshold=float(self.risk_limits.max_single_position),
                        suggested_action="Diversify positions across strikes and expiries",
                    )
                )

            # 检查到期风险
            if (
                self.current_metrics.days_to_nearest_expiry
                <= self.risk_limits.expiry_warning_days
            ):
                risk_level = (
                    RiskLevel.CRITICAL
                    if self.current_metrics.days_to_nearest_expiry
                    <= self.risk_limits.min_days_to_expiry
                    else RiskLevel.HIGH
                )
                risk_events.append(
                    RiskEvent(
                        event_id=f"expiry_risk_{int(time.time())}",
                        risk_type=RiskType.EXPIRY,
                        risk_level=risk_level,
                        message=f"Options expiring soon: {self.current_metrics.days_to_nearest_expiry} days",
                        current_value=self.current_metrics.days_to_nearest_expiry,
                        threshold=self.risk_limits.expiry_warning_days,
                        suggested_action="Prepare for expiry or roll positions",
                    )
                )

            return risk_events

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk limit check failed: {e}")
            return risk_events

    async def _handle_risk_events(self, risk_events: list[RiskEvent]):
        """处理风险事件"""
        try:
            for event in risk_events:
                # 记录风险事件
                self.risk_events.append(event)

                # 发布风险事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.RISK_EVENT,
                            data={
                                "event_id": event.event_id,
                                "risk_type": event.risk_type.value,
                                "risk_level": event.risk_level.value,
                                "message": event.message,
                                "current_value": float(event.current_value),
                                "threshold": float(event.threshold),
                                "suggested_action": event.suggested_action,
                                "timestamp": event.timestamp.isoformat(),
                            },
                        )
                    )

                # 记录日志
                if self.logger:
                    log_level = (
                        "critical"
                        if event.risk_level == RiskLevel.CRITICAL
                        else "warning"
                    )
                    await getattr(self.logger, log_level)(
                        f"Risk Event: {event.message} - {event.suggested_action}"
                    )

                # 执行自动化风险处理
                if self._emergency_actions_enabled and event.risk_level in [
                    RiskLevel.HIGH,
                    RiskLevel.CRITICAL,
                ]:
                    await self._execute_emergency_actions(event)

            # 清理旧的风险事件（保留最近100个）
            if len(self.risk_events) > 100:
                self.risk_events = self.risk_events[-100:]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk event handling failed: {e}")

    async def _execute_emergency_actions(self, event: RiskEvent):
        """执行紧急风险处理"""
        try:
            if (
                event.risk_type == RiskType.MARGIN
                and event.risk_level == RiskLevel.CRITICAL
            ):
                # 保证金不足，触发紧急减仓
                await self._trigger_emergency_position_reduction(0.3)  # 减仓30%

            elif (
                event.risk_type == RiskType.DELTA and event.risk_level == RiskLevel.HIGH
            ):
                # Delta敞口过大，触发对冲
                await self._trigger_delta_hedging()

            elif (
                event.risk_type == RiskType.EXPIRY
                and event.risk_level == RiskLevel.CRITICAL
            ):
                # 即将到期，触发紧急处理
                await self._trigger_expiry_emergency_handling()

            # 进入紧急模式
            if not self._emergency_mode:
                self._emergency_mode = True
                if self.logger:
                    await self.logger.critical(
                        "System entered emergency mode due to critical risk"
                    )

                # 发布紧急模式事件
                if self.event_bus:
                    from src.core.event_bus import EventType

                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.EMERGENCY_MODE_ACTIVATED,
                            data={
                                "trigger_event": event.event_id,
                                "risk_type": event.risk_type.value,
                                "timestamp": datetime.now(UTC).isoformat(),
                            },
                        )
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Emergency action execution failed: {e}")

    async def _trigger_emergency_position_reduction(self, reduction_ratio: float):
        """触发紧急减仓"""
        if self.event_bus:
            from src.core.event_bus import EventType

            await self.event_bus.publish(
                BaseEvent(
                    event_type=EventType.EMERGENCY_POSITION_REDUCTION,
                    data={
                        "reduction_ratio": reduction_ratio,
                        "reason": "margin_risk",
                        "timestamp": datetime.now(UTC).isoformat(),
                    },
                )
            )

    async def _trigger_delta_hedging(self):
        """触发Delta对冲"""
        if self.event_bus:
            from src.core.event_bus import EventType

            await self.event_bus.publish(
                BaseEvent(
                    event_type=EventType.EMERGENCY_DELTA_HEDGING,
                    data={
                        "current_delta": float(self.current_metrics.total_delta)
                        if self.current_metrics
                        else 0,
                        "timestamp": datetime.now(UTC).isoformat(),
                    },
                )
            )

    async def _trigger_expiry_emergency_handling(self):
        """触发到期紧急处理"""
        if self.event_bus:
            from src.core.event_bus import EventType

            await self.event_bus.publish(
                BaseEvent(
                    event_type=EventType.EXPIRY_EMERGENCY_HANDLING,
                    data={
                        "days_to_expiry": self.current_metrics.days_to_nearest_expiry
                        if self.current_metrics
                        else 0,
                        "timestamp": datetime.now(UTC).isoformat(),
                    },
                )
            )

    # 公共接口方法

    async def get_current_risk_metrics(self) -> RiskMetrics | None:
        """获取当前风险指标"""
        return self.current_metrics

    async def get_risk_events(self, limit: int = 50) -> list[RiskEvent]:
        """获取风险事件历史"""
        return self.risk_events[-limit:] if self.risk_events else []

    async def validate_order_risk(self, order_data: dict[str, Any]) -> tuple[bool, str]:
        """验证订单风险 - 增强版策略集成"""
        try:
            if not self.current_metrics:
                return False, "Risk metrics not available"

            # 1. 基础风险检查
            # 检查保证金是否充足
            if self.current_metrics.margin_usage_ratio > Decimal("0.9"):
                return False, "Insufficient margin for new orders"

            # 检查流动性
            if self.current_metrics.liquidity_score < 0.3:
                return False, "Market liquidity too low for new orders"

            # 检查是否在紧急模式
            if self._emergency_mode:
                return False, "System in emergency mode, new orders blocked"

            # 2. 策略特定风险检查
            instrument = order_data.get("instrument", {})
            signal = order_data.get("signal")

            # Delta风险检查
            current_delta = abs(self.current_metrics.total_delta)
            if current_delta > self.risk_limits.max_delta_exposure * Decimal(
                "0.8"
            ):  # 80%阈值
                return False, f"Delta exposure near limit: {current_delta:.3f}"

            # Gamma风险检查
            current_gamma = abs(self.current_metrics.total_gamma)
            if current_gamma > self.risk_limits.max_gamma_exposure * Decimal("0.8"):
                return False, f"Gamma exposure near limit: {current_gamma:.3f}"

            # 3. 期权特定检查
            if instrument:
                greeks = instrument.get("greeks", {})

                # 检查单个仓位的Delta贡献
                option_delta = abs(greeks.get("delta", 0))
                # 假设标准仓位大小，检查Delta贡献
                estimated_position_delta = Decimal(str(option_delta)) * Decimal(
                    "1.0"
                )  # 标准化仓位
                if (
                    current_delta + estimated_position_delta
                    > self.risk_limits.max_delta_exposure
                ):
                    return (
                        False,
                        f"New position would exceed Delta limit: {current_delta + estimated_position_delta:.3f}",
                    )

                # 检查期权到期风险
                time_to_expiry = instrument.get("time_to_expiry_days", 999)
                if time_to_expiry <= self.risk_limits.min_days_to_expiry:
                    return False, f"Option too close to expiry: {time_to_expiry} days"

            # 4. 信号质量检查
            if signal:
                confidence = getattr(signal, "confidence", 0.5)
                strength = getattr(signal, "strength", 0.5)

                # 信号强度过低
                if strength < 0.3:
                    return False, f"Signal strength too low: {strength:.2f}"

                # 置信度过低
                if confidence < 0.5:
                    return False, f"Signal confidence too low: {confidence:.2f}"

                # 组合信号质量评分
                signal_quality = strength * 0.6 + confidence * 0.4
                if signal_quality < 0.4:
                    return (
                        False,
                        f"Overall signal quality too low: {signal_quality:.2f}",
                    )

            # 5. 集中度风险检查
            if self.current_metrics.concentration_risk > 0.6:  # 60%集中度限制
                return (
                    False,
                    f"Portfolio concentration too high: {self.current_metrics.concentration_risk:.2%}",
                )

            # 6. VaR检查
            if (
                self.current_metrics.portfolio_var
                > self.risk_limits.max_portfolio_var * Decimal("0.8")
            ):
                return (
                    False,
                    f"Portfolio VaR near limit: {float(self.current_metrics.portfolio_var):.2%}",
                )

            return True, "Order risk validation passed"

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order risk validation failed: {e}")
            return False, f"Risk validation error: {e}"

    async def update_risk_limits(self, new_limits: dict[str, Any]) -> bool:
        """更新风险限制"""
        try:
            for key, value in new_limits.items():
                if hasattr(self.risk_limits, key):
                    if isinstance(getattr(self.risk_limits, key), Decimal):
                        setattr(self.risk_limits, key, Decimal(str(value)))
                    else:
                        setattr(self.risk_limits, key, value)

            if self.logger:
                await self.logger.info(f"Risk limits updated: {new_limits}")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk limits update failed: {e}")
            return False

    async def reset_emergency_mode(self) -> bool:
        """重置紧急模式"""
        try:
            self._emergency_mode = False

            if self.logger:
                await self.logger.info("Emergency mode reset")

            # 发布紧急模式重置事件
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.EMERGENCY_MODE_RESET,
                        data={"timestamp": datetime.now(UTC).isoformat()},
                    )
                )

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Emergency mode reset failed: {e}")
            return False

    async def get_risk_summary(self) -> dict[str, Any]:
        """获取风险摘要"""
        try:
            if not self.current_metrics:
                return {"status": "no_data"}

            # 计算风险评分
            risk_score = 0.0
            risk_factors = []

            # Delta风险
            delta_risk = min(
                1.0,
                float(
                    abs(self.current_metrics.total_delta)
                    / self.risk_limits.max_delta_exposure
                ),
            )
            risk_score += delta_risk * 0.2
            if delta_risk > 0.8:
                risk_factors.append(
                    f"High Delta exposure: {self.current_metrics.total_delta}"
                )

            # 保证金风险
            margin_risk = float(
                self.current_metrics.margin_usage_ratio
                / self.risk_limits.max_margin_usage
            )
            risk_score += margin_risk * 0.3
            if margin_risk > 0.8:
                risk_factors.append(
                    f"High margin usage: {self.current_metrics.margin_usage_ratio:.2%}"
                )

            # 流动性风险
            liquidity_risk = max(
                0.0,
                1.0
                - self.current_metrics.liquidity_score
                / self.risk_limits.min_liquidity_score,
            )
            risk_score += liquidity_risk * 0.2
            if liquidity_risk > 0.5:
                risk_factors.append(
                    f"Low liquidity: {self.current_metrics.liquidity_score:.2f}"
                )

            # 到期风险
            expiry_risk = max(
                0.0,
                1.0
                - self.current_metrics.days_to_nearest_expiry
                / self.risk_limits.expiry_warning_days,
            )
            risk_score += expiry_risk * 0.3
            if expiry_risk > 0.5:
                risk_factors.append(
                    f"Near expiry: {self.current_metrics.days_to_nearest_expiry} days"
                )

            # 确定风险级别
            if risk_score > 0.8:
                risk_level = "CRITICAL"
            elif risk_score > 0.6:
                risk_level = "HIGH"
            elif risk_score > 0.4:
                risk_level = "MEDIUM"
            else:
                risk_level = "LOW"

            return {
                "status": "active",
                "risk_score": round(risk_score, 3),
                "risk_level": risk_level,
                "emergency_mode": self._emergency_mode,
                "risk_factors": risk_factors,
                "metrics": {
                    "total_delta": float(self.current_metrics.total_delta),
                    "total_gamma": float(self.current_metrics.total_gamma),
                    "margin_usage": float(self.current_metrics.margin_usage_ratio),
                    "liquidity_score": self.current_metrics.liquidity_score,
                    "days_to_expiry": self.current_metrics.days_to_nearest_expiry,
                },
                "last_check": self.last_risk_check.isoformat()
                if self.last_risk_check
                else None,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk summary generation failed: {e}")
            return {"status": "error", "message": str(e)}

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    async def get_strategy_risk_advice(
        self, strategy_context: dict[str, Any]
    ) -> dict[str, Any]:
        """为策略提供实时风险建议"""
        try:
            _ = strategy_context  # future use; referenced to avoid unused warnings
            if not self.current_metrics:
                return {
                    "advice": "wait",
                    "reason": "Risk metrics not available",
                    "risk_score": 1.0,
                    "suggested_actions": ["Wait for risk metrics to be calculated"],
                }

            # 计算综合风险评分
            risk_factors = {}
            total_risk_score = 0.0
            suggestions = []

            # 1. 保证金风险评分 (权重30%)
            margin_usage = float(self.current_metrics.margin_usage_ratio)
            margin_risk = min(1.0, margin_usage / 0.8)  # 80%为风险上限
            risk_factors["margin_risk"] = margin_risk
            total_risk_score += margin_risk * 0.3

            if margin_risk > 0.7:
                suggestions.append(
                    "Consider reducing position sizes due to high margin usage"
                )
            elif margin_risk < 0.3:
                suggestions.append(
                    "Margin usage healthy, can consider larger positions"
                )

            # 2. 流动性风险评分 (权重20%)
            liquidity_risk = max(0.0, 1.0 - self.current_metrics.liquidity_score / 0.8)
            risk_factors["liquidity_risk"] = liquidity_risk
            total_risk_score += liquidity_risk * 0.2

            if liquidity_risk > 0.5:
                suggestions.append("Market liquidity poor, avoid large positions")

            # 3. Greeks风险评分 (权重25%)
            delta_risk = min(
                1.0,
                float(abs(self.current_metrics.total_delta))
                / float(self.risk_limits.max_delta_exposure),
            )
            gamma_risk = min(
                1.0,
                float(abs(self.current_metrics.total_gamma))
                / float(self.risk_limits.max_gamma_exposure),
            )
            vega_risk = min(
                1.0,
                float(abs(self.current_metrics.total_vega))
                / float(self.risk_limits.max_vega_exposure),
            )

            greeks_risk = max(delta_risk, gamma_risk, vega_risk)
            risk_factors["greeks_risk"] = greeks_risk
            risk_factors["delta_risk"] = delta_risk
            risk_factors["gamma_risk"] = gamma_risk
            risk_factors["vega_risk"] = vega_risk
            total_risk_score += greeks_risk * 0.25

            if greeks_risk > 0.7:
                suggestions.append(
                    "Greeks exposure high, consider hedging or reducing positions"
                )

            # 4. 到期风险评分 (权重15%)
            days_to_expiry = self.current_metrics.days_to_nearest_expiry
            expiry_risk = max(0.0, 1.0 - days_to_expiry / 14.0)  # 14天内开始有风险
            risk_factors["expiry_risk"] = expiry_risk
            total_risk_score += expiry_risk * 0.15

            if expiry_risk > 0.5:
                suggestions.append(
                    f"Options expiring soon ({days_to_expiry} days), prepare exit or roll"
                )

            # 5. VaR风险评分 (权重10%)
            var_risk = min(
                1.0,
                float(self.current_metrics.portfolio_var)
                / float(self.risk_limits.max_portfolio_var),
            )
            risk_factors["var_risk"] = var_risk
            total_risk_score += var_risk * 0.1

            # 确定建议动作
            advice = "proceed"  # 默认
            if total_risk_score > 0.8:
                advice = "reduce_risk"
                suggestions.insert(
                    0, "High overall risk detected - reduce positions immediately"
                )
            elif total_risk_score > 0.6:
                advice = "caution"
                suggestions.insert(0, "Moderate risk levels - proceed with caution")
            elif total_risk_score < 0.3:
                advice = "opportunity"
                suggestions.insert(
                    0, "Low risk environment - good opportunity for larger positions"
                )

            # 紧急模式覆盖
            if self._emergency_mode:
                advice = "emergency_stop"
                suggestions = ["System in emergency mode - halt all new positions"]

            return {
                "advice": advice,
                "risk_score": round(total_risk_score, 3),
                "risk_factors": risk_factors,
                "suggested_actions": suggestions,
                "emergency_mode": self._emergency_mode,
                "metrics_timestamp": self.current_metrics.timestamp.isoformat()
                if self.current_metrics.timestamp
                else None,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Strategy risk advice generation failed: {e}")
            return {
                "advice": "wait",
                "reason": f"Risk advice error: {e}",
                "risk_score": 1.0,
                "suggested_actions": ["Wait for system stabilization"],
            }

    async def _get_current_btc_price(self) -> float:
        """获取当前BTC价格 - 使用统一的MarketDataService"""
        try:
            if self.market_data_service:
                price = await self.market_data_service.get_btc_price()
                if price:
                    return float(price)

            if not self.cache_manager:
                return 50000.0  # 默认价格

            return 50000.0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get current BTC price: {e}")
            return 50000.0

    async def _get_current_volatility(self) -> float:
        """获取当前波动率"""
        try:
            if not self.market_data_service:
                return 0.5  # 默认50%波动率

            vol_data = await self.market_data_service.get_market_data(
                "btc", "implied_volatility"
            )
            if vol_data:
                return float(vol_data.get("iv", 0.5))

            return 0.5

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get current volatility: {e}")
            return 0.5

    async def _calculate_portfolio_var(
        self, positions_data: dict[str, Any], market_params: dict[str, float]
    ) -> Decimal:
        """计算组合VaR - 使用统一的VaR计算服务"""
        try:
            if not positions_data:
                return Decimal("0")

            # 检查VaR计算服务是否可用
            if not self.var_calculation_service:
                return Decimal("0.0")

            # 转换持仓数据格式
            positions = []
            for symbol, position_data in positions_data.items():
                if (
                    isinstance(position_data, dict)
                    and abs(position_data.get("size", 0)) > 0.001
                    and self._is_option_symbol(symbol)
                ):
                    option_info = self._parse_option_symbol(symbol)
                    if option_info:
                        # 计算到期时间
                        current_time = datetime.now(UTC)
                        time_to_expiry = (
                            option_info["expiry_date"] - current_time
                        ).total_seconds() / (365.25 * 24 * 3600)
                        time_to_expiry = max(0.001, time_to_expiry)

                        position = {
                            "instrument_type": "option",
                            "option_type": option_info["option_type"],
                            "strike": float(option_info["strike_price"]),
                            "time_to_expiry": time_to_expiry,
                            "side": "long"
                            if position_data.get("size", 0) > 0
                            else "short",
                            "quantity": abs(position_data.get("size", 0)),
                            "delta": position_data.get("delta", 0),
                            "gamma": position_data.get("gamma", 0),
                            "market_value": position_data.get("market_value", 0),
                        }
                        positions.append(position)

            if not positions:
                return Decimal("0")

            # 自适应模拟次数配置
            num_positions = len(positions)
            if self._adaptive_simulations:
                if num_positions <= 5:
                    num_sims = self._min_simulations
                elif num_positions <= 15:
                    num_sims = 5000
                else:
                    num_sims = self._max_simulations
            else:
                num_sims = self._max_simulations

            # VaR计算参数
            var_params = VaRParameters(
                confidence_level=0.95,
                time_horizon=1.0,
                num_simulations=num_sims,
                price_change_threshold=0.05,
            )

            # 市场数据格式化
            market_data = {
                "current_price": market_params.get("current_price", 50000.0),
                "volatility": market_params.get("volatility", 0.5),
            }

            # 使用统一VaR计算服务
            var_result = await self.var_calculation_service.calculate_portfolio_var(
                positions, market_data, var_params, method="monte_carlo"
            )

            if self.logger:
                await self.logger.debug(
                    f"Portfolio VaR (unified service): VaR95={var_result.var_95:.4f}, "
                    f"VaR99={var_result.var_99:.4f}, method={var_result.calculation_method}, "
                    f"time={var_result.computation_time:.3f}s"
                )

            return var_result.var_95

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio VaR calculation failed: {e}")

            return Decimal("0.0")

    def _is_option_symbol(self, symbol: str) -> bool:
        """判断是否为期权符号"""
        return "-C" in symbol or "-P" in symbol

    def _parse_option_symbol(self, symbol: str) -> dict[str, Any] | None:
        """解析期权符号"""
        try:
            # 示例: BTC-07JAN25-45000-C
            parts = symbol.split("-")
            if len(parts) != 4:
                return None

            underlying = parts[0]
            expiry_str = parts[1]
            strike_str = parts[2]
            option_type = "call" if parts[3] == "C" else "put"

            # 解析到期日
            expiry_date = datetime.strptime(
                f"20{expiry_str[5:]}-{expiry_str[2:5]}-{expiry_str[:2]}", "%Y-%b-%d"
            ).replace(tzinfo=UTC)

            # 解析行权价
            strike_price = Decimal(strike_str)

            return {
                "underlying": underlying,
                "expiry_date": expiry_date,
                "strike_price": strike_price,
                "option_type": option_type,
            }

        except Exception:
            return None

    def set_market_data_service(self, market_data_service: MarketDataService):
        """设置市场数据服务"""
        self.market_data_service = market_data_service

    def set_var_calculation_service(self, var_service: VaRCalculationService):
        """设置VaR计算服务"""
        self.var_calculation_service = var_service

    async def _persist_risk_metrics(self) -> None:
        """将当前风险指标写入TimescaleDB的risk_metrics表"""
        try:
            if not self.current_metrics:
                return
            # 兜底解析 db_manager
            if not self.db_manager and self.dependency_injector:
                try:
                    self.db_manager = await self.dependency_injector.resolve(
                        TimescaleDBManager
                    )
                except Exception:
                    pass

            if not self.db_manager:
                return

            metrics = self.current_metrics
            payload = {
                "time": metrics.timestamp,
                "total_delta": metrics.total_delta,
                "total_gamma": metrics.total_gamma,
                "total_theta": metrics.total_theta,
                "total_vega": metrics.total_vega,
                "margin_used": metrics.margin_used,
                "margin_available": metrics.margin_available,
                "var_1d": metrics.portfolio_var,
                "max_drawdown": Decimal("0"),  # 可由PnLTracker计算后替换
                "portfolio_value": metrics.portfolio_value,
                "metadata": {
                    "margin_usage_ratio": float(metrics.margin_usage_ratio),
                    "liquidity_score": float(metrics.liquidity_score),
                    "concentration_risk": float(metrics.concentration_risk),
                    "days_to_nearest_expiry": metrics.days_to_nearest_expiry,
                    "max_single_position_risk": float(
                        metrics.max_single_position_risk
                    ),
                },
            }

            await self.db_manager.insert_risk_metrics(payload)
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Persist risk metrics failed: {e}")
