"""
保证金计算器

负责精确计算期权交易的保证金要求，支持组合保证金优惠和交易所规则适配
"""

from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import EventBus
from src.data.cache_manager import CacheManager
from src.services.market_data_service import MarketDataService


class MarginType(Enum):
    """保证金类型"""

    INITIAL = "initial"  # 初始保证金
    MAINTENANCE = "maintenance"  # 维持保证金
    PORTFOLIO = "portfolio"  # 组合保证金


class OptionType(Enum):
    """期权类型"""

    CALL = "call"
    PUT = "put"


@dataclass
class OptionContract:
    """期权合约"""

    symbol: str
    option_type: OptionType
    strike: Decimal
    expiry: datetime
    size: Decimal  # 正数为多头，负数为空头
    mark_price: Decimal
    underlying_price: Decimal
    implied_vol: Decimal
    delta: Decimal = Decimal("0")
    gamma: Decimal = Decimal("0")
    theta: Decimal = Decimal("0")
    vega: Decimal = Decimal("0")


@dataclass
class MarginRequirement:
    """保证金要求"""

    contract_symbol: str
    initial_margin: Decimal
    maintenance_margin: Decimal
    portfolio_margin: Decimal
    margin_type: MarginType
    calculation_method: str
    risk_factor: Decimal = Decimal("1.0")
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


@dataclass
class PortfolioMargin:
    """组合保证金"""

    total_initial_margin: Decimal
    total_maintenance_margin: Decimal
    total_portfolio_margin: Decimal
    margin_utilization: Decimal
    available_margin: Decimal
    margin_call_level: Decimal
    liquidation_level: Decimal
    diversification_benefit: Decimal = Decimal("0")
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


class MarginCalculator(BaseComponent):
    """
    保证金计算器

    功能特性：
    - 期权组合保证金精确计算
    - 支持Deribit保证金规则
    - 组合保证金优惠计算
    - 保证金需求预测
    - 实时保证金监控
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("MarginCalculator", config)

        # 组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None

        # 保证金参数配置
        self._load_margin_config()

        # 保证金计算缓存
        self._margin_cache: dict[str, MarginRequirement] = {}
        self._portfolio_margin_cache: PortfolioMargin | None = None
        self._cache_ttl = 300  # 5分钟缓存

        # 风险参数
        self.risk_free_rate = Decimal("0.02")  # 无风险利率2%（适合加密货币市场）
        self.volatility_floor = Decimal("0.1")  # 最小波动率10%
        self.volatility_cap = Decimal("3.0")  # 最大波动率300%

    def _load_margin_config(self):
        """加载保证金配置"""
        margin_config = self.config.get("margin", {}) if self.config else {}

        # Deribit保证金参数
        self.deribit_params = {
            "option_margin_rate": Decimal(
                str(margin_config.get("option_margin_rate", "0.15"))
            ),  # 15%
            "underlying_margin_rate": Decimal(
                str(margin_config.get("underlying_margin_rate", "0.1"))
            ),  # 10%
            "min_margin_rate": Decimal(
                str(margin_config.get("min_margin_rate", "0.05"))
            ),  # 5%
            "portfolio_margin_discount": Decimal(
                str(margin_config.get("portfolio_margin_discount", "0.8"))
            ),  # 20%折扣
            "stress_test_scenarios": margin_config.get(
                "stress_test_scenarios", 16
            ),  # 16个压力测试场景
        }

        # 风险参数
        self.risk_params = {
            "price_shock_up": Decimal(
                str(margin_config.get("price_shock_up", "0.15"))
            ),  # 上涨15%
            "price_shock_down": Decimal(
                str(margin_config.get("price_shock_down", "0.15"))
            ),  # 下跌15%
            "vol_shock_up": Decimal(
                str(margin_config.get("vol_shock_up", "0.25"))
            ),  # 波动率上升25%
            "vol_shock_down": Decimal(
                str(margin_config.get("vol_shock_down", "0.25"))
            ),  # 波动率下降25%
            "time_decay": Decimal(
                str(margin_config.get("time_decay", "0.1"))
            ),  # 时间衰减10%
        }

    async def _initialize_impl(self) -> bool:
        """初始化保证金计算器"""
        try:
            if self.logger:
                await self.logger.info("MarginCalculator initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarginCalculator initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动保证金计算器"""
        try:
            if self.logger:
                await self.logger.info("MarginCalculator started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarginCalculator start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止保证金计算器"""
        try:
            # 清理缓存
            self._margin_cache.clear()
            self._portfolio_margin_cache = None

            if self.logger:
                await self.logger.info("MarginCalculator stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarginCalculator stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 测试保证金计算
            test_contract = OptionContract(
                symbol="BTC-TEST",
                option_type=OptionType.CALL,
                strike=Decimal("50000"),
                expiry=datetime.now(UTC),
                size=Decimal("1"),
                mark_price=Decimal("1000"),
                underlying_price=Decimal("50000"),
                implied_vol=Decimal("0.8"),
            )

            margin_req = await self.calculate_option_margin(test_contract)
            if margin_req and margin_req.initial_margin > 0:
                return HealthCheckResult(
                    status=HealthStatus.HEALTHY,
                    message="Margin calculation operational",
                )
            else:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message="Margin calculation test failed",
                )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def calculate_option_margin(
        self, contract: OptionContract
    ) -> MarginRequirement | None:
        """计算单个期权合约的保证金"""
        try:
            # 检查缓存
            cache_key = f"{contract.symbol}_{contract.size}_{contract.mark_price}"
            if cache_key in self._margin_cache:
                cached_margin = self._margin_cache[cache_key]
                if (
                    datetime.now(UTC) - cached_margin.timestamp
                ).total_seconds() < self._cache_ttl:
                    return cached_margin

            # 计算初始保证金
            initial_margin = await self._calculate_initial_margin(contract)

            # 计算维持保证金（通常是初始保证金的80%）
            maintenance_margin = initial_margin * Decimal("0.8")

            # 计算组合保证金（使用压力测试）
            portfolio_margin = await self._calculate_portfolio_margin_single(contract)

            # 创建保证金要求
            margin_req = MarginRequirement(
                contract_symbol=contract.symbol,
                initial_margin=initial_margin,
                maintenance_margin=maintenance_margin,
                portfolio_margin=portfolio_margin,
                margin_type=MarginType.INITIAL,
                calculation_method="deribit_standard",
            )

            # 缓存结果
            self._margin_cache[cache_key] = margin_req

            return margin_req

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Option margin calculation failed: {e}")
            return None

    async def _calculate_initial_margin(self, contract: OptionContract) -> Decimal:
        """计算初始保证金"""
        try:
            # Deribit期权保证金公式:
            # Margin = max(
            #   Option_Price + max(0, Underlying_Price * Margin_Rate - OTM_Amount),
            #   Option_Price + Underlying_Price * Min_Margin_Rate
            # )

            option_price = contract.mark_price * abs(contract.size)
            underlying_price = contract.underlying_price

            # 计算价外金额
            if contract.option_type == OptionType.CALL:
                otm_amount = max(Decimal("0"), contract.strike - underlying_price)
            else:  # PUT
                otm_amount = max(Decimal("0"), underlying_price - contract.strike)

            # 标准保证金计算
            standard_margin = option_price + max(
                Decimal("0"),
                underlying_price * self.deribit_params["option_margin_rate"]
                - otm_amount,
            )

            # 最小保证金计算
            min_margin = (
                option_price + underlying_price * self.deribit_params["min_margin_rate"]
            )

            # 取较大值
            initial_margin = max(standard_margin, min_margin)

            # 对于空头期权，保证金更高
            if contract.size < 0:
                initial_margin = max(initial_margin, option_price * Decimal("2"))

            return initial_margin

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Initial margin calculation failed: {e}")
            return Decimal("0")

    async def _calculate_portfolio_margin_single(
        self, contract: OptionContract
    ) -> Decimal:
        """计算单个合约的组合保证金"""
        try:
            # 使用简化的压力测试方法
            max_loss = Decimal("0")

            # 价格上涨场景
            price_up = contract.underlying_price * (
                Decimal("1") + self.risk_params["price_shock_up"]
            )
            loss_up = await self._calculate_scenario_loss(
                contract, price_up, contract.implied_vol
            )
            max_loss = max(max_loss, loss_up)

            # 价格下跌场景
            price_down = contract.underlying_price * (
                Decimal("1") - self.risk_params["price_shock_down"]
            )
            loss_down = await self._calculate_scenario_loss(
                contract, price_down, contract.implied_vol
            )
            max_loss = max(max_loss, loss_down)

            # 波动率上升场景
            vol_up = contract.implied_vol * (
                Decimal("1") + self.risk_params["vol_shock_up"]
            )
            loss_vol_up = await self._calculate_scenario_loss(
                contract, contract.underlying_price, vol_up
            )
            max_loss = max(max_loss, loss_vol_up)

            # 波动率下降场景
            vol_down = contract.implied_vol * (
                Decimal("1") - self.risk_params["vol_shock_down"]
            )
            loss_vol_down = await self._calculate_scenario_loss(
                contract, contract.underlying_price, vol_down
            )
            max_loss = max(max_loss, loss_vol_down)

            return max_loss

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio margin calculation failed: {e}")
            return Decimal("0")

    async def _calculate_scenario_loss(
        self, contract: OptionContract, scenario_price: Decimal, scenario_vol: Decimal
    ) -> Decimal | None:
        """计算压力测试场景下的损失 - 完整Black-Scholes模型"""
        try:
            from math import exp, log, sqrt

            from scipy.stats import norm

            # 参数准备
            S = float(scenario_price)  # 标的价格
            K = float(contract.strike)  # 执行价格
            T = contract.time_to_expiry  # 到期时间（年）
            r = 0.05  # 无风险利率（假设5%）
            sigma = float(scenario_vol)  # 波动率

            if T <= 0:  # 已到期
                if contract.option_type == OptionType.CALL:
                    return max(Decimal("0"), scenario_price - contract.strike)
                else:
                    return max(Decimal("0"), contract.strike - scenario_price)

            # Black-Scholes公式实现
            try:
                # 计算d1和d2
                d1 = (log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * sqrt(T))
                d2 = d1 - sigma * sqrt(T)

                # 计算期权价值
                if contract.option_type == OptionType.CALL:
                    # Call期权价格
                    option_price = S * norm.cdf(d1) - K * exp(-r * T) * norm.cdf(d2)
                else:
                    # Put期权价格
                    option_price = K * exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

                scenario_option_value = Decimal(str(max(0, option_price)))

                # 计算Greeks用于更精确的估值
                if scenario_option_value > 0:
                    # Delta
                    delta = (
                        norm.cdf(d1)
                        if contract.option_type == OptionType.CALL
                        else norm.cdf(d1) - 1
                    )

                    # Gamma
                    gamma = norm.pdf(d1) / (S * sigma * sqrt(T))

                    # Vega
                    vega = S * norm.pdf(d1) * sqrt(T) / 100  # 除以100转换为百分点

                    # Theta
                    if contract.option_type == OptionType.CALL:
                        theta = (
                            -S * norm.pdf(d1) * sigma / (2 * sqrt(T))
                            - r * K * exp(-r * T) * norm.cdf(d2)
                        ) / 365
                    else:
                        theta = (
                            -S * norm.pdf(d1) * sigma / (2 * sqrt(T))
                            + r * K * exp(-r * T) * norm.cdf(-d2)
                        ) / 365

                    # 保存Greeks到合约对象（如果需要）
                    if hasattr(contract, "_calculated_greeks"):
                        contract._calculated_greeks = {
                            "delta": delta,
                            "gamma": gamma,
                            "vega": vega,
                            "theta": theta,
                        }

            except (ValueError, ZeroDivisionError, OverflowError) as math_error:
                if self.logger:
                    await self.logger.error(
                        f"Black-Scholes calculation failed: {math_error}"
                    )
                return None

            # 计算损失（当前价值 vs 情景价值）
            current_option_value = contract.mark_price
            loss = current_option_value - scenario_option_value

            # 对于空头仓位，损失方向相反
            if hasattr(contract, "position_size") and contract.position_size < 0:
                loss = -loss

            return max(Decimal("0"), loss)  # 只返回正损失

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Scenario loss calculation failed: {e}")
            return None

    async def calculate_portfolio_margin(
        self, contracts: list[OptionContract]
    ) -> PortfolioMargin | None:
        """计算投资组合保证金"""
        try:
            if not contracts:
                return None

            # 计算单个合约保证金
            total_initial = Decimal("0")
            total_maintenance = Decimal("0")
            individual_margins = []

            for contract in contracts:
                margin_req = await self.calculate_option_margin(contract)
                if margin_req:
                    total_initial += margin_req.initial_margin
                    total_maintenance += margin_req.maintenance_margin
                    individual_margins.append(margin_req)

            # 计算组合保证金（压力测试）
            portfolio_margin = await self._calculate_portfolio_stress_test(contracts)
            if portfolio_margin is None:
                return None

            # 计算多样化收益
            diversification_benefit = max(
                Decimal("0"), total_initial - portfolio_margin
            )

            # 获取账户信息
            account_info = await self._get_account_margin_info()
            available_margin = (
                account_info.get("available_margin", Decimal("0"))
                if account_info
                else Decimal("0")
            )

            # 计算保证金使用率
            total_equity = (
                account_info.get("total_equity", Decimal("0"))
                if account_info
                else Decimal("0")
            )
            margin_utilization = (
                portfolio_margin / total_equity if total_equity > 0 else Decimal("0")
            )

            # 计算保证金追缴和强平水平
            margin_call_level = portfolio_margin * Decimal("1.2")  # 120%
            liquidation_level = portfolio_margin * Decimal("1.1")  # 110%

            portfolio_margin_result = PortfolioMargin(
                total_initial_margin=total_initial,
                total_maintenance_margin=total_maintenance,
                total_portfolio_margin=portfolio_margin,
                margin_utilization=margin_utilization,
                available_margin=available_margin,
                margin_call_level=margin_call_level,
                liquidation_level=liquidation_level,
                diversification_benefit=diversification_benefit,
            )

            # 缓存结果
            self._portfolio_margin_cache = portfolio_margin_result

            return portfolio_margin_result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio margin calculation failed: {e}")
            return None

    async def _calculate_portfolio_stress_test(
        self, contracts: list[OptionContract]
    ) -> Decimal | None:
        """计算投资组合压力测试保证金"""
        try:
            max_portfolio_loss = Decimal("0")

            # 定义压力测试场景
            scenarios = [
                # 价格冲击场景
                {"price_factor": Decimal("1.15"), "vol_factor": Decimal("1.0")},  # +15%
                {"price_factor": Decimal("0.85"), "vol_factor": Decimal("1.0")},  # -15%
                {
                    "price_factor": Decimal("1.10"),
                    "vol_factor": Decimal("1.25"),
                },  # +10%, +25% vol
                {
                    "price_factor": Decimal("0.90"),
                    "vol_factor": Decimal("1.25"),
                },  # -10%, +25% vol
                {
                    "price_factor": Decimal("1.05"),
                    "vol_factor": Decimal("0.75"),
                },  # +5%, -25% vol
                {
                    "price_factor": Decimal("0.95"),
                    "vol_factor": Decimal("0.75"),
                },  # -5%, -25% vol
                # 极端场景
                {
                    "price_factor": Decimal("1.25"),
                    "vol_factor": Decimal("1.5"),
                },  # +25%, +50% vol
                {
                    "price_factor": Decimal("0.75"),
                    "vol_factor": Decimal("1.5"),
                },  # -25%, +50% vol
            ]

            for scenario in scenarios:
                portfolio_loss = Decimal("0")

                for contract in contracts:
                    scenario_price = (
                        contract.underlying_price * scenario["price_factor"]
                    )
                    scenario_vol = contract.implied_vol * scenario["vol_factor"]

                    contract_loss = await self._calculate_scenario_loss(
                        contract, scenario_price, scenario_vol
                    )
                    if contract_loss is None:
                        return None
                    portfolio_loss += contract_loss

                max_portfolio_loss = max(max_portfolio_loss, portfolio_loss)

            # 应用组合保证金折扣
            portfolio_margin = (
                max_portfolio_loss * self.deribit_params["portfolio_margin_discount"]
            )

            return portfolio_margin

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio stress test failed: {e}")
            return None

    async def _get_account_margin_info(self) -> dict[str, Decimal]:
        """获取账户保证金信息"""
        try:
            # 优先使用 MarketDataService
            mds = await self.dependency_injector.resolve(MarketDataService)
            margin_info = await mds.get_margin_info()
            if margin_info:
                return {
                    "total_equity": Decimal(str(margin_info.get("total_equity", 0))),
                    "available_margin": Decimal(
                        str(margin_info.get("available_margin", 0))
                    ),
                    "used_margin": Decimal(str(margin_info.get("used_margin", 0))),
                    "maintenance_margin": Decimal(
                        str(margin_info.get("maintenance_margin", 0))
                    ),
                }

            # 尝试从交易所获取真实保证金信息
            real_margin_info = await self._fetch_real_margin_info()
            if real_margin_info:
                return real_margin_info

            # 最后的保守默认值（仅在所有方法失败时使用）
            if self.logger:
                await self.logger.warning(
                    "无法获取真实保证金信息，使用保守默认值。建议检查交易所连接。"
                )
            return {
                "total_equity": Decimal("5000"),  # 保守的默认账户价值
                "available_margin": Decimal("2000"),  # 保守的可用保证金
                "used_margin": Decimal("0"),
                "maintenance_margin": Decimal("0"),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get account margin info: {e}")
            return {}

    async def get_margin_summary(self) -> dict[str, Any]:
        """获取保证金摘要"""
        try:
            current_positions = await self._get_current_positions()
            portfolio_margin = await self.calculate_portfolio_margin(current_positions)

            if portfolio_margin:
                return {
                    "total_initial_margin": float(
                        portfolio_margin.total_initial_margin
                    ),
                    "total_maintenance_margin": float(
                        portfolio_margin.total_maintenance_margin
                    ),
                    "total_portfolio_margin": float(
                        portfolio_margin.total_portfolio_margin
                    ),
                    "margin_utilization": float(portfolio_margin.margin_utilization),
                    "available_margin": float(portfolio_margin.available_margin),
                    "diversification_benefit": float(
                        portfolio_margin.diversification_benefit
                    ),
                    "margin_call_level": float(portfolio_margin.margin_call_level),
                    "liquidation_level": float(portfolio_margin.liquidation_level),
                    "timestamp": portfolio_margin.timestamp.isoformat(),
                }

            return {"status": "no_positions"}

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Margin summary generation failed: {e}")
            return {"status": "error", "message": str(e)}

    async def _get_current_positions(self) -> list[OptionContract]:
        """获取当前期权仓位"""
        try:
            # 获取当前仓位
            mds = await self.dependency_injector.resolve(MarketDataService)
            positions_data = await mds.get_positions()
            if not positions_data:
                return []

            contracts = []
            for symbol, position_data in positions_data.items():
                if isinstance(position_data, dict) and "option" in symbol.lower():
                    # 解析期权合约信息
                    contract = await self._parse_position_to_contract(
                        symbol, position_data
                    )
                    if contract:
                        contracts.append(contract)

            return contracts

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get current positions: {e}")
            return []

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    async def _fetch_real_margin_info(self) -> dict[str, Decimal] | None:
        """从交易所API获取真实保证金信息"""
        try:
            # 方法1: 从Deribit获取保证金信息
            deribit_margin = await self._get_deribit_margin_info()
            if deribit_margin:
                return deribit_margin

            # 方法2: 从Binance获取保证金信息
            binance_margin = await self._get_binance_margin_info()
            if binance_margin:
                return binance_margin

            # 方法3: 从数据库获取最近的保证金快照
            if hasattr(self, "timescale_manager") and self.timescale_manager:
                db_margin = await self._get_margin_info_from_db()
                if db_margin:
                    return db_margin

            return None

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"获取真实保证金信息失败: {e}")
            return None

    async def _get_deribit_margin_info(self) -> dict[str, Decimal] | None:
        """从Deribit API获取保证金信息"""
        try:
            # 通过依赖注入获取Deribit客户端
            if hasattr(self, "deribit_client") and self.deribit_client:
                client = self.deribit_client
            else:
                # 尝试通过依赖注入器获取
                if hasattr(self, "dependency_injector") and self.dependency_injector:
                    from src.gateways.deribit_client import DeribitClient

                    client = await self.dependency_injector.resolve(DeribitClient)
                else:
                    return None

            if not client:
                return None

            # 获取账户余额信息
            account_summary = await client.get_account_summary()
            if account_summary:
                # 转换为标准格式
                return {
                    "total_equity": Decimal(str(account_summary.get("equity", 0))),
                    "available_margin": Decimal(
                        str(account_summary.get("available_funds", 0))
                    ),
                    "used_margin": Decimal(
                        str(account_summary.get("initial_margin", 0))
                    ),
                    "maintenance_margin": Decimal(
                        str(account_summary.get("maintenance_margin", 0))
                    ),
                }

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从Deribit获取保证金信息失败: {e}")

        return None

    async def _get_binance_margin_info(self) -> dict[str, Decimal] | None:
        """从Binance API获取保证金信息"""
        try:
            # 通过依赖注入获取Binance客户端
            if hasattr(self, "binance_client") and self.binance_client:
                client = self.binance_client
            else:
                # 尝试通过依赖注入器获取
                if hasattr(self, "dependency_injector") and self.dependency_injector:
                    from src.gateways.binance_client import BinanceClient

                    client = await self.dependency_injector.resolve(BinanceClient)
                else:
                    return None

            if not client:
                return None

            # 获取账户信息
            account_info = await client.get_account_info()
            if account_info:
                # 计算总权益和可用保证金
                total_wallet_balance = Decimal("0")
                available_balance = Decimal("0")

                # 处理资产余额
                for asset in account_info.get("balances", []):
                    if asset.get("asset") in ["USDT", "BUSD", "USD"]:
                        balance = Decimal(str(asset.get("free", 0)))
                        locked = Decimal(str(asset.get("locked", 0)))
                        total_wallet_balance += balance + locked
                        available_balance += balance

                return {
                    "total_equity": total_wallet_balance,
                    "available_margin": available_balance,
                    "used_margin": total_wallet_balance - available_balance,
                    "maintenance_margin": Decimal("0"),  # Binance不直接提供该数据
                }

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从Binance获取保证金信息失败: {e}")

        return None

    async def _get_margin_info_from_db(self) -> dict[str, Decimal] | None:
        """从数据库获取最近的保证金信息"""
        try:
            if not self.timescale_manager:
                return None

            # 查询最近1小时内的保证金快照
            query = """
            SELECT total_equity, available_margin, used_margin, maintenance_margin
            FROM margin_snapshots
            WHERE time >= NOW() - INTERVAL '1 hour'
            ORDER BY time DESC
            LIMIT 1
            """

            async with self.timescale_manager.get_connection(read_only=True) as conn:
                result = await conn.fetchrow(query)
                if result:
                    return {
                        "total_equity": Decimal(str(result.get("total_equity", 0))),
                        "available_margin": Decimal(
                            str(result.get("available_margin", 0))
                        ),
                        "used_margin": Decimal(str(result.get("used_margin", 0))),
                        "maintenance_margin": Decimal(
                            str(result.get("maintenance_margin", 0))
                        ),
                    }

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从数据库获取保证金信息失败: {e}")

        return None
