"""
统一市场数据服务

替换分散在各个模块中的市场数据获取逻辑，提供标准化的数据访问接口和缓存键管理
"""

from decimal import Decimal, InvalidOperation
from typing import Any

from src.core.base_component import BaseComponent
from src.data.cache_manager import CacheManager


class MarketDataService(BaseComponent):
    """统一市场数据服务"""

    def __init__(self, cache_manager: CacheManager):
        super().__init__("MarketDataService")
        self.cache_manager = cache_manager
        self.last_btc_price: Decimal | None = None

        # 统一的缓存键映射
        self.CACHE_KEYS = {
            "btc_spot_price": [
                "market_data:btc:spot_price",
                "latest:binance_spot:BTCUSDT",
                ("btc_price", "market_data"),
            ],
            "option_chain": [
                "market_data:deribit_options:BTC:option_chain",
                ("deribit_options", "market_data"),
                ("btc_option_chain", "option_chain"),
                "BTC_options",
            ],
            "market_analysis": [
                ("latest_market_analysis", "analysis"),
                "market_analysis:latest",
                ("market_analysis", "analysis"),
            ],
            "spot_positions": [
                ("spot_positions", "trading"),
                "trading:spot_positions",
                "positions:spot",
            ],
            "price_history": [
                ("btc_price_history", "market_data"),
                "market_data:btc:history",
                "price_history:btc",
            ],
        }

    async def get_btc_price(self) -> Decimal | None:
        """获取当前BTC价格 - 统一实现"""
        try:
            if not self.cache_manager:
                return self.last_btc_price

            # 标准化缓存键
            market_data = await self.cache_manager.get("market_data:btc:spot_price")

            # 如果主键没有数据，尝试备用键
            if not market_data:
                market_data = await self.cache_manager.get(
                    "latest:binance_spot:BTCUSDT"
                )

            # 如果还是没有数据，尝试其他可能的键
            if not market_data:
                market_data = await self.cache_manager.get("btc_price")

            if market_data:
                # 确保market_data是字典类型
                if isinstance(market_data, dict) and "price" in market_data:
                    price = Decimal(str(market_data["price"]))
                    self.last_btc_price = price  # 缓存最后价格
                    return price
                # 如果market_data是数字类型，直接转换
                elif isinstance(market_data, (int | float | str | Decimal)):
                    try:
                        price = Decimal(str(market_data))
                        self.last_btc_price = price
                        return price
                    except (InvalidOperation, ValueError):
                        pass

            # 返回缓存的最后价格
            return self.last_btc_price

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get BTC price: {e}")
            return self.last_btc_price

    async def get_cached(self, key: str, default: Any | None = None) -> Any:
        """通用读取：通过服务访问缓存键（屏蔽直接依赖CacheManager）"""
        try:
            if not self.cache_manager:
                return default
            return await self.cache_manager.get(key, default)
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get cached key {key}: {e}")
            return default

    async def get_ibit_price(self, prefer_realtime: bool = False) -> Decimal | None:
        """获取 IBIT 当前价格

        设计目标：
        - 对信号侧：默认不依赖 IBKR 行情（prefer_realtime=False），仅返回缓存可用值
        - 对执行侧：可设置 prefer_realtime=True 时优先拉取 IBKR 实时价格
        """
        try:
            # 实时优先（执行侧使用）
            if prefer_realtime and self.dependency_injector:
                try:
                    ibkr_client = await self.dependency_injector.resolve_optional(
                        "IBKRClient"
                    )
                    if ibkr_client:
                        md = await ibkr_client.get_market_data("IBIT")
                        if md and (md.get("last_price") or md.get("last")):
                            val = md.get("last_price") or md.get("last")
                            return Decimal(str(val))
                except Exception:
                    # 降级到缓存
                    pass

            # 缓存键读取（信号侧/降级路径）
            for key in (
                "ibit_last_price",
                "market_data:ibit:spot_price",
                "latest:ibit_spot:IBIT",
            ):
                try:
                    val = await self.get_cached(key)
                    if val is not None:
                        return Decimal(str(val))
                except Exception:
                    continue

            return None
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get IBIT price: {e}")
            return None

    async def get_latest_ticker(
        self, exchange: str = "binance"
    ) -> dict[str, Any] | None:
        """获取交易所最新ticker（统一接口）"""
        try:
            ex = exchange.lower()
            # 兼容当前系统的标准缓存键
            if ex == "binance":
                # DataEngine 写入 latest:{source}:{symbol}
                for k in (
                    "latest:binance_spot:BTCUSDT",
                    "latest:binance_futures:BTCUSDT",
                    f"{ex}:latest_ticker",  # 兼容旧键
                ):
                    data = await self.get_cached(k)
                    if data:
                        return data
                return None
            elif ex == "deribit":
                # 期权端统一写入一个代表性的“最新期权快照”键，供就绪性检测
                for k in (
                    "deribit:latest_option",
                    f"{ex}:latest_ticker",  # 兼容旧键
                ):
                    data = await self.get_cached(k)
                    if data:
                        return data
                return None
            else:
                # 其他交易所回退
                return await self.get_cached(f"{ex}:latest_ticker")
        except Exception as e:
            if self.logger:
                await self.logger.error(f"get_latest_ticker failed: {e}")
            return None

    async def get_binance_perpetual(self) -> dict[str, Any] | None:
        """获取Binance永续相关汇总数据"""
        return await self.get_cached("binance:perpetual")

    async def get_price_history(self, symbol: str = "BTC") -> Any:
        """获取价格历史（统一封装多种可能键）"""
        keys = [
            f"market_data:{symbol.lower()}:history",
            f"{symbol.lower()}_price_history",
            "btc_price_history" if symbol.upper() == "BTC" else None,
        ]
        for k in keys:
            if not k:
                continue
            data = await self.get_cached(k)
            if data:
                return data
        return None

    async def get_funding_rate(self, symbol: str = "BTCUSDT") -> Any:
        key = f"market_data:binance_futures:{symbol}:funding_rate"
        data = await self.get_cached(key)
        if isinstance(data, dict) and "rate" in data:
            return data["rate"]
        return data

    async def get_volume_profile(self, symbol: str = "BTCUSDT") -> Any:
        key = f"market_data:binance_spot:{symbol}:volume_profile"
        data = await self.get_cached(key)
        if isinstance(data, dict) and "profile" in data:
            return data["profile"]
        return data

    # ===== 针对 DataEngine 暴露给外部读取的通用键，提供统一服务接口 =====
    async def get_latest_by_source_symbol(self, source: str, symbol: str) -> Any:
        """读取 latest:{source}:{symbol} 规范键"""
        key = f"latest:{source}:{symbol}"
        return await self.get_cached(key)

    async def get_last_price_by_source_symbol(
        self, source: str, symbol: str
    ) -> Decimal | float | None:
        """读取 last_price:{source}:{symbol}，返回数值"""
        key = f"last_price:{source}:{symbol}"
        data = await self.get_cached(key)
        try:
            if data is None:
                return None
            return Decimal(str(data))
        except Exception:
            try:
                return float(data)
            except Exception:
                return None

    async def get_price_history_by_source_symbol(self, source: str, symbol: str) -> Any:
        """读取 price_history:{source}:{symbol}"""
        key = f"price_history:{source}:{symbol}"
        return await self.get_cached(key)

    async def get_market_data(
        self, symbol: str, data_type: str = "spot_price"
    ) -> dict[str, Any] | None:
        """获取标准化市场数据"""
        try:
            if not self.cache_manager:
                return None

            # 标准化缓存键格式
            cache_key = f"market_data:{symbol.lower()}:{data_type}"
            return await self.cache_manager.get(cache_key)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get market data for {symbol}: {e}")
            return None

    async def get_option_chain(self, underlying: str = "BTC") -> dict[str, Any] | None:
        """获取期权链数据"""
        try:
            if not self.cache_manager:
                return None

            # 尝试多个可能的缓存键
            possible_keys = [
                f"market_data:deribit_options:{underlying}:option_chain",
                "deribit_options",
                f"{underlying}_options",
            ]

            for key in possible_keys:
                data = await self.cache_manager.get(key)
                if data:
                    return data

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Failed to get option chain for {underlying}: {e}"
                )
            return None

    async def get_positions(self) -> dict[str, Any] | None:
        """获取持仓数据"""
        try:
            if not self.cache_manager:
                return None

            # 尝试多个可能的缓存键
            possible_keys = ["positions", "trading:spot_positions", "positions:spot"]

            for key in possible_keys:
                data = await self.cache_manager.get(key)
                if data:
                    return data

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get positions: {e}")
            return None

    async def get_greeks(self, symbol: str) -> dict[str, Any] | None:
        """获取Greeks数据"""
        try:
            if not self.cache_manager:
                return None

            # 尝试多个可能的缓存键
            possible_keys = [
                f"greeks:{symbol}",
                f"market_data:greeks:{symbol}",
                f"options:greeks:{symbol}",
            ]

            for key in possible_keys:
                data = await self.cache_manager.get(key)
                if data:
                    return data

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get Greeks for {symbol}: {e}")
            return None

    async def get_margin_info(self) -> dict[str, Any] | None:
        """获取保证金信息"""
        try:
            if not self.cache_manager:
                return None

            # 尝试多个可能的缓存键
            possible_keys = [
                "margin_info",
                "trading:margin_info",
                "account:margin_info",
                "margin_data",
            ]

            for key in possible_keys:
                data = await self.cache_manager.get(key)
                if data:
                    return data

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to get margin info: {e}")
            return None

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("MarketDataService initialized successfully")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"MarketDataService initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        return True

    async def _stop_impl(self) -> bool:
        """停止实现"""
        return True

    async def _health_check_impl(self):
        """健康检查实现"""
        from datetime import UTC, datetime

        from src.core.base_component import HealthCheckResult, HealthStatus

        try:
            # 检查缓存管理器状态
            cache_status = "available" if self.cache_manager else "unavailable"
            last_price_status = "cached" if self.last_btc_price else "none"

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="MarketDataService is healthy",
                details={
                    "cache_manager": cache_status,
                    "last_btc_price": last_price_status,
                },
                timestamp=datetime.now(UTC),
            )
        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"MarketDataService health check failed: {e}",
                details={"error": str(e)},
                timestamp=datetime.now(UTC),
            )
