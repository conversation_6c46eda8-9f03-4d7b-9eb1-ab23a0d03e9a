"""
VaR计算服务

提供统一的VaR计算接口，集成Monte Carlo引擎，消除重复的风险计算代码
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from decimal import Decimal
from typing import Any

from src.core.base_component import BaseComponent
from src.utils.data_converter import safe_float_conversion


@dataclass
class VaRParameters:
    """VaR计算参数"""

    confidence_level: float = 0.95  # 置信度
    time_horizon: float = 1.0  # 时间窗口（天）
    num_simulations: int = 10000  # Monte Carlo模拟次数
    price_change_threshold: float = 0.05  # 价格变化阈值


@dataclass
class VaRResult:
    """VaR计算结果"""

    var_95: Decimal  # 95% VaR
    var_99: Decimal  # 99% VaR
    expected_shortfall_95: Decimal  # 95% 期望损失值
    expected_shortfall_99: Decimal  # 99% 期望损失值
    max_loss: Decimal  # 最大损失
    confidence_level: float  # 置信度
    calculation_method: str  # 计算方法
    computation_time: float  # 计算时间（秒）


class BaseVaRCalculator(ABC):
    """抽象VaR计算器基类"""

    @abstractmethod
    async def calculate_var(
        self,
        positions: list[dict[str, Any]],
        market_data: dict[str, Any],
        parameters: VaRParameters,
    ) -> VaRResult:
        """计算VaR"""
        pass

    @abstractmethod
    def supports_position_type(self, position_type: str) -> bool:
        """检查是否支持该持仓类型"""
        pass


class MonteCarloVaRCalculator(BaseVaRCalculator):
    """Monte Carlo VaR计算器"""

    def __init__(self):
        self.monte_carlo_engine = None

    async def initialize(self):
        """初始化Monte Carlo引擎"""
        try:
            from ..analysis.monte_carlo_engine import (
                MarketParameters,
                MonteCarloEngine,
                OptionContract,
                SimulationConfig,
                StrategyLeg,
            )

            self.monte_carlo_engine = MonteCarloEngine()
            await self.monte_carlo_engine.initialize()

            # 存储类引用以便使用
            self._MarketParameters = MarketParameters
            self._OptionContract = OptionContract
            self._SimulationConfig = SimulationConfig
            self._StrategyLeg = StrategyLeg

        except Exception as e:
            raise RuntimeError(f"Failed to initialize Monte Carlo engine: {e}") from e

    async def calculate_var(
        self,
        positions: list[dict[str, Any]],
        market_data: dict[str, Any],
        parameters: VaRParameters,
    ) -> VaRResult:
        """使用Monte Carlo方法计算VaR"""
        try:
            if not self.monte_carlo_engine:
                await self.initialize()

            # 构建市场参数
            from src.utils.data_converter import safe_float_conversion

            market_params = self._MarketParameters(
                spot_price=safe_float_conversion(market_data.get("current_price")),
                volatility=safe_float_conversion(market_data.get("volatility"), 0.5),
                risk_free_rate=0.02,
                dividend_yield=0.0,
            )

            # 构建模拟配置
            sim_config = self._SimulationConfig(
                num_simulations=parameters.num_simulations,
                num_steps=max(1, int(parameters.time_horizon * 252)),
                antithetic_variates=True,
                control_variates=True,
                parallel_execution=True,
            )

            # 转换持仓为策略腿
            strategy_legs = []
            for position in positions:
                if self._is_option_position(position):
                    contract = self._OptionContract(
                        option_type=position.get("option_type", "call").lower(),
                        strike=safe_float_conversion(position.get("strike")),
                        time_to_expiry=safe_float_conversion(
                            position.get("time_to_expiry"), 0.1
                        ),
                        option_side=position.get("side", "long").lower(),
                        quantity=abs(safe_float_conversion(position.get("quantity"))),
                    )

                    weight = (
                        1.0 if position.get("side", "long").lower() == "long" else -1.0
                    )
                    strategy_legs.append(
                        self._StrategyLeg(contract=contract, weight=weight)
                    )

            if not strategy_legs:
                # 如果没有期权持仓，返回零风险
                return VaRResult(
                    var_95=Decimal("0"),
                    var_99=Decimal("0"),
                    expected_shortfall_95=Decimal("0"),
                    expected_shortfall_99=Decimal("0"),
                    max_loss=Decimal("0"),
                    confidence_level=parameters.confidence_level,
                    calculation_method="monte_carlo_zero_positions",
                    computation_time=0.0,
                )

            # 执行Monte Carlo模拟
            mc_result = await self.monte_carlo_engine.price_strategy(
                strategy_legs, market_params, sim_config
            )

            # 从Monte Carlo结果提取VaR指标
            var_95 = abs(Decimal(str(mc_result.var_95)))
            var_99 = abs(Decimal(str(mc_result.var_99)))
            es_95 = abs(Decimal(str(mc_result.expected_shortfall_95)))

            # 估算99% ES（如果未直接计算）
            es_99 = es_95 * Decimal("1.2")  # 简单估算

            # 最大损失为统计最小值的绝对值
            max_loss = abs(Decimal(str(mc_result.path_statistics.get("min", 0))))

            return VaRResult(
                var_95=var_95,
                var_99=var_99,
                expected_shortfall_95=es_95,
                expected_shortfall_99=es_99,
                max_loss=max_loss,
                confidence_level=parameters.confidence_level,
                calculation_method="monte_carlo",
                computation_time=mc_result.computation_time,
            )

        except Exception as e:
            raise RuntimeError(f"Monte Carlo VaR calculation failed: {e}") from e

    def supports_position_type(self, position_type: str) -> bool:
        """检查是否支持该持仓类型"""
        return position_type.lower() in ["option", "options"]

    def _is_option_position(self, position: dict[str, Any]) -> bool:
        """检查是否为期权持仓"""
        return (
            "option_type" in position
            or "strike" in position
            or position.get("instrument_type", "").lower() in ["option", "options"]
        )


class SimplifiedVaRCalculator(BaseVaRCalculator):
    """简化VaR计算器（Delta-Normal方法）"""

    async def calculate_var(
        self,
        positions: list[dict[str, Any]],
        market_data: dict[str, Any],
        parameters: VaRParameters,
    ) -> VaRResult:
        """使用Delta-Normal方法计算VaR"""
        try:
            import math
            from datetime import UTC, datetime

            start_time = datetime.now(UTC)

            current_price = safe_float_conversion(market_data.get("current_price"))
            volatility = safe_float_conversion(market_data.get("volatility"), 0.5)

            # Z值（正态分布）
            z_95 = 1.645  # 95%置信度
            z_99 = 2.326  # 99%置信度

            portfolio_delta = 0.0
            portfolio_gamma = 0.0
            portfolio_value = 0.0

            for position in positions:
                quantity = float(position.get("quantity", 0))
                delta = float(position.get("delta", 0))
                gamma = float(position.get("gamma", 0))
                value = float(position.get("market_value", 0))

                portfolio_delta += quantity * delta
                portfolio_gamma += quantity * gamma
                portfolio_value += quantity * value

            # Delta-Normal VaR
            price_volatility = (
                current_price * volatility * math.sqrt(parameters.time_horizon)
            )
            delta_var_95 = z_95 * abs(portfolio_delta) * price_volatility
            delta_var_99 = z_99 * abs(portfolio_delta) * price_volatility

            # Gamma调整
            gamma_adjustment = 0.5 * portfolio_gamma * (price_volatility**2)

            var_95 = Decimal(str(delta_var_95 + gamma_adjustment))
            var_99 = Decimal(str(delta_var_99 + gamma_adjustment))

            # 简化的期望损失值估算
            es_95 = var_95 * Decimal("1.2")
            es_99 = var_99 * Decimal("1.2")

            # 最大损失估算
            max_loss = var_99 * Decimal("1.5")

            computation_time = (datetime.now(UTC) - start_time).total_seconds()

            return VaRResult(
                var_95=var_95,
                var_99=var_99,
                expected_shortfall_95=es_95,
                expected_shortfall_99=es_99,
                max_loss=max_loss,
                confidence_level=parameters.confidence_level,
                calculation_method="delta_normal",
                computation_time=computation_time,
            )

        except Exception as e:
            raise RuntimeError(f"Simplified VaR calculation failed: {e}") from e

    def supports_position_type(self, position_type: str) -> bool:
        """检查是否支持该持仓类型"""
        return True  # 支持所有类型


class VaRCalculationService(BaseComponent):
    """VaR计算服务"""

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__(component_name="VaRCalculationService", config=config)

        # 计算器注册
        self.calculators: dict[str, BaseVaRCalculator] = {}
        self.default_calculator = "monte_carlo"

        # 性能统计
        self.calculation_stats = {
            "total_calculations": 0,
            "method_usage": {},
            "avg_computation_time": {},
        }

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 注册计算器
            monte_carlo_calc = MonteCarloVaRCalculator()
            await monte_carlo_calc.initialize()
            self.calculators["monte_carlo"] = monte_carlo_calc

            self.calculators["simplified"] = SimplifiedVaRCalculator()

            if self.logger:
                await self.logger.info("VaRCalculationService initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"VaRCalculationService initialization failed: {e}"
                )
            return False

    async def calculate_portfolio_var(
        self,
        positions: list[dict[str, Any]],
        market_data: dict[str, Any],
        parameters: VaRParameters | None = None,
        method: str | None = None,
    ) -> VaRResult:
        """计算投资组合VaR"""
        try:
            parameters = parameters or VaRParameters()
            method = method or self.default_calculator

            # 选择计算器
            if method not in self.calculators:
                raise ValueError(f"Unsupported VaR calculation method: {method}")

            calculator = self.calculators[method]

            # 执行计算
            result = await calculator.calculate_var(positions, market_data, parameters)

            # 更新统计
            self.calculation_stats["total_calculations"] += 1
            self.calculation_stats["method_usage"][method] = (
                self.calculation_stats["method_usage"].get(method, 0) + 1
            )

            # 更新平均计算时间
            current_avg = self.calculation_stats["avg_computation_time"].get(
                method, 0.0
            )
            count = self.calculation_stats["method_usage"][method]
            new_avg = (current_avg * (count - 1) + result.computation_time) / count
            self.calculation_stats["avg_computation_time"][method] = new_avg

            if self.logger:
                await self.logger.debug(
                    f"VaR calculated using {method}: VaR95={result.var_95:.4f}, "
                    f"VaR99={result.var_99:.4f}, time={result.computation_time:.3f}s"
                )

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Portfolio VaR calculation failed: {e}")
            raise

    async def calculate_position_var(
        self,
        position: dict[str, Any],
        market_data: dict[str, Any],
        parameters: VaRParameters | None = None,
        method: str | None = None,
    ) -> VaRResult:
        """计算单个持仓VaR"""
        return await self.calculate_portfolio_var(
            [position], market_data, parameters, method
        )

    def get_available_methods(self) -> list[str]:
        """获取可用的计算方法"""
        return list(self.calculators.keys())

    async def get_calculation_statistics(self) -> dict[str, Any]:
        """获取计算统计信息"""
        return {
            "total_calculations": self.calculation_stats["total_calculations"],
            "method_usage": dict(self.calculation_stats["method_usage"]),
            "avg_computation_time": dict(
                self.calculation_stats["avg_computation_time"]
            ),
            "available_methods": self.get_available_methods(),
            "default_method": self.default_calculator,
        }

    async def _start_impl(self) -> bool:
        """启动实现"""
        return True

    async def _stop_impl(self) -> bool:
        """停止实现"""
        # 清理Monte Carlo引擎
        if "monte_carlo" in self.calculators:
            mc_calc = self.calculators["monte_carlo"]
            if hasattr(mc_calc, "monte_carlo_engine") and mc_calc.monte_carlo_engine:
                await mc_calc.monte_carlo_engine.stop()
        return True

    async def _health_check_impl(self):
        """健康检查实现"""
        from datetime import UTC, datetime

        from src.core.base_component import HealthCheckResult, HealthStatus

        try:
            issues = []

            # 检查计算器状态
            if not self.calculators:
                issues.append("No calculators available")

            # 检查Monte Carlo引擎状态
            if "monte_carlo" in self.calculators:
                mc_calc = self.calculators["monte_carlo"]
                if (
                    not hasattr(mc_calc, "monte_carlo_engine")
                    or not mc_calc.monte_carlo_engine
                ):
                    issues.append("Monte Carlo engine not initialized")

            status = HealthStatus.HEALTHY if not issues else HealthStatus.DEGRADED
            message = (
                "VaRCalculationService is healthy"
                if not issues
                else f"Issues: {', '.join(issues)}"
            )

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "total_calculations": self.calculation_stats["total_calculations"],
                    "available_methods": self.get_available_methods(),
                    "default_method": self.default_calculator,
                },
                timestamp=datetime.now(UTC),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"VaRCalculationService health check failed: {e}",
                details={"error": str(e)},
                timestamp=datetime.now(UTC),
            )
