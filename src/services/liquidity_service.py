"""
Liquidity service

Builds a Deribit-based liquidity proxy for IBIT option orders, using
Deribit option chain data as a proxy for spread, OI, volume and IV.

Inputs are parameterized so strategies can tune ATM band, tenor weight and
scaling factors without duplicating logic across modules.
"""

from __future__ import annotations

from datetime import date as _date
from datetime import datetime as _dt
from typing import Any

from src.services.market_data_service import MarketDataService


async def build_deribit_liquidity_proxy(
    mds: MarketDataService,
    tenor_days: int = 30,
    option_type: str = "call",
    config: dict[str, Any] | None = None,
    spot_price: float | None = None,
) -> dict | None:
    """Return a Deribit-derived liquidity proxy for an IBIT order.

    - Uses tenor and ATM proximity scoring to select a representative option
      from Deribit option chain
    - Computes spread_bps from bid/ask if available; returns None if missing
    - Scales spread/oi/volume by provided factors

    Args:
        mds: MarketDataService instance
        tenor_days: target days to expiry
        option_type: 'call' or 'put'
        config: optional parameters: atm_moneyness_band, tenor_weight,
                spread_scale, oi_scale, vol_scale
        spot_price: optional BTC spot; if None, fetched via mds

    Returns:
        dict with keys: spread_bps, open_interest, volume, iv, days_to_expiry
        or None when insufficient data
    """
    cfg = config or {}
    atm_band = float(cfg.get("atm_moneyness_band", 0.05))
    tenor_weight = float(cfg.get("tenor_weight", 1.0))
    spread_scale = float(cfg.get("spread_scale", 1.5))
    oi_scale = float(cfg.get("oi_scale", 1.0))
    vol_scale = float(cfg.get("vol_scale", 1.0))

    chain = await mds.get_option_chain("BTC")
    if not chain:
        return None

    # Spot price
    if spot_price is None:
        spot_dec = await mds.get_btc_price()
        spot_price = float(spot_dec) if spot_dec else None

    best = None
    best_score = 1e9
    rights = {"call": "CALL", "put": "PUT"}
    for opt in chain.get("options", []) or []:
        try:
            if str(opt.get("right", "")).upper() != rights.get(option_type, "CALL"):
                continue
            exp_raw = opt.get("expiry")
            if not exp_raw:
                continue
            if isinstance(exp_raw, str) and len(exp_raw) == 8 and exp_raw.isdigit():
                exp_d = _dt.strptime(exp_raw, "%Y%m%d").date()
            else:
                exp_d = _dt.fromisoformat(str(exp_raw)).date()
            dte = max(0, (exp_d - _date.today()).days)
            score = abs(dte - tenor_days) / max(tenor_weight, 1e-6)
            if spot_price is not None:
                mny = abs(float(opt.get("strike", 0.0)) - spot_price) / max(
                    spot_price, 1.0
                )
                score += (mny * 0.5) if mny <= atm_band else mny
            if score < best_score:
                best = (dte, opt)
                best_score = score
        except Exception:
            continue

    if not best:
        return None

    dte, opt = best
    bid = float(opt.get("bid", 0) or 0)
    ask = float(opt.get("ask", 0) or 0)
    iv = float(opt.get("iv", 0) or 0)
    oi = int(opt.get("open_interest", 0) or 0)
    vol = int(opt.get("volume", 0) or 0)

    if not (bid > 0 and ask > 0 and ask >= bid):
        return None

    mid = (bid + ask) / 2.0
    if mid <= 0:
        return None

    spread_bps = (ask - bid) / mid * 10000.0
    spread_bps *= spread_scale

    return {
        "spread_bps": float(spread_bps),
        "open_interest": int(oi * oi_scale),
        "volume": int(vol * vol_scale),
        "iv": float(iv),
        "days_to_expiry": int(dte),
    }
