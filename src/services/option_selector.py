"""
期权选择服务

提供统一的期权筛选和选择算法，消除重复代码
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any

from src.core.base_component import BaseComponent


@dataclass
class OptionSelectionCriteria:
    """期权选择标准"""

    # 基础筛选条件
    option_type: str | None = None  # "call", "put", None=both
    min_expiry_days: int = 1
    max_expiry_days: int = 365

    # 流动性条件
    min_bid: Decimal = Decimal("0.01")
    min_ask: Decimal = Decimal("0.01")
    min_volume: int = 0
    min_open_interest: int = 0

    # Greeks条件
    min_delta: float | None = None
    max_delta: float | None = None
    target_delta: float | None = None

    # Strike价格条件
    min_strike: Decimal | None = None
    max_strike: Decimal | None = None
    target_strike: Decimal | None = None

    # 其他条件
    max_spread_bps: float = 500  # 最大买卖价差（基点）


@dataclass
class OptionScore:
    """期权评分"""

    total_score: float
    liquidity_score: float
    strike_score: float
    delta_score: float
    time_score: float
    spread_score: float


class BaseOptionSelector(ABC):
    """抽象期权选择器基类"""

    @abstractmethod
    async def score_option(
        self, option: dict[str, Any], criteria: OptionSelectionCriteria
    ) -> OptionScore:
        """为期权打分"""
        pass

    @abstractmethod
    def meets_criteria(
        self, option: dict[str, Any], criteria: OptionSelectionCriteria
    ) -> bool:
        """检查期权是否满足标准"""
        pass


class StandardOptionSelector(BaseOptionSelector):
    """标准期权选择器"""

    async def score_option(
        self, option: dict[str, Any], criteria: OptionSelectionCriteria
    ) -> OptionScore:
        """为期权打分"""

        # 1. 流动性评分 (0-1)
        volume = option.get("volume", 0)
        open_interest = option.get("open_interest", 0)
        liquidity_score = min(1.0, (volume / 1000 + open_interest / 5000) / 2)

        # 2. Strike价格评分 (0-1)
        strike_score = 0.5  # 默认分数
        if criteria.target_strike:
            from src.utils.data_converter import safe_float_conversion

            strike_diff = abs(Decimal(str(option["strike"])) - criteria.target_strike)
            strike_score = max(
                0.0,
                1.0
                - safe_float_conversion(strike_diff)
                / safe_float_conversion(criteria.target_strike, 1.0),
            )

        # 3. Delta评分 (0-1)
        delta_score = 0.5  # 默认分数
        if criteria.target_delta:
            option_delta = abs(option.get("greeks", {}).get("delta", 0))
            delta_diff = abs(option_delta - criteria.target_delta)
            delta_score = max(0.0, 1.0 - delta_diff / criteria.target_delta)

        # 4. 时间价值评分 (0-1)
        try:
            expiry_str = option.get("expiration_timestamp", "")
            if expiry_str:
                expiry_date = datetime.fromisoformat(expiry_str.replace("Z", "+00:00"))
                days_to_expiry = (expiry_date - datetime.now(UTC)).days
                optimal_days = (criteria.min_expiry_days + criteria.max_expiry_days) / 2
                time_score = max(
                    0.0, 1.0 - abs(days_to_expiry - optimal_days) / optimal_days
                )
            else:
                time_score = 0.1  # 低分但不为零
        except Exception:
            time_score = 0.1

        # 5. 价差评分 (0-1)
        bid = option.get("bid_price", 0)
        ask = option.get("ask_price", 0)
        if bid > 0 and ask > 0:
            mid_price = (bid + ask) / 2
            spread_bps = (
                ((ask - bid) / mid_price) * 10000 if mid_price > 0 else float("inf")
            )
            spread_score = max(0.0, 1.0 - spread_bps / criteria.max_spread_bps)
        else:
            spread_score = 0.0

        # 综合评分（加权平均）
        total_score = (
            liquidity_score * 0.3
            + strike_score * 0.25
            + delta_score * 0.2
            + time_score * 0.15
            + spread_score * 0.1
        )

        return OptionScore(
            total_score=total_score,
            liquidity_score=liquidity_score,
            strike_score=strike_score,
            delta_score=delta_score,
            time_score=time_score,
            spread_score=spread_score,
        )

    def meets_criteria(
        self, option: dict[str, Any], criteria: OptionSelectionCriteria
    ) -> bool:
        """检查期权是否满足基本标准"""
        try:
            # 1. 期权类型检查
            if criteria.option_type:
                option_type = option.get("option_type", "").lower()
                if option_type != criteria.option_type.lower():
                    return False

            # 2. 到期时间检查
            expiry_str = option.get("expiration_timestamp", "")
            if expiry_str:
                expiry_date = datetime.fromisoformat(expiry_str.replace("Z", "+00:00"))
                now = datetime.now(UTC)
                days_to_expiry = (expiry_date - now).days

                if not (
                    criteria.min_expiry_days
                    <= days_to_expiry
                    <= criteria.max_expiry_days
                ):
                    return False

            # 3. 流动性检查
            bid = Decimal(str(option.get("bid_price", 0)))
            ask = Decimal(str(option.get("ask_price", 0)))
            volume = option.get("volume", 0)
            open_interest = option.get("open_interest", 0)

            if bid < criteria.min_bid or ask < criteria.min_ask:
                return False
            if (
                volume < criteria.min_volume
                or open_interest < criteria.min_open_interest
            ):
                return False

            # 4. Greeks检查
            greeks = option.get("greeks", {})
            delta = abs(greeks.get("delta", 0))

            if criteria.min_delta is not None and delta < criteria.min_delta:
                return False
            if criteria.max_delta is not None and delta > criteria.max_delta:
                return False

            # 5. Strike价格检查
            strike = Decimal(str(option["strike"]))
            return not (
                (criteria.min_strike is not None and strike < criteria.min_strike)
                or (criteria.max_strike is not None and strike > criteria.max_strike)
            )

        except Exception:
            return False


class OptionSelectorService(BaseComponent):
    """期权选择服务"""

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__(component_name="OptionSelectorService", config=config)
        self.selector = StandardOptionSelector()

    async def select_best_option(
        self, options: list[dict[str, Any]], criteria: OptionSelectionCriteria
    ) -> dict[str, Any] | None:
        """选择最佳期权"""
        try:
            if not options:
                return None

            # 1. 基础筛选
            eligible_options = [
                option
                for option in options
                if self.selector.meets_criteria(option, criteria)
            ]

            if not eligible_options:
                if self.logger:
                    await self.logger.warning("No options meet the selection criteria")
                return None

            # 2. 评分排序
            scored_options = []
            for option in eligible_options:
                score = await self.selector.score_option(option, criteria)
                scored_options.append((option, score))

            # 3. 选择最高分
            best_option, best_score = max(
                scored_options, key=lambda x: x[1].total_score
            )

            if self.logger:
                await self.logger.info(
                    f"Selected option {best_option.get('instrument_name', 'unknown')} "
                    f"with score {best_score.total_score:.3f}"
                )

            return best_option

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Option selection failed: {e}")
            return None

    async def select_multiple_options(
        self,
        options: list[dict[str, Any]],
        criteria: OptionSelectionCriteria,
        count: int = 1,
    ) -> list[dict[str, Any]]:
        """选择多个最佳期权"""
        try:
            if not options or count <= 0:
                return []

            # 基础筛选
            eligible_options = [
                option
                for option in options
                if self.selector.meets_criteria(option, criteria)
            ]

            if not eligible_options:
                return []

            # 评分排序
            scored_options = []
            for option in eligible_options:
                score = await self.selector.score_option(option, criteria)
                scored_options.append((option, score))

            # 按分数降序排序并取前N个
            sorted_options = sorted(
                scored_options, key=lambda x: x[1].total_score, reverse=True
            )
            selected_options = [option for option, _ in sorted_options[:count]]

            if self.logger:
                await self.logger.info(
                    f"Selected {len(selected_options)} options from {len(options)} candidates"
                )

            return selected_options

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Multiple option selection failed: {e}")
            return []

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("OptionSelectorService initialized successfully")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"OptionSelectorService initialization failed: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        return True

    async def _stop_impl(self) -> bool:
        """停止实现"""
        return True

    async def _health_check_impl(self):
        """健康检查实现"""
        from datetime import UTC, datetime

        from src.core.base_component import HealthCheckResult, HealthStatus

        return HealthCheckResult(
            status=HealthStatus.HEALTHY,
            message="OptionSelectorService is healthy",
            details={"selector_type": type(self.selector).__name__},
            timestamp=datetime.now(UTC),
        )
