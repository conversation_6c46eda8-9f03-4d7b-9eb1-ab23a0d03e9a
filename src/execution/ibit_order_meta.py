from __future__ import annotations

from dataclasses import dataclass
from decimal import Decimal

from src.gateways.ibkr_client import OptionContract

"""
IBIT 期权订单元数据结构化定义
- 替代 order.tags 的弱类型字典，提供强类型与验证
- 用于 pretrade 校验与 IBKR 执行路径
"""


@dataclass
class IBITOptionOrderMeta:
    """IBIT 期权订单元数据"""

    # 策略动作
    action: str  # 'SELL_PUT_CSP' | 'LONG_CALL' | 'BUY_SPOT'

    # 合约信息
    contract: OptionContract | None = None

    # 市场数据（用于 pretrade 校验）
    spread_bps: float | None = None
    open_interest: int | None = None
    volume: int | None = None
    iv: float | None = None
    delta: float | None = None

    # 风控参数
    buying_power: Decimal | None = None
    days_to_expiry: int | None = None

    # 映射来源
    btc_price: Decimal | None = None
    ibit_estimated_price: Decimal | None = None
    tracking_error_bps: float | None = None

    def validate_for_pretrade(self) -> tuple[bool, str]:
        """验证是否具备 pretrade 校验所需的最小字段"""
        if not self.action:
            return False, "Missing action"

        if self.action == "SELL_PUT_CSP":
            if self.contract is None or self.buying_power is None:
                return False, "CSP requires contract and buying_power"

        elif self.action == "LONG_CALL" and self.days_to_expiry is None:
            return False, "Long Call requires days_to_expiry"

        # 流动性校验字段
        if self.spread_bps is None or self.open_interest is None or self.volume is None:
            return False, "Missing liquidity data (spread_bps/oi/volume)"

        return True, "OK"

    @classmethod
    def from_tags(cls, tags: dict) -> IBITOptionOrderMeta:
        """从弱类型的订单 tags 构造强类型 IBITOptionOrderMeta（用于兼容旧测试）"""
        from src.gateways.ibkr_client import OptionContract, Right

        action = tags.get("action")
        # 合约信息（若 tags 提供足够信息则构造；否则留空）
        contract = None
        if tags.get("symbol_parts"):
            # 允许测试传入分解后的结构
            sp = tags["symbol_parts"]  # {name, date, strike, right}
            try:
                contract = OptionContract(
                    sp["name"],
                    sp["date"],
                    Decimal(str(sp["strike"])),
                    Right(sp["right"]),
                )
            except Exception:
                contract = None

        return cls(
            action=action,
            contract=contract,
            spread_bps=float(tags.get("spread_bps"))
            if tags.get("spread_bps") is not None
            else None,
            open_interest=int(tags.get("oi")) if tags.get("oi") is not None else None,
            volume=int(tags.get("volume")) if tags.get("volume") is not None else None,
            iv=float(tags.get("iv")) if tags.get("iv") is not None else None,
            delta=float(tags.get("delta")) if tags.get("delta") is not None else None,
            buying_power=Decimal(str(tags.get("bp")))
            if tags.get("bp") is not None
            else None,
            days_to_expiry=int(tags.get("days_to_expiry"))
            if tags.get("days_to_expiry") is not None
            else None,
        )
