"""
仓位管理器

负责实时仓位跟踪、多账户管理、异常检测和修复
"""

import asyncio
import contextlib
from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.event_bus import (
    BaseEvent,
    EventBus,
    EventType,
    OrderExecutionEvent,
    PositionUpdateEvent,
)
from src.data.cache_manager import CacheManager
from src.risk.pretrade_filters import PretradeFilters
from src.services.market_data_service import MarketDataService


class PositionType(Enum):
    """仓位类型"""

    SPOT = "spot"
    OPTION = "option"
    FUTURE = "future"


class PositionSide(Enum):
    """仓位方向"""

    LONG = "long"
    SHORT = "short"
    NEUTRAL = "neutral"


@dataclass
class Position:
    """仓位对象"""

    symbol: str
    position_type: PositionType
    side: PositionSide
    size: Decimal
    avg_price: Decimal
    market_value: Decimal
    unrealized_pnl: Decimal = Decimal("0")
    realized_pnl: Decimal = Decimal("0")

    # 期权特有属性
    strike_price: Decimal | None = None
    expiry_date: datetime | None = None
    option_type: str | None = None  # 'call' or 'put'

    # 时间戳
    created_at: datetime = field(default_factory=lambda: datetime.now(UTC))
    updated_at: datetime = field(default_factory=lambda: datetime.now(UTC))

    # 账户信息
    account_id: str = "default"
    strategy_id: str | None = None

    # 风险指标
    delta: Decimal | None = None
    gamma: Decimal | None = None
    theta: Decimal | None = None
    vega: Decimal | None = None

    # 元数据
    tags: dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionSnapshot:
    """仓位快照"""

    timestamp: datetime
    positions: dict[str, Position]
    total_value: Decimal
    total_pnl: Decimal
    account_id: str


@dataclass
class PositionStats:
    """仓位统计"""

    total_positions: int = 0
    long_positions: int = 0
    short_positions: int = 0
    total_value: Decimal = Decimal("0")
    total_unrealized_pnl: Decimal = Decimal("0")
    total_realized_pnl: Decimal = Decimal("0")
    largest_position: str | None = None
    largest_position_value: Decimal = Decimal("0")


class PositionManager(BaseComponent):
    """
    仓位管理器

    功能特性：
    - 实时仓位跟踪和同步
    - 多账户和多策略仓位管理
    - 仓位异常检测和修复
    - 仓位历史查询和导出
    - 仓位风险监控和告警
    """

    def __init__(
        self,
        config: dict[str, Any] | None = None,
        market_data_service: MarketDataService | None = None,
    ):
        super().__init__("PositionManager", config)

        # 组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None
        self.market_data_service = market_data_service

        # 仓位配置
        self._load_position_config()

        # 仓位存储
        self.positions: dict[str, Position] = {}  # symbol -> Position
        self.account_positions: dict[
            str, dict[str, Position]
        ] = {}  # account_id -> positions
        self.strategy_positions: dict[
            str, dict[str, Position]
        ] = {}  # strategy_id -> positions

        # 历史快照
        self.position_snapshots: list[PositionSnapshot] = []
        self._max_snapshots = 1000

        # 同步任务
        self._sync_task: asyncio.Task | None = None
        self._monitor_task: asyncio.Task | None = None
        self._sync_interval = 10  # 10秒同步间隔

        # 异常检测
        self.anomaly_threshold = Decimal("0.05")  # 5%差异阈值
        self.last_sync_time: datetime | None = None

        # 统计信息
        self.stats = PositionStats()

        # 支持的交易所
        self.supported_exchanges = set()
        # 预交易过滤器（仅对 IBIT/IBKR 路径启用）
        self.pretrade_filters = PretradeFilters()

        self._load_exchange_config()

    def _load_position_config(self):
        """加载仓位配置"""
        position_config = self.config.get("positions", {}) if self.config else {}

        # 基础配置
        self.position_settings = {
            "enable_real_time_sync": position_config.get("enable_real_time_sync", True),
            "sync_interval": position_config.get("sync_interval", 10),
            "enable_anomaly_detection": position_config.get(
                "enable_anomaly_detection", True
            ),
            "anomaly_threshold": Decimal(
                str(position_config.get("anomaly_threshold", "0.05"))
            ),
            "max_position_age_hours": position_config.get("max_position_age_hours", 24),
            "enable_auto_reconciliation": position_config.get(
                "enable_auto_reconciliation", True
            ),
        }

        # 更新配置
        self._sync_interval = self.position_settings["sync_interval"]
        self.anomaly_threshold = self.position_settings["anomaly_threshold"]

    def _load_exchange_config(self):
        """加载交易所配置"""
        exchange_config = self.config.get("exchanges", {}) if self.config else {}

        # 默认支持的交易所
        self.supported_exchanges = set(
            exchange_config.get("supported", ["deribit", "binance"])
        )

    async def _initialize_impl(self) -> bool:
        """初始化仓位管理器"""
        try:
            # 订阅事件
            if self.event_bus:
                # Unified naming alignment with OrderManager: order_executed / order_filled
                from src.core.event_bus import EventType

                await self.event_bus.subscribe(
                    EventType.ORDER_FILLED, self._handle_order_fill
                )
                await self.event_bus.subscribe(
                    EventType.ORDER_EXECUTED, self._handle_order_executed
                )
                # Backward compatibility
                await self.event_bus.subscribe(
                    "trade_executed", self._handle_trade_execution
                )
                from src.core.event_bus import EventType

                await self.event_bus.subscribe(
                    EventType.POSITION_UPDATED, self._handle_position_update
                )
                await self.event_bus.subscribe(
                    event_types={EventType.DATA_UPDATE},
                    callback=self._handle_market_update,
                    subscriber_id=f"{self.component_name}_market_data_handler",
                )

            # 初始化账户仓位
            self.account_positions["default"] = {}

            # 加载初始仓位
            await self._load_initial_positions()

            if self.logger:
                await self.logger.info("PositionManager initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PositionManager initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动仓位管理器"""
        try:
            # 启动同步任务
            if self.position_settings["enable_real_time_sync"]:
                self._sync_task = asyncio.create_task(self._position_sync_loop())
                self._monitor_task = asyncio.create_task(self._position_monitor_loop())

            if self.logger:
                await self.logger.info("PositionManager started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PositionManager start failed: {e}")
            return False

    async def pretrade_check_ibit(
        self,
        action: str,
        strike: Decimal | None = None,
        multiplier: int = 100,
        qty: int = 1,
        spread_bps: float | None = None,
        oi: int | None = None,
        volume: int | None = None,
        buying_power: Decimal | None = None,
        days_to_expiry: int | None = None,
        iv: float | None = None,
    ) -> dict[str, Any]:
        """
        IBIT/IBKR 路径的增强预交易校验：
        - action: 'SELL_PUT_CSP' or 'LONG_CALL'
        返回: { ok: bool, reasons: [..], risk_adjusted_qty: int }
        """
        reasons: list[str] = []
        ok = True

        # 1. 基础流动性过滤
        liq = self.pretrade_filters.liquidity(spread_bps, oi, volume)
        if not (liq.spread_bps_ok and liq.oi_ok and liq.vol_ok):
            ok = False
            reasons.append(liq.reason or "LIQ_FAIL")

        # 2. 动作特定校验
        if action == "SELL_PUT_CSP":
            if strike is None or buying_power is None:
                ok = False
                reasons.append("MISSING_STRIKE_OR_BP")
            else:
                chk = self.pretrade_filters.csp_buying_power(
                    strike, multiplier, qty, buying_power
                )
                if not chk.ok:
                    ok = False
                    reasons.append(chk.reason or "BP_FAIL")
        elif action == "LONG_CALL":
            if days_to_expiry is None:
                reasons.append("MISSING_TTE")
            if not self.pretrade_filters.long_call_theta_guard(days_to_expiry or 0, iv):
                ok = False
                reasons.append("THETA_GUARD")

        # 3. 智能仓位大小建议 - 新增功能
        risk_adjusted_qty = qty
        if ok:  # 只有基本检查通过才进行仓位调整
            try:
                position_advice = await self._calculate_smart_position_size(
                    {
                        "action": action,
                        "strike": strike,
                        "days_to_expiry": days_to_expiry,
                        "iv": iv,
                        "spread_bps": spread_bps,
                        "volume": volume,
                        "oi": oi,
                        "buying_power": buying_power,
                        "base_qty": qty,
                    }
                )

                risk_adjusted_qty = position_advice.get("suggested_qty", qty)

                # 如果建议仓位为0，则拒绝交易
                if risk_adjusted_qty <= 0:
                    ok = False
                    reasons.append("RISK_ADJ_QTY_ZERO")
                elif risk_adjusted_qty < qty:
                    reasons.append(f"QTY_REDUCED_FROM_{qty}_TO_{risk_adjusted_qty}")

            except Exception as calc_error:
                if self.logger:
                    await self.logger.warning(
                        f"Smart position calculation failed: {calc_error}"
                    )
                # 计算失败时使用原始数量

        return {
            "ok": ok,
            "reasons": reasons,
            "risk_adjusted_qty": risk_adjusted_qty,
            "original_qty": qty,
        }

    async def _calculate_smart_position_size(
        self, trade_params: dict[str, Any]
    ) -> dict[str, Any]:
        """智能仓位大小计算 - 针对IBIT期权优化"""
        try:
            action = trade_params.get("action")
            strike = trade_params.get("strike")
            days_to_expiry = trade_params.get("days_to_expiry", 30)
            iv = trade_params.get("iv", 0.3)
            spread_bps = trade_params.get("spread_bps", 50)
            volume = trade_params.get("volume", 0)
            oi = trade_params.get("oi", 0)
            buying_power = trade_params.get("buying_power")
            base_qty = trade_params.get("base_qty", 1)

            # 基础风险调整因子
            risk_multiplier = 1.0
            adjustment_reasons = []

            # 1. 时间价值衰减风险调整
            if days_to_expiry <= 7:
                risk_multiplier *= 0.5  # 临近到期大幅减仓
                adjustment_reasons.append("NEAR_EXPIRY_REDUCTION")
            elif days_to_expiry <= 14:
                risk_multiplier *= 0.7  # 两周内适度减仓
                adjustment_reasons.append("SHORT_DTE_REDUCTION")
            elif days_to_expiry > 45:
                risk_multiplier *= 0.8  # 过长期限减仓（时间价值过高）
                adjustment_reasons.append("LONG_DTE_REDUCTION")

            # 2. 隐含波动率风险调整
            if iv > 0.6:  # 高波动率环境
                risk_multiplier *= 0.6
                adjustment_reasons.append("HIGH_IV_REDUCTION")
            elif iv < 0.15:  # 极低波动率（可能不准确）
                risk_multiplier *= 0.7
                adjustment_reasons.append("LOW_IV_REDUCTION")

            # 3. 流动性风险调整
            if spread_bps and spread_bps > 100:  # 价差超过1%
                risk_multiplier *= 0.5
                adjustment_reasons.append("WIDE_SPREAD_REDUCTION")
            elif spread_bps and spread_bps > 50:  # 价差超过0.5%
                risk_multiplier *= 0.7
                adjustment_reasons.append("MODERATE_SPREAD_REDUCTION")

            # 成交量和持仓量风险调整
            if volume == 0 or oi == 0:
                risk_multiplier *= 0.3  # 无流动性时大幅减仓
                adjustment_reasons.append("NO_LIQUIDITY_REDUCTION")
            elif volume < 10 or oi < 100:
                risk_multiplier *= 0.6  # 低流动性减仓
                adjustment_reasons.append("LOW_LIQUIDITY_REDUCTION")

            # 4. 策略特定调整
            if action == "SELL_PUT_CSP":
                # 现金担保看跌期权：资金使用效率考虑
                if buying_power and strike:
                    required_bp = float(strike) * 100  # 每份合约需要的资金
                    max_contracts_by_bp = float(buying_power) / required_bp

                    if max_contracts_by_bp < base_qty:
                        risk_multiplier = min(
                            risk_multiplier, max_contracts_by_bp / base_qty
                        )
                        adjustment_reasons.append("BP_CONSTRAINT")

                # CSP特定风险：避免接近价内时大仓位
                # 这里需要当前股价信息，暂时用简化逻辑
                risk_multiplier *= 0.8  # CSP保守调整
                adjustment_reasons.append("CSP_CONSERVATIVE")

            elif action == "LONG_CALL":
                # 长期权：Theta衰减风险更敏感
                if days_to_expiry <= 21:
                    risk_multiplier *= 0.6  # 三周内更保守
                    adjustment_reasons.append("LONG_CALL_THETA_RISK")

            # 5. 组合风险检查（如果有当前仓位数据）
            current_positions_count = len(self.positions)
            if current_positions_count >= 5:  # 仓位过多时减少新仓位
                risk_multiplier *= 0.7
                adjustment_reasons.append("POSITION_CONCENTRATION")

            # 计算最终建议数量
            suggested_qty = max(0, int(base_qty * risk_multiplier))

            return {
                "suggested_qty": suggested_qty,
                "risk_multiplier": risk_multiplier,
                "adjustment_reasons": adjustment_reasons,
                "base_qty": base_qty,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Smart position calculation error: {e}")
            return {
                "suggested_qty": trade_params.get("base_qty", 1),
                "risk_multiplier": 1.0,
                "adjustment_reasons": ["CALCULATION_ERROR"],
                "base_qty": trade_params.get("base_qty", 1),
            }

    async def _stop_impl(self) -> bool:
        """停止仓位管理器"""
        try:
            # 停止同步任务
            if self._sync_task and not self._sync_task.done():
                self._sync_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._sync_task

            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._monitor_task

            # 保存最终快照
            await self._create_snapshot()

            if self.logger:
                await self.logger.info("PositionManager stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PositionManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查同步任务状态
            if self.position_settings["enable_real_time_sync"] and (
                not self._sync_task or self._sync_task.done()
            ):
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Position sync task not running",
                )

            # 检查最后同步时间
            if self.last_sync_time:
                time_since_sync = (
                    datetime.now(UTC) - self.last_sync_time
                ).total_seconds()
                if time_since_sync > self._sync_interval * 3:
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message=f"Position sync delayed: {time_since_sync:.1f}s",
                    )

            # 检查仓位数量
            position_count = len(self.positions)
            if position_count > 1000:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Large number of positions: {position_count}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"Position system operational, {position_count} positions tracked",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _load_initial_positions(self):
        """加载初始仓位"""
        try:
            # 优先使用 MarketDataService 加载仓位
            positions_data = None
            if getattr(self, "market_data_service", None):
                positions_data = await self.market_data_service.get_positions()

            if positions_data:
                for _symbol, position_data in positions_data.items():
                    position = self._dict_to_position(position_data)
                    await self._add_position(position)

            # 从交易所同步仓位
            await self._sync_from_exchanges()

            if self.logger:
                await self.logger.info(
                    f"Loaded {len(self.positions)} initial positions"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Initial position loading failed: {e}")

    async def _position_sync_loop(self):
        """仓位同步循环"""
        while True:
            try:
                await self._sync_from_exchanges()
                await self._detect_anomalies()
                await self._update_statistics()
                self.last_sync_time = datetime.now(UTC)

                await asyncio.sleep(self._sync_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Position sync error: {e}")
                await asyncio.sleep(30)  # 错误后等待30秒

    async def _position_monitor_loop(self):
        """仓位监控循环"""
        while True:
            try:
                await self._monitor_position_health()
                await self._create_snapshot()
                await self._cleanup_old_data()
                await asyncio.sleep(60)  # 1分钟监控间隔

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Position monitor error: {e}")
                await asyncio.sleep(60)

    async def _sync_from_exchanges(self):
        """从交易所同步仓位"""
        try:
            # 从交易所获取仓位数据

            for exchange in self.supported_exchanges:
                exchange_positions = await self._fetch_exchange_positions(exchange)

                for position_data in exchange_positions:
                    position = self._create_position_from_data(position_data)
                    await self._update_position(position)

            # 缓存仓位数据
            if self.cache_manager:
                positions_data = {
                    symbol: self._position_to_dict(pos)
                    for symbol, pos in self.positions.items()
                }
                await self.cache_manager.set("positions", positions_data, ttl=300)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Exchange sync failed: {e}")

    async def _fetch_exchange_positions(self, exchange: str) -> list[dict[str, Any]]:
        """获取交易所仓位数据"""
        try:
            # 调用交易所API获取仓位数据
            positions = []

            if exchange.lower() == "binance" and hasattr(self, "binance_client"):
                try:
                    binance_positions = await self.binance_client.get_positions()
                    if binance_positions:
                        positions.extend(binance_positions)
                except Exception as e:
                    if hasattr(self, "logger") and self.logger:
                        await self.logger.error(
                            f"Failed to fetch Binance positions: {e}"
                        )

            elif exchange.lower() == "deribit" and hasattr(self, "deribit_client"):
                try:
                    deribit_positions = await self.deribit_client.get_positions()
                    if deribit_positions:
                        positions.extend(deribit_positions)
                except Exception as e:
                    if hasattr(self, "logger") and self.logger:
                        await self.logger.error(
                            f"Failed to fetch Deribit positions: {e}"
                        )

            elif exchange.lower() == "ibkr" and hasattr(self, "ibkr_client"):
                try:
                    ibkr_positions = await self.ibkr_client.get_positions()
                    if ibkr_positions:
                        positions.extend(ibkr_positions)
                except Exception as e:
                    if hasattr(self, "logger") and self.logger:
                        await self.logger.error(f"Failed to fetch IBKR positions: {e}")

            return positions  # 返回从真实交易所获取的仓位数据

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Fetch positions from {exchange} failed: {e}")
            return []

    # 事件处理方法

    async def _handle_order_fill(self, event: BaseEvent):
        """处理订单成交事件"""
        try:
            data = event.data
            symbol = data.get("symbol")
            side = data.get("side")
            quantity = Decimal(str(data.get("quantity", 0)))
            price = Decimal(str(data.get("price", 0)))

            if symbol in self.positions:
                position = self.positions[symbol]

                # 更新仓位
                if side == "buy":
                    new_size = position.size + quantity
                    new_avg_price = (
                        (position.size * position.avg_price) + (quantity * price)
                    ) / new_size
                    position.size = new_size
                    position.avg_price = new_avg_price
                else:  # sell
                    position.size = max(Decimal("0"), position.size - quantity)
                    # 计算已实现盈亏
                    realized_pnl = quantity * (price - position.avg_price)
                    position.realized_pnl += realized_pnl

                position.updated_at = datetime.now(UTC)

                # 更新市值
                position.market_value = position.size * price

                if self.logger:
                    await self.logger.info(
                        f"Position updated from order fill: {symbol}"
                    )

                # 发布仓位更新事件
                await self._publish_position_update_event(
                    symbol,
                    position,
                    "adjust",
                    {
                        "side": side,
                        "quantity": float(quantity),
                        "price": float(price),
                        "realized_pnl": float(getattr(position, "realized_pnl", 0)),
                    },
                )
            else:
                # 创建新仓位
                position_type = (
                    PositionType.OPTION if "BTC-" in symbol else PositionType.SPOT
                )
                position_side = (
                    PositionSide.LONG if side == "buy" else PositionSide.SHORT
                )

                new_position = Position(
                    symbol=symbol,
                    position_type=position_type,
                    side=position_side,
                    size=quantity,
                    avg_price=price,
                    market_value=quantity * price,
                    account_id=data.get("account_id", "default"),
                    strategy_id=data.get("strategy_id"),
                )

                await self._add_position(new_position)

                # 发布新仓位事件
                await self._publish_position_update_event(
                    symbol,
                    new_position,
                    "open",
                    {"side": side, "quantity": float(quantity), "price": float(price)},
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order fill handling failed: {e}")

    async def _handle_trade_execution(self, event: BaseEvent):
        """处理交易执行事件"""
        try:
            # 类似于订单成交处理
            await self._handle_order_fill(event)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Trade execution handling failed: {e}")

    async def _handle_order_executed(self, event: BaseEvent):
        """处理订单已被交易所接受（不一定成交）事件"""
        try:
            data = event.data or {}
            if self.logger:
                await self.logger.info(
                    f"Order executed: order_id={data.get('order_id')} exch={data.get('exchange')}"
                )
            # No position mutation on execution acceptance
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order executed handling failed: {e}")

    async def _handle_position_update(self, event: BaseEvent):
        """处理仓位更新事件"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if symbol in self.positions:
                position = self.positions[symbol]

                # 更新仓位属性
                if "size" in data:
                    position.size = Decimal(str(data["size"]))
                if "market_value" in data:
                    position.market_value = Decimal(str(data["market_value"]))
                if "unrealized_pnl" in data:
                    position.unrealized_pnl = Decimal(str(data["unrealized_pnl"]))

                position.updated_at = datetime.now(UTC)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position update handling failed: {e}")

    async def _handle_market_update(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if symbol in self.positions:
                position = self.positions[symbol]

                # 更新市值和未实现盈亏
                if "last_price" in data:
                    last_price = Decimal(str(data["last_price"]))
                    position.market_value = position.size * last_price
                    position.unrealized_pnl = position.size * (
                        last_price - position.avg_price
                    )

                # 更新Greeks（期权）
                if position.position_type == PositionType.OPTION:
                    if "delta" in data:
                        position.delta = Decimal(str(data["delta"]))
                    if "gamma" in data:
                        position.gamma = Decimal(str(data["gamma"]))
                    if "theta" in data:
                        position.theta = Decimal(str(data["theta"]))
                    if "vega" in data:
                        position.vega = Decimal(str(data["vega"]))

                position.updated_at = datetime.now(UTC)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market update handling failed: {e}")

    # 公共接口方法

    async def get_position(self, symbol: str) -> dict[str, Any] | None:
        """获取单个仓位"""
        try:
            if symbol in self.positions:
                return self._position_to_dict(self.positions[symbol])
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get position failed: {e}")
            return None

    async def get_all_positions(
        self, account_id: str | None = None
    ) -> list[dict[str, Any]]:
        """获取所有仓位"""
        try:
            if account_id:
                positions = self.account_positions.get(account_id, {})
                return [self._position_to_dict(pos) for pos in positions.values()]
            else:
                return [self._position_to_dict(pos) for pos in self.positions.values()]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get all positions failed: {e}")
            return []

    async def get_positions_by_strategy(self, strategy_id: str) -> list[dict[str, Any]]:
        """按策略获取仓位"""
        try:
            positions = self.strategy_positions.get(strategy_id, {})
            return [self._position_to_dict(pos) for pos in positions.values()]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get positions by strategy failed: {e}")
            return []

    async def get_position_statistics(self) -> dict[str, Any]:
        """获取仓位统计"""
        try:
            return {
                "total_positions": self.stats.total_positions,
                "long_positions": self.stats.long_positions,
                "short_positions": self.stats.short_positions,
                "total_value": float(self.stats.total_value),
                "total_unrealized_pnl": float(self.stats.total_unrealized_pnl),
                "total_realized_pnl": float(self.stats.total_realized_pnl),
                "largest_position": self.stats.largest_position,
                "largest_position_value": float(self.stats.largest_position_value),
                "last_sync_time": self.last_sync_time.isoformat()
                if self.last_sync_time
                else None,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get statistics failed: {e}")
            return {"status": "error", "message": str(e)}

    def _position_to_dict(self, position: Position) -> dict[str, Any]:
        """仓位对象转字典"""
        return {
            "symbol": position.symbol,
            "position_type": position.position_type.value,
            "side": position.side.value,
            "size": float(position.size),
            "avg_price": float(position.avg_price),
            "market_value": float(position.market_value),
            "unrealized_pnl": float(position.unrealized_pnl),
            "realized_pnl": float(position.realized_pnl),
            "strike_price": float(position.strike_price)
            if position.strike_price
            else None,
            "expiry_date": position.expiry_date.isoformat()
            if position.expiry_date
            else None,
            "option_type": position.option_type,
            "created_at": position.created_at.isoformat(),
            "updated_at": position.updated_at.isoformat(),
            "account_id": position.account_id,
            "strategy_id": position.strategy_id,
            "delta": float(position.delta) if position.delta else None,
            "gamma": float(position.gamma) if position.gamma else None,
            "theta": float(position.theta) if position.theta else None,
            "vega": float(position.vega) if position.vega else None,
            "tags": position.tags,
        }

    def _dict_to_position(self, data: dict[str, Any]) -> Position:
        """字典转仓位对象"""
        return Position(
            symbol=data["symbol"],
            position_type=PositionType(data["position_type"]),
            side=PositionSide(data["side"]),
            size=Decimal(str(data["size"])),
            avg_price=Decimal(str(data["avg_price"])),
            market_value=Decimal(str(data["market_value"])),
            unrealized_pnl=Decimal(str(data.get("unrealized_pnl", 0))),
            realized_pnl=Decimal(str(data.get("realized_pnl", 0))),
            strike_price=Decimal(str(data["strike_price"]))
            if data.get("strike_price")
            else None,
            expiry_date=datetime.fromisoformat(data["expiry_date"])
            if data.get("expiry_date")
            else None,
            option_type=data.get("option_type"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"]),
            account_id=data.get("account_id", "default"),
            strategy_id=data.get("strategy_id"),
            delta=Decimal(str(data["delta"])) if data.get("delta") else None,
            gamma=Decimal(str(data["gamma"])) if data.get("gamma") else None,
            theta=Decimal(str(data["theta"])) if data.get("theta") else None,
            vega=Decimal(str(data["vega"])) if data.get("vega") else None,
            tags=data.get("tags", {}),
        )

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    def _create_position_from_data(self, data: dict[str, Any]) -> Position:
        """从数据创建仓位对象"""
        try:
            symbol = data["symbol"]
            size = Decimal(str(data["size"]))

            # 确定仓位类型和方向
            position_type = PositionType(data.get("position_type", "spot"))
            side = (
                PositionSide.LONG
                if size > 0
                else PositionSide.SHORT
                if size < 0
                else PositionSide.NEUTRAL
            )

            # 解析期权信息
            strike_price = None
            expiry_date = None
            option_type = None

            if position_type == PositionType.OPTION and "BTC-" in symbol:
                parts = symbol.split("-")
                if len(parts) >= 4:
                    strike_price = Decimal(parts[2])
                    option_type = "call" if parts[3] == "C" else "put"
                    # 解析到期日期
                    with contextlib.suppress(ValueError):
                        expiry_date = datetime.strptime(parts[1], "%d%b%y").replace(
                            tzinfo=UTC
                        )

            return Position(
                symbol=symbol,
                position_type=position_type,
                side=side,
                size=abs(size),
                avg_price=Decimal(str(data.get("avg_price", "0"))),
                market_value=Decimal(str(data.get("market_value", "0"))),
                unrealized_pnl=Decimal(str(data.get("unrealized_pnl", "0"))),
                strike_price=strike_price,
                expiry_date=expiry_date,
                option_type=option_type,
                account_id=data.get("account_id", "default"),
            )

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Position creation failed: {e}"))
            # 返回默认仓位
            return Position(
                symbol=data.get("symbol", "UNKNOWN"),
                position_type=PositionType.SPOT,
                side=PositionSide.NEUTRAL,
                size=Decimal("0"),
                avg_price=Decimal("0"),
                market_value=Decimal("0"),
            )

    async def _add_position(self, position: Position):
        """添加仓位"""
        try:
            self.positions[position.symbol] = position

            # 按账户分组
            if position.account_id not in self.account_positions:
                self.account_positions[position.account_id] = {}
            self.account_positions[position.account_id][position.symbol] = position

            # 按策略分组
            if position.strategy_id:
                if position.strategy_id not in self.strategy_positions:
                    self.strategy_positions[position.strategy_id] = {}
                self.strategy_positions[position.strategy_id][position.symbol] = (
                    position
                )

            # 发布事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.POSITION_ADDED,
                        data={
                            "symbol": position.symbol,
                            "size": float(position.size),
                            "side": position.side.value,
                            "account_id": position.account_id,
                        },
                    )
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Add position failed: {e}")

    async def _update_position(self, new_position: Position):
        """更新仓位"""
        try:
            symbol = new_position.symbol

            if symbol in self.positions:
                old_position = self.positions[symbol]

                # 检查是否有显著变化
                size_change = abs(new_position.size - old_position.size)
                if (
                    size_change > self.anomaly_threshold * old_position.size
                    and self.logger
                ):
                    await self.logger.warning(
                        f"Significant position change detected: {symbol}"
                    )

                # 更新仓位
                new_position.updated_at = datetime.now(UTC)
                await self._add_position(new_position)

                # 发布更新事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.POSITION_UPDATED,
                            data={
                                "symbol": symbol,
                                "old_size": float(old_position.size),
                                "new_size": float(new_position.size),
                                "size_change": float(size_change),
                            },
                        )
                    )
            else:
                # 新仓位
                await self._add_position(new_position)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Update position failed: {e}")

    async def _detect_anomalies(self):
        """检测仓位异常"""
        try:
            if not self.position_settings["enable_anomaly_detection"]:
                return

            anomalies = []
            current_time = datetime.now(UTC)

            for symbol, position in self.positions.items():
                # 检查仓位年龄
                age_hours = (current_time - position.updated_at).total_seconds() / 3600
                if age_hours > self.position_settings["max_position_age_hours"]:
                    anomalies.append(
                        {
                            "type": "stale_position",
                            "symbol": symbol,
                            "age_hours": age_hours,
                        }
                    )

                # 检查异常大小
                if position.size == 0 and position.market_value != 0:
                    anomalies.append(
                        {
                            "type": "zero_size_nonzero_value",
                            "symbol": symbol,
                            "market_value": float(position.market_value),
                        }
                    )

                # 检查价格异常
                if position.avg_price <= 0 and position.size > 0:
                    anomalies.append(
                        {
                            "type": "invalid_price",
                            "symbol": symbol,
                            "avg_price": float(position.avg_price),
                        }
                    )

            # 处理异常
            if anomalies:
                await self._handle_anomalies(anomalies)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Anomaly detection failed: {e}")

    async def _handle_anomalies(self, anomalies: list[dict[str, Any]]):
        """处理仓位异常"""
        try:
            for anomaly in anomalies:
                if self.logger:
                    await self.logger.warning(f"Position anomaly detected: {anomaly}")

                # 发布异常事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.POSITION_ANOMALY, metadata=anomaly
                        )
                    )

                # 自动修复
                if self.position_settings["enable_auto_reconciliation"]:
                    await self._auto_reconcile_position(anomaly)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Anomaly handling failed: {e}")

    async def _auto_reconcile_position(self, anomaly: dict[str, Any]):
        """自动修复仓位"""
        try:
            symbol = anomaly.get("symbol")
            anomaly_type = anomaly.get("type")

            if anomaly_type == "stale_position":
                # 重新同步该仓位
                await self._force_sync_position(symbol)
            elif anomaly_type == "zero_size_nonzero_value":
                # 清零市值
                if symbol in self.positions:
                    self.positions[symbol].market_value = Decimal("0")
            elif anomaly_type == "invalid_price":
                # 重新获取价格
                await self._update_position_price(symbol)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Auto reconciliation failed: {e}")

    async def _force_sync_position(self, symbol: str):
        """强制同步单个仓位"""
        try:
            # 强制同步单个仓位
            if symbol not in self.positions:
                if self.logger:
                    await self.logger.info(f"Creating new position entry for {symbol}")
                # 创建空的仓位记录
                self.positions[symbol] = Position(
                    symbol=symbol,
                    size=0,
                    avg_price=Decimal("0"),
                    current_price=Decimal("0"),
                    unrealized_pnl=Decimal("0"),
                )

            # 从各交易所强制同步该仓位
            total_size = 0
            weighted_price = Decimal("0")
            total_cost = Decimal("0")

            for exchange in self.supported_exchanges:
                try:
                    # 根据交易所类型调用相应的API
                    exchange_positions = (
                        await self._fetch_single_position_from_exchange(
                            exchange, symbol
                        )
                    )

                    for pos_data in exchange_positions:
                        size = pos_data.get("size", 0)
                        price = Decimal(str(pos_data.get("avg_price", 0)))

                        if size != 0:
                            total_size += size
                            cost = abs(size) * price
                            total_cost += cost

                except Exception as exchange_error:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to sync {symbol} from {exchange}: {exchange_error}"
                        )

            # 计算加权平均价格
            if total_cost > 0 and total_size != 0:
                weighted_price = total_cost / abs(total_size)

            # 更新仓位信息
            position = self.positions[symbol]
            position.size = total_size
            position.avg_price = weighted_price

            # 更新当前价格和未实现盈亏
            await self._update_position_price(symbol)

            if self.logger:
                await self.logger.info(
                    f"Force synced position {symbol}: size={total_size}, avg_price={weighted_price}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Force sync failed for {symbol}: {e}")

    async def _update_position_price(self, symbol: str):
        """更新仓位价格"""
        try:
            latest_price = None

            # 使用MarketDataService获取价格
            if self.market_data_service and symbol.upper() in ["BTC", "BTCUSDT"]:
                latest_price = await self.market_data_service.get_btc_price()
            elif self.market_data_service:
                market_data = await self.market_data_service.get_market_data(symbol)
                if market_data and "last_price" in market_data:
                    latest_price = Decimal(str(market_data["last_price"]))

            # 更新仓位
            if latest_price and latest_price > 0 and symbol in self.positions:
                position = self.positions[symbol]
                position.market_value = position.size * latest_price
                position.updated_at = datetime.now(UTC)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Price update failed for {symbol}: {e}")

    async def _monitor_position_health(self):
        """监控仓位健康状态"""
        try:
            # 检查总仓位价值
            total_value = sum(pos.market_value for pos in self.positions.values())

            # 检查集中度风险
            if self.positions:
                largest_position = max(
                    self.positions.values(), key=lambda p: p.market_value
                )
                concentration = (
                    largest_position.market_value / total_value
                    if total_value > 0
                    else 0
                )

                if concentration > Decimal("0.5") and self.event_bus:  # 50%集中度告警
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.POSITION_CONCENTRATION_ALERT,
                            data={
                                "symbol": largest_position.symbol,
                                "concentration": float(concentration),
                                "value": float(largest_position.market_value),
                            },
                        )
                    )

            # 检查到期风险
            await self._check_expiry_risk()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position health monitoring failed: {e}")

    async def _check_expiry_risk(self):
        """检查到期风险"""
        try:
            current_time = datetime.now(UTC)

            for position in self.positions.values():
                if (
                    position.expiry_date
                    and position.position_type == PositionType.OPTION
                ):
                    days_to_expiry = (position.expiry_date - current_time).days

                    if days_to_expiry <= 7 and self.event_bus:  # 7天内到期
                        await self.event_bus.publish(
                            BaseEvent(
                                event_type=EventType.POSITION_EXPIRY_ALERT,
                                data={
                                    "symbol": position.symbol,
                                    "days_to_expiry": days_to_expiry,
                                    "size": float(position.size),
                                    "market_value": float(position.market_value),
                                },
                            )
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Expiry risk check failed: {e}")

    async def _create_snapshot(self):
        """创建仓位快照"""
        try:
            snapshot = PositionSnapshot(
                timestamp=datetime.now(UTC),
                positions=self.positions.copy(),
                total_value=sum(pos.market_value for pos in self.positions.values()),
                total_pnl=sum(
                    pos.unrealized_pnl + pos.realized_pnl
                    for pos in self.positions.values()
                ),
                account_id="default",
            )

            self.position_snapshots.append(snapshot)

            # 限制快照数量
            if len(self.position_snapshots) > self._max_snapshots:
                self.position_snapshots = self.position_snapshots[
                    -self._max_snapshots :
                ]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Snapshot creation failed: {e}")

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            total_positions = len(self.positions)
            long_positions = sum(
                1 for pos in self.positions.values() if pos.side == PositionSide.LONG
            )
            short_positions = sum(
                1 for pos in self.positions.values() if pos.side == PositionSide.SHORT
            )

            total_value = sum(pos.market_value for pos in self.positions.values())
            total_unrealized_pnl = sum(
                pos.unrealized_pnl for pos in self.positions.values()
            )
            total_realized_pnl = sum(
                pos.realized_pnl for pos in self.positions.values()
            )

            # 找到最大仓位
            largest_position = None
            largest_position_value = Decimal("0")

            if self.positions:
                largest_pos = max(self.positions.values(), key=lambda p: p.market_value)
                largest_position = largest_pos.symbol
                largest_position_value = largest_pos.market_value

            self.stats = PositionStats(
                total_positions=total_positions,
                long_positions=long_positions,
                short_positions=short_positions,
                total_value=total_value,
                total_unrealized_pnl=total_unrealized_pnl,
                total_realized_pnl=total_realized_pnl,
                largest_position=largest_position,
                largest_position_value=largest_position_value,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Statistics update failed: {e}")

    async def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 清理旧快照
            if len(self.position_snapshots) > self._max_snapshots:
                self.position_snapshots = self.position_snapshots[
                    -self._max_snapshots :
                ]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Data cleanup failed: {e}")

    # 新增缺失的辅助方法

    async def _fetch_single_position_from_exchange(
        self, exchange: str, symbol: str
    ) -> list[dict[str, Any]]:
        """从特定交易所获取单个仓位数据"""
        try:
            positions = []

            if exchange.lower() == "binance" and hasattr(self, "binance_client"):
                try:
                    # 获取Binance特定仓位
                    all_positions = await self.binance_client.get_positions()
                    if all_positions:
                        # 过滤出目标symbol的仓位
                        for pos in all_positions:
                            if pos.get("symbol") == symbol and pos.get("size", 0) != 0:
                                positions.append(
                                    {
                                        "symbol": pos["symbol"],
                                        "size": pos["size"],
                                        "avg_price": pos.get("avg_price", 0),
                                        "exchange": "binance",
                                    }
                                )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to fetch {symbol} from Binance: {e}"
                        )

            elif exchange.lower() == "deribit" and hasattr(self, "deribit_client"):
                try:
                    # 获取Deribit特定仓位
                    all_positions = await self.deribit_client.get_positions()
                    if all_positions:
                        for pos in all_positions:
                            if pos.get("symbol") == symbol and pos.get("size", 0) != 0:
                                positions.append(
                                    {
                                        "symbol": pos["symbol"],
                                        "size": pos["size"],
                                        "avg_price": pos.get("avg_price", 0),
                                        "exchange": "deribit",
                                    }
                                )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to fetch {symbol} from Deribit: {e}"
                        )

            elif exchange.lower() == "ibkr" and hasattr(self, "ibkr_client"):
                try:
                    # 获取IBKR特定仓位
                    all_positions = self.ibkr_client.get_positions()
                    if symbol in all_positions and all_positions[symbol] != 0:
                        positions.append(
                            {
                                "symbol": symbol,
                                "size": all_positions[symbol],
                                "avg_price": 0,  # IBKR需要单独获取成本价
                                "exchange": "ibkr",
                            }
                        )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to fetch {symbol} from IBKR: {e}"
                        )

            return positions

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Fetch single position from {exchange} failed: {e}"
                )
            return []

    async def _publish_position_update_event(
        self, symbol: str, position, change_type: str, change_data: dict[str, Any]
    ):
        """发布仓位更新事件"""
        try:
            if not self.event_bus:
                return

            # 计算PnL数据
            current_price = change_data.get("price", float(position.avg_price))
            unrealized_pnl = float(position.size) * (
                current_price - float(position.avg_price)
            )

            position_update_event = PositionUpdateEvent(
                symbol=symbol,
                position_change={
                    "quantity": change_data.get("quantity", 0),
                    "side": change_data.get("side", ""),
                    "price": change_data.get("price", 0),
                },
                current_position={
                    "symbol": symbol,
                    "size": float(position.size),
                    "avg_price": float(position.avg_price),
                    "market_value": float(position.market_value),
                    "position_type": position.position_type.value
                    if hasattr(position.position_type, "value")
                    else str(position.position_type),
                    "side": position.side.value
                    if hasattr(position.side, "value")
                    else str(position.side),
                },
                change_type=change_type,
                pnl_data={
                    "realized_pnl": change_data.get("realized_pnl", 0),
                    "unrealized_pnl": unrealized_pnl,
                    "total_pnl": change_data.get("realized_pnl", 0) + unrealized_pnl,
                },
                source=self.component_name,
            )

            await self.event_bus.publish(position_update_event)

            if self.logger:
                await self.logger.info(
                    f"发布仓位更新事件: {symbol} {change_type}, "
                    f"size={position.size}, pnl={unrealized_pnl:.2f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"发布仓位更新事件失败: {e}")

    async def _publish_order_execution_event(
        self,
        order_id: str,
        symbol: str,
        side: str,
        quantity: float,
        price: float,
        execution_type: str,
        remaining_quantity: float = 0.0,
    ):
        """发布订单执行事件"""
        try:
            if not self.event_bus:
                return

            order_execution_event = OrderExecutionEvent(
                order_id=order_id,
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=price,
                execution_type=execution_type,
                remaining_quantity=remaining_quantity,
                source=self.component_name,
            )

            await self.event_bus.publish(order_execution_event)

            if self.logger:
                await self.logger.info(
                    f"发布订单执行事件: {order_id} {symbol} {side} {quantity}@{price}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"发布订单执行事件失败: {e}")
