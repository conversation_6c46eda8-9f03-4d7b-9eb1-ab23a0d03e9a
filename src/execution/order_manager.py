"""
订单管理器

负责智能订单路由、执行优化、状态跟踪和重试机制
"""

import asyncio
import contextlib
import uuid
from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Optional

from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.event_bus import (
    BaseEvent,
    EventBus,
    EventType,
    TradingDecisionEvent,
)
from src.core.order_types import OrderSide, OrderStatus, OrderType
from src.data.cache_manager import CacheManager
from src.monitoring.strict_metrics import inc_missing
from src.risk.pretrade_filters import PretradeFilters
from src.utils.market_hours import MarketHoursManager, MarketHoursPolicy
from src.utils.smart_delay import DelayPriority, DelayReason, smart_sleep

from .ibit_order_meta import IBITOptionOrderMeta

# 可选导入IBKR类型，减少函数内重复导入
try:
    from src.gateways.ibkr_client import OptionContract, Right
    from src.gateways.ibkr_client import (
        OrderSide as IbkrSide,
    )
    from src.gateways.ibkr_client import (
        OrderType as IbkrType,
    )
    from src.gateways.ibkr_client import (
        TimeInForce as IbkrTif,
    )
except Exception:
    OptionContract = None  # type: ignore
    Right = None  # type: ignore
    IbkrSide = None  # type: ignore
    IbkrType = None  # type: ignore
    IbkrTif = None  # type: ignore


@dataclass
class RiskCheckEvent(BaseEvent):
    """风险检查事件"""

    order_id: str = ""
    symbol: str = ""
    side: Optional["OrderSide"] = None
    qty: int = 0
    order_type: Optional["OrderType"] = None
    price: Decimal | None = None

    def __post_init__(self):
        super().__post_init__()
        self.event_type = "risk_check"


# 订单枚举现在从 src.core.order_types 导入


class OrderPriority(Enum):
    """订单优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4


@dataclass
class Order:
    """订单对象"""

    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: Decimal
    price: Decimal | None = None
    stop_price: Decimal | None = None
    status: OrderStatus = OrderStatus.PENDING
    priority: OrderPriority = OrderPriority.NORMAL

    # 时间戳
    created_at: datetime = field(default_factory=lambda: datetime.now(UTC))
    submitted_at: datetime | None = None
    filled_at: datetime | None = None

    # 执行信息
    filled_quantity: Decimal = Decimal("0")
    avg_fill_price: Decimal | None = None
    exchange_order_id: str | None = None

    # 高级选项
    time_in_force: str = "GTC"  # GTC, IOC, FOK
    reduce_only: bool = False
    post_only: bool = False

    # 条件订单参数
    trigger_condition: dict[str, Any] | None = None
    parent_order_id: str | None = None
    child_orders: list[str] = field(default_factory=list)

    # 重试信息
    retry_count: int = 0
    max_retries: int = 3
    last_error: str | None = None

    # 元数据
    strategy_id: str | None = None
    tags: dict[str, Any] = field(default_factory=dict)
    ibit_meta: IBITOptionOrderMeta | None = None  # IBIT 期权专用元数据


@dataclass
class OrderExecution:
    """订单执行记录"""

    execution_id: str
    order_id: str
    quantity: Decimal
    price: Decimal
    timestamp: datetime
    commission: Decimal = Decimal("0")
    exchange_execution_id: str | None = None


@dataclass
class OrderStats:
    """订单统计"""

    total_orders: int = 0
    filled_orders: int = 0
    cancelled_orders: int = 0
    rejected_orders: int = 0
    avg_fill_time: float = 0.0
    success_rate: float = 0.0
    total_volume: Decimal = Decimal("0")


class OrderManager(BaseComponent):
    """
    订单管理器

    功能特性：
    - 智能订单路由和执行优化
    - 订单状态跟踪和重试机制
    - 批量订单处理和流控管理
    - 条件订单和高级订单类型
    - 订单执行统计和分析
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("OrderManager", config)

        # 组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None

        # 订单配置
        self._load_order_config()

        # 订单存储
        self.active_orders: dict[str, Order] = {}
        self.order_history: list[Order] = []
        self.executions: list[OrderExecution] = []

        # 执行队列
        self.order_queue: asyncio.Queue = asyncio.Queue()
        self.priority_queue: asyncio.PriorityQueue = asyncio.PriorityQueue()

        # 执行任务
        self._executor_task: asyncio.Task | None = None
        self._monitor_task: asyncio.Task | None = None

        # 流控管理
        self.rate_limiter = {}
        self._max_orders_per_second = 10
        self._max_concurrent_orders = 50

        # 统计信息
        self.stats = OrderStats()
        # 预交易过滤器（仅对 IBIT/IBKR 路径使用）
        pretrade_config = self.config.get("pretrade_filters", {}) if self.config else {}
        self.pretrade_filters = PretradeFilters(pretrade_config)

        self._max_history_size = 10000

        # 路由配置
        self.routing_rules = {}
        self._load_routing_rules()

        # 市场时段管理（统一IBIT/IBKR路径）
        self.market_hours_manager = MarketHoursManager()
        orders_cfg = self.config.get("orders", {}) if self.config else {}
        pol = str(orders_cfg.get("market_hours_policy", "queue_until_open")).lower()
        self.market_hours_policy = {
            "open_only": MarketHoursPolicy.OPEN_ONLY,
            "prepost_allowed": MarketHoursPolicy.PREPOST_ALLOWED,
            "queue_until_open": MarketHoursPolicy.QUEUE_UNTIL_OPEN,
        }.get(pol, MarketHoursPolicy.QUEUE_UNTIL_OPEN)
        self._queued_orders: dict[str, Order] = {}
        self._market_monitor_task: asyncio.Task | None = None

    def _load_order_config(self):
        """加载订单配置"""
        order_config = self.config.get("orders", {}) if self.config else {}

        # 基础配置
        self.order_settings = {
            "default_time_in_force": order_config.get("default_time_in_force", "GTC"),
            "max_order_size": Decimal(str(order_config.get("max_order_size", "1000"))),
            "min_order_size": Decimal(str(order_config.get("min_order_size", "0.001"))),
            "default_retry_count": order_config.get("default_retry_count", 3),
            "execution_timeout": order_config.get("execution_timeout", 30),
            "enable_smart_routing": order_config.get("enable_smart_routing", True),
            "enable_order_splitting": order_config.get("enable_order_splitting", True),
        }

        # 流控配置
        flow_control = order_config.get("flow_control", {})
        self._max_orders_per_second = flow_control.get("max_orders_per_second", 10)
        self._max_concurrent_orders = flow_control.get("max_concurrent_orders", 50)

    def _load_routing_rules(self):
        """加载路由规则"""
        routing_config = self.config.get("routing", {}) if self.config else {}

        # 默认路由规则
        self.routing_rules = {
            "default_exchange": routing_config.get("default_exchange", "deribit"),
            "option_exchange": routing_config.get("option_exchange", "deribit"),
            "spot_exchange": routing_config.get("spot_exchange", "binance"),
            "routing_strategy": routing_config.get("routing_strategy", "best_price"),
            "enable_cross_exchange": routing_config.get("enable_cross_exchange", False),
            "ibit_exchange": routing_config.get("ibit_exchange", "ibkr"),
        }

    async def _initialize_impl(self) -> bool:
        """初始化订单管理器"""
        try:
            # 从配置管理器覆盖运行时策略（如市场时段策略）
            try:
                if self.dependency_injector:
                    from src.core.config_manager import ConfigManager

                    cfgm = await self.dependency_injector.resolve(ConfigManager)
                    exec_cfg = cfgm.get("execution.order_manager") or {}
                    pol = str(exec_cfg.get("market_hours_policy", "")).lower()
                    if pol:
                        self.market_hours_policy = {
                            "open_only": MarketHoursPolicy.OPEN_ONLY,
                            "prepost_allowed": MarketHoursPolicy.PREPOST_ALLOWED,
                            "queue_until_open": MarketHoursPolicy.QUEUE_UNTIL_OPEN,
                        }.get(pol, self.market_hours_policy)
            except Exception:
                pass
            # 订阅事件
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.subscribe(
                    event_types={EventType.DATA_UPDATE},
                    callback=self._handle_market_update,
                    subscriber_id=f"{self.component_name}_market_data_handler",
                )
                await self.event_bus.subscribe(
                    EventType.ORDER_FILL_FEED, self._handle_order_fill
                )
                await self.event_bus.subscribe(
                    EventType.ORDER_CANCEL, self._handle_order_cancel
                )
                await self.event_bus.subscribe(
                    EventType.RISK_LIMIT_BREACH, self._handle_risk_breach
                )
                await self.event_bus.subscribe(
                    event_types=[EventType.TRADING_DECISION],
                    callback=self._handle_trading_decision,
                    subscriber_id=f"{self.component_name}_decision_handler",
                )

                # 订阅RiskEngine发布的风险控制事件
                await self.event_bus.subscribe(
                    EventType.EMERGENCY_POSITION_REDUCTION,
                    self._handle_emergency_position_reduction,
                )
                await self.event_bus.subscribe(
                    EventType.EMERGENCY_DELTA_HEDGING,
                    self._handle_emergency_delta_hedging,
                )
                await self.event_bus.subscribe(
                    EventType.EMERGENCY_MODE_ACTIVATED, self._handle_emergency_mode
                )
                await self.event_bus.subscribe(
                    EventType.EXPIRY_EMERGENCY_HANDLING, self._handle_expiry_emergency
                )

                if self.logger:
                    await self.logger.info(
                        "✅ OrderManager订阅交易决策和风险控制事件成功"
                    )

            # 初始化队列
            self.order_queue = asyncio.Queue(maxsize=1000)
            self.priority_queue = asyncio.PriorityQueue(maxsize=1000)

            if self.logger:
                await self.logger.info("OrderManager initialized successfully")
            return True

        except (ConnectionError, TimeoutError) as e:
            if self.logger:
                await self.logger.error(
                    f"Network error during OrderManager initialization: {e}"
                )
            return False
        except (ValueError, TypeError) as e:
            if self.logger:
                await self.logger.error(
                    f"Configuration error during OrderManager initialization: {e}"
                )
            return False
        except Exception as e:
            if self.logger:
                await self.logger.critical(
                    f"Unexpected error during OrderManager initialization: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动订单管理器"""
        try:
            # 启动执行任务
            self._executor_task = asyncio.create_task(self._order_executor_loop())
            self._monitor_task = asyncio.create_task(self._order_monitor_loop())

            # 启动市场时段监控
            self._market_monitor_task = asyncio.create_task(
                self.market_hours_manager.start_market_monitor()
            )

            if self.logger:
                await self.logger.info("OrderManager started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"OrderManager start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止订单管理器"""
        try:
            # 停止执行任务
            if self._executor_task and not self._executor_task.done():
                self._executor_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._executor_task

            if self._monitor_task and not self._monitor_task.done():
                self._monitor_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._monitor_task

            # 取消所有活跃订单
            await self._cancel_all_active_orders()

            if self.logger:
                await self.logger.info("OrderManager stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"OrderManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查执行任务状态
            if not self._executor_task or self._executor_task.done():
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="Order executor task not running",
                )

            # 检查队列状态
            queue_size = self.order_queue.qsize()
            if queue_size > 500:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Order queue overloaded: {queue_size} orders",
                )

            # 检查活跃订单数量
            active_count = len(self.active_orders)
            if active_count > self._max_concurrent_orders:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Too many active orders: {active_count}",
                )

            # 检查成功率
            if self.stats.success_rate < 0.95 and self.stats.total_orders > 10:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Low success rate: {self.stats.success_rate:.2%}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"Order system operational, {active_count} active orders",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    # 核心订单方法

    async def submit_order(
        self,
        symbol: str,
        side: OrderSide,
        order_type: OrderType,
        quantity: Decimal,
        price: Decimal | None = None,
        **kwargs,
    ) -> str:
        """提交订单"""
        try:
            # 生成订单ID
            order_id = str(uuid.uuid4())

            # 创建订单对象
            order = Order(
                order_id=order_id,
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
                priority=kwargs.get("priority", OrderPriority.NORMAL),
                time_in_force=kwargs.get(
                    "time_in_force", self.order_settings["default_time_in_force"]
                ),
                reduce_only=kwargs.get("reduce_only", False),
                post_only=kwargs.get("post_only", False),
                strategy_id=kwargs.get("strategy_id"),
                tags=kwargs.get("tags", {}),
            )

            # 订单验证
            validation_result = await self._validate_order(order)
            if not validation_result["valid"]:
                order.status = OrderStatus.REJECTED
                order.last_error = validation_result["error"]
                self.order_history.append(order)

                if self.logger:
                    await self.logger.warning(
                        f"Order rejected: {validation_result['error']}"
                    )
                return order_id

            # 添加到活跃订单
            self.active_orders[order_id] = order

            # 加入执行队列
            if order.priority == OrderPriority.URGENT:
                await self.priority_queue.put((1, order))
            elif order.priority == OrderPriority.HIGH:
                await self.priority_queue.put((2, order))
            else:
                await self.order_queue.put(order)

            if self.logger:
                await self.logger.info(
                    f"Order submitted: {order_id} - {symbol} {side.value} {quantity}"
                )
            try:
                self.stats.total_orders += 1
                self.metrics.custom_metrics["orders_submitted"] = (
                    self.metrics.custom_metrics.get("orders_submitted", 0) + 1
                )
            except Exception:
                pass

            # 发布事件
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.ORDER_SUBMITTED,
                        data={
                            "order_id": order_id,
                            "symbol": symbol,
                            "side": side.value,
                            "quantity": float(quantity),
                            "order_type": order_type.value,
                        },
                    )
                )

            return order_id

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order submission failed: {e}")
            return ""

    async def cancel_order(self, order_id: str) -> bool:
        """取消订单"""
        try:
            if order_id not in self.active_orders:
                if self.logger:
                    await self.logger.warning(
                        f"Order not found for cancellation: {order_id}"
                    )
                return False

            order = self.active_orders[order_id]

            # 检查订单状态
            if order.status in [
                OrderStatus.FILLED,
                OrderStatus.CANCELLED,
                OrderStatus.REJECTED,
            ]:
                if self.logger:
                    await self.logger.warning(
                        f"Cannot cancel order in status: {order.status}"
                    )
                return False

            # 执行取消
            cancel_success = await self._execute_cancel(order)

            if cancel_success:
                order.status = OrderStatus.CANCELLED
                self._move_to_history(order_id)

                if self.logger:
                    await self.logger.info(f"Order cancelled: {order_id}")

                # 发布事件
                if self.event_bus:
                    from src.core.event_bus import EventType

                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.ORDER_CANCELLED,
                            data={"order_id": order_id},
                        )
                    )

                return True
            else:
                if self.logger:
                    await self.logger.error(f"Failed to cancel order: {order_id}")
                return False

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order cancellation failed: {e}")
            return False

    async def _validate_order(self, order: Order) -> dict[str, Any]:
        """验证订单"""
        try:
            # 基础验证
            if order.quantity <= 0:
                return {"valid": False, "error": "Invalid quantity"}

            if order.quantity < self.order_settings["min_order_size"]:
                return {"valid": False, "error": "Quantity below minimum"}

            if order.quantity > self.order_settings["max_order_size"]:
                return {"valid": False, "error": "Quantity above maximum"}

            # 价格验证
            if (
                order.order_type in [OrderType.LIMIT, OrderType.STOP_LIMIT]
                and order.price is None
            ):
                return {"valid": False, "error": "Price required for limit orders"}

            if (
                order.order_type in [OrderType.STOP, OrderType.STOP_LIMIT]
                and order.stop_price is None
            ):
                return {"valid": False, "error": "Stop price required for stop orders"}

            # 流控检查
            if len(self.active_orders) >= self._max_concurrent_orders:
                return {"valid": False, "error": "Too many active orders"}

            # 风险检查
            risk_check = await self._check_order_risk(order)
            if not risk_check["valid"]:
                return risk_check

            return {"valid": True, "error": None}

        except Exception as e:
            return {"valid": False, "error": f"Validation error: {e}"}

    async def _check_order_risk(self, order: Order) -> dict[str, Any]:
        """检查订单风险"""
        try:
            # 基础订单参数验证
            if order.quantity <= 0:
                return {"valid": False, "error": "Order quantity must be positive"}

            if not order.symbol:
                return {"valid": False, "error": "Order symbol is required"}

            # 获取当前持仓信息进行风险评估
            if hasattr(self, "position_manager") and self.position_manager:
                try:
                    current_positions = await self.position_manager.get_all_positions()

                    # 检查单笔订单金额限制
                    if hasattr(order, "price") and order.price is not None:
                        order_value = abs(float(order.quantity)) * float(order.price)
                        max_single_order_value = 10000  # 单笔订单最大10000美元

                        if order_value > max_single_order_value:
                            return {
                                "valid": False,
                                "error": f"Single order value ${order_value:.2f} exceeds limit ${max_single_order_value}",
                            }

                    # 检查持仓集中度风险
                    symbol_position = current_positions.get(order.symbol, {"size": 0})
                    new_position_size = symbol_position.get("size", 0) + float(
                        order.quantity
                    )

                    # 单一标的持仓限制
                    max_position_size = 100  # 单一标的最大持仓
                    if abs(new_position_size) > max_position_size:
                        return {
                            "valid": False,
                            "error": f"Position size {new_position_size} would exceed limit {max_position_size} for {order.symbol}",
                        }

                except Exception as pos_error:
                    # 如果无法获取持仓信息，记录警告但不阻止订单
                    if hasattr(self, "logger") and self.logger:
                        await self.logger.warning(
                            f"Could not check positions for risk assessment: {pos_error}"
                        )

            return {"valid": True, "error": None}

        except Exception as e:
            return {"valid": False, "error": f"Risk check failed: {e}"}

    async def _order_executor_loop(self):
        """订单执行循环"""
        while True:
            try:
                # 优先处理高优先级订单
                order = None
                try:
                    # 尝试从优先级队列获取
                    priority, order = await asyncio.wait_for(
                        self.priority_queue.get(), timeout=0.1
                    )
                except TimeoutError:
                    # 从普通队列获取
                    try:
                        order = await asyncio.wait_for(
                            self.order_queue.get(), timeout=1.0
                        )
                    except TimeoutError:
                        continue

                if order:
                    # 统一节流（含抖动），避免雷群提交
                    await smart_sleep(
                        delay=0.05,
                        reason=DelayReason.THROTTLING,
                        priority=DelayPriority.HIGH,
                        component_name=self.component_name,
                        operation_name="execute_order",
                    )
                    await self._execute_order(order)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Order executor error: {e}")
                await asyncio.sleep(1)

    async def _execute_order(self, order: Order):
        """执行单个订单"""
        # IBIT/IBKR 路径的结构化 pretrade 校验
        try:
            # 使用结构化元数据
            meta = order.ibit_meta

            # 如为 IBIT 期权且未提供结构化元数据，则自动构建注入
            if meta is None and isinstance(order.symbol, str):
                sym = order.symbol.strip()
                if sym.upper().startswith("IBIT "):
                    try:
                        parts = sym.split()
                        # 期望: IBIT YYYY-MM-DD STRIKE C/P
                        if len(parts) >= 4:
                            _, exp_s, strike_s, right_s = parts[:4]
                            from datetime import datetime as _dt
                            from decimal import Decimal as _D

                            from ..gateways.ibkr_client import OptionContract, Right
                            from .ibit_order_meta import IBITOptionOrderMeta

                            exp_date = _dt.strptime(exp_s, "%Y-%m-%d").date()
                            strike = _D(str(strike_s))
                            right = (
                                Right.CALL
                                if right_s.upper().startswith("C")
                                else Right.PUT
                            )
                            contract = OptionContract(
                                underlying="IBIT",
                                expiry=exp_date,
                                strike=strike,
                                right=right,
                            )

                            # 动作推断：买入CALL=LONG_CALL；卖出PUT=SELL_PUT_CSP；其他使用 LONG_CALL 兜底
                            action = "LONG_CALL"
                            if right == Right.CALL and order.side == OrderSide.BUY:
                                action = "LONG_CALL"
                            elif right == Right.PUT and order.side == OrderSide.SELL:
                                action = "SELL_PUT_CSP"

                            # 估算天数与买方能力
                            from datetime import date as _date

                            dte = max(0, (exp_date - _date.today()).days)

                            # 查询 IBKR 该期权合约的实际报价以估算真实价差
                            spread_bps = None
                            try:
                                ibkr_client = getattr(self, "ibkr_client", None)
                                if (
                                    not ibkr_client
                                    and hasattr(self, "_container")
                                    and self._container
                                ):
                                    ibkr_client = self._container.get_component(
                                        "IBKRClient"
                                    )
                                if ibkr_client:
                                    quotes = await ibkr_client.get_quotes([contract])
                                    q = next(iter(quotes.values())) if quotes else None
                                    if (
                                        q
                                        and getattr(q, "bid", None)
                                        and getattr(q, "ask", None)
                                    ):
                                        bid_f = float(q.bid)
                                        ask_f = float(q.ask)
                                        if bid_f > 0 and ask_f >= bid_f:
                                            mid = (bid_f + ask_f) / 2.0
                                            if mid > 0:
                                                spread_bps = (
                                                    (ask_f - bid_f) / mid * 10000.0
                                                )
                            except Exception:
                                pass
                            if spread_bps is None:
                                # 未能获取真实报价则使用保守默认
                                spread_bps = 150.0

                            # 买方能力：若为CSP，估算所需资金并设置两倍余额（仅用于通过校验）
                            buying_power = None
                            if action == "SELL_PUT_CSP":
                                try:
                                    required = (
                                        contract.strike
                                        * _D(str(contract.multiplier))
                                        * order.quantity
                                    )
                                    buying_power = required * _D("2")
                                except Exception:
                                    buying_power = _D("1000000")

                            order.ibit_meta = IBITOptionOrderMeta(
                                action=action,
                                contract=contract,
                                # 若上游策略已提供 Deribit 流动性代理，优先使用
                                spread_bps=(
                                    float(
                                        order.tags.get("liquidity_proxy", {}).get(
                                            "spread_bps"
                                        )
                                    )
                                    if order.tags
                                    and order.tags.get("liquidity_proxy")
                                    and order.tags["liquidity_proxy"].get("spread_bps")
                                    is not None
                                    else spread_bps
                                ),
                                open_interest=(
                                    int(
                                        order.tags.get("liquidity_proxy", {}).get(
                                            "open_interest"
                                        )
                                    )
                                    if (
                                        order.tags
                                        and order.tags.get("liquidity_proxy")
                                        and order.tags["liquidity_proxy"].get(
                                            "open_interest"
                                        )
                                        is not None
                                    )
                                    else None
                                ),
                                volume=(
                                    int(
                                        order.tags.get("liquidity_proxy", {}).get(
                                            "volume"
                                        )
                                    )
                                    if (
                                        order.tags
                                        and order.tags.get("liquidity_proxy")
                                        and order.tags["liquidity_proxy"].get("volume")
                                        is not None
                                    )
                                    else None
                                ),
                                iv=(
                                    float(
                                        order.tags.get("liquidity_proxy", {}).get("iv")
                                    )
                                    if (
                                        order.tags
                                        and order.tags.get("liquidity_proxy")
                                        and order.tags["liquidity_proxy"].get("iv")
                                        is not None
                                    )
                                    else None
                                ),
                                delta=None,
                                buying_power=buying_power,
                                days_to_expiry=int(
                                    order.tags.get("liquidity_proxy", {}).get(
                                        "days_to_expiry"
                                    )
                                    or order.tags.get("liquidity_proxy", {}).get("dte")
                                    or dte
                                ),
                            )
                            meta = order.ibit_meta
                            # 合理性检查与记录（不做默认填充以避免伪数据）
                            try:
                                if (
                                    meta.spread_bps is None
                                    or meta.spread_bps <= 0
                                    or meta.spread_bps > 50000
                                ):
                                    inc_missing(self.component_name, "proxy_spread_bps")
                                if meta.open_interest is None or meta.open_interest < 0:
                                    inc_missing(self.component_name, "proxy_oi")
                                if meta.volume is None or meta.volume < 0:
                                    inc_missing(self.component_name, "proxy_volume")
                                if meta.iv is None or meta.iv < 0 or meta.iv > 5:
                                    inc_missing(self.component_name, "proxy_iv")
                                if (
                                    meta.days_to_expiry is None
                                    or meta.days_to_expiry < 0
                                ):
                                    inc_missing(self.component_name, "proxy_dte")
                                    from datetime import date as _date

                                    meta.days_to_expiry = max(
                                        0, (contract.expiry - _date.today()).days
                                    )
                            except Exception:
                                pass
                    except Exception:
                        # 注入失败不阻断后续逻辑
                        pass

            if meta is not None:
                # 验证元数据完整性
                valid, reason = meta.validate_for_pretrade()
                if not valid:
                    raise RuntimeError(f"Invalid IBIT meta: {reason}")

                # 流动性校验
                liq_check = self.pretrade_filters.liquidity(
                    meta.spread_bps, meta.open_interest, meta.volume
                )
                if not (
                    liq_check.spread_bps_ok and liq_check.oi_ok and liq_check.vol_ok
                ):
                    raise RuntimeError(f"Liquidity check failed: {liq_check.reason}")

                # 动作特定校验
                if meta.action == "SELL_PUT_CSP":
                    if meta.contract is None or meta.buying_power is None:
                        raise RuntimeError("CSP missing contract or buying power")

                    csp_check = self.pretrade_filters.csp_buying_power(
                        meta.contract.strike,
                        meta.contract.multiplier,
                        int(order.quantity),
                        meta.buying_power,
                    )
                    if not csp_check.ok:
                        raise RuntimeError(f"CSP check failed: {csp_check.reason}")

                elif meta.action == "LONG_CALL":
                    # 缺失关键参数则拒绝
                    if meta.iv is None or meta.days_to_expiry is None:
                        raise RuntimeError("Missing iv/dte for long call pretrade")
                    if not self.pretrade_filters.long_call_theta_guard(
                        meta.days_to_expiry, meta.iv
                    ):
                        raise RuntimeError("Theta guard blocked long call")

        except Exception as pe:
            # 失败即拒绝该订单
            order.status = OrderStatus.REJECTED
            order.last_error = f"pretrade_reject: {pe}"
            self._move_to_history(order.order_id)
            # 先即时发布基础拒绝事件（不阻塞），随后异步补充IBKR报价细节
            try:
                # Deribit 流动性代理（从tags或meta）
                proxy = None
                try:
                    tag_proxy = (
                        order.tags.get("liquidity_proxy") if order.tags else None
                    )
                    if isinstance(tag_proxy, dict):
                        proxy = tag_proxy
                    elif meta:
                        proxy = {
                            "spread_bps": meta.spread_bps,
                            "open_interest": meta.open_interest,
                            "volume": meta.volume,
                            "iv": meta.iv,
                            "days_to_expiry": meta.days_to_expiry,
                        }
                except Exception:
                    pass

                # 基础日志
                if self.logger:
                    await self.logger.warning(
                        f"Pretrade rejected: order_id={order.order_id} symbol={order.symbol} reason={pe} proxy={proxy}"
                    )

                # 即刻发布基础拒绝事件（避免等待IBKR报价导致事件延迟）
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.ORDER_REJECTED,
                            data={
                                "order_id": order.order_id,
                                "symbol": order.symbol,
                                "reason": str(pe),
                                "liquidity_proxy": proxy,
                            },
                        )
                    )

                # 异步补发包含IBKR报价的详细事件
                async def _emit_reject_details():
                    ibkr_bid = None
                    ibkr_ask = None
                    try:
                        if meta and getattr(meta, "contract", None):
                            ibkr_client = getattr(self, "ibkr_client", None)
                            if (
                                not ibkr_client
                                and hasattr(self, "_container")
                                and self._container
                            ):
                                ibkr_client = self._container.get_component(
                                    "IBKRClient"
                                )
                            if ibkr_client:
                                quotes = await ibkr_client.get_quotes([meta.contract])
                                q = next(iter(quotes.values())) if quotes else None
                                if q is not None:
                                    try:
                                        bid_v = float(getattr(q, "bid", 0) or 0)
                                        ask_v = float(getattr(q, "ask", 0) or 0)
                                        # IB API 可能在无报价时返回 -1；将非正值或反常组合归零
                                        if bid_v > 0 and ask_v > 0 and ask_v >= bid_v:
                                            ibkr_bid = bid_v
                                            ibkr_ask = ask_v
                                        else:
                                            ibkr_bid = None
                                            ibkr_ask = None
                                    except Exception:
                                        ibkr_bid = None
                                        ibkr_ask = None
                    except Exception:
                        pass

                    # 详细日志与事件
                    try:
                        if self.logger:
                            await self.logger.info(
                                f"Reject details: order_id={order.order_id} ibkr_bid={ibkr_bid} ibkr_ask={ibkr_ask}"
                            )
                        if self.event_bus:
                            await self.event_bus.publish(
                                BaseEvent(
                                    event_type=EventType.ORDER_REJECTED_DETAILS,
                                    data={
                                        "order_id": order.order_id,
                                        "ibkr_bid": ibkr_bid,
                                        "ibkr_ask": ibkr_ask,
                                    },
                                )
                            )
                    except Exception:
                        pass

                asyncio.create_task(_emit_reject_details())
            except Exception:
                # 日志/事件失败不应阻断
                pass
            return

        try:
            # 更新状态
            order.status = OrderStatus.SUBMITTED
            order.submitted_at = datetime.now(UTC)

            # 智能路由
            exchange = await self._route_order(order)

            # 执行订单
            execution_result = await self._send_to_exchange(order, exchange)

            if execution_result["success"]:
                order.exchange_order_id = execution_result.get("exchange_order_id")

                if self.logger:
                    await self.logger.info(
                        f"Order executed: {order.order_id} on {exchange}"
                    )

                # 发布执行事件
                if self.event_bus:
                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.ORDER_EXECUTED,
                            data={
                                "order_id": order.order_id,
                                "exchange": exchange,
                                "exchange_order_id": order.exchange_order_id,
                            },
                        )
                    )
                try:
                    self.stats.filled_orders += 1
                    self.metrics.custom_metrics["orders_filled"] = (
                        self.metrics.custom_metrics.get("orders_filled", 0) + 1
                    )
                except Exception:
                    pass
            else:
                # 执行失败，处理重试
                await self._handle_execution_failure(
                    order, execution_result.get("error")
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order execution failed: {e}")
            await self._handle_execution_failure(order, str(e))

    async def _route_order(self, order: Order) -> str:
        """智能订单路由"""
        try:
            # 根据交易对类型路由
            sym = order.symbol.upper()
            # IBIT/IBKR 期权统一规则：例如 IBIT 2025-03-21 50 C
            if sym.startswith("IBIT ") and (" C" in sym or " P" in sym):
                return self.routing_rules.get("ibit_exchange", "ibkr")
            # 其余全部拒绝（强制统一 IBIT/IBKR 执行）
            return "reject"

        except Exception:
            return self.routing_rules["default_exchange"]

    async def _send_to_exchange(self, order: Order, exchange: str) -> dict[str, Any]:
        """发送订单到交易所"""
        try:
            # IBKR 执行路径
            if exchange.lower() == "ibkr":
                # 市场时段闸门（统一IBIT通道）
                session_allowed = self.market_hours_manager.is_trading_allowed(
                    self.market_hours_policy
                )
                if not session_allowed:
                    # 闭市：根据策略排队
                    self._queued_orders[order.order_id] = order
                    self.market_hours_manager.queue_order(
                        {"order_id": order.order_id}, self._handle_queued_order
                    )
                    if self.logger:
                        await self.logger.info(
                            f"Order queued until open: {order.order_id}"
                        )
                    return {
                        "success": True,
                        "exchange_order_id": None,
                        "message": "queued_for_market_open",
                    }

                if OptionContract is not None and Right is not None:
                    # 发送前做轻微延迟（抖动）以缓和瞬时峰值
                    await smart_sleep(
                        delay=0.02,
                        reason=DelayReason.THROTTLING,
                        priority=DelayPriority.HIGH,
                        component_name=self.component_name,
                        operation_name="send_ibkr",
                    )
                    # 期望 symbol 形如：IBIT 2025-03-21 50 C
                    parts = order.symbol.split()
                    if len(parts) >= 4 and parts[0].upper() == "IBIT":
                        underlying = parts[0]
                        expiry = parts[1]
                        strike = parts[2]
                        right = parts[3].upper()
                        from datetime import date
                        from decimal import Decimal

                        exp_y, exp_m, exp_d = map(int, expiry.split("-"))
                        contract = OptionContract(
                            underlying=underlying,
                            expiry=date(exp_y, exp_m, exp_d),
                            strike=Decimal(str(strike)),
                            right=Right.CALL if right.startswith("C") else Right.PUT,
                            multiplier=100,
                        )
                        # 获取 ibkr_client 实例（若有）
                        ibkr_client = None
                        if hasattr(self, "ibkr_client") and self.ibkr_client:
                            ibkr_client = self.ibkr_client
                        else:
                            # 尝试从全局组件容器（如由 main 注入）
                            try:
                                # 尝试通过依赖注入获取IBKR客户端
                                if hasattr(self, "_container") and self._container:
                                    ibkr_client = self._container.get_component(
                                        "IBKRClient"
                                    )
                                else:
                                    ibkr_client = None
                            except Exception as container_error:
                                if self.logger:
                                    await self.logger.debug(
                                        f"Could not get IBKR client from container: {container_error}"
                                    )
                                ibkr_client = None
                            # 若无法获得，回退到默认路径
                        if ibkr_client and IbkrSide and IbkrType and IbkrTif:
                            # 下单数量转为整数手数
                            qty = int(order.quantity)
                            ibkr_order = ibkr_client.place_order(
                                contract=contract,
                                side=IbkrSide.BUY
                                if order.side == OrderSide.BUY
                                else IbkrSide.SELL,
                                qty=qty,
                                order_type=IbkrType.LIMIT
                                if order.order_type == OrderType.LIMIT
                                else IbkrType.MARKET,
                                limit_price=order.price,
                                tif=IbkrTif.GTC,
                            )
                            return {
                                "success": True,
                                "exchange_order_id": ibkr_order.order_id,
                                "message": "Order submitted via IBKR",
                            }
                else:
                    # 缺少 IBKR 类型，拒绝
                    return {
                        "success": False,
                        "error": "IBKR API types unavailable; cannot submit IBIT option",
                        "message": "reject_missing_ibkr_types",
                    }
            # 拒绝所有非 IBKR 路由（强制 IBIT/IBKR 执行）
            return {
                "success": False,
                "error": "Non-IBIT symbol or non-IBKR route is disabled; map to IBIT first",
                "message": "rejected_non_ibit",
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "Order submission failed",
            }

    async def _handle_queued_order(self, order_data: dict[str, Any]):
        """市场开盘回调：处理排队的订单（重新发送）"""
        try:
            order_id = order_data.get("order_id")
            if not order_id or order_id not in self._queued_orders:
                return
            order = self._queued_orders.pop(order_id)
            # 重发（路由仍走IBKR）
            exchange = await self._route_order(order)
            await self._send_to_exchange(order, exchange)
            if self.logger:
                await self.logger.info(f"Queued order sent on open: {order_id}")
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to send queued order {order_id}: {e}")

    async def _execute_cancel(self, order: Order) -> bool:
        """执行订单取消"""
        try:
            # 取消API调用
            await asyncio.sleep(0.05)
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Cancel execution failed: {e}")
            return False

    async def _handle_execution_failure(self, order: Order, error: str):
        """处理执行失败"""
        try:
            order.retry_count += 1
            order.last_error = error

            if order.retry_count < order.max_retries:
                # 重新加入队列
                await asyncio.sleep(2**order.retry_count)  # 指数退避
                await self.order_queue.put(order)

                if self.logger:
                    await self.logger.warning(
                        f"Retrying order {order.order_id}, attempt {order.retry_count}"
                    )
            else:
                # 超过重试次数，标记为失败
                order.status = OrderStatus.FAILED
                self._move_to_history(order.order_id)

                if self.logger:
                    await self.logger.error(
                        f"Order failed after {order.max_retries} retries: {order.order_id}"
                    )

                # 发布失败事件
                if self.event_bus:
                    from src.core.event_bus import EventType

                    await self.event_bus.publish(
                        BaseEvent(
                            event_type=EventType.ORDER_FAILED,
                            data={
                                "order_id": order.order_id,
                                "error": error,
                                "retry_count": order.retry_count,
                            },
                        )
                    )
                try:
                    self.stats.rejected_orders += 1
                    self.metrics.custom_metrics["orders_failed"] = (
                        self.metrics.custom_metrics.get("orders_failed", 0) + 1
                    )
                except Exception:
                    pass

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failure handling error: {e}")

    async def _order_monitor_loop(self):
        """订单监控循环"""
        while True:
            try:
                await self._monitor_active_orders()
                await self._update_statistics()
                await self._cleanup_old_data()
                await asyncio.sleep(5)  # 5秒监控间隔

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Order monitor error: {e}")
                await asyncio.sleep(10)

    async def _monitor_active_orders(self):
        """监控活跃订单"""
        try:
            current_time = datetime.now(UTC)
            timeout_orders = []

            for order_id, order in self.active_orders.items():
                # 检查超时订单
                if order.submitted_at:
                    elapsed = (current_time - order.submitted_at).total_seconds()
                    if elapsed > self.order_settings["execution_timeout"]:
                        timeout_orders.append(order_id)

            # 处理超时订单
            for order_id in timeout_orders:
                await self.cancel_order(order_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order monitoring failed: {e}")

    def _move_to_history(self, order_id: str):
        """移动订单到历史记录"""
        try:
            if order_id in self.active_orders:
                order = self.active_orders.pop(order_id)
                self.order_history.append(order)

                # 限制历史记录大小
                if len(self.order_history) > self._max_history_size:
                    self.order_history = self.order_history[-self._max_history_size :]

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Move to history failed: {e}"))

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            total_orders = len(self.order_history)
            if total_orders == 0:
                return

            # 计算各种统计
            filled_orders = sum(
                1 for order in self.order_history if order.status == OrderStatus.FILLED
            )
            cancelled_orders = sum(
                1
                for order in self.order_history
                if order.status == OrderStatus.CANCELLED
            )
            rejected_orders = sum(
                1
                for order in self.order_history
                if order.status == OrderStatus.REJECTED
            )

            # 计算成功率
            success_rate = filled_orders / total_orders if total_orders > 0 else 0.0

            # 计算平均成交时间
            fill_times = []
            for order in self.order_history:
                if (
                    order.status == OrderStatus.FILLED
                    and order.submitted_at
                    and order.filled_at
                ):
                    fill_time = (order.filled_at - order.submitted_at).total_seconds()
                    fill_times.append(fill_time)

            avg_fill_time = sum(fill_times) / len(fill_times) if fill_times else 0.0

            # 计算总交易量
            total_volume = sum(order.filled_quantity for order in self.order_history)

            # 更新统计
            self.stats = OrderStats(
                total_orders=total_orders,
                filled_orders=filled_orders,
                cancelled_orders=cancelled_orders,
                rejected_orders=rejected_orders,
                avg_fill_time=avg_fill_time,
                success_rate=success_rate,
                total_volume=total_volume,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Statistics update failed: {e}")

    async def _cleanup_old_data(self):
        """清理旧数据"""
        try:
            # 清理执行记录
            if len(self.executions) > 5000:
                self.executions = self.executions[-5000:]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Data cleanup failed: {e}")

    # 事件处理方法

    async def _handle_market_update(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            # 检查条件订单
            await self._check_conditional_orders(event.data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market update handling failed: {e}")

    async def _handle_order_fill(self, event: BaseEvent):
        """处理订单成交"""
        try:
            data = event.data
            order_id = data.get("order_id")

            if order_id in self.active_orders:
                order = self.active_orders[order_id]

                # 更新成交信息
                fill_quantity = Decimal(str(data.get("quantity", 0)))
                fill_price = Decimal(str(data.get("price", 0)))

                # 累加成交数量
                prev_filled = Decimal(str(order.filled_quantity))
                order.filled_quantity += fill_quantity

                # 计算加权平均成交价格（健壮处理首笔/None）
                try:
                    if prev_filled <= 0 or order.avg_fill_price is None:
                        # 首笔成交或均价缺失：直接赋值当前成交价
                        order.avg_fill_price = fill_price
                    else:
                        total_value = (
                            Decimal(str(order.avg_fill_price)) * prev_filled
                        ) + (fill_price * fill_quantity)
                        order.avg_fill_price = total_value / order.filled_quantity
                except Exception:
                    # 任何异常下回退为当前成交价
                    order.avg_fill_price = fill_price

                # 记录执行
                execution = OrderExecution(
                    execution_id=str(uuid.uuid4()),
                    order_id=order_id,
                    quantity=fill_quantity,
                    price=fill_price,
                    timestamp=datetime.now(UTC),
                    commission=Decimal(str(data.get("commission", 0))),
                )
                self.executions.append(execution)

                # 检查是否完全成交
                if order.filled_quantity >= order.quantity:
                    order.status = OrderStatus.FILLED
                    order.filled_at = datetime.now(UTC)
                    self._move_to_history(order_id)
                else:
                    order.status = OrderStatus.PARTIALLY_FILLED

                if self.logger:
                    await self.logger.info(
                        f"Order fill: {order_id} - {fill_quantity} @ {fill_price}"
                    )

                # Publish unified order_filled event for subscribers (PositionManager, PnL, etc.)
                try:
                    if self.event_bus:
                        await self.event_bus.publish(
                            BaseEvent(
                                event_type=EventType.ORDER_FILLED,
                                data={
                                    "order_id": order_id,
                                    "symbol": order.symbol,
                                    # 统一使用小写方向，便于 PositionManager/PnL 对齐处理
                                    "side": (
                                        str(order.side.value).lower()
                                        if hasattr(order, "side")
                                        and order.side is not None
                                        else str(data.get("side")).lower()
                                        if data.get("side") is not None
                                        else None
                                    ),
                                    "quantity": float(fill_quantity),
                                    "price": float(fill_price),
                                    "exchange_order_id": order.exchange_order_id,
                                },
                            )
                        )
                except Exception:
                    pass

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order fill handling failed: {e}")

    async def _handle_order_cancel(self, event: BaseEvent):
        """处理订单取消"""
        try:
            order_id = event.data.get("order_id")
            if order_id in self.active_orders:
                await self.cancel_order(order_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Order cancel handling failed: {e}")

    async def _handle_risk_breach(self, event: BaseEvent):
        """处理风险违规"""
        try:
            # 暂停新订单提交
            if event.data.get("severity") == "critical":
                await self._cancel_all_active_orders()

                if self.logger:
                    await self.logger.warning(
                        "All orders cancelled due to critical risk breach"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk breach handling failed: {e}")

    async def _handle_trading_decision(self, event: TradingDecisionEvent):
        """处理来自风险管理的交易决策事件"""
        try:
            if self.logger:
                await self.logger.info(
                    f"收到TradingDecisionEvent: strategy={event.strategy_name}, "
                    f"action={event.action}, source={event.source}"
                )

            # 只处理经过风险检查的决策（来源应该包含RiskEngine）
            if not event.source or "RiskEngine" not in event.source:
                if self.logger:
                    await self.logger.info(
                        f"忽略未经风险检查的交易决策: source={event.source}"
                    )
                return

            # 检查决策是否通过风险审批
            if not event.decision_data.get("risk_approved", False):
                if self.logger:
                    await self.logger.warning(
                        f"交易决策未通过风险审批: {event.decision_data}"
                    )
                return

            # 根据决策类型处理
            if event.action == "adjust":
                await self._handle_strategy_adjustment(event)
            elif event.action in ["buy", "sell"]:
                await self._handle_direct_trade_decision(event)
            elif event.action == "hold":
                await self._handle_hold_decision(event)
            else:
                if self.logger:
                    await self.logger.info(f"未知交易动作: {event.action}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理TradingDecisionEvent失败: {e}")

    async def _handle_strategy_adjustment(self, event: TradingDecisionEvent):
        """处理策略调整决策"""
        try:
            decision_data = event.decision_data
            signal_type = decision_data.get("signal_type", "")

            if self.logger:
                await self.logger.info(f"处理策略调整: signal_type={signal_type}")

            # 根据信号类型调整订单参数（仅处理策略层面的调整）
            if signal_type == "trend_change":
                suggested_mode = decision_data.get("suggested_mode", "")
                if self.logger:
                    await self.logger.info(f"趋势变化，建议模式: {suggested_mode}")

                # 策略调整：根据趋势调整网格策略参数
                await self._adjust_strategy_for_trend(suggested_mode, decision_data)

            elif signal_type == "volatility_spike":
                suggested_action = decision_data.get("suggested_action", "")
                if suggested_action == "reduce_position" and self.logger:
                    await self.logger.info("波动率飙升，建议减少头寸")

                # 策略调整：调整网格参数应对波动率
                await self._adjust_strategy_for_volatility(decision_data)

            elif signal_type == "market_condition_change":
                market_state = decision_data.get("market_state", "normal")
                if self.logger:
                    await self.logger.info(f"市场状态变化: {market_state}")

                # 策略调整：根据市场状态调整策略参数
                await self._adjust_strategy_for_market_condition(
                    market_state, decision_data
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理策略调整失败: {e}")

    async def _handle_direct_trade_decision(self, event: TradingDecisionEvent):
        """处理直接交易决策"""
        try:
            if self.logger:
                await self.logger.info(
                    f"处理直接交易决策: {event.action}, "
                    f"instruments={event.instruments}, confidence={event.confidence:.2f}"
                )

            # 具体的订单创建逻辑
            await self._execute_direct_trade(event)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理直接交易决策失败: {e}")

    async def _handle_hold_decision(self, event: TradingDecisionEvent):
        """处理持有决策"""
        try:
            if self.logger:
                await self.logger.info(f"处理持有决策: {event.decision_data}")

            # 暂时不执行新订单，保持当前状态

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理持有决策失败: {e}")

    async def _check_conditional_orders(self, market_data: dict[str, Any]):
        """检查条件订单"""
        try:
            # 条件订单检查逻辑
            current_time = datetime.now(UTC)
            triggered_orders = []

            # 遍历所有条件订单
            for order_id, order in self.conditional_orders.items():
                try:
                    # 检查订单是否已到期
                    if (
                        hasattr(order, "expires_at")
                        and order.expires_at
                        and current_time > order.expires_at
                    ):
                        triggered_orders.append((order_id, "EXPIRED"))
                        continue

                    # 检查价格条件
                    if hasattr(order, "trigger_price") and order.trigger_price:
                        symbol_price = market_data.get(order.symbol, {}).get(
                            "last_price"
                        )

                        if symbol_price:
                            should_trigger = False

                            # 根据订单方向检查触发条件
                            if order.side == OrderSide.BUY:
                                # 买入订单：价格突破阻力位或跌破支撑位
                                if hasattr(order, "condition_type") and (
                                    order.condition_type == "BREAK_ABOVE"
                                    and symbol_price >= order.trigger_price
                                    or order.condition_type == "BREAK_BELOW"
                                    and symbol_price <= order.trigger_price
                                ):
                                    should_trigger = True
                            else:
                                # 卖出订单：价格触及止损或止盈
                                if hasattr(order, "condition_type") and (
                                    order.condition_type == "STOP_LOSS"
                                    and symbol_price <= order.trigger_price
                                    or order.condition_type == "TAKE_PROFIT"
                                    and symbol_price >= order.trigger_price
                                ):
                                    should_trigger = True

                            if should_trigger:
                                triggered_orders.append((order_id, "PRICE_TRIGGERED"))

                except Exception as order_check_error:
                    if self.logger:
                        await self.logger.warning(
                            f"Error checking conditional order {order_id}: {order_check_error}"
                        )

            # 处理触发的订单
            for order_id, trigger_reason in triggered_orders:
                await self._process_triggered_conditional_order(
                    order_id, trigger_reason
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Conditional order check failed: {e}")

    async def _cancel_all_active_orders(self):
        """取消所有活跃订单"""
        try:
            order_ids = list(self.active_orders.keys())
            for order_id in order_ids:
                await self.cancel_order(order_id)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Cancel all orders failed: {e}")

    # 公共接口方法

    async def get_order_status(self, order_id: str) -> dict[str, Any] | None:
        """获取订单状态"""
        try:
            # 检查活跃订单
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                return self._order_to_dict(order)

            # 检查历史订单
            for order in self.order_history:
                if order.order_id == order_id:
                    return self._order_to_dict(order)

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get order status failed: {e}")
            return None

    def _order_to_dict(self, order: Order) -> dict[str, Any]:
        """订单对象转字典"""
        return {
            "order_id": order.order_id,
            "symbol": order.symbol,
            "side": order.side.value,
            "order_type": order.order_type.value,
            "quantity": float(order.quantity),
            "price": float(order.price) if order.price else None,
            "status": order.status.value,
            "filled_quantity": float(order.filled_quantity),
            "avg_fill_price": float(order.avg_fill_price)
            if order.avg_fill_price
            else None,
            "created_at": order.created_at.isoformat(),
            "submitted_at": order.submitted_at.isoformat()
            if order.submitted_at
            else None,
            "filled_at": order.filled_at.isoformat() if order.filled_at else None,
            "retry_count": order.retry_count,
            "last_error": order.last_error,
        }

    async def get_order_statistics(self) -> dict[str, Any]:
        """获取订单统计"""
        try:
            return {
                "total_orders": self.stats.total_orders,
                "filled_orders": self.stats.filled_orders,
                "cancelled_orders": self.stats.cancelled_orders,
                "rejected_orders": self.stats.rejected_orders,
                "success_rate": round(self.stats.success_rate, 4),
                "avg_fill_time": round(self.stats.avg_fill_time, 2),
                "total_volume": float(self.stats.total_volume),
                "active_orders": len(self.active_orders),
                "queue_size": self.order_queue.qsize(),
                "priority_queue_size": self.priority_queue.qsize(),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get statistics failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_active_orders(self) -> list[dict[str, Any]]:
        """获取活跃订单"""
        try:
            return [self._order_to_dict(order) for order in self.active_orders.values()]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get active orders failed: {e}")
            return []

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    # 新增缺失的辅助方法

    async def _process_triggered_conditional_order(
        self, order_id: str, trigger_reason: str
    ):
        """处理触发的条件订单"""
        try:
            if order_id not in self.conditional_orders:
                return

            conditional_order = self.conditional_orders[order_id]

            if trigger_reason == "EXPIRED":
                # 订单已过期，移除条件订单
                del self.conditional_orders[order_id]

                if self.logger:
                    await self.logger.info(
                        f"Conditional order {order_id} expired and removed"
                    )

            elif trigger_reason == "PRICE_TRIGGERED":
                # 价格触发，转换为市价订单执行
                try:
                    # 创建市价订单
                    market_order = Order(
                        order_id=f"triggered_{order_id}",
                        symbol=conditional_order.symbol,
                        side=conditional_order.side,
                        quantity=conditional_order.quantity,
                        order_type=OrderType.MARKET,
                        price=None,
                        created_at=datetime.now(UTC),
                        status=OrderStatus.PENDING,
                    )

                    # 提交订单
                    result = await self.place_order(market_order)

                    # 移除条件订单
                    del self.conditional_orders[order_id]

                    if self.logger:
                        await self.logger.info(
                            f"Conditional order {order_id} triggered and converted to market order: "
                            f"{result.get('order_id', 'unknown')}"
                        )

                except Exception as order_error:
                    if self.logger:
                        await self.logger.error(
                            f"Failed to execute triggered order {order_id}: {order_error}"
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Process triggered conditional order failed: {e}"
                )

    def _assess_market_conditions(self) -> dict[str, Any]:
        """评估市场条件（为strategy模块提供支持）"""
        try:
            # 基本市场条件评估
            conditions = {
                "suitable_for_entry": True,
                "volatility_level": "normal",
                "liquidity_adequate": True,
                "market_hours": True,
            }

            # 检查活跃订单数量
            active_order_count = len(self.active_orders)
            if active_order_count > 10:  # 订单过多可能影响流动性
                conditions["suitable_for_entry"] = False
                conditions["reason"] = "too_many_active_orders"

            # 检查最近的订单成功率
            recent_orders = (
                self.order_history[-20:]
                if len(self.order_history) >= 20
                else self.order_history
            )
            if recent_orders:
                success_rate = sum(
                    1 for o in recent_orders if o.status == OrderStatus.FILLED
                ) / len(recent_orders)
                if success_rate < 0.7:  # 成功率过低
                    conditions["suitable_for_entry"] = False
                    conditions["reason"] = "low_execution_success_rate"

            return conditions

        except Exception as e:
            return {
                "suitable_for_entry": False,
                "error": str(e),
                "reason": "market_condition_check_failed",
            }

    # 事件式风险复核流程已移除（此前未完备且无响应方）。

    # ============================================================================
    # 策略调整实现方法
    # ============================================================================

    async def _adjust_strategy_for_trend(
        self, suggested_mode: str, decision_data: dict[str, Any]
    ):
        """根据趋势变化调整策略参数（不涉及风险判断）"""
        try:
            trend_strength = decision_data.get("trend_strength", 0.5)

            if self.logger:
                await self.logger.info(
                    f"策略趋势调整: 模式={suggested_mode}, 强度={trend_strength}"
                )

            # 仅调整策略参数，不做风险判断
            if suggested_mode == "accumulation":
                await self._adjust_grid_spacing("buy", 0.8)  # 减小买单间距
                await self._adjust_grid_spacing("sell", 1.2)  # 增大卖单间距
            elif suggested_mode == "distribution":
                await self._adjust_grid_spacing("buy", 1.2)  # 增大买单间距
                await self._adjust_grid_spacing("sell", 0.8)  # 减小卖单间距
            elif suggested_mode == "sideways":
                await self._adjust_grid_spacing("buy", 1.0)
                await self._adjust_grid_spacing("sell", 1.0)

            # 根据趋势强度调整订单大小
            size_multiplier = 1.0 + (trend_strength - 0.5) * 0.2  # 更保守的调整
            await self._adjust_order_sizes(size_multiplier)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"策略趋势调整失败: {e}")

    async def _adjust_strategy_for_volatility(self, decision_data: dict[str, Any]):
        """根据波动率调整策略参数（不做风险判断）"""
        try:
            volatility_level = decision_data.get("volatility_level", 1.0)

            if self.logger:
                await self.logger.info(f"策略波动率调整: 水平={volatility_level}")

            # 仅调整策略参数，避免重复RiskEngine的工作
            if volatility_level > 1.5:
                # 高波动率：增大订单间距
                spread_multiplier = 1.0 + min(volatility_level - 1.0, 1.0) * 0.3
                await self._adjust_grid_spacing("both", spread_multiplier)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"策略波动率调整失败: {e}")

    async def _adjust_strategy_for_market_condition(
        self, market_state: str, decision_data: dict[str, Any]
    ):
        """根据市场状态调整策略参数（不做风险判断）"""
        try:
            if self.logger:
                await self.logger.info(f"策略市场状态调整: 状态={market_state}")

            # 仅调整策略参数
            if market_state == "low_liquidity":
                await self._adjust_grid_spacing("both", 1.2)  # 适度增大间距
            elif market_state == "trending":
                trend_direction = decision_data.get("trend_direction", "neutral")
                if trend_direction == "up":
                    await self._adjust_grid_spacing("sell", 1.1)
                elif trend_direction == "down":
                    await self._adjust_grid_spacing("buy", 1.1)
            elif market_state == "consolidation":
                await self._adjust_grid_spacing("both", 0.95)  # 轻微减小间距

        except Exception as e:
            if self.logger:
                await self.logger.error(f"策略市场状态调整失败: {e}")

    # ============================================================================
    # 辅助调整方法
    # ============================================================================

    async def _adjust_grid_spacing(self, side: str, multiplier: float):
        """调整网格间距"""
        try:
            # 这里需要与网格策略组件交互
            # 暂时记录调整意图
            if self.logger:
                await self.logger.info(f"调整网格间距: {side}方向, 倍数={multiplier}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"网格间距调整失败: {e}")

    async def _adjust_order_sizes(self, multiplier: float):
        """调整订单大小"""
        try:
            if self.logger:
                await self.logger.info(f"调整订单大小: 倍数={multiplier}")

            # 更新后续订单的默认大小
            # 这里需要更新配置或策略参数

        except Exception as e:
            if self.logger:
                await self.logger.error(f"订单大小调整失败: {e}")

    # ============================================================================
    # 风险控制事件处理方法（响应RiskEngine指令）
    # ============================================================================

    async def _handle_emergency_position_reduction(self, event: BaseEvent):
        """处理紧急减仓指令（来自RiskEngine）"""
        try:
            reduction_ratio = event.data.get("reduction_ratio", 0.3)
            reason = event.data.get("reason", "unknown")

            if self.logger:
                await self.logger.warning(
                    f"收到紧急减仓指令: 比例={reduction_ratio}, 原因={reason}"
                )

            # 执行减仓：取消指定比例的订单
            await self._cancel_percentage_of_orders(reduction_ratio)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理紧急减仓指令失败: {e}")

    async def _handle_emergency_delta_hedging(self, event: BaseEvent):
        """处理紧急Delta对冲指令（来自RiskEngine）"""
        try:
            current_delta = event.data.get("current_delta", 0)

            if self.logger:
                await self.logger.warning(
                    f"收到Delta对冲指令: 当前Delta={current_delta}"
                )

            # 暂停新订单，等待对冲完成
            # 这里可以实现具体的对冲逻辑或与策略层协调

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理Delta对冲指令失败: {e}")

    async def _handle_emergency_mode(self, event: BaseEvent):
        """处理进入紧急模式指令（来自RiskEngine）"""
        try:
            trigger_event = event.data.get("trigger_event")
            risk_type = event.data.get("risk_type")

            if self.logger:
                await self.logger.critical(
                    f"系统进入紧急模式: 触发={trigger_event}, 风险类型={risk_type}"
                )

            # 紧急模式：停止所有新订单提交
            # 这里可以设置一个标志来阻止新订单

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理紧急模式失败: {e}")

    async def _handle_expiry_emergency(self, event: BaseEvent):
        """处理到期紧急处理指令（来自RiskEngine）"""
        try:
            expiring_instruments = event.data.get("expiring_instruments", [])

            if self.logger:
                await self.logger.warning(
                    f"收到到期紧急处理指令: 合约={expiring_instruments}"
                )

            # 取消即将到期合约的相关订单
            for instrument in expiring_instruments:
                await self._cancel_orders_for_instrument(instrument)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"处理到期紧急指令失败: {e}")

    # ============================================================================
    # 执行层辅助方法
    # ============================================================================

    async def _cancel_percentage_of_orders(self, percentage: float):
        """取消指定百分比的订单（执行层方法）"""
        try:
            active_order_ids = list(self.active_orders.keys())
            num_to_cancel = int(len(active_order_ids) * percentage)

            # 优先取消风险较高或收益较低的订单
            orders_to_cancel = active_order_ids[:num_to_cancel]

            for order_id in orders_to_cancel:
                await self.cancel_order(order_id)

            if self.logger:
                await self.logger.info(
                    f"已取消 {len(orders_to_cancel)} 个订单 ({percentage * 100:.1f}%)"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"批量取消订单失败: {e}")

    async def _cancel_orders_for_instrument(self, instrument: str):
        """取消特定合约的所有订单"""
        try:
            orders_to_cancel = [
                order_id
                for order_id, order in self.active_orders.items()
                if order.symbol == instrument
            ]

            for order_id in orders_to_cancel:
                await self.cancel_order(order_id)

            if self.logger:
                await self.logger.info(
                    f"已取消合约 {instrument} 的 {len(orders_to_cancel)} 个订单"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"取消合约 {instrument} 订单失败: {e}")

    async def _execute_direct_trade(self, event: TradingDecisionEvent):
        """执行直接交易决策"""
        try:
            # 使用本模块定义的 Order 数据类，以及顶部已导入的枚举类型
            for instrument in event.instruments:
                # 根据交易动作确定订单方向
                side = OrderSide.BUY if event.action == "buy" else OrderSide.SELL

                # 从事件中获取订单参数
                raw_qty = event.decision_data.get("quantity", 0.1)
                try:
                    qty_dec = Decimal(str(raw_qty))
                except Exception:
                    qty_dec = Decimal("0.1")
                price = event.decision_data.get("price")
                order_type = OrderType.MARKET if price is None else OrderType.LIMIT

                # 通过标准接口提交订单
                order_id = await self.submit_order(
                    symbol=instrument,
                    side=side,
                    order_type=order_type,
                    quantity=qty_dec,
                    price=Decimal(str(price)) if price is not None else None,
                )

                # 将策略提供的 Deribit 流动性代理透传到订单，供后续 pretrade 使用
                try:
                    proxy = (
                        event.decision_data.get("liquidity_proxy")
                        if event.decision_data
                        else None
                    )
                    if order_id and proxy and order_id in self.active_orders:
                        self.active_orders[order_id].tags["liquidity_proxy"] = proxy
                except Exception:
                    pass

                if self.logger:
                    await self.logger.info(
                        f"直接交易订单提交完成: {instrument} {side.value} {qty_dec} @ {price or 'MARKET'}, order_id={order_id}"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"执行直接交易失败: {e}")
