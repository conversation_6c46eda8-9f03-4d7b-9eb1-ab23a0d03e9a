"""
盈亏跟踪器

负责实时PnL计算、归因分析、策略级统计和历史分析
"""

import asyncio
import contextlib
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Any

from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.event_bus import BaseEvent, EventBus, EventType
from src.data.cache_manager import CacheManager


class PnLType(Enum):
    """盈亏类型"""

    REALIZED = "realized"
    UNREALIZED = "unrealized"
    TOTAL = "total"


class AttributionType(Enum):
    """归因类型"""

    DELTA = "delta"
    GAMMA = "gamma"
    THETA = "theta"
    VEGA = "vega"
    RHO = "rho"
    CARRY = "carry"
    RESIDUAL = "residual"


@dataclass
class PnLRecord:
    """盈亏记录"""

    timestamp: datetime
    symbol: str
    pnl_type: PnLType
    amount: Decimal
    attribution: dict[AttributionType, Decimal] = field(default_factory=dict)

    # 关联信息
    account_id: str = "default"
    strategy_id: str | None = None
    trade_id: str | None = None

    # 计算详情
    position_size: Decimal = Decimal("0")
    price_change: Decimal = Decimal("0")
    time_decay: Decimal = Decimal("0")

    # 元数据
    tags: dict[str, Any] = field(default_factory=dict)


@dataclass
class PnLSummary:
    """盈亏汇总"""

    period_start: datetime
    period_end: datetime
    total_realized: Decimal = Decimal("0")
    total_unrealized: Decimal = Decimal("0")
    total_pnl: Decimal = Decimal("0")

    # 归因分析
    delta_pnl: Decimal = Decimal("0")
    gamma_pnl: Decimal = Decimal("0")
    theta_pnl: Decimal = Decimal("0")
    vega_pnl: Decimal = Decimal("0")
    rho_pnl: Decimal = Decimal("0")
    carry_pnl: Decimal = Decimal("0")
    residual_pnl: Decimal = Decimal("0")

    # 统计指标
    win_rate: float = 0.0
    profit_factor: float = 0.0
    max_drawdown: Decimal = Decimal("0")
    sharpe_ratio: float = 0.0

    # 交易统计
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    avg_win: Decimal = Decimal("0")
    avg_loss: Decimal = Decimal("0")


@dataclass
class PnLStats:
    """盈亏统计"""

    total_records: int = 0
    daily_pnl: Decimal = Decimal("0")
    weekly_pnl: Decimal = Decimal("0")
    monthly_pnl: Decimal = Decimal("0")
    ytd_pnl: Decimal = Decimal("0")

    # 风险指标
    var_95: Decimal = Decimal("0")  # 95% VaR
    var_99: Decimal = Decimal("0")  # 99% VaR
    expected_shortfall: Decimal = Decimal("0")

    # 最大回撤
    max_drawdown: Decimal = Decimal("0")
    max_drawdown_duration: int = 0  # 天数

    # 波动率
    daily_volatility: float = 0.0
    annualized_volatility: float = 0.0


class PnLTracker(BaseComponent):
    """
    盈亏跟踪器

    功能特性：
    - 实时PnL计算和更新
    - 多维度归因分析（Greeks、时间衰减等）
    - 策略级和组合级PnL统计
    - 风险指标计算（VaR、最大回撤等）
    - 历史PnL分析和报告
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("PnLTracker", config)

        # 组件依赖
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None

        # PnL配置
        self._load_pnl_config()

        # PnL存储
        self.pnl_records: list[PnLRecord] = []
        self.daily_summaries: dict[str, PnLSummary] = {}  # date -> summary
        self.strategy_pnl: dict[str, list[PnLRecord]] = {}  # strategy_id -> records
        self.account_pnl: dict[str, list[PnLRecord]] = {}  # account_id -> records

        # 实时PnL
        self.current_unrealized_pnl: dict[str, Decimal] = {}  # symbol -> pnl
        self.current_realized_pnl: Decimal = Decimal("0")

        # 计算任务
        self._calculation_task: asyncio.Task | None = None
        self._reporting_task: asyncio.Task | None = None
        self._calculation_interval = 5  # 5秒计算间隔

        # 历史数据
        self._max_records = 100000
        self.price_history: dict[str, list[tuple[datetime, Decimal]]] = {}

        # 统计信息
        self.stats = PnLStats()

        # Greeks缓存
        self.greeks_cache: dict[str, dict[str, Decimal]] = {}

    def _load_pnl_config(self):
        """加载PnL配置"""
        pnl_config = self.config.get("pnl", {}) if self.config else {}

        # 基础配置
        self.pnl_settings = {
            "enable_real_time_calculation": pnl_config.get(
                "enable_real_time_calculation", True
            ),
            "calculation_interval": pnl_config.get("calculation_interval", 5),
            "enable_attribution_analysis": pnl_config.get(
                "enable_attribution_analysis", True
            ),
            "enable_risk_metrics": pnl_config.get("enable_risk_metrics", True),
            "var_confidence_levels": pnl_config.get(
                "var_confidence_levels", [0.95, 0.99]
            ),
            "lookback_days": pnl_config.get("lookback_days", 252),  # 1年
            "enable_daily_reporting": pnl_config.get("enable_daily_reporting", True),
        }

        # 更新配置
        self._calculation_interval = self.pnl_settings["calculation_interval"]

    async def _initialize_impl(self) -> bool:
        """初始化PnL跟踪器"""
        try:
            # 订阅事件
            if self.event_bus:
                from src.core.event_bus import EventType

                # 统一使用 ORDER_FILLED 事件
                # 对齐至 OrderManager 的统一事件命名：order_filled
                await self.event_bus.subscribe(
                    EventType.ORDER_FILLED, self._handle_order_filled
                )
                from src.core.event_bus import EventType as _ET

                await self.event_bus.subscribe(
                    _ET.POSITION_UPDATED, self._handle_position_update
                )
                await self.event_bus.subscribe(
                    event_types={EventType.DATA_UPDATE},
                    callback=self._handle_market_update,
                    subscriber_id=f"{self.component_name}_market_data_handler",
                )

            # 初始化账户PnL
            self.account_pnl["default"] = []

            # 加载历史数据
            await self._load_historical_data()

            if self.logger:
                await self.logger.info("PnLTracker initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PnLTracker initialization failed: {e}")
            return False

    async def _handle_order_filled(self, event: BaseEvent):
        """对齐 OM 的 order_filled 事件，复用 trade_executed 处理逻辑"""
        try:
            await self._handle_trade_execution(event)
        except Exception as e:
            if self.logger:
                await self.logger.error(f"PnLTracker order_filled handling failed: {e}")

    async def _start_impl(self) -> bool:
        """启动PnL跟踪器"""
        try:
            # 启动计算任务
            if self.pnl_settings["enable_real_time_calculation"]:
                self._calculation_task = asyncio.create_task(
                    self._pnl_calculation_loop()
                )

            if self.pnl_settings["enable_daily_reporting"]:
                self._reporting_task = asyncio.create_task(self._daily_reporting_loop())

            if self.logger:
                await self.logger.info("PnLTracker started")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PnLTracker start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止PnL跟踪器"""
        try:
            # 停止计算任务
            if self._calculation_task and not self._calculation_task.done():
                self._calculation_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._calculation_task

            if self._reporting_task and not self._reporting_task.done():
                self._reporting_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._reporting_task

            # 保存最终数据
            await self._save_pnl_data()

            if self.logger:
                await self.logger.info("PnLTracker stopped")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PnLTracker stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查"""
        try:
            # 检查计算任务状态
            if self.pnl_settings["enable_real_time_calculation"] and (
                not self._calculation_task or self._calculation_task.done()
            ):
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="PnL calculation task not running",
                )

            # 检查记录数量
            record_count = len(self.pnl_records)
            if record_count > self._max_records * 0.9:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"High number of PnL records: {record_count}",
                )

            # 检查数据新鲜度
            if self.pnl_records:
                latest_record = max(self.pnl_records, key=lambda r: r.timestamp)
                age_minutes = (
                    datetime.now(UTC) - latest_record.timestamp
                ).total_seconds() / 60

                if age_minutes > 60:  # 1小时无更新
                    return HealthCheckResult(
                        status=HealthStatus.DEGRADED,
                        message=f"PnL data stale: {age_minutes:.1f} minutes",
                    )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message=f"PnL tracking operational, {record_count} records",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _load_historical_data(self):
        """加载历史数据"""
        try:
            # 通过统一服务读取历史数据
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            cached_records = await mds.get_cached("pnl_records")
            if cached_records:
                for record_data in cached_records:
                    record = self._dict_to_pnl_record(record_data)
                    self.pnl_records.append(record)

            cached_summaries = await mds.get_cached("daily_summaries")
            if cached_summaries:
                for date_str, summary_data in cached_summaries.items():
                    summary = self._dict_to_pnl_summary(summary_data)
                    self.daily_summaries[date_str] = summary

            # 重建索引
            await self._rebuild_indices()

            if self.logger:
                await self.logger.info(
                    f"Loaded {len(self.pnl_records)} historical PnL records"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Historical data loading failed: {e}")

    async def _rebuild_indices(self):
        """重建索引"""
        try:
            # 清空索引
            self.strategy_pnl.clear()
            self.account_pnl.clear()

            # 重建策略和账户索引
            for record in self.pnl_records:
                # 策略索引
                if record.strategy_id:
                    if record.strategy_id not in self.strategy_pnl:
                        self.strategy_pnl[record.strategy_id] = []
                    self.strategy_pnl[record.strategy_id].append(record)

                # 账户索引
                if record.account_id not in self.account_pnl:
                    self.account_pnl[record.account_id] = []
                self.account_pnl[record.account_id].append(record)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Index rebuilding failed: {e}")

    async def _pnl_calculation_loop(self):
        """PnL计算循环"""
        while True:
            try:
                await self._calculate_real_time_pnl()
                await self._update_statistics()
                await asyncio.sleep(self._calculation_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"PnL calculation error: {e}")
                await asyncio.sleep(30)

    async def _daily_reporting_loop(self):
        """日报循环"""
        while True:
            try:
                # 每天UTC 0点生成日报
                now = datetime.now(UTC)
                next_midnight = (now + timedelta(days=1)).replace(
                    hour=0, minute=0, second=0, microsecond=0
                )
                sleep_seconds = (next_midnight - now).total_seconds()

                await asyncio.sleep(sleep_seconds)
                await self._generate_daily_report()

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Daily reporting error: {e}")
                await asyncio.sleep(3600)  # 1小时后重试

    async def _calculate_real_time_pnl(self):
        """计算实时PnL"""
        try:
            # 计算未实现PnL
            total_unrealized = Decimal("0")

            for _symbol, current_pnl in self.current_unrealized_pnl.items():
                total_unrealized += current_pnl

            # 更新统计
            self.stats.daily_pnl = self.current_realized_pnl + total_unrealized

            # 发布PnL更新事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.PNL_UPDATE,
                        data={
                            "realized_pnl": float(self.current_realized_pnl),
                            "unrealized_pnl": float(total_unrealized),
                            "total_pnl": float(self.stats.daily_pnl),
                            "timestamp": datetime.now(UTC).isoformat(),
                        },
                    )
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Real-time PnL calculation failed: {e}")

    async def _update_statistics(self):
        """更新统计信息"""
        try:
            if not self.pnl_records:
                return

            # 计算各期间PnL
            now = datetime.now(UTC)

            # 日PnL
            today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
            daily_records = [r for r in self.pnl_records if r.timestamp >= today_start]
            self.stats.daily_pnl = sum(r.amount for r in daily_records)

            # 周PnL
            week_start = now - timedelta(days=7)
            weekly_records = [r for r in self.pnl_records if r.timestamp >= week_start]
            self.stats.weekly_pnl = sum(r.amount for r in weekly_records)

            # 月PnL
            month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            monthly_records = [
                r for r in self.pnl_records if r.timestamp >= month_start
            ]
            self.stats.monthly_pnl = sum(r.amount for r in monthly_records)

            # 年初至今PnL
            ytd_start = now.replace(
                month=1, day=1, hour=0, minute=0, second=0, microsecond=0
            )
            ytd_records = [r for r in self.pnl_records if r.timestamp >= ytd_start]
            self.stats.ytd_pnl = sum(r.amount for r in ytd_records)

            # 计算风险指标
            if self.pnl_settings["enable_risk_metrics"]:
                await self._calculate_risk_metrics()

            self.stats.total_records = len(self.pnl_records)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Statistics update failed: {e}")

    async def _calculate_risk_metrics(self):
        """计算风险指标"""
        try:
            if len(self.pnl_records) < 30:  # 需要至少30个数据点
                return

            # 获取日PnL序列
            daily_pnls = self._get_daily_pnl_series()

            if len(daily_pnls) < 10:
                return

            # 计算VaR
            daily_pnls_sorted = sorted(daily_pnls)

            # 95% VaR
            var_95_index = int(len(daily_pnls_sorted) * 0.05)
            self.stats.var_95 = abs(daily_pnls_sorted[var_95_index])

            # 99% VaR
            var_99_index = int(len(daily_pnls_sorted) * 0.01)
            self.stats.var_99 = abs(daily_pnls_sorted[var_99_index])

            # Expected Shortfall (CVaR)
            tail_losses = daily_pnls_sorted[:var_95_index]
            if tail_losses:
                self.stats.expected_shortfall = abs(sum(tail_losses) / len(tail_losses))

            # 计算最大回撤
            await self._calculate_max_drawdown(daily_pnls)

            # 计算波动率
            if len(daily_pnls) > 1:
                mean_pnl = sum(daily_pnls) / len(daily_pnls)
                variance = sum((pnl - mean_pnl) ** 2 for pnl in daily_pnls) / (
                    len(daily_pnls) - 1
                )
                self.stats.daily_volatility = float(variance**0.5)
                self.stats.annualized_volatility = self.stats.daily_volatility * (
                    252**0.5
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk metrics calculation failed: {e}")

    def _get_daily_pnl_series(self) -> list[Decimal]:
        """获取日PnL序列"""
        try:
            daily_pnls = {}

            for record in self.pnl_records:
                date_key = record.timestamp.date()
                if date_key not in daily_pnls:
                    daily_pnls[date_key] = Decimal("0")
                daily_pnls[date_key] += record.amount

            return list(daily_pnls.values())

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Daily PnL series calculation failed: {e}")
                )
            return []

    async def _calculate_max_drawdown(self, daily_pnls: list[Decimal]):
        """计算最大回撤"""
        try:
            if not daily_pnls:
                return

            # 计算累计PnL
            cumulative_pnl = []
            running_total = Decimal("0")

            for pnl in daily_pnls:
                running_total += pnl
                cumulative_pnl.append(running_total)

            # 计算回撤
            max_drawdown = Decimal("0")
            max_drawdown_duration = 0
            current_drawdown_duration = 0
            peak = cumulative_pnl[0]

            for _i, value in enumerate(cumulative_pnl):
                if value > peak:
                    peak = value
                    current_drawdown_duration = 0
                else:
                    drawdown = peak - value
                    if drawdown > max_drawdown:
                        max_drawdown = drawdown
                    current_drawdown_duration += 1
                    if current_drawdown_duration > max_drawdown_duration:
                        max_drawdown_duration = current_drawdown_duration

            self.stats.max_drawdown = max_drawdown
            self.stats.max_drawdown_duration = max_drawdown_duration

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Max drawdown calculation failed: {e}")

    async def _generate_daily_report(self):
        """生成日报"""
        try:
            yesterday = datetime.now(UTC).date() - timedelta(days=1)
            date_key = yesterday.isoformat()

            if date_key in self.daily_summaries:
                return  # 已生成

            # 获取昨日记录
            yesterday_start = datetime.combine(yesterday, datetime.min.time()).replace(
                tzinfo=UTC
            )
            yesterday_end = yesterday_start + timedelta(days=1)

            daily_records = [
                r
                for r in self.pnl_records
                if yesterday_start <= r.timestamp < yesterday_end
            ]

            if not daily_records:
                return

            # 生成汇总
            summary = await self._create_pnl_summary(
                yesterday_start, yesterday_end, daily_records
            )
            self.daily_summaries[date_key] = summary

            # 发布日报事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.DAILY_PNL_REPORT,
                        data={
                            "date": date_key,
                            "total_pnl": float(summary.total_pnl),
                            "realized_pnl": float(summary.total_realized),
                            "unrealized_pnl": float(summary.total_unrealized),
                            "win_rate": summary.win_rate,
                            "total_trades": summary.total_trades,
                        },
                    )
                )

            if self.logger:
                await self.logger.info(f"Daily report generated for {date_key}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Daily report generation failed: {e}")

    async def _create_pnl_summary(
        self, start_time: datetime, end_time: datetime, records: list[PnLRecord]
    ) -> PnLSummary:
        """创建PnL汇总"""
        try:
            summary = PnLSummary(period_start=start_time, period_end=end_time)

            # 基础统计
            realized_records = [r for r in records if r.pnl_type == PnLType.REALIZED]
            unrealized_records = [
                r for r in records if r.pnl_type == PnLType.UNREALIZED
            ]

            summary.total_realized = sum(r.amount for r in realized_records)
            summary.total_unrealized = sum(r.amount for r in unrealized_records)
            summary.total_pnl = summary.total_realized + summary.total_unrealized

            # 归因分析
            if self.pnl_settings["enable_attribution_analysis"]:
                for record in records:
                    for attr_type, amount in record.attribution.items():
                        if attr_type == AttributionType.DELTA:
                            summary.delta_pnl += amount
                        elif attr_type == AttributionType.GAMMA:
                            summary.gamma_pnl += amount
                        elif attr_type == AttributionType.THETA:
                            summary.theta_pnl += amount
                        elif attr_type == AttributionType.VEGA:
                            summary.vega_pnl += amount
                        elif attr_type == AttributionType.RHO:
                            summary.rho_pnl += amount
                        elif attr_type == AttributionType.CARRY:
                            summary.carry_pnl += amount
                        elif attr_type == AttributionType.RESIDUAL:
                            summary.residual_pnl += amount

            # 交易统计
            trade_records = [r for r in realized_records if r.trade_id]
            summary.total_trades = len(trade_records)

            winning_trades = [r for r in trade_records if r.amount > 0]
            losing_trades = [r for r in trade_records if r.amount < 0]

            summary.winning_trades = len(winning_trades)
            summary.losing_trades = len(losing_trades)

            if summary.total_trades > 0:
                summary.win_rate = summary.winning_trades / summary.total_trades

            if winning_trades:
                summary.avg_win = sum(r.amount for r in winning_trades) / len(
                    winning_trades
                )

            if losing_trades:
                summary.avg_loss = sum(r.amount for r in losing_trades) / len(
                    losing_trades
                )

            # 盈亏比
            if summary.avg_loss != 0:
                summary.profit_factor = float(abs(summary.avg_win / summary.avg_loss))

            return summary

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PnL summary creation failed: {e}")
            return PnLSummary(period_start=start_time, period_end=end_time)

    # 事件处理方法

    async def _handle_trade_execution(self, event: BaseEvent):
        """处理交易执行事件"""
        try:
            data = event.data
            symbol = data.get("symbol")
            side = data.get("side")
            quantity = Decimal(str(data.get("quantity", 0)))
            price = Decimal(str(data.get("price", 0)))
            avg_cost = Decimal(str(data.get("avg_cost", 0)))

            # 计算已实现PnL
            if side == "sell" and avg_cost > 0:
                realized_pnl = quantity * (price - avg_cost)

                # 创建PnL记录
                record = PnLRecord(
                    timestamp=datetime.now(UTC),
                    symbol=symbol,
                    pnl_type=PnLType.REALIZED,
                    amount=realized_pnl,
                    account_id=data.get("account_id", "default"),
                    strategy_id=data.get("strategy_id"),
                    trade_id=data.get("trade_id"),
                    position_size=quantity,
                    price_change=price - avg_cost,
                )

                await self._add_pnl_record(record)
                self.current_realized_pnl += realized_pnl

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Trade execution handling failed: {e}")

    async def _handle_position_update(self, event: BaseEvent):
        """处理仓位更新事件"""
        try:
            data = event.data
            symbol = data.get("symbol")
            unrealized_pnl = Decimal(str(data.get("unrealized_pnl", 0)))

            # 更新未实现PnL
            old_pnl = self.current_unrealized_pnl.get(symbol, Decimal("0"))
            pnl_change = unrealized_pnl - old_pnl

            if abs(pnl_change) > Decimal("0.01"):  # 避免微小变化
                self.current_unrealized_pnl[symbol] = unrealized_pnl

                # 创建PnL记录
                record = PnLRecord(
                    timestamp=datetime.now(UTC),
                    symbol=symbol,
                    pnl_type=PnLType.UNREALIZED,
                    amount=pnl_change,
                    account_id=data.get("account_id", "default"),
                    strategy_id=data.get("strategy_id"),
                )

                await self._add_pnl_record(record)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Position update handling failed: {e}")

    async def _handle_market_update(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")
            price = Decimal(str(data.get("last_price", 0)))

            # 更新价格历史
            if symbol not in self.price_history:
                self.price_history[symbol] = []

            self.price_history[symbol].append((datetime.now(UTC), price))

            # 限制历史长度
            if len(self.price_history[symbol]) > 1000:
                self.price_history[symbol] = self.price_history[symbol][-1000:]

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Market update handling failed: {e}")

    async def _handle_greeks_update(self, event: BaseEvent):
        """处理Greeks更新事件"""
        try:
            data = event.data
            symbol = data.get("symbol")

            # 更新Greeks缓存
            self.greeks_cache[symbol] = {
                "delta": Decimal(str(data.get("delta", 0))),
                "gamma": Decimal(str(data.get("gamma", 0))),
                "theta": Decimal(str(data.get("theta", 0))),
                "vega": Decimal(str(data.get("vega", 0))),
                "rho": Decimal(str(data.get("rho", 0))),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Greeks update handling failed: {e}")

    async def _add_pnl_record(self, record: PnLRecord):
        """添加PnL记录"""
        try:
            self.pnl_records.append(record)

            # 添加到策略索引
            if record.strategy_id:
                if record.strategy_id not in self.strategy_pnl:
                    self.strategy_pnl[record.strategy_id] = []
                self.strategy_pnl[record.strategy_id].append(record)

            # 添加到账户索引
            if record.account_id not in self.account_pnl:
                self.account_pnl[record.account_id] = []
            self.account_pnl[record.account_id].append(record)

            # 限制记录数量
            if len(self.pnl_records) > self._max_records:
                self.pnl_records = self.pnl_records[-self._max_records :]
                await self._rebuild_indices()

            # 发布PnL记录事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.PNL_RECORD_ADDED,
                        data={
                            "symbol": record.symbol,
                            "pnl_type": record.pnl_type.value,
                            "amount": float(record.amount),
                            "timestamp": record.timestamp.isoformat(),
                        },
                    )
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Add PnL record failed: {e}")

    async def _save_pnl_data(self):
        """保存PnL数据"""
        try:
            if self.cache_manager:
                # 保存记录
                records_data = [
                    self._pnl_record_to_dict(r) for r in self.pnl_records[-1000:]
                ]
                await self.cache_manager.set("pnl_records", records_data, ttl=86400)

                # 保存日汇总
                summaries_data = {
                    k: self._pnl_summary_to_dict(v)
                    for k, v in self.daily_summaries.items()
                }
                await self.cache_manager.set(
                    "daily_summaries", summaries_data, ttl=86400
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"PnL data saving failed: {e}")

    # 公共接口方法

    async def get_current_pnl(self) -> dict[str, Any]:
        """获取当前PnL"""
        try:
            total_unrealized = sum(self.current_unrealized_pnl.values())

            return {
                "realized_pnl": float(self.current_realized_pnl),
                "unrealized_pnl": float(total_unrealized),
                "total_pnl": float(self.current_realized_pnl + total_unrealized),
                "daily_pnl": float(self.stats.daily_pnl),
                "weekly_pnl": float(self.stats.weekly_pnl),
                "monthly_pnl": float(self.stats.monthly_pnl),
                "ytd_pnl": float(self.stats.ytd_pnl),
                "timestamp": datetime.now(UTC).isoformat(),
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get current PnL failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_pnl_by_strategy(self, strategy_id: str) -> dict[str, Any]:
        """按策略获取PnL"""
        try:
            if strategy_id not in self.strategy_pnl:
                return {"total_pnl": 0.0, "records": []}

            records = self.strategy_pnl[strategy_id]
            total_pnl = sum(r.amount for r in records)

            return {
                "strategy_id": strategy_id,
                "total_pnl": float(total_pnl),
                "record_count": len(records),
                "records": [
                    self._pnl_record_to_dict(r) for r in records[-100:]
                ],  # 最近100条
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get strategy PnL failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_pnl_statistics(self) -> dict[str, Any]:
        """获取PnL统计"""
        try:
            return {
                "total_records": self.stats.total_records,
                "daily_pnl": float(self.stats.daily_pnl),
                "weekly_pnl": float(self.stats.weekly_pnl),
                "monthly_pnl": float(self.stats.monthly_pnl),
                "ytd_pnl": float(self.stats.ytd_pnl),
                "var_95": float(self.stats.var_95),
                "var_99": float(self.stats.var_99),
                "expected_shortfall": float(self.stats.expected_shortfall),
                "max_drawdown": float(self.stats.max_drawdown),
                "max_drawdown_duration": self.stats.max_drawdown_duration,
                "daily_volatility": self.stats.daily_volatility,
                "annualized_volatility": self.stats.annualized_volatility,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get PnL statistics failed: {e}")
            return {"status": "error", "message": str(e)}

    async def get_daily_summary(self, date: str) -> dict[str, Any] | None:
        """获取日汇总"""
        try:
            if date in self.daily_summaries:
                return self._pnl_summary_to_dict(self.daily_summaries[date])
            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Get daily summary failed: {e}")
            return None

    async def get_attribution_analysis(
        self, start_date: str | None = None, end_date: str | None = None
    ) -> dict[str, Any]:
        """获取归因分析"""
        try:
            # 过滤记录
            records = self.pnl_records

            if start_date:
                start_dt = datetime.fromisoformat(start_date).replace(tzinfo=UTC)
                records = [r for r in records if r.timestamp >= start_dt]

            if end_date:
                end_dt = datetime.fromisoformat(end_date).replace(tzinfo=UTC)
                records = [r for r in records if r.timestamp <= end_dt]

            # 计算归因
            attribution = {
                "delta": Decimal("0"),
                "gamma": Decimal("0"),
                "theta": Decimal("0"),
                "vega": Decimal("0"),
                "rho": Decimal("0"),
                "carry": Decimal("0"),
                "residual": Decimal("0"),
            }

            for record in records:
                for attr_type, amount in record.attribution.items():
                    if attr_type.value in attribution:
                        attribution[attr_type.value] += amount

            return {attr: float(amount) for attr, amount in attribution.items()}

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Attribution analysis failed: {e}")
            return {"status": "error", "message": str(e)}

    def _pnl_record_to_dict(self, record: PnLRecord) -> dict[str, Any]:
        """PnL记录转字典"""
        return {
            "timestamp": record.timestamp.isoformat(),
            "symbol": record.symbol,
            "pnl_type": record.pnl_type.value,
            "amount": float(record.amount),
            "attribution": {k.value: float(v) for k, v in record.attribution.items()},
            "account_id": record.account_id,
            "strategy_id": record.strategy_id,
            "trade_id": record.trade_id,
            "position_size": float(record.position_size),
            "price_change": float(record.price_change),
            "time_decay": float(record.time_decay),
            "tags": record.tags,
        }

    def _dict_to_pnl_record(self, data: dict[str, Any]) -> PnLRecord:
        """字典转PnL记录"""
        attribution = {}
        for k, v in data.get("attribution", {}).items():
            attribution[AttributionType(k)] = Decimal(str(v))

        return PnLRecord(
            timestamp=datetime.fromisoformat(data["timestamp"]),
            symbol=data["symbol"],
            pnl_type=PnLType(data["pnl_type"]),
            amount=Decimal(str(data["amount"])),
            attribution=attribution,
            account_id=data.get("account_id", "default"),
            strategy_id=data.get("strategy_id"),
            trade_id=data.get("trade_id"),
            position_size=Decimal(str(data.get("position_size", 0))),
            price_change=Decimal(str(data.get("price_change", 0))),
            time_decay=Decimal(str(data.get("time_decay", 0))),
            tags=data.get("tags", {}),
        )

    def _pnl_summary_to_dict(self, summary: PnLSummary) -> dict[str, Any]:
        """PnL汇总转字典"""
        return {
            "period_start": summary.period_start.isoformat(),
            "period_end": summary.period_end.isoformat(),
            "total_realized": float(summary.total_realized),
            "total_unrealized": float(summary.total_unrealized),
            "total_pnl": float(summary.total_pnl),
            "delta_pnl": float(summary.delta_pnl),
            "gamma_pnl": float(summary.gamma_pnl),
            "theta_pnl": float(summary.theta_pnl),
            "vega_pnl": float(summary.vega_pnl),
            "rho_pnl": float(summary.rho_pnl),
            "carry_pnl": float(summary.carry_pnl),
            "residual_pnl": float(summary.residual_pnl),
            "win_rate": summary.win_rate,
            "profit_factor": summary.profit_factor,
            "max_drawdown": float(summary.max_drawdown),
            "sharpe_ratio": summary.sharpe_ratio,
            "total_trades": summary.total_trades,
            "winning_trades": summary.winning_trades,
            "losing_trades": summary.losing_trades,
            "avg_win": float(summary.avg_win),
            "avg_loss": float(summary.avg_loss),
        }

    def _dict_to_pnl_summary(self, data: dict[str, Any]) -> PnLSummary:
        """字典转PnL汇总"""
        return PnLSummary(
            period_start=datetime.fromisoformat(data["period_start"]),
            period_end=datetime.fromisoformat(data["period_end"]),
            total_realized=Decimal(str(data.get("total_realized", 0))),
            total_unrealized=Decimal(str(data.get("total_unrealized", 0))),
            total_pnl=Decimal(str(data.get("total_pnl", 0))),
            delta_pnl=Decimal(str(data.get("delta_pnl", 0))),
            gamma_pnl=Decimal(str(data.get("gamma_pnl", 0))),
            theta_pnl=Decimal(str(data.get("theta_pnl", 0))),
            vega_pnl=Decimal(str(data.get("vega_pnl", 0))),
            rho_pnl=Decimal(str(data.get("rho_pnl", 0))),
            carry_pnl=Decimal(str(data.get("carry_pnl", 0))),
            residual_pnl=Decimal(str(data.get("residual_pnl", 0))),
            win_rate=data.get("win_rate", 0.0),
            profit_factor=data.get("profit_factor", 0.0),
            max_drawdown=Decimal(str(data.get("max_drawdown", 0))),
            sharpe_ratio=data.get("sharpe_ratio", 0.0),
            total_trades=data.get("total_trades", 0),
            winning_trades=data.get("winning_trades", 0),
            losing_trades=data.get("losing_trades", 0),
            avg_win=Decimal(str(data.get("avg_win", 0))),
            avg_loss=Decimal(str(data.get("avg_loss", 0))),
        )

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager
