"""Deribit API客户端模块。"""

import asyncio
import contextlib
import logging
import ssl
import time
import traceback
from collections.abc import Callable, Coroutine
from dataclasses import dataclass
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any

import aiohttp
from aiohttp import ClientWSTimeout

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.celery_manager import CeleryTaskManager
from src.core.config_manager import ConfigManager
from src.core.event_bus import DataUpdateEvent, EventBus
from src.data.cache_manager import CacheManager
from src.utils.data_converter import safe_float_conversion

logger = logging.getLogger(__name__)


@dataclass
class MarketData:
    """期权市场数据类"""

    currency: str
    instrument_name: str
    option_type: str
    strike: float
    expiry: str
    mark_price: float
    open_interest: float
    gamma: float
    volume_24h: Decimal
    bid_price: float
    ask_price: float
    mark_iv: float
    timestamp: datetime


@dataclass
class PositionData:
    """持仓数据模型"""

    currency: str
    instrument_name: str
    size: float
    average_price: float
    mark_price: float
    total_profit_loss: float
    gamma: float
    timestamp: datetime
    liquidity_data: dict[str, Any]


class DeribitClient(BaseComponent):
    """Deribit API客户端"""

    # 类型注解 - 供依赖注入器使用
    config_manager: ConfigManager | None
    event_bus: EventBus | None
    cache_manager: CacheManager | None

    def __init__(
        self,
        api_key: str | None = None,
        api_secret: str | None = None,
        celery_manager: CeleryTaskManager | None = None,
        cache_manager: CacheManager | None = None,
        event_bus: EventBus | None = None,
        config_manager: ConfigManager | None = None,
    ):
        super().__init__("deribit_client")
        self._logger = logger

        # API认证 - 从环境变量获取
        import os

        if not api_key:
            api_key = os.getenv("DERIBIT_API_KEY")
        if not api_secret:
            api_secret = os.getenv("DERIBIT_API_SECRET")

        # 支持公共模式：当未提供API Key/Secret时，启用public模式（仅订阅public频道）
        self._public_mode = False
        if not api_key or not api_secret:
            self._public_mode = True
            self._api_key = None
            self._api_secret = None
            if self._logger:
                self._logger.warning(
                    "Deribit API Key/Secret 未提供，启用公共模式（仅订阅public频道，无需认证）"
                )
        else:
            self._api_key = api_key
            self._api_secret = api_secret

        # 基础设施组件
        self.celery_manager = celery_manager
        self.event_bus = event_bus
        self.config_manager = config_manager

        # 添加支持的币种列表 - BTC期权网格交易专用
        self.currencies = ["BTC"]

        # 基础设施
        self._session = None
        self._ws = None
        self._ws_lock = asyncio.Lock()
        self._session_lock = asyncio.Lock()  # 新增session锁
        self._subscription_lock = asyncio.Lock()
        self._message_lock = asyncio.Semaphore(1000000)
        self._callback_lock = asyncio.Lock()
        self._request_id = 0

        # 添加WebSocket异步处理相关属性
        self._ws_response_futures = {}  # request_id -> Future
        self._ws_processor_task = None

        # HTTP认证相关
        self._http_token = None
        self._http_token_expiry = None
        self._http_token_timestamp = None
        self._http_token_refresh_task = None

        # 订阅状态
        self._monitored_positions = set()
        self._subscribed_instruments = set()

        # 回调函数
        self._market_data_callbacks = set()
        self._position_callbacks = set()

        # 缓存管理器
        self.cache_manager = cache_manager

        # 加载配置
        self._load_config()

    async def _initialize_impl(self) -> bool:
        """初始化组件实现"""
        try:
            # 确保 aiohttp session
            await self._ensure_session()

            self._logger.info("DeribitClient初始化完成")
            return True

        except Exception as e:
            self._logger.error(f"DeribitClient初始化失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return False

    def _load_config(self):
        """从配置管理器加载参数"""

        # 如果有ConfigManager，优先从配置管理器读取；否则使用环境变量
        if self.config_manager:
            # 读取 exchanges.deribit 配置
            exchanges_config = self.config_manager.get("exchanges", {})
            deribit_config = exchanges_config.get("deribit", {})

            # 读取订阅配置
            self._subscription_config = deribit_config.get("subscriptions", {})
            self._options_config = self._subscription_config.get("options", {})

            # 获取订阅的币种
            underlying = self._options_config.get("underlying", "BTC")
            if isinstance(underlying, str):
                self.currencies = [underlying]
            else:
                self.currencies = underlying

            # 获取数据类型配置
            self._data_types = self._options_config.get("data_types", ["ticker"])

            # API配置 - 仅使用生产环境
            endpoints = deribit_config.get("endpoints", {})
            mainnet_config = endpoints.get("mainnet", {})
            self._api_url = mainnet_config.get("api", "https://www.deribit.com/api/v2")
            self._ws_url = mainnet_config.get("ws", "wss://www.deribit.com/ws/api/v2")
            self._logger.info("使用生产网络环境")

        else:
            # 回退到环境变量配置 - 仅使用生产环境
            self.currencies = ["BTC"]
            self._data_types = ["ticker"]
            self._api_url = "https://www.deribit.com/api/v2"
            self._ws_url = "wss://www.deribit.com/ws/api/v2"
            self._logger.info("使用生产网络环境")

        # WebSocket配置
        self._ws_heartbeat_interval = 15  # 心跳间隔，默认15秒
        self._ws_max_retries = 5  # 最大重试次数，默认5次

        # 价格监控范围参数
        self._price_range_lower = 0.8  # 价格范围下限
        self._price_range_upper = 1.2  # 价格范围上限
        self._max_days_to_expiry = 30  # 最大到期天数

        self._logger.info(
            "DeribitClient配置加载完成:\n"
            f"- 支持币种: {self.currencies}\n"
            f"- 数据类型: {self._data_types}\n"
            f"- API URL: {self._api_url}\n"
            f"- WebSocket URL: {self._ws_url}\n"
            f"- 生产环境: True\n"
            f"- WebSocket心跳间隔: {self._ws_heartbeat_interval}秒\n"
            f"- WebSocket最大重试: {self._ws_max_retries}次\n"
            f"- 价格范围: {self._price_range_lower} - {self._price_range_upper}\n"
            f"- 最大到期天数: {self._max_days_to_expiry}"
        )

    async def _authenticate(self) -> bool:
        """获取HTTP访问令牌"""
        try:
            async with self._ws_lock:  # 使用已有的 WebSocket 锁来保护认证过程
                # 构建认证请求
                params = {
                    "grant_type": "client_credentials",
                    "client_id": self._api_key,
                    "client_secret": self._api_secret,
                }

                # 使用正确的base_url
                base_url = self._api_url
                url = f"{base_url}/public/auth"

                request_data = {
                    "jsonrpc": "2.0",
                    "id": self._get_request_id(),
                    "method": "public/auth",
                    "params": params,
                }

                headers = {"Content-Type": "application/json"}

                async with self._session.post(
                    url, json=request_data, headers=headers
                ) as response:
                    data = await response.json()
                    self._logger.debug(f"HTTP认证响应: {data}")

                    if "error" in data:
                        self._logger.error(f"HTTP认证失败: {data.get('error')}")
                        return False

                    result = data.get("result", {})
                    if result and "access_token" in result:
                        self._http_token = result["access_token"]
                        self._http_token_expiry = result.get("expires_in", 600)
                        self._http_token_timestamp = time.time()
                        self._logger.debug(
                            f"HTTP认证成功，令牌有效期: {self._http_token_expiry}秒"
                        )

                        # 如果已有HTTP刷新任务，先取消
                        if (
                            self._http_token_refresh_task
                            and not self._http_token_refresh_task.done()
                        ):
                            self._http_token_refresh_task.cancel()
                            with contextlib.suppress(asyncio.CancelledError):
                                await self._http_token_refresh_task

                        # 启动新的HTTP令牌刷新任务
                        self._http_token_refresh_task = asyncio.create_task(
                            self._refresh_http_token()
                        )
                        return True

                    self._logger.error(f"HTTP认证失败，无效的响应数据: {data}")
                    return False

        except Exception as e:
            self._logger.error(f"HTTP认证失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return False

    async def _refresh_http_token(self) -> None:
        """HTTP令牌刷新任务，每14分钟刷新一次"""
        try:
            while True:
                await asyncio.sleep(14 * 60)  # 14分钟
                self._logger.debug("开始刷新HTTP令牌")
                if not await self._authenticate():
                    self._logger.error("HTTP令牌刷新失败")

        except asyncio.CancelledError:
            self._logger.debug("HTTP令牌刷新任务被取消")
        except Exception as e:
            self._logger.error(f"HTTP令牌刷新任务出错: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    def _is_http_token_expired(self) -> bool:
        """检查HTTP令牌是否过期"""
        if not hasattr(self, "_http_token_timestamp") or not hasattr(
            self, "_http_token_expiry"
        ):
            return True
        elapsed = time.time() - self._http_token_timestamp
        return elapsed >= (self._http_token_expiry - 60)  # 提前60秒认为令牌过期

    async def _authenticate_ws(self) -> bool:
        """WebSocket认证 - 使用专门的认证任务队列"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self._logger.info(f"WebSocket认证尝试 {attempt + 1}/{max_retries}")

                # 验证WebSocket连接状态
                if not self._ws or self._ws.closed:
                    self._logger.error("WebSocket连接不可用，无法进行认证")
                    return False

                # 构建认证请求
                auth_params = {
                    "grant_type": "client_credentials",
                    "client_id": self._api_key,
                    "client_secret": self._api_secret,
                }

                # 使用专门的认证消息发送，增加更长的超时
                result = await self._send_auth_message("public/auth", auth_params)

                if result and result.get("access_token"):
                    self._logger.info("WebSocket认证成功")
                    return True
                elif result:
                    self._logger.warning(f"WebSocket认证响应异常: {result}")
                else:
                    self._logger.warning("WebSocket认证无响应")

                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3  # 增加等待时间：3, 6, 9秒
                    self._logger.info(f"等待{wait_time}秒后重试...")
                    await asyncio.sleep(wait_time)

            except Exception as e:
                self._logger.error(f"WebSocket认证失败 (尝试{attempt + 1}): {e}")
                self._logger.debug(f"认证错误详情: {traceback.format_exc()}")
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 3
                    await asyncio.sleep(wait_time)

        return False

    async def _send_http_request(
        self, method: str, endpoint: str, params: dict = None, auth: bool = False
    ) -> dict | None:
        """发送HTTP请求

        Args:
            method: HTTP方法 (GET/POST)
            endpoint: API端点
            params: 请求参数
            auth: 是否需要认证
        """
        try:
            # 确保endpoint包含public/或private/前缀
            if not endpoint.startswith(("public/", "private/")):
                endpoint = f"{'private' if auth else 'public'}/{endpoint}"

            url = f"{self._api_url}"

            request_data = {
                "jsonrpc": "2.0",
                "id": self._get_request_id(),
                "method": endpoint,
                "params": params or {},
            }

            headers = {"Content-Type": "application/json"}

            if auth:
                if not hasattr(self, "_http_token") or self._is_http_token_expired():
                    self._logger.info("HTTP令牌不存在或已过期，尝试重新认证")
                    if not await self._authenticate():
                        self._logger.error("HTTP认证失败，无法执行请求")
                        return None

                headers["Authorization"] = f"Bearer {self._http_token}"
                self._logger.debug("使用HTTP认证令牌: [REDACTED]")

            self._logger.debug(f"发送HTTP请求: {request_data}")

            retry_count = 0
            max_retries = 3

            while retry_count < max_retries:
                try:
                    # 添加超时设置
                    timeout = aiohttp.ClientTimeout(total=10)  # 10秒总超时
                    async with self._session.post(
                        url, json=request_data, headers=headers, timeout=timeout
                    ) as response:
                        # 检查HTTP状态码
                        if response.status >= 400:
                            retry_count += 1
                            self._logger.warning(
                                f"HTTP请求失败，状态码: {response.status}，尝试重试 ({retry_count}/{max_retries})"
                            )
                            if retry_count < max_retries:
                                await asyncio.sleep(1)
                                continue
                            else:
                                self._logger.error(
                                    f"HTTP请求重试次数超过限制，状态码: {response.status}"
                                )
                                return None

                        try:
                            data = await response.json()
                        except aiohttp.ContentTypeError:
                            # 如果响应不是JSON格式
                            text = await response.text()
                            self._logger.error(
                                f"响应不是有效的JSON格式: {text[:200]}..."
                            )
                            retry_count += 1
                            if retry_count < max_retries:
                                await asyncio.sleep(1)
                                continue
                            else:
                                return None

                        self._logger.debug(f"收到响应: {data}")

                        if "error" in data:
                            error = data.get("error", {})
                            if error.get("code") == 13009:
                                retry_count += 1
                                if retry_count < max_retries:
                                    self._logger.warning(
                                        f"访问令牌无效，尝试重新认证 (尝试 {retry_count}/{max_retries})"
                                    )
                                    if await self._authenticate():
                                        headers["Authorization"] = (
                                            f"Bearer {self._http_token}"
                                        )
                                        continue
                            self._logger.error(f"API错误: {error}")
                            return None

                        # 检查结果是否为None
                        result = data.get("result")
                        if result is None:
                            self._logger.warning(f"API响应中没有结果字段: {data}")

                        # 特殊处理某些接口的响应
                        if (
                            endpoint == "private/get_order_history_by_instrument"
                            and result is None
                        ):
                            # 订单历史接口返回空列表而不是None是正常的
                            self._logger.warning("获取订单历史返回空结果")
                            return []

                        # 特殊处理用户交易历史接口
                        if (
                            endpoint == "private/get_user_trades_by_instrument"
                            and result is None
                        ):
                            # 用户交易历史接口返回空列表而不是None是正常的
                            self._logger.warning("获取用户交易历史返回空结果")
                            return []

                        return result

                except TimeoutError:
                    retry_count += 1
                    self._logger.warning(
                        f"HTTP请求超时，尝试重试 ({retry_count}/{max_retries})"
                    )
                    if retry_count < max_retries:
                        await asyncio.sleep(1)
                    else:
                        self._logger.error("HTTP请求重试次数超过限制: 请求超时")
                        return None
                except aiohttp.ClientError as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        self._logger.warning(
                            f"HTTP请求失败，尝试重试 ({retry_count}/{max_retries}): {e}"
                        )
                        await asyncio.sleep(1)
                    else:
                        self._logger.error(f"HTTP请求重试次数超过限制: {e}")
                        return None

        except Exception as e:
            self._logger.error(f"HTTP请求失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return None

    async def get_index_price(self, currency: str) -> float:
        """获取指数价格

        Args:
            currency: 币种 ('BTC' or 'ETH')

        Returns:
            float: 指数价格
        """
        try:
            # Deribit的指数名称格式是小写的，例如：btc_usd, eth_usd
            response = await self._send_http_request(
                "GET",
                "public/get_index_price",
                {"index_name": f"{currency.lower()}_usd"},  # 改为小写
            )
            return float(response["index_price"]) if response else 0.0

        except Exception as e:
            self._logger.error(f"获取指数价格失败: {e}")
            return 0.0

    async def get_instruments(self, currency: str, kind: str = "option") -> list[dict]:
        """获取合约列表

        Returns:
            List[Dict]: 合约信息列表
        """
        try:
            response = await self._send_http_request(
                "GET", "public/get_instruments", {"currency": currency, "kind": kind}
            )
            return response if response else []

        except Exception as e:
            self._logger.error(f"获取合约列表失败: {e}")
            return []

    async def get_instrument_data(self, instrument_name: str) -> dict | None:
        """获取合约详情"""
        result = await self._send_http_request(
            "GET", "public/get_instrument", {"instrument_name": instrument_name}
        )
        return result

    async def get_book_summary_by_currency(
        self, currency: str, kind: str = "option"
    ) -> list[dict]:
        """获取指定货币和类型的合约摘要信息

        Args:
            currency: 货币类型 ('BTC' or 'ETH')
            kind: 合约类型 ('future' or 'option')

        Returns:
            List[Dict]: 合约摘要信息列表，包含mark_iv等字段
        """
        try:
            response = await self._send_http_request(
                "GET",
                "public/get_book_summary_by_currency",
                {"currency": currency, "kind": kind},
            )
            return response if response else []

        except Exception as e:
            self._logger.error(f"获取{currency} {kind}合约摘要失败: {e}")
            return []

    async def get_order_book(self, instrument_name: str, depth: int = 5) -> dict | None:
        """获取订单簿"""
        result = await self._send_http_request(
            "GET",
            "public/get_order_book",
            {"instrument_name": instrument_name, "depth": depth},
        )
        return result

    async def get_account_summary(self, currency: str) -> dict:
        """获取账户摘要信息

        Args:
            currency: 币种（必需）

        Returns:
            Dict: 账户信息字典
        """
        try:
            if not currency:
                self._logger.error("获取账户信息失败: 必须指定币种")
                return {}

            response = await self._send_http_request(
                "POST",
                "private/get_account_summary",
                {"currency": currency.upper()},  # 确保币种是大写的
                auth=True,
            )
            return response if response else {}

        except Exception as e:
            self._logger.error(f"获取账户信息失败: {e}")
            return {}

    async def _setup_websocket(self) -> None:
        """设置 WebSocket 连接"""
        max_retries = 2  # 减少重试次数
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 确保session存在
                session = await self._ensure_session()

                # 创建SSL上下文
                ssl_context = ssl.create_default_context()

                self._logger.info(f"尝试连接WebSocket... (第{retry_count + 1}次)")

                # 为WebSocket连接添加总体超时
                connect_timeout = 5.0  # 5秒连接超时

                # 创建 WebSocket 连接 - 使用更短的超时
                self._ws = await asyncio.wait_for(
                    session.ws_connect(
                        self._ws_url,
                        ssl=ssl_context,
                        timeout=ClientWSTimeout(
                            ws_receive=10.0,  # 减少到10秒
                            ws_close=5.0,  # 减少到5秒
                        ),
                    ),
                    timeout=connect_timeout,
                )

                self._logger.info("✅ Deribit WebSocket连接建立成功")
                self._logger.info(
                    f"🔗 Deribit WebSocket状态: closed={getattr(self._ws, 'closed', 'unknown')}"
                )
                # 等待连接稳定
                await asyncio.sleep(0.5)  # 减少等待时间
                break  # 连接成功，退出重试循环

            except TimeoutError:
                retry_count += 1
                self._logger.warning(
                    f"WebSocket连接超时 (尝试 {retry_count}/{max_retries})"
                )
                if retry_count < max_retries:
                    await asyncio.sleep(2**retry_count)  # 指数退避
                    continue
                else:
                    self._logger.error("WebSocket连接超时，已达到最大重试次数")
                    raise
            except Exception as e:
                retry_count += 1
                self._logger.warning(
                    f"WebSocket连接失败: {e} (尝试 {retry_count}/{max_retries})"
                )
                if retry_count < max_retries:
                    await asyncio.sleep(2**retry_count)
                    continue
                else:
                    self._logger.error(f"WebSocket连接失败，已达到最大重试次数: {e}")
                    raise

        try:
            # 先启动消息处理循环，这样认证时可以接收响应
            asyncio.create_task(self._message_handler())

            # 等待消息处理循环启动
            await asyncio.sleep(0.5)

            # 专门的认证阶段 - 在消息处理器启动后完成
            await self._ws_authentication_flow()

        except Exception as e:
            self._logger.error(f"设置 WebSocket 连接失败: {e}")

    async def _ws_authentication_flow(self):
        """专门的WebSocket认证流程，按照Deribit官方文档的正确顺序"""
        try:
            self._logger.info("开始WebSocket完整连接流程...")

            # 等待连接完全稳定
            await asyncio.sleep(1)

            # 使用专用锁确保整个流程的原子性
            async with self._ws_lock:
                # 第一步：发送 public/hello 握手（Deribit官方要求）
                hello_success = await self._send_hello()
                if not hello_success:
                    self._logger.error("WebSocket hello握手失败")
                    return

                # 第二步：进行 WebSocket 认证
                auth_success = True
                if not getattr(self, "_public_mode", False):
                    auth_success = await self._authenticate_ws()

                if auth_success:
                    self._logger.info("WebSocket 认证阶段成功或公共模式")

                    # 第三步：设置心跳
                    heartbeat_interval = max(10, self._ws_heartbeat_interval)
                    result = await self._send_ws_message(
                        "public/set_heartbeat", {"interval": heartbeat_interval}
                    )

                    if result == "ok":
                        self._logger.info(
                            f"Deribit 心跳设置成功，间隔: {heartbeat_interval}秒"
                        )
                    else:
                        self._logger.error("Deribit 心跳设置失败")
                        return

                else:
                    self._logger.error("WebSocket 认证失败")
                    return

            # 认证完成后，重新订阅合约
            if auth_success:
                await self._resubscribe_instruments()

        except Exception as e:
            self._logger.error(f"WebSocket认证流程失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            raise

    async def _send_hello(self) -> bool:
        """发送 public/hello 握手消息，按照Deribit官方文档要求"""
        try:
            self._logger.info("发送WebSocket hello握手...")

            if not self._ws or self._ws.closed:
                self._logger.error("WebSocket连接不可用")
                return False

            request_id = self._get_request_id()
            hello_request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": "public/hello",
                "params": {
                    "client_name": "BTC Options Grid Trading Bot",
                    "client_version": "1.0.0",
                },
            }

            # 创建Future对象等待响应
            future = asyncio.Future()
            self._ws_response_futures[request_id] = future

            try:
                # 发送hello请求
                await self._ws.send_json(hello_request)
                self._logger.debug(f"已发送hello请求: {hello_request}")

                # 等待响应
                result = await asyncio.wait_for(future, timeout=30)

                if result:
                    self._logger.info(f"Hello握手成功: {result}")
                    return True
                else:
                    self._logger.error("Hello握手失败：无响应结果")
                    return False

            except TimeoutError:
                self._logger.error("Hello握手超时")
                # 清理Future
                self._ws_response_futures.pop(request_id, None)
                return False
            except Exception as e:
                self._logger.error(f"Hello握手过程出错: {e}")
                # 清理Future
                self._ws_response_futures.pop(request_id, None)
                return False

        except Exception as e:
            self._logger.error(f"Hello握手失败: {e}")
            self._logger.debug(f"Hello握手错误详情: {traceback.format_exc()}")
            return False

    async def _message_handler(self) -> None:
        """处理WebSocket消息 - 主消息处理循环"""
        try:
            while True:
                try:
                    if not self._ws or self._ws.closed:
                        self._logger.warning("WebSocket连接已关闭，重新连接")
                        await self._setup_websocket()
                        await self._resubscribe_all()  # 重新订阅所有数据
                        continue

                    # 处理WebSocket消息
                    msg = await self._ws.receive()
                    if msg.type == aiohttp.WSMsgType.TEXT:
                        data = msg.json()
                        await self._handle_message(data)
                    elif msg.type == aiohttp.WSMsgType.CLOSED:
                        self._logger.warning(f"WebSocket连接已关闭 - 消息: {msg.data}")
                        break
                    elif msg.type == aiohttp.WSMsgType.ERROR:
                        self._logger.error(f"WebSocket错误: {msg.data}")
                        break

                except Exception as e:
                    self._logger.error(f"处理WebSocket消息时出错: {e}")
                    await asyncio.sleep(1)

        except asyncio.CancelledError:
            self._logger.info("消息处理循环被取消")
        finally:
            # 清理所有等待中的futures
            for future in self._ws_response_futures.values():
                if not future.done():
                    future.set_exception(ConnectionError("WebSocket连接已关闭"))
            self._ws_response_futures.clear()

    async def _handle_message(self, data: dict) -> None:
        """处理单个消息"""
        try:
            # 处理心跳消息
            if (
                data.get("method") == "heartbeat"
                and data.get("params", {}).get("type") == "test_request"
            ):
                try:
                    test_request = {
                        "jsonrpc": "2.0",
                        "id": self._get_request_id(),
                        "method": "public/test",
                        "params": {},
                    }
                    await self._ws.send_json(test_request)
                except Exception as e:
                    self._logger.error(f"发送心跳响应失败: {e}")
                return

            # 处理订阅消息
            if data.get("method") == "subscription":
                await self._handle_websocket_subscription(data.get("params", {}))
                return

            # 处理请求响应
            if "id" in data:
                request_id = data["id"]

                # 首先检查是否是认证消息的响应
                auth_key = f"auth_{request_id}"
                future_key = None

                if auth_key in self._ws_response_futures:
                    future_key = auth_key
                    self._logger.debug(f"处理认证响应，ID: {request_id}")
                elif request_id in self._ws_response_futures:
                    future_key = request_id

                if future_key:
                    future = self._ws_response_futures[future_key]
                    if not future.done():
                        if "error" in data:
                            error_msg = data["error"]
                            self._logger.error(f"WebSocket请求错误: {error_msg}")
                            future.set_exception(Exception(str(error_msg)))
                        else:
                            result = data.get("result")
                            self._logger.debug(f"WebSocket请求成功，结果: {result}")
                            future.set_result(result)
                    self._ws_response_futures.pop(future_key, None)
                else:
                    self._logger.debug(f"收到未知ID的响应: {request_id}")

        except Exception as e:
            self._logger.error(f"处理消息失败: {e}")
            self._logger.debug(f"消息内容: {data}")

    async def _subscribe_channels(
        self, channels: list[str], is_private: bool = False, max_retries: int = 3
    ) -> bool:
        """通用的订阅方法

        Args:
            channels: 要订阅的频道列表
            is_private: 是否为私有频道
            max_retries: 最大重试次数

        Returns:
            bool: 订阅是否成功
        """
        try:
            retry_count = 0
            while retry_count < max_retries:
                result = await self._send_ws_message(
                    "private/subscribe" if is_private else "public/subscribe",
                    {"channels": channels},
                )

                if result:
                    self._logger.info(f"✅ Deribit订阅成功: {channels}")
                    return True

                retry_count += 1
                if retry_count < max_retries:
                    self._logger.warning(
                        f"订阅失败，重试 ({retry_count}/{max_retries})"
                    )
                    await asyncio.sleep(1)
                else:
                    self._logger.error("订阅失败，达到最大重试次数")
                    return False

            return False

        except Exception as e:
            self._logger.error(f"订阅失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return False

    async def subscribe_instruments(self, instruments: list[str]) -> bool:
        """订阅多个合约"""
        try:
            if not instruments:
                return True

            batch_size = 100
            for i in range(0, len(instruments), batch_size):
                batch = instruments[i : i + batch_size]
                channels = [
                    f"book.{instrument}.raw.10.100ms" for instrument in batch
                ] + [f"ticker.{instrument}.raw" for instrument in batch]

                result = await self._subscribe_channels(channels, is_private=True)
                if result:
                    self._subscribed_instruments.update(batch)
                    self._logger.info(
                        f"已订阅{len(batch)}个合约 ({i + 1}-{min(i + batch_size, len(instruments))})"
                    )
                else:
                    self._logger.error(
                        f"订阅失败 ({i + 1}-{min(i + batch_size, len(instruments))})"
                    )
                    return False

                await asyncio.sleep(0.1)  # 避免请求过快

            return True

        except Exception as e:
            self._logger.error(f"订阅合约时出错: {e}")
            return False

    async def subscribe_position(self, instrument_name: str) -> None:
        """订阅持仓数据"""
        try:
            # 先通过HTTP API获取持仓信息
            position = await self.get_position(instrument_name)
            if not position or float(position.get("size", 0)) == 0:
                self._logger.warning(f"未找到活跃持仓或持仓量为0: {instrument_name}")
                return

            async with self._subscription_lock:
                if instrument_name not in self._monitored_positions:
                    # 同时订阅trades和ticker频道
                    channels = [
                        f"user.trades.{instrument_name}.raw",
                        f"ticker.{instrument_name}.raw",
                    ]
                    await self._send_ws_message(
                        method="private/subscribe", params={"channels": channels}
                    )
                    self._monitored_positions.add(instrument_name)
                    self._logger.info(
                        f"订阅持仓数据: {instrument_name} (当前持仓量: {position['size']})"
                    )
                else:
                    self._logger.debug(f"已订阅持仓数据: {instrument_name}")

        except Exception as e:
            self._logger.error(f"订阅持仓数据失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    async def get_position(self, instrument_name: str) -> dict[str, Any] | None:
        """获取单个合约的持仓信息"""
        try:
            response = await self._send_http_request(
                method="GET",
                endpoint="private/get_position",
                params={"instrument_name": instrument_name},
                auth=True,
            )

            if response and isinstance(response, dict):
                return response
            return None

        except Exception as e:
            self._logger.error(f"获取持仓信息失败: {e}")
            return None

    async def get_positions(
        self, currency: str, kind: str = "option"
    ) -> list[dict[str, Any]]:
        """获取指定币种和类型的所有持仓"""
        try:
            response = await self._send_http_request(
                method="GET",
                endpoint="private/get_positions",
                params={"currency": currency, "kind": kind},
                auth=True,  # 添加auth=True参数
            )

            if response and isinstance(response, list):
                return response
            return []

        except Exception as e:
            self._logger.error(f"获取持仓列表失败: {e}")
            return []

    async def _resubscribe_instruments(self) -> None:
        """重新订阅所有合约"""
        try:
            if not self._subscribed_instruments:
                return

            self._logger.info(
                f"开始重新订阅 {len(self._subscribed_instruments)} 个合约"
            )
            await self.subscribe_instruments(list(self._subscribed_instruments))
            self._logger.info("重新订阅完成")

        except Exception as e:
            self._logger.error(f"重新订阅失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    async def _resubscribe_positions(self) -> None:
        """重新订阅所有持仓数据"""
        try:
            if not self._monitored_positions:
                return

            self._logger.info(f"开始重新订阅 {len(self._monitored_positions)} 个持仓")

            # 批量订阅，每批100个
            batch_size = 100
            positions_list = list(self._monitored_positions)

            for i in range(0, len(positions_list), batch_size):
                batch = positions_list[i : i + batch_size]
                channels = []
                for instrument in batch:
                    channels.extend(
                        [f"user.trades.{instrument}.raw", f"ticker.{instrument}.raw"]
                    )

                result = await self._subscribe_channels(channels, is_private=True)
                if result:
                    self._logger.info(
                        f"持仓数据重新订阅成功: {i + 1}-{min(i + batch_size, len(positions_list))}"
                    )
                else:
                    self._logger.error(
                        f"持仓数据重新订阅失败: {i + 1}-{min(i + batch_size, len(positions_list))}"
                    )

                await asyncio.sleep(0.1)  # 避免请求过快

            self._logger.info("持仓数据重新订阅完成")

        except Exception as e:
            self._logger.error(f"重新订阅持仓数据失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    async def _handle_position_data(self, data: dict) -> PositionData | None:
        """处理持仓数据更新

        Args:
            data: portfolio频道的持仓数据
        Returns:
            Optional[PositionData]: 处理后的持仓数据对象
        """
        try:
            instrument = data.get("instrument_name")
            if not instrument:
                return None

            # 提取所需的数据值
            return PositionData(
                currency="BTC" if "BTC-" in instrument else "ETH",
                instrument_name=instrument,
                size=float(data.get("size", 0)),
                average_price=float(data.get("average_price", 0)),
                mark_price=float(data.get("mark_price", 0)),
                total_profit_loss=float(data.get("total_profit_loss", 0)),
                gamma=float(data.get("greeks", {}).get("gamma", 0)),
                timestamp=datetime.fromtimestamp(data.get("timestamp", 0) / 1000, UTC),
                liquidity_data={
                    "has_liquidity": bool(
                        data.get("best_bid_price") and data.get("best_ask_price")
                    ),
                    "bid_price": safe_float_conversion(data.get("best_bid_price"), 0.0),
                    "ask_price": safe_float_conversion(data.get("best_ask_price"), 0.0),
                    "bid_amount": safe_float_conversion(
                        data.get("best_bid_amount"), 0.0
                    ),
                    "ask_amount": safe_float_conversion(
                        data.get("best_ask_amount"), 0.0
                    ),
                    "mark_iv": safe_float_conversion(data.get("mark_iv"), 0.0),
                    "volume_24h": Decimal(str(data.get("stats", {}).get("volume", 0))),
                },
            )

        except Exception as e:
            self._logger.error(f"处理持仓数据失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return None

    async def _handle_websocket_subscription(self, params: dict) -> None:
        """处理WebSocket订阅消息"""
        try:
            channel = params.get("channel", "")
            data = params.get("data", {})

            # 从频道中提取信息
            channel_parts = channel.split(".")
            if len(channel_parts) < 2:
                self._logger.warning(f"无效的频道格式: {channel}")
                return

            channel_type = channel_parts[0]

            # 处理市场数据订阅（ticker频道）
            if channel_type == "ticker":
                instrument = channel_parts[1]
                if instrument in self._monitored_positions:
                    self._logger.debug(f"收到持仓ticker数据 - {instrument}")
                    position = await self.get_position(instrument)
                    if position:
                        merged_data = {**position, **data}
                        position_data = await self._handle_position_data(merged_data)
                        if position_data:
                            await self._notify_position_callbacks(position_data)

                if instrument in self._subscribed_instruments:
                    self._logger.debug(f"收到市场ticker数据 - {instrument}")
                    asyncio.create_task(self._handle_ticker_update(data))

            # 处理持仓监控数据（trades频道）
            elif channel_type == "user" and channel_parts[1] == "trades":
                instrument = channel_parts[2]
                if instrument in self._monitored_positions:
                    self._logger.debug(f"收到trades数据 - {instrument}")
                    # trades数据主要用于触发更新，实际数据从position和ticker获取
                    position = await self.get_position(instrument)
                    if position:
                        # 获取最新的ticker数据
                        ticker = await self.get_order_book(instrument)
                        if ticker:
                            # 合并数据
                            merged_data = {**position, **ticker}
                            # 创建持仓数据对象并通知回调
                            position_data = await self._handle_position_data(
                                merged_data
                            )
                            if position_data:
                                await self._notify_position_callbacks(position_data)

        except Exception as e:
            self._logger.error(f"处理订阅消息失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    async def _handle_ticker_update(self, data: dict) -> None:
        """处理行情更新"""
        try:
            instrument = data.get("instrument_name")
            if not instrument or instrument not in self._subscribed_instruments:
                return

            self._logger.debug(f"行情更新 - {instrument}")
            self._logger.debug(f"Greeks: {data.get('greeks', {})}")
            self._logger.debug(f"OI: {data.get('open_interest', 0)}")

            market_data = self._handle_market_data(data)
            if market_data:
                self._logger.debug(f"创建期权数据: {market_data}")
                await self._notify_market_data_callbacks(market_data)

                # 发布到EventBus
                if self.event_bus:
                    # 解析期权合约信息（如BTC-26SEP25-125000-P）
                    parts = instrument.split("-")
                    strike = 0
                    option_type = "call"
                    if len(parts) == 4:
                        try:
                            strike = float(parts[2])
                            option_type = "put" if parts[3] == "P" else "call"
                        except (ValueError, IndexError):
                            pass

                    try:
                        event = DataUpdateEvent(
                            data={
                                "symbol": instrument,  # 添加symbol字段
                                "instrument_name": instrument,
                                "strike": strike,
                                "option_type": option_type,
                                "mark_price": data.get("mark_price")
                                or data.get("last_price", 0),
                                "bid_price": data.get("best_bid_price"),
                                "ask_price": data.get("best_ask_price"),
                                "last_price": data.get("last_price"),
                                "volume": data.get("volume"),
                                "open_interest": data.get("open_interest"),
                                "mark_iv": data.get("mark_iv", 0),
                                "greeks": data.get("greeks", {}),
                                "timestamp": datetime.now(UTC).isoformat(),
                            },
                            data_type="option",
                            exchange="deribit",
                            source="deribit_options",
                        )
                        await self.event_bus.publish(event)
                        from contextlib import suppress

                        with suppress(Exception):
                            self.metrics.custom_metrics["events_published"] = (
                                self.metrics.custom_metrics.get("events_published", 0)
                                + 1
                            )
                        self._logger.debug(
                            f"📤 发布Deribit期权数据: {instrument} strike={strike} type={option_type}"
                        )
                    except Exception as e:
                        self._logger.error(f"❌ EventBus发布失败: {e}")
                # 回写一个标准化的“最新期权”缓存键，供上层就绪检测
                try:
                    if self.cache_manager:
                        await self.cache_manager.set(
                            "deribit:latest_option",
                            {
                                "instrument_name": instrument,
                                "strike": strike,
                                "option_type": option_type,
                                "mark_price": data.get("mark_price")
                                or data.get("last_price", 0),
                                "timestamp": datetime.now(UTC).isoformat(),
                            },
                            ttl=30,
                        )
                except Exception as _e:
                    # 缓存失败不影响主流程
                    self._logger.debug(f"latest_option cache set failed: {_e}")
            else:
                self._logger.warning("❌ EventBus未注入，无法发布期权数据")

        except Exception as e:
            self._logger.error(f"处理行情更新失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    def _handle_market_data(self, data: dict) -> MarketData | None:
        """处理市场数据，创建期权数据对象"""
        try:
            instrument = data.get("instrument_name", "")

            # 解析期权类型
            parts = instrument.split("-")
            if len(parts) != 4:
                self._logger.warning(f"无效的期权合约名格式: {instrument}")
                return None

            option_type = (
                "call" if parts[3] == "C" else "put" if parts[3] == "P" else None
            )
            if option_type is None:
                self._logger.warning(f"无法识别期权类型: {instrument}")
                return None

            # 创建期权数据对象
            market_data = MarketData(
                currency="BTC" if "BTC-" in instrument else "ETH",
                instrument_name=instrument,
                option_type=option_type,
                strike=safe_float_conversion(data.get("strike"), 0.0),
                expiry=str(data.get("expiration_timestamp", "")),
                mark_price=safe_float_conversion(data.get("mark_price"), 0.0),
                open_interest=safe_float_conversion(data.get("open_interest"), 0.0),
                gamma=safe_float_conversion(data.get("greeks", {}).get("gamma"), 0.0),
                volume_24h=Decimal(str(data.get("stats", {}).get("volume", 0))),
                bid_price=safe_float_conversion(data.get("best_bid_price"), 0.0),
                ask_price=safe_float_conversion(data.get("best_ask_price"), 0.0),
                mark_iv=safe_float_conversion(data.get("mark_iv"), 0.0),
                timestamp=datetime.fromtimestamp(
                    data.get("timestamp", int(time.time() * 1000)) / 1000, UTC
                ),
            )

            self._logger.debug(
                f"创建期权数据: {market_data.instrument_name} - {market_data.option_type}"
            )
            return market_data

        except Exception as e:
            self._logger.error(f"创建期权数据失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            self._logger.error(f"原始数据: {data}")
            return None

    def _get_request_id(self) -> int:
        """获取请求ID"""
        self._request_id += 1
        return self._request_id

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            self._logger.info("开始启动DeribitClient...")

            # 使用更长的超时时间避免启动阻塞
            startup_timeout = 60.0  # 增加到60秒超时

            try:
                # 如果是公共模式则跳过认证，直接建立WebSocket
                if getattr(self, "_public_mode", False):
                    self._logger.info("🔓 公共模式：跳过Deribit认证，直接连接WebSocket")
                else:
                    # 首先进行认证（带超时）
                    self._logger.info("🔐 开始Deribit认证...")
                    auth_success = await asyncio.wait_for(
                        self._authenticate(), timeout=startup_timeout
                    )
                    if not auth_success:
                        self._logger.error("❌ Deribit认证失败")
                        return False
                    self._logger.info("✅ Deribit认证成功")

                # 设置WebSocket连接（带超时）
                self._logger.info("🌐 建立WebSocket连接...")
                await asyncio.wait_for(self._setup_websocket(), timeout=startup_timeout)
                self._logger.info("✅ WebSocket连接建立成功")

                # 初始化市场数据订阅（异步非阻塞）
                self._logger.info("初始化市场数据订阅...")
                # 创建异步任务，不等待完成
                self._logger.info("启动异步订阅任务...")
                asyncio.create_task(self._initialize_market_data_subscription_async())

                # 启动期权列表更新任务（每4小时更新一次）
                asyncio.create_task(self._update_instruments_task())

                self._logger.info("✅ DeribitClient启动完成")
                return True

            except TimeoutError:
                self._logger.error("⏰ DeribitClient启动超时")
                return False

        except Exception as e:
            self._logger.error(f"❌ DeribitClient启动失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return False

    async def _initialize_market_data_subscription_async(self) -> None:
        """异步初始化市场数据订阅（非阻塞）"""
        try:
            self._logger.info("🔄 开始异步初始化市场数据订阅...")
            # 等待一小段时间确保WebSocket连接稳定
            await asyncio.sleep(1.0)

            # 调用原有的订阅逻辑
            await self._initialize_market_data_subscription()
            self._logger.info("✅ 市场数据订阅初始化完成")

        except Exception as e:
            self._logger.error(f"❌ 市场数据订阅初始化失败: {e}")
            # 不抛出异常，允许DeribitClient继续运行

    async def _update_instruments_task(self) -> None:
        """定时更新期权列表任务"""
        try:
            while True:
                await asyncio.sleep(4 * 3600)  # 每4小时更新一次
                self._logger.info("开始更新期权列表...")

                # 保存当前订阅的合约
                old_instruments = self._subscribed_instruments.copy()

                # 更新期权列表
                await self._initialize_market_data_subscription()

                # 取消不再监控的合约订阅
                for instrument in old_instruments:
                    if instrument not in self._subscribed_instruments:
                        self._logger.info(f"取消过期合约订阅: {instrument}")

        except asyncio.CancelledError:
            self._logger.info("期权列表更新任务被取消")
        except Exception as e:
            self._logger.error(f"期权列表更新任务出错: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 停止WebSocket处理任务
            if (
                hasattr(self, "_ws_processor_task")
                and self._ws_processor_task
                and not self._ws_processor_task.done()
            ):
                self._ws_processor_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._ws_processor_task

            # 关闭WebSocket连接
            if self._ws and not self._ws.closed:
                await self._ws.close()
                self._ws = None

            # 关闭HTTP session，但不设置为None，让重连时重新创建
            if self._session and not self._session.closed:
                await self._session.close()
                self._session = None

            self._logger.info("组件停止完成")
            return True
        except Exception as e:
            self._logger.error(f"停止失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""

        # 检查连接状态
        ws_connected = self._ws and not self._ws.closed
        session_active = self._session and not self._session.closed
        token_valid = self._http_token is not None and not self._is_http_token_expired()

        # 确定健康状态
        if ws_connected and session_active and token_valid:
            status = HealthStatus.HEALTHY
            message = "All systems operational"
        elif session_active and token_valid:
            status = HealthStatus.DEGRADED
            message = "WebSocket disconnected but HTTP active"
        elif session_active:
            status = HealthStatus.DEGRADED
            message = "Session active but token expired"
        else:
            status = HealthStatus.UNHEALTHY
            message = "No active connections"

        details = {
            "ws_connected": ws_connected,
            "session_active": session_active,
            "subscribed_instruments": len(self._subscribed_instruments),
            "monitored_positions": len(self._monitored_positions),
            "http_token_valid": token_valid,
        }

        return HealthCheckResult(status=status, message=message, details=details)

    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        ws_connected = self._ws and not self._ws.closed
        session_active = self._session and not self._session.closed
        return ws_connected and session_active

    def add_market_data_callback(
        self, callback: Callable[[MarketData], Coroutine]
    ) -> None:
        """添加市场数据回调"""
        self._market_data_callbacks.add(callback)
        self._logger.info(f"注测市场数据回调: {callback.__name__}")
        self._logger.info(f"当前市场数据回调数量: {len(self._market_data_callbacks)}")

    def remove_market_data_callback(
        self, callback: Callable[[MarketData], Coroutine]
    ) -> None:
        """移除市场数据回调"""
        self._market_data_callbacks.discard(callback)

    def add_position_callback(
        self, callback: Callable[[PositionData], Coroutine]
    ) -> None:
        """添加持仓数据回调"""
        self._position_callbacks.add(callback)
        self._logger.info(f"注册持仓数据回调: {callback.__name__}")
        self._logger.info(f"当前持仓数据回调数量: {len(self._position_callbacks)}")

    def remove_position_callback(
        self, callback: Callable[[PositionData], Coroutine]
    ) -> None:
        """移除持仓数据回调"""
        self._position_callbacks.discard(callback)

    async def _notify_position_callbacks(self, data: PositionData) -> None:
        """通知持仓数据回调函数"""
        async with self._callback_lock:
            for callback in self._position_callbacks:
                try:
                    await callback(data)
                except Exception as e:
                    self._logger.error(f"执行持仓数据回调失败: {e}")

    async def _notify_market_data_callbacks(self, data: MarketData) -> None:
        """通知市场数据回调函数"""
        async with self._callback_lock:
            for callback in self._market_data_callbacks:
                try:
                    await callback(data)
                except Exception as e:
                    self._logger.error(f"执行市场数据回调失败: {e}")

    async def _cleanup(self) -> None:
        """清理资源"""
        try:
            # 取消HTTP令牌刷新任务
            if (
                hasattr(self, "_http_token_refresh_task")
                and self._http_token_refresh_task
            ):
                self._http_token_refresh_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._http_token_refresh_task

            # 取消WebSocket息处理任务
            if hasattr(self, "_ws_processor_task") and self._ws_processor_task:
                self._ws_processor_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._ws_processor_task

            # 清理所有等待中的futures
            for future in self._ws_response_futures.values():
                if not future.done():
                    future.set_exception(ConnectionError("WebSocket连接已关闭"))
            self._ws_response_futures.clear()

            # 清理令牌相关属性
            self._http_token = None
            self._http_token_expiry = None
            self._http_token_timestamp = None
            self._http_token_refresh_task = None

            # 关闭WebSocket连接
            if self._ws:
                await self._ws.close()
                self._ws = None

            # 关闭HTTP会话
            if self._session and not self._session.closed:
                await self._session.close()
                self._session = None

            self._logger.info("DeribitClient资源清理完成")

        except Exception as e:
            self._logger.error(f"清理资源出错: {e}")

    async def _initialize_market_data_subscription(self) -> None:
        """初始化市场数据订阅"""
        try:
            # 检查是否有配置订阅
            if not hasattr(self, "_options_config") or not self._options_config:
                self._logger.warning("没有找到期权订阅配置，跳过订阅")
                return

            instruments_setting = self._options_config.get("instruments", "all")

            valid_instruments = []

            # 获取并筛选合约
            for currency in self.currencies:
                self._logger.info(f"为币种 {currency} 获取期权合约...")

                if instruments_setting == "all":
                    # 动态获取符合条件的合约
                    index_price = await self.get_index_price(currency)
                    if not index_price:
                        self._logger.warning(f"无法获取 {currency} 的指数价格，跳过")
                        continue

                    price_lower = index_price * self._price_range_lower
                    price_upper = index_price * self._price_range_upper

                    instruments = await self.get_instruments(currency)
                    if not instruments:
                        self._logger.warning(
                            f"无法获取 {currency} 的期权合约列表，跳过"
                        )
                        continue

                    currency_instruments = [
                        instrument["instrument_name"]
                        for instrument in instruments
                        if self._is_market_valid_instrument(
                            instrument, price_lower, price_upper
                        )
                    ]
                    valid_instruments.extend(currency_instruments)
                    self._logger.info(
                        f"找到 {len(currency_instruments)} 个 {currency} 期权合约"
                    )

                elif isinstance(instruments_setting, list):
                    # 指定特定的合约列表
                    valid_instruments.extend(instruments_setting)
                    self._logger.info(
                        f"使用配置指定的 {len(instruments_setting)} 个期权合约"
                    )

            if not valid_instruments:
                self._logger.warning("没有找到符合条件的期权合约")
                return

            self._logger.info(
                f"准备订阅 {len(valid_instruments)} 个期权合约，数据类型: {self._data_types}"
            )

            # 根据配置的数据类型进行订阅
            if "ticker" in self._data_types:
                await self.subscribe_instruments(valid_instruments)

            # 如果配置了其他数据类型，可以在这里添加更多订阅逻辑
            # 例如: trades, book, greeks 等

        except Exception as e:
            self._logger.error(f"初始化市场数据订阅失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            raise

    def _is_market_valid_instrument(
        self, instrument: dict, price_lower: float, price_upper: float
    ) -> bool:
        """检查期权合约是否符合市场监控条件"""
        try:
            # 合成期权
            if instrument["instrument_name"].startswith("SYN"):
                return False

            # 检查到期时
            expiry = datetime.fromtimestamp(
                instrument["expiration_timestamp"] / 1000, tz=UTC
            )
            days_to_expiry = (expiry - datetime.now(UTC)).days
            if days_to_expiry > self._max_days_to_expiry:
                return False

            # 检查执行价格
            strike = float(instrument["strike"])
            return price_lower <= strike <= price_upper

        except Exception:
            return False

    async def unsubscribe_position(self, instrument: str) -> bool:
        """取消订阅持仓数据"""
        try:
            async with self._subscription_lock:
                self._logger.info(f"取消订阅持仓数据: {instrument}")

                # 从监控列表中移除
                self._monitored_positions.discard(instrument)

                # 取消订阅trades和ticker频道
                channels = [f"user.trades.{instrument}.raw", f"ticker.{instrument}.raw"]

                result = await self._send_ws_message(
                    "private/unsubscribe", {"channels": channels}
                )

                if result:
                    self._logger.info(f"持仓数据取消订阅成功: {instrument}")
                    return True
                else:
                    self._logger.error(f"持仓数据取消订阅失败: {instrument}")
                    return False

        except Exception as e:
            self._logger.error(f"取消订阅持仓数据失败: {e}")
            return False

    async def _send_auth_message(self, method: str, params: dict) -> dict | None:
        """专门用于认证的WebSocket消息发送，使用独立的超时和优先级"""
        try:
            if not self._ws or self._ws.closed:
                self._logger.error("WebSocket连接已关闭，无法发送认证消息")
                return None

            request_id = self._get_request_id()
            request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": method,
                "params": params,
            }

            self._logger.debug(f"发送WebSocket认证请求: {request}")

            # 创建Future对象，使用专门的认证标识
            future = asyncio.Future()
            auth_key = f"auth_{request_id}"
            self._ws_response_futures[auth_key] = future

            try:
                # 发送认证请求
                await self._ws.send_json(request)

                # 认证请求使用更长的超时时间（90秒）
                result = await asyncio.wait_for(future, timeout=90)

                self._logger.debug(f"认证响应: {result}")
                return result

            except TimeoutError:
                self._logger.error(f"WebSocket认证请求超时: {method}")
                return None
            finally:
                # 清理认证相关的future
                self._ws_response_futures.pop(auth_key, None)
                # 同时也清理普通的request_id，防止冲突
                self._ws_response_futures.pop(request_id, None)

        except Exception as e:
            self._logger.error(f"发送WebSocket认证消息失败: {e}")
            self._logger.debug(f"认证发送错误详情: {traceback.format_exc()}")
            return None

    async def _send_ws_message(self, method: str, params: dict) -> dict | None:
        """发送WebSocket消息"""
        try:
            if not self._ws or self._ws.closed:
                self._logger.error("WebSocket连接已关闭，无法发送消息")
                return None

            request_id = self._get_request_id()
            request = {
                "jsonrpc": "2.0",
                "id": request_id,
                "method": method,
                "params": params,
            }

            self._logger.debug(f"发送WebSocket请求: {request}")

            # 创建Future对象
            future = asyncio.Future()
            self._ws_response_futures[request_id] = future

            try:
                # 发送请求
                await self._ws.send_json(request)

                # 等待响应 - 认证请求给更长时间
                timeout = 60 if method == "public/auth" else 30
                result = await asyncio.wait_for(future, timeout=timeout)

                # 对于订阅请求返回True表示成功
                if method in ["public/subscribe", "private/subscribe"]:
                    return True

                return result

            except TimeoutError:
                self._logger.error(f"WebSocket请求超时: {method}")
                if request_id in self._ws_response_futures:
                    future = self._ws_response_futures[request_id]
                    if not future.done():
                        future.set_exception(TimeoutError("WebSocket请求超时"))
                return None
            finally:
                self._ws_response_futures.pop(request_id, None)

        except Exception as e:
            self._logger.error(f"发送WebSocket消息失败: {e}")
            return None

    async def create_market_order(
        self, instrument_name: str, amount: float, side: str, reduce_only: bool = False
    ) -> dict | None:
        """创建市价单

        Args:
            instrument_name (str): 合约名称
            amount (float): 数量（正数）
            side (str): 方向 ("buy" 或 "sell")
            reduce_only (bool): 是否仅减仓,默认False

        Returns:
            Optional[dict]: 订单信息失败返回 None
        """
        try:
            params = {
                "instrument_name": instrument_name,
                "amount": abs(amount),  # 确保数量为正数
                "type": "market",
                "side": side.lower(),
                "reduce_only": reduce_only,
            }

            self._logger.info(f"创建市价单: {params}")

            result = await self._send_http_request(
                "POST",
                "private/buy" if side.lower() == "buy" else "private/sell",
                params,
                auth=True,
            )

            if result:
                self._logger.info(
                    f"市价单创建成功: {result.get('order', {}).get('order_id')}"
                )
                return result

            self._logger.error("市价单创建失败")
            return None

        except Exception as e:
            self._logger.error(f"创建市价单时出错: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")
            return None

    async def _resubscribe_all(self) -> None:
        """重新订阅所有数据（市场数据和持仓数据）"""
        try:
            # 1. 重新订阅市场数据
            await self._resubscribe_instruments()

            # 2. 重新订阅持仓数据
            await self._resubscribe_positions()

        except Exception as e:
            self._logger.error(f"重新订阅所有数据失败: {e}")
            self._logger.debug(f"错误详情: {traceback.format_exc()}")

    async def _ensure_session(self) -> aiohttp.ClientSession:
        """确保session存在且可用"""
        async with self._session_lock:
            if self._session is None or self._session.closed:
                if self._session and not self._session.closed:
                    await self._session.close()

                # 创建新的session，配置合理的连接池和超时
                connector = aiohttp.TCPConnector(
                    limit=100,  # 总连接池大小
                    limit_per_host=10,  # 每个主机的连接池大小
                    keepalive_timeout=30,  # 保活超时
                    ssl=True,  # 启用SSL验证
                )

                timeout = aiohttp.ClientTimeout(total=30, connect=10)

                self._session = aiohttp.ClientSession(
                    connector=connector,
                    timeout=timeout,
                    trust_env=True,
                )
                self._logger.info("创建新的 aiohttp session (线程安全)")

            return self._session

    async def buy_option(
        self,
        instrument_name: str,
        amount: float,
        price: float | None = None,
        order_type: str = "limit",
    ) -> dict | None:
        """买入期权

        Args:
            instrument_name: 期权合约名称
            amount: 数量
            price: 价格（限价单需要）
            order_type: 订单类型 ("limit" 或 "market")

        Returns:
            dict: 订单信息
        """
        try:
            params = {
                "instrument_name": instrument_name,
                "amount": amount,
                "type": order_type,
            }

            if order_type == "limit" and price is not None:
                params["price"] = price

            response = await self._send_http_request(
                "POST", "private/buy", params, auth=True
            )

            if response:
                self._logger.info(
                    f"买入期权订单提交成功: {instrument_name}, 数量: {amount}"
                )
                return response

            return None

        except Exception as e:
            self._logger.error(f"买入期权失败: {e}")
            return None

    async def sell_option(
        self,
        instrument_name: str,
        amount: float,
        price: float | None = None,
        order_type: str = "limit",
    ) -> dict | None:
        """卖出期权

        Args:
            instrument_name: 期权合约名称
            amount: 数量
            price: 价格（限价单需要）
            order_type: 订单类型 ("limit" 或 "market")

        Returns:
            dict: 订单信息
        """
        try:
            params = {
                "instrument_name": instrument_name,
                "amount": amount,
                "type": order_type,
            }

            if order_type == "limit" and price is not None:
                params["price"] = price

            response = await self._send_http_request(
                "POST", "private/sell", params, auth=True
            )

            if response:
                self._logger.info(
                    f"卖出期权订单提交成功: {instrument_name}, 数量: {amount}"
                )
                return response

            return None

        except Exception as e:
            self._logger.error(f"卖出期权失败: {e}")
            return None

    async def cancel_order(self, order_id: str) -> dict | None:
        """取消订单

        Args:
            order_id: 订单ID

        Returns:
            dict: 取消结果
        """
        try:
            response = await self._send_http_request(
                "POST", "private/cancel", {"order_id": order_id}, auth=True
            )

            if response:
                self._logger.info(f"订单取消成功: {order_id}")
                return response

            return None

        except Exception as e:
            self._logger.error(f"取消订单失败: {e}")
            return None

    async def get_order_state(self, order_id: str) -> dict | None:
        """获取订单状态

        Args:
            order_id: 订单ID

        Returns:
            dict: 订单状态信息
        """
        try:
            response = await self._send_http_request(
                "GET", "private/get_order_state", {"order_id": order_id}, auth=True
            )

            return response

        except Exception as e:
            self._logger.error(f"获取订单状态失败: {e}")
            return None

    async def get_open_orders(self, currency: str, kind: str = "option") -> list[dict]:
        """获取未成交订单

        Args:
            currency: 币种
            kind: 合约类型

        Returns:
            List[dict]: 未成交订单列表
        """
        try:
            response = await self._send_http_request(
                "GET",
                "private/get_open_orders_by_currency",
                {"currency": currency, "kind": kind},
                auth=True,
            )

            return response if response else []

        except Exception as e:
            self._logger.error(f"获取未成交订单失败: {e}")
            return []

    async def _get_cached_greeks(self, cache_key: str) -> dict[str, Any] | None:
        """从CacheManager获取Greeks数据"""
        if self.cache_manager:
            return await self.cache_manager.get(cache_key)
        return None

    async def _cache_greeks_data(self, cache_key: str, data: dict[str, Any]) -> None:
        """缓存Greeks数据到CacheManager"""
        if self.cache_manager:
            await self.cache_manager.set(cache_key, data, ttl=30)  # 30秒TTL

    async def subscribe_greeks_data(self, instruments: list[str]) -> bool:
        """订阅期权Greeks数据

        Args:
            instruments: 期权合约名称列表

        Returns:
            bool: 订阅是否成功
        """
        try:
            if not instruments:
                return True

            # 构建ticker订阅频道（包含Greeks数据）
            channels = [f"ticker.{instrument}.raw" for instrument in instruments]

            # 批量订阅
            batch_size = 50
            for i in range(0, len(channels), batch_size):
                batch = channels[i : i + batch_size]
                result = await self._subscribe_channels(batch, is_private=True)
                if not result:
                    self._logger.error(
                        f"Failed to subscribe Greeks data batch {i // batch_size + 1}"
                    )
                    return False

                self._logger.info(
                    f"Subscribed to Greeks data for {len(batch)} instruments"
                )

            self._logger.info(
                f"Successfully subscribed to Greeks data for {len(instruments)} instruments"
            )
            return True

        except Exception as e:
            self._logger.error(f"Failed to subscribe Greeks data: {e}")
            return False

    async def get_all_greeks_data(
        self, currency: str = "BTC"
    ) -> dict[str, dict[str, Any]] | None:
        """获取所有期权合约的Greeks数据（带缓存）

        Args:
            currency: 币种，默认BTC

        Returns:
            dict: 所有期权合约的Greeks数据
        """
        try:
            cache_key = f"deribit:{currency}:all_greeks"

            # 检查缓存
            cached_data = await self._get_cached_greeks(cache_key)
            if cached_data:
                return cached_data

            # CacheManager自动处理过期数据，无需手动清理

            # 获取期权合约列表
            instruments = await self.get_instruments(currency, "option")
            if not instruments:
                self._logger.warning(f"No option instruments found for {currency}")
                return None

            # 获取期权摘要数据（包含Greeks）
            summary_data = await self.get_book_summary_by_currency(currency, "option")
            if not summary_data:
                self._logger.warning(f"No summary data found for {currency} options")
                return None

            greeks_data = {}

            for item in summary_data:
                instrument_name = item.get("instrument_name")
                if not instrument_name:
                    continue

                greeks = item.get("greeks", {})
                if not greeks:
                    continue

                # 提取完整的Greeks数据
                greeks_data[instrument_name] = {
                    "instrument_name": instrument_name,
                    "greeks": {
                        "delta": float(greeks.get("delta", 0)),
                        "gamma": float(greeks.get("gamma", 0)),
                        "theta": float(greeks.get("theta", 0)),
                        "vega": float(greeks.get("vega", 0)),
                        "rho": float(greeks.get("rho", 0)),
                    },
                    "mark_price": float(item.get("mark_price", 0)),
                    "mark_iv": float(item.get("mark_iv", 0)),
                    "underlying_price": float(item.get("underlying_price", 0)),
                    "open_interest": float(item.get("open_interest", 0)),
                    "volume_24h": Decimal(str(item.get("stats", {}).get("volume", 0))),
                    "timestamp": datetime.now(UTC),
                }

            # 缓存数据
            await self._cache_greeks_data(cache_key, greeks_data)

            self._logger.info(
                f"Retrieved Greeks data for {len(greeks_data)} instruments"
            )
            return greeks_data

        except Exception as e:
            self._logger.error(f"Failed to get all Greeks data: {e}")
            return None

    async def get_greeks_data(self, instrument_name: str) -> dict | None:
        """获取期权Greeks数据（带缓存）

        Args:
            instrument_name: 期权合约名称

        Returns:
            dict: Greeks数据
        """
        try:
            cache_key = f"deribit:{instrument_name}:greeks"

            # 检查缓存
            cached_data = await self._get_cached_greeks(cache_key)
            if cached_data:
                return cached_data.get("greeks")

            # 通过ticker获取Greeks数据
            response = await self._send_http_request(
                "GET", "public/ticker", {"instrument_name": instrument_name}
            )

            if response and "greeks" in response:
                greeks_data = {
                    "instrument_name": instrument_name,
                    "greeks": response["greeks"],
                    "mark_price": float(response.get("mark_price", 0)),
                    "mark_iv": float(response.get("mark_iv", 0)),
                    "underlying_price": float(response.get("underlying_price", 0)),
                    "timestamp": datetime.now(UTC),
                }

                # 缓存数据
                await self._cache_greeks_data(cache_key, greeks_data)

                return response["greeks"]

            return None

        except Exception as e:
            self._logger.error(f"获取Greeks数据失败: {e}")
            return None

    async def calculate_real_time_gex(
        self, currency: str = "BTC"
    ) -> dict[str, Any] | None:
        """实时计算GEX（通过CeleryManager分布式计算）

        Args:
            currency: 币种，默认BTC

        Returns:
            dict: GEX计算结果
        """
        try:
            # 获取所有期权Greeks数据
            greeks_data = await self.get_all_greeks_data(currency)
            if not greeks_data:
                self._logger.warning(f"No Greeks data available for {currency}")
                return None

            # 通过CeleryManager提交GEX计算任务
            if hasattr(self, "celery_manager") and self.celery_manager:
                task_id = await self.celery_manager.submit_gex_calculation(greeks_data)
                if task_id:
                    # 等待任务完成（最多等待30秒）
                    result = await self.celery_manager.get_task_result(
                        task_id, timeout=30
                    )
                    if result and result.status == "SUCCESS":
                        return result.result
                    else:
                        self._logger.warning(
                            f"GEX calculation task failed or timed out: {task_id}"
                        )

            # 如果CeleryManager不可用，记录警告并返回None
            self._logger.warning("CeleryManager not available, GEX calculation skipped")
            return None

        except Exception as e:
            self._logger.error(f"Real-time GEX calculation failed: {e}")
            return None

    async def get_greeks_stream_data(
        self, instrument_name: str
    ) -> dict[str, Any] | None:
        """获取实时Greeks流数据

        Args:
            instrument_name: 期权合约名称

        Returns:
            dict: 实时Greeks数据
        """
        try:
            # 通过ticker API获取实时数据
            response = await self._send_http_request(
                "GET", "public/ticker", {"instrument_name": instrument_name}
            )

            if not response:
                return None

            # 提取Greeks和市场数据
            greeks = response.get("greeks", {})

            stream_data = {
                "instrument_name": instrument_name,
                "timestamp": datetime.now(UTC),
                "greeks": {
                    "delta": safe_float_conversion(greeks.get("delta"), 0.0),
                    "gamma": safe_float_conversion(greeks.get("gamma"), 0.0),
                    "theta": safe_float_conversion(greeks.get("theta"), 0.0),
                    "vega": safe_float_conversion(greeks.get("vega"), 0.0),
                    "rho": safe_float_conversion(greeks.get("rho"), 0.0),
                },
                "market_data": {
                    "mark_price": safe_float_conversion(
                        response.get("mark_price"), 0.0
                    ),
                    "mark_iv": safe_float_conversion(response.get("mark_iv"), 0.0),
                    "underlying_price": safe_float_conversion(
                        response.get("underlying_price"), 0.0
                    ),
                    "best_bid_price": safe_float_conversion(
                        response.get("best_bid_price"), 0.0
                    ),
                    "best_ask_price": safe_float_conversion(
                        response.get("best_ask_price"), 0.0
                    ),
                    "open_interest": safe_float_conversion(
                        response.get("open_interest"), 0.0
                    ),
                    "volume_24h": safe_float_conversion(
                        response.get("stats", {}).get("volume"), 0.0
                    ),
                },
                "calculated_metrics": {},
            }

            # 计算额外指标
            gamma = stream_data["greeks"]["gamma"]
            open_interest = stream_data["market_data"]["open_interest"]

            if gamma != 0 and open_interest > 0:
                # 计算单个合约的GEX贡献
                gex_contribution = gamma * open_interest * 100
                stream_data["calculated_metrics"]["gex_contribution"] = gex_contribution

                # 计算Gamma Dollar（Gamma × Underlying Price × Contract Size）
                underlying_price = stream_data["market_data"]["underlying_price"]
                if underlying_price > 0:
                    gamma_dollar = gamma * underlying_price * 1.0  # Contract size = 1
                    stream_data["calculated_metrics"]["gamma_dollar"] = gamma_dollar

            return stream_data

        except Exception as e:
            self._logger.error(f"Failed to get Greeks stream data: {e}")
            return None

    async def get_option_chain_data(self, currency: str) -> dict | None:
        """获取期权链数据用于GEX计算

        Args:
            currency: 币种

        Returns:
            dict: 格式化的期权链数据，供Celery任务使用
        """
        try:
            # 获取期权合约列表
            instruments = await self.get_instruments(currency, "option")
            if not instruments:
                return None

            # 获取期权摘要数据（包含Greeks和持仓量）
            summary_data = await self.get_book_summary_by_currency(currency, "option")
            if not summary_data:
                return None

            # 构建期权链数据字典
            option_chain_data = {}
            current_price = await self.get_index_price(currency)

            # 创建合约名称到合约信息的映射
            instrument_map = {inst.get("instrument_name"): inst for inst in instruments}

            # 处理每个期权合约的数据
            for item in summary_data:
                instrument_name = item.get("instrument_name", "")
                if instrument_name not in instrument_map:
                    continue

                instrument = instrument_map[instrument_name]
                strike = float(instrument.get("strike", 0))

                # 提取Greeks和市场数据
                greeks = item.get("greeks", {})
                if not greeks or greeks.get("gamma", 0) == 0:
                    continue

                option_chain_data[str(strike)] = {
                    "instrument_name": instrument_name,
                    "strike": strike,
                    "option_type": instrument.get("option_type", ""),
                    "expiry": instrument.get("expiration_timestamp", 0),
                    "greeks": {
                        "delta": float(greeks.get("delta", 0)),
                        "gamma": float(greeks.get("gamma", 0)),
                        "theta": float(greeks.get("theta", 0)),
                        "vega": float(greeks.get("vega", 0)),
                        "rho": float(greeks.get("rho", 0)),
                    },
                    "open_interest": float(item.get("open_interest", 0)),
                    "contract_size": float(instrument.get("contract_size", 1)),
                    "mark_price": float(item.get("mark_price", 0)),
                    "volume_24h": Decimal(str(item.get("stats", {}).get("volume", 0))),
                }

            self._logger.info(f"获取期权链数据完成，包含{len(option_chain_data)}个合约")

            return {
                "currency": currency,
                "current_price": current_price,
                "timestamp": time.time(),
                "option_data": option_chain_data,
            }

        except Exception as e:
            self._logger.error(f"获取期权链数据失败: {e}")
            return None

    async def analyze_option_liquidity(self, instrument_name: str) -> dict | None:
        """分析期权流动性

        Args:
            instrument_name: 期权合约名称

        Returns:
            dict: 流动性分析结果
        """
        try:
            # 获取订单簿
            orderbook = await self.get_order_book(instrument_name, depth=10)
            if not orderbook:
                return None

            # 获取ticker数据
            ticker_response = await self._send_http_request(
                "GET", "public/ticker", {"instrument_name": instrument_name}
            )

            if not ticker_response:
                return None

            mark_price = float(ticker_response.get("mark_price", 0))
            volume_24h = Decimal(str(ticker_response.get("stats", {}).get("volume", 0)))
            open_interest = float(ticker_response.get("open_interest", 0))

            # 计算买卖价差
            best_bid = float(orderbook.get("best_bid_price", 0))
            best_ask = float(orderbook.get("best_ask_price", 0))

            if best_bid > 0 and best_ask > 0:
                spread = best_ask - best_bid
                spread_ratio = spread / mark_price if mark_price > 0 else 0
            else:
                spread = 0
                spread_ratio = 0

            # 计算市场深度
            bid_depth = sum(float(level[1]) for level in orderbook.get("bids", []))
            ask_depth = sum(float(level[1]) for level in orderbook.get("asks", []))
            total_depth = bid_depth + ask_depth

            # 流动性评分 (0-100)
            liquidity_score = 0

            # 价差评分 (价差越小越好，调整为适合加密货币期权)
            if spread_ratio < 0.05:  # 5%以内（加密期权优秀）
                liquidity_score += 30
            elif spread_ratio < 0.10:  # 10%以内（加密期权良好）
                liquidity_score += 20
            elif spread_ratio < 0.20:  # 20%以内（加密期权可接受）
                liquidity_score += 10

            # 成交量评分
            if volume_24h > 100:
                liquidity_score += 25
            elif volume_24h > 50:
                liquidity_score += 15
            elif volume_24h > 10:
                liquidity_score += 10

            # 持仓量评分
            if open_interest > 500:
                liquidity_score += 25
            elif open_interest > 100:
                liquidity_score += 15
            elif open_interest > 50:
                liquidity_score += 10

            # 市场深度评分
            if total_depth > 10:
                liquidity_score += 20
            elif total_depth > 5:
                liquidity_score += 15
            elif total_depth > 1:
                liquidity_score += 10

            return {
                "instrument_name": instrument_name,
                "mark_price": mark_price,
                "best_bid": best_bid,
                "best_ask": best_ask,
                "spread": spread,
                "spread_ratio": spread_ratio,
                "volume_24h": volume_24h,
                "open_interest": open_interest,
                "bid_depth": bid_depth,
                "ask_depth": ask_depth,
                "total_depth": total_depth,
                "liquidity_score": liquidity_score,
                "is_liquid": liquidity_score >= 40,  # 40分以上认为有足够流动性
            }

        except Exception as e:
            self._logger.error(f"分析期权流动性失败: {e}")
            return None

    async def get_liquid_options(
        self, currency: str, min_score: int = 40
    ) -> list[dict]:
        """获取流动性充足的期权列表

        Args:
            currency: 币种
            min_score: 最低流动性评分

        Returns:
            List[dict]: 流动性充足的期权列表
        """
        try:
            instruments = await self.get_instruments(currency, "option")
            if not instruments:
                return []

            liquid_options = []

            # 批量分析流动性，避免请求过于频繁
            for i, instrument in enumerate(instruments):
                instrument_name = instrument.get("instrument_name", "")

                liquidity_data = await self.analyze_option_liquidity(instrument_name)
                if liquidity_data and liquidity_data["liquidity_score"] >= min_score:
                    liquid_options.append(
                        {**instrument, "liquidity_data": liquidity_data}
                    )

                # 每10个请求后暂停一下，避免请求过快
                if (i + 1) % 10 == 0:
                    await asyncio.sleep(0.1)

            # 按流动性评分排序
            liquid_options.sort(
                key=lambda x: x["liquidity_data"]["liquidity_score"], reverse=True
            )

            self._logger.info(f"找到{len(liquid_options)}个流动性充足的期权")
            return liquid_options

        except Exception as e:
            self._logger.error(f"获取流动性充足期权失败: {e}")
            return []

    async def calculate_margin_requirement(
        self, instrument_name: str, amount: float, side: str
    ) -> dict | None:
        """计算保证金要求

        Args:
            instrument_name: 期权合约名称
            amount: 数量
            side: 方向 ("buy" 或 "sell")

        Returns:
            dict: 保证金要求信息
        """
        try:
            # 获取合约信息
            instrument_data = await self.get_instrument_data(instrument_name)
            if not instrument_data:
                return None

            # 获取当前价格
            ticker_response = await self._send_http_request(
                "GET", "public/ticker", {"instrument_name": instrument_name}
            )

            if not ticker_response:
                return None

            mark_price = float(ticker_response.get("mark_price", 0))
            underlying_price = float(ticker_response.get("underlying_price", 0))

            # 基本参数
            strike = float(instrument_data.get("strike", 0))
            option_type = instrument_data.get("option_type", "")
            contract_size = float(instrument_data.get("contract_size", 1))

            # 计算保证金
            if side == "buy":
                # 买入期权只需要支付权利金
                margin_required = mark_price * amount * contract_size
                margin_type = "premium"
            else:
                # 卖出期权需要保证金
                if option_type == "call":
                    # Call期权保证金计算
                    margin_required = (
                        (
                            mark_price
                            + max(
                                0.1 * underlying_price
                                - max(0, strike - underlying_price),
                                0.1 * underlying_price,
                            )
                        )
                        * amount
                        * contract_size
                    )
                else:
                    # Put期权保证金计算
                    margin_required = (
                        (
                            mark_price
                            + max(
                                0.1 * underlying_price
                                - max(0, underlying_price - strike),
                                0.1 * strike,
                            )
                        )
                        * amount
                        * contract_size
                    )

                margin_type = "initial_margin"

            return {
                "instrument_name": instrument_name,
                "amount": amount,
                "side": side,
                "margin_required": margin_required,
                "margin_type": margin_type,
                "mark_price": mark_price,
                "underlying_price": underlying_price,
                "strike": strike,
                "option_type": option_type,
                "contract_size": contract_size,
            }

        except Exception as e:
            self._logger.error(f"计算保证金要求失败: {e}")
            return None

    async def get_expiring_options(
        self, currency: str, days_ahead: int = 7
    ) -> list[dict]:
        """获取即将到期的期权

        Args:
            currency: 币种
            days_ahead: 提前天数

        Returns:
            List[dict]: 即将到期的期权列表
        """
        try:
            positions = await self.get_positions(currency, "option")
            if not positions:
                return []

            expiring_options = []
            current_timestamp = time.time()
            cutoff_timestamp = current_timestamp + (days_ahead * 24 * 3600)

            for position in positions:
                size = float(position.get("size", 0))
                if size == 0:
                    continue

                instrument_name = position.get("instrument_name", "")

                # 获取合约信息
                instrument_data = await self.get_instrument_data(instrument_name)
                if instrument_data:
                    expiration_timestamp = instrument_data.get(
                        "expiration_timestamp", 0
                    )

                    if expiration_timestamp <= cutoff_timestamp:
                        expiring_options.append(
                            {
                                "instrument_name": instrument_name,
                                "position_size": size,
                                "expiration_timestamp": expiration_timestamp,
                                "days_to_expiry": (
                                    expiration_timestamp - current_timestamp
                                )
                                / (24 * 3600),
                                "position_data": position,
                                "instrument_data": instrument_data,
                            }
                        )

            # 按到期时间排序
            expiring_options.sort(key=lambda x: x["expiration_timestamp"])

            self._logger.info(f"找到{len(expiring_options)}个即将到期的期权持仓")
            return expiring_options

        except Exception as e:
            self._logger.error(f"获取即将到期期权失败: {e}")
            return []

    async def check_exercise_probability(self, instrument_name: str) -> dict | None:
        """检查期权行权概率

        Args:
            instrument_name: 期权合约名称

        Returns:
            dict: 行权概率分析
        """
        try:
            # 获取合约信息
            instrument_data = await self.get_instrument_data(instrument_name)
            if not instrument_data:
                return None

            # 获取当前价格
            ticker_response = await self._send_http_request(
                "GET", "public/ticker", {"instrument_name": instrument_name}
            )

            if not ticker_response:
                return None

            underlying_price = float(ticker_response.get("underlying_price", 0))
            mark_price = float(ticker_response.get("mark_price", 0))

            strike = float(instrument_data.get("strike", 0))
            option_type = instrument_data.get("option_type", "")
            expiration_timestamp = instrument_data.get("expiration_timestamp", 0)

            # 计算内在价值
            if option_type == "call":
                intrinsic_value = max(0, underlying_price - strike)
                is_itm = underlying_price > strike
                moneyness = underlying_price / strike
            else:
                intrinsic_value = max(0, strike - underlying_price)
                is_itm = underlying_price < strike
                moneyness = strike / underlying_price

            # 计算时间价值
            time_value = mark_price - intrinsic_value

            # 计算到期时间
            current_time = time.time()
            time_to_expiry = (expiration_timestamp - current_time) / (24 * 3600)  # 天数

            # 估算行权概率
            if is_itm:
                if time_to_expiry < 1:  # 1天内到期
                    exercise_probability = 0.9 if intrinsic_value > 0.001 else 0.1
                elif time_to_expiry < 7:  # 1周内到期
                    exercise_probability = 0.7 if moneyness > 1.02 else 0.3
                else:
                    exercise_probability = 0.5 if moneyness > 1.05 else 0.2
            else:
                exercise_probability = 0.05  # OTM期权行权概率很低

            return {
                "instrument_name": instrument_name,
                "underlying_price": underlying_price,
                "strike": strike,
                "option_type": option_type,
                "mark_price": mark_price,
                "intrinsic_value": intrinsic_value,
                "time_value": time_value,
                "is_itm": is_itm,
                "moneyness": moneyness,
                "time_to_expiry": time_to_expiry,
                "exercise_probability": exercise_probability,
                "should_prepare_funds": exercise_probability > 0.5,
            }

        except Exception as e:
            self._logger.error(f"检查行权概率失败: {e}")
            return None
