"""
数据同步器模块
实现跨交易所数据时间对齐和质量控制
"""

import asyncio
import contextlib
import logging
import statistics
from collections import deque
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import Any

from src.analysis.common_types import DataQuality
from src.analysis.option_surface_aggregator import (
    OptionSurfaceAggregator,
    SurfaceConfig,
)
from src.core.base_component import BaseComponent
from src.core.config_manager import ConfigManager
from src.core.event_bus import DataUpdateEvent, EventBus

logger = logging.getLogger(__name__)


@dataclass
class SyncedData:
    """同步后的数据"""

    timestamp: datetime
    binance_data: dict[str, Any] | None = None
    deribit_data: dict[str, Any] | None = None
    quality: DataQuality = DataQuality.HIGH
    latency_ms: float = 0.0
    interpolated: bool = False
    metadata: dict[str, Any] = field(default_factory=dict)


@dataclass
class DataBuffer:
    """数据缓冲区"""

    exchange: str
    symbol: str
    data: deque = field(default_factory=lambda: deque(maxlen=1000))
    last_update: datetime | None = None

    def add_data(self, timestamp: datetime, data: dict[str, Any]):
        """添加数据到缓冲区"""
        self.data.append((timestamp, data))
        self.last_update = datetime.now(UTC)

    def get_data_at_time(
        self, target_time: datetime, tolerance_ms: int = 200
    ) -> tuple[datetime, dict[str, Any]] | None:
        """获取指定时间附近的数据"""
        tolerance_seconds = tolerance_ms / 1000.0
        best_match = None
        min_diff = float("inf")

        for timestamp, data in self.data:
            diff = abs((target_time - timestamp).total_seconds())
            if diff <= tolerance_seconds and diff < min_diff:
                min_diff = diff
                best_match = (timestamp, data)

        return best_match


@dataclass
class SyncMetrics:
    """同步指标"""

    total_synced: int = 0
    high_quality_count: int = 0
    medium_quality_count: int = 0
    low_quality_count: int = 0
    stale_count: int = 0
    interpolated_count: int = 0
    avg_latency_ms: float = 0.0
    max_latency_ms: float = 0.0
    sync_success_rate: float = 0.0

    @property
    def quality_distribution(self) -> dict[str, float]:
        """质量分布百分比"""
        total = self.total_synced
        if total == 0:
            return {quality.value: 0.0 for quality in DataQuality}

        return {
            DataQuality.HIGH.value: self.high_quality_count / total * 100,
            DataQuality.MEDIUM.value: self.medium_quality_count / total * 100,
            DataQuality.LOW.value: self.low_quality_count / total * 100,
            DataQuality.STALE.value: self.stale_count / total * 100,
        }


class DataSynchronizer(BaseComponent):
    """
    数据同步器
    负责跨交易所数据的时间对齐和质量控制
    """

    # 类型注解 - 供依赖注入器使用
    config_manager: ConfigManager | None
    event_bus: EventBus | None

    def __init__(
        self,
        config_manager: ConfigManager | None = None,
        event_bus: EventBus | None = None,
    ):
        super().__init__("data_synchronizer")

        # 基础设施组件
        self.config_manager = config_manager
        self.event_bus = event_bus

        # 数据缓冲区
        self.buffers: dict[str, DataBuffer] = {}

        # 同步配置
        self.sync_tolerance_ms = 200  # ±200ms延迟容忍
        self.sync_window_ms = 1000  # 1秒同步窗口
        self.buffer_size = 1000  # 缓冲区大小

        # 新增：按时间对齐模式（by_time）支持（仅使用统一配置）
        try:
            sec = (
                self.config_manager.get_section("data_synchronizer")
                if self.config_manager
                else {}
            )
            self.alignment_mode = str(sec.get("alignment_mode", "by_time")).lower()
        except Exception:
            self.alignment_mode = "by_time"
        # by_time 模式所需的通道缓冲与现货价
        self.channel_buffers: dict[str, DataBuffer] = {}
        self._last_spot_price: float | None = None
        self._last_spot_ts: datetime | None = None
        # 期权面聚合器（按桶聚合IV/OI/Greeks）
        self._surface_agg = OptionSurfaceAggregator(self._load_surface_config())

        # 自适应参数与质量/插值配置
        self.adaptive_window = True
        self.min_window_ms = 500
        self.max_window_ms = 2000
        self.window_adjustment_factor = 0.1

        self.quality_thresholds = {
            DataQuality.HIGH: 50,  # <50ms
            DataQuality.MEDIUM: 200,  # 50-200ms
            DataQuality.LOW: 500,  # 200-500ms
            DataQuality.STALE: float("inf"),  # >500ms
        }

        self.enable_interpolation = True
        self.max_interpolation_gap_ms = 30000  # 30秒最大插值间隔

        # 回调函数
        self.sync_callbacks: list[Callable[[SyncedData], None]] = []
        self.quality_callbacks: list[Callable[[DataQuality, dict], None]] = []

        # 性能监控（避免与 BaseComponent.metrics 冲突）
        self.sync_metrics = SyncMetrics()
        self.latency_history = deque(maxlen=1000)

        # 订阅去重：记录最近处理的事件ID，防止重复处理
        self._seen_event_ids: set[str] = set()
        self._seen_event_queue: deque[str] = deque(maxlen=5000)

        # 同步任务
        self.sync_task: asyncio.Task | None = None
        self.sync_interval = 0.1  # 100ms同步间隔（by_symbol默认）

    def _load_surface_config(self) -> SurfaceConfig:
        cfg = {}
        try:
            if hasattr(self, "config") and isinstance(self.config, dict):
                cfg = self.config.get("option_surface", {}) or {}
        except Exception:
            pass
        try:
            if self.config_manager:
                sec = self.config_manager.get_section("option_surface")
                if isinstance(sec, dict):
                    cfg.update(sec)
        except Exception:
            pass
        expiry_days = tuple(cfg.get("expiry_days", (7, 30, 90)))
        use_delta = bool(cfg.get("use_delta_buckets", True))
        delta_breaks = tuple(cfg.get("delta_breaks", (0.1, 0.25, 0.4)))
        ks_band = float(cfg.get("ks_atm_band", 0.03))
        weight = str(cfg.get("weight", "oi")).lower()
        return SurfaceConfig(
            expiry_days=expiry_days,
            use_delta_buckets=use_delta,
            delta_breaks=delta_breaks,
            ks_atm_band=ks_band,
            weight=weight,
        )

    def set_event_bus(self, event_bus):
        """设置事件总线"""
        self.event_bus = event_bus

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        logger.info("DataSynchronizer初始化完成")
        # 注册配置热更新：alignment_mode
        try:
            if self.config_manager and hasattr(
                self.config_manager, "register_change_callback"
            ):
                self.config_manager.register_change_callback(self._on_config_changes)
        except Exception:
            pass
        return True

    async def _start_impl(self) -> bool:
        """启动实现"""
        # 启动同步器

        # 订阅EventBus的数据更新事件
        if self.event_bus:
            from src.core.event_bus import EventType

            await self.event_bus.subscribe(
                event_types={EventType.DATA_UPDATE},
                callback=self._handle_data_update,
                subscriber_id="data_synchronizer",
            )
            logger.info("DataSynchronizer已订阅DataUpdateEvent事件")

        # 设置同步间隔
        if self.alignment_mode == "by_time":
            self.sync_interval = self.sync_window_ms / 1000.0  # e.g. 1秒
        else:
            self.sync_interval = 0.1

        # 启动同步任务
        self.sync_task = asyncio.create_task(self._sync_loop())
        logger.info("DataSynchronizer启动成功")
        # 启动完成
        return True

    async def _on_config_changes(self, changes):
        """处理配置热更新：关注 data_synchronizer.alignment_mode 变更"""
        try:
            for change in changes:
                path = str(getattr(change, "config_path", ""))
                if path == "data_synchronizer.alignment_mode":
                    old = self.alignment_mode
                    self.alignment_mode = str(change.new_value).lower()
                    if self.alignment_mode == "by_time":
                        self.sync_interval = self.sync_window_ms / 1000.0
                    else:
                        self.sync_interval = 0.1
                    logger.info(
                        f"DataSynchronizer hot-updated alignment_mode: {old} -> {self.alignment_mode}, "
                        f"sync_interval={self.sync_interval}s"
                    )
                    from contextlib import suppress

                    with suppress(Exception):
                        self.metrics.custom_metrics["config_hot_updates"] = (
                            self.metrics.custom_metrics.get("config_hot_updates", 0) + 1
                        )
        except Exception as e:
            logger.error(f"DataSynchronizer hot-update handler error: {e}")

    async def _stop_impl(self) -> bool:
        """停止实现"""
        if self.sync_task and not self.sync_task.done():
            self.sync_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self.sync_task
        logger.info("DataSynchronizer已停止")
        return True

    async def _health_check_impl(self) -> dict[str, Any]:
        """健康检查实现"""
        return {
            "buffer_count": len(self.buffers),
            "sync_tolerance_ms": self.sync_tolerance_ms,
            "sync_window_ms": self.sync_window_ms,
            "total_synced": self.sync_metrics.total_synced,
            "avg_latency_ms": self.sync_metrics.avg_latency_ms,
            "sync_success_rate": self.sync_metrics.sync_success_rate,
            "quality_distribution": self.sync_metrics.quality_distribution,
        }

    def register_data_source(self, exchange: str, symbol: str) -> None:
        """注册数据源"""
        buffer_key = f"{exchange}:{symbol}"
        if buffer_key not in self.buffers:
            self.buffers[buffer_key] = DataBuffer(exchange=exchange, symbol=symbol)
            logger.info(f"注册数据源: {buffer_key}")

    def add_data(
        self, exchange: str, symbol: str, timestamp: datetime, data: dict[str, Any]
    ) -> None:
        """添加数据到缓冲区"""
        buffer_key = f"{exchange}:{symbol}"

        if buffer_key not in self.buffers:
            self.register_data_source(exchange, symbol)

        self.buffers[buffer_key].add_data(timestamp, data)

    async def _handle_data_update(self, event: DataUpdateEvent) -> None:
        """处理数据更新事件"""
        try:
            # 去重：基于事件ID
            evt_id = getattr(event, "event_id", None)
            if evt_id:
                if evt_id in self._seen_event_ids:
                    return
                self._seen_event_ids.add(evt_id)
                self._seen_event_queue.append(evt_id)
                # 控制集合大小
                if len(self._seen_event_ids) > 6000:
                    while len(self._seen_event_queue) > 5000:
                        old = self._seen_event_queue.popleft()
                        self._seen_event_ids.discard(old)

            # 从事件中提取数据
            symbol = event.data.get("symbol", "")
            timestamp_str = event.data.get("timestamp", "")

            if not symbol or not timestamp_str:
                logger.warning(
                    f"DataUpdateEvent缺少必要字段: symbol={symbol}, timestamp={timestamp_str}"
                )
                return

            # 解析时间戳
            if isinstance(timestamp_str, str):
                timestamp = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
            else:
                timestamp = datetime.now(UTC)

            # 使用事件中的exchange字段
            exchange = event.exchange.lower()

            # 调用已有的add_data方法
            self.add_data(exchange, symbol, timestamp, event.data)

            # by_time 模式维护现货价与期权聚合
            if self.alignment_mode == "by_time":
                if event.data_type == "ticker" and exchange == "binance":
                    pr = event.data.get("price")
                    if pr is not None:
                        try:
                            self._last_spot_price = float(pr)
                            self._last_spot_ts = timestamp
                        except Exception:
                            pass
                if event.data_type == "option" and exchange == "deribit":
                    try:
                        self._surface_agg.update_option(
                            option_event=event.data,
                            event_ts=timestamp,
                            spot_price=self._last_spot_price,
                        )
                    except Exception as e:
                        logger.error(f"期权聚合更新失败: {e}")

            logger.debug(f"DataSynchronizer收到数据: {exchange}:{symbol} @ {timestamp}")

        except Exception as e:
            logger.error(f"处理DataUpdateEvent失败: {e}")

    async def _sync_loop(self) -> None:
        """同步循环"""
        while self.is_running:
            try:
                await self._perform_sync()
                await asyncio.sleep(self.sync_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"同步循环错误: {e}")
                await asyncio.sleep(1)

    async def _perform_sync(self) -> None:
        """执行数据同步"""
        if self.alignment_mode == "by_time":
            await self._sync_by_time()
            return
        current_time = datetime.now(UTC)
        # 同步稍早的时间点，给数据到达留出时间
        sync_time = current_time - timedelta(milliseconds=500)

        # 按symbol分组同步
        symbols = set()
        for buffer_key in self.buffers:
            _, symbol = buffer_key.split(":", 1)
            symbols.add(symbol)

        for symbol in symbols:
            await self._sync_symbol_data(symbol, sync_time)

    async def _sync_by_time(self) -> None:
        """按时间对齐 - 聚合现货与期权桶特征并发布 FeaturesEvent"""
        try:
            sync_time = datetime.now(UTC)
            # 现货特征
            spot = None
            if self._last_spot_price is not None and self._last_spot_ts is not None:
                spot = {
                    "price": self._last_spot_price,
                    "timestamp": self._last_spot_ts.isoformat(),
                }
            # 期权聚合
            option_features = self._surface_agg.snapshot_features(
                spot_price=self._last_spot_price or 0.0
            )
            features = {"spot": spot or {}, "options": option_features}
            # 发布
            if self.event_bus:
                from src.core.event_bus import EventType, FeaturesEvent

                await self.event_bus.publish(
                    FeaturesEvent(
                        event_type=EventType.FEATURES,
                        features=features,
                        source=self.component_name,
                        timestamp=sync_time,
                    )
                )
        except Exception as e:
            logger.error(f"按时间对齐失败: {e}")

    async def _sync_symbol_data(self, symbol: str, sync_time: datetime) -> None:
        """同步特定symbol的数据"""
        binance_key = f"binance:{symbol}"
        deribit_key = f"deribit:{symbol}"

        binance_buffer = self.buffers.get(binance_key)
        deribit_buffer = self.buffers.get(deribit_key)

        if not binance_buffer and not deribit_buffer:
            return

        # 获取同步时间点的数据
        binance_data = None
        deribit_data = None
        max_latency = 0.0

        if binance_buffer:
            binance_match = binance_buffer.get_data_at_time(
                sync_time, self.sync_tolerance_ms
            )
            if binance_match:
                timestamp, data = binance_match
                latency = abs((sync_time - timestamp).total_seconds() * 1000)
                max_latency = max(max_latency, latency)
                binance_data = data

        if deribit_buffer:
            deribit_match = deribit_buffer.get_data_at_time(
                sync_time, self.sync_tolerance_ms
            )
            if deribit_match:
                timestamp, data = deribit_match
                latency = abs((sync_time - timestamp).total_seconds() * 1000)
                max_latency = max(max_latency, latency)
                deribit_data = data

        # 如果没有数据，尝试插值
        was_interpolated = False
        if not binance_data and not deribit_data and self.enable_interpolation:
            (
                binance_data,
                deribit_data,
                was_interpolated,
            ) = await self._interpolate_missing_data(
                symbol, sync_time, binance_buffer, deribit_buffer
            )
            if was_interpolated:
                self.sync_metrics.interpolated_count += 1

        # 如果仍然没有数据，跳过
        if not binance_data and not deribit_data:
            return

        # 确定数据质量
        quality = self._determine_quality(max_latency)

        # 创建同步数据
        synced_data = SyncedData(
            timestamp=sync_time,
            binance_data=binance_data,
            deribit_data=deribit_data,
            quality=quality,
            latency_ms=max_latency,
            interpolated=was_interpolated,
        )

        # 更新指标
        self._update_metrics(synced_data)

        # 自适应窗口调整
        if self.adaptive_window:
            await self._adjust_sync_window(max_latency)

        # 调用回调函数
        await self._notify_callbacks(synced_data)

    def _determine_quality(self, latency_ms: float) -> DataQuality:
        """确定数据质量"""
        if latency_ms < self.quality_thresholds[DataQuality.HIGH]:
            return DataQuality.HIGH
        elif latency_ms < self.quality_thresholds[DataQuality.MEDIUM]:
            return DataQuality.MEDIUM
        elif latency_ms < self.quality_thresholds[DataQuality.LOW]:
            return DataQuality.LOW
        else:
            return DataQuality.STALE

    async def _interpolate_missing_data(
        self,
        symbol: str,
        target_time: datetime,
        binance_buffer: DataBuffer | None,
        deribit_buffer: DataBuffer | None,
    ) -> tuple[dict | None, dict | None, bool]:
        """插值缺失数据，返回(两侧数据, 是否发生插值)"""
        binance_data = None
        deribit_data = None
        used_interpolation = False

        if binance_buffer:
            binance_data = await self._interpolate_buffer_data(
                binance_buffer, target_time
            )
            if binance_data is not None:
                used_interpolation = True

        if deribit_buffer:
            deribit_data = await self._interpolate_buffer_data(
                deribit_buffer, target_time
            )
            if deribit_data is not None:
                used_interpolation = True

        return binance_data, deribit_data, used_interpolation

    async def _interpolate_buffer_data(
        self, buffer: DataBuffer, target_time: datetime
    ) -> dict[str, Any] | None:
        """插值缓冲区数据"""
        if len(buffer.data) < 2:
            return None

        # 找到目标时间前后的数据点
        before_data = None
        after_data = None

        for timestamp, data in buffer.data:
            if timestamp <= target_time:
                before_data = (timestamp, data)
            elif timestamp > target_time and after_data is None:
                after_data = (timestamp, data)
                break

        if not before_data or not after_data:
            return None

        before_time, before_values = before_data
        after_time, after_values = after_data

        # 检查时间间隔是否在允许范围内
        time_gap = (after_time - before_time).total_seconds() * 1000
        if time_gap > self.max_interpolation_gap_ms:
            return None

        # 线性插值
        ratio = (target_time - before_time).total_seconds() / (
            after_time - before_time
        ).total_seconds()

        interpolated_data = {}
        for key in before_values:
            if key in after_values and isinstance(
                before_values[key], int | float | Decimal
            ):
                before_val = float(before_values[key])
                after_val = float(after_values[key])
                interpolated_val = before_val + (after_val - before_val) * ratio
                interpolated_data[key] = interpolated_val
            else:
                # 非数值数据使用最近的值
                interpolated_data[key] = before_values[key]

        return interpolated_data

    async def _adjust_sync_window(self, current_latency: float) -> None:
        """自适应调整同步窗口"""
        if not self.adaptive_window:
            return

        # 基于延迟调整窗口大小
        if current_latency > self.sync_tolerance_ms:
            # 延迟过高，增加窗口
            adjustment = self.sync_window_ms * self.window_adjustment_factor
            self.sync_window_ms = min(
                self.max_window_ms, self.sync_window_ms + adjustment
            )
        elif current_latency < self.sync_tolerance_ms * 0.5:
            # 延迟很低，减少窗口
            adjustment = self.sync_window_ms * self.window_adjustment_factor
            self.sync_window_ms = max(
                self.min_window_ms, self.sync_window_ms - adjustment
            )

    def _update_metrics(self, synced_data: SyncedData) -> None:
        """更新同步指标"""
        self.sync_metrics.total_synced += 1

        # 更新质量计数
        if synced_data.quality == DataQuality.HIGH:
            self.sync_metrics.high_quality_count += 1
        elif synced_data.quality == DataQuality.MEDIUM:
            self.sync_metrics.medium_quality_count += 1
        elif synced_data.quality == DataQuality.LOW:
            self.sync_metrics.low_quality_count += 1
        else:
            self.sync_metrics.stale_count += 1

        # 更新延迟统计
        self.latency_history.append(synced_data.latency_ms)
        if self.latency_history:
            self.sync_metrics.avg_latency_ms = statistics.mean(self.latency_history)
            self.sync_metrics.max_latency_ms = max(self.latency_history)

        # 更新成功率
        success_count = (
            self.sync_metrics.high_quality_count
            + self.sync_metrics.medium_quality_count
            + self.sync_metrics.low_quality_count
        )
        self.sync_metrics.sync_success_rate = (
            success_count / self.sync_metrics.total_synced * 100
        )

    async def _notify_callbacks(self, synced_data: SyncedData) -> None:
        """通知回调函数"""
        # 同步数据回调
        for callback in self.sync_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(synced_data)
                else:
                    callback(synced_data)
            except Exception as e:
                logger.error(f"同步回调执行失败: {e}")

        # 质量告警回调
        if synced_data.quality in [DataQuality.LOW, DataQuality.STALE]:
            quality_info = {
                "symbol": "unknown",  # 可以从metadata中获取
                "latency_ms": synced_data.latency_ms,
                "timestamp": synced_data.timestamp,
            }

            for callback in self.quality_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(synced_data.quality, quality_info)
                    else:
                        callback(synced_data.quality, quality_info)
                except Exception as e:
                    logger.error(f"质量回调执行失败: {e}")

    def add_sync_callback(self, callback: Callable[[SyncedData], None]) -> None:
        """添加同步数据回调"""
        self.sync_callbacks.append(callback)
        logger.info(
            f"添加同步回调: {callback.__name__ if hasattr(callback, '__name__') else 'anonymous'}"
        )

    def add_quality_callback(
        self, callback: Callable[[DataQuality, dict], None]
    ) -> None:
        """添加数据质量回调"""
        self.quality_callbacks.append(callback)
        logger.info(
            f"添加质量回调: {callback.__name__ if hasattr(callback, '__name__') else 'anonymous'}"
        )

    def remove_sync_callback(self, callback: Callable[[SyncedData], None]) -> None:
        """移除同步数据回调"""
        if callback in self.sync_callbacks:
            self.sync_callbacks.remove(callback)

    def remove_quality_callback(
        self, callback: Callable[[DataQuality, dict], None]
    ) -> None:
        """移除数据质量回调"""
        if callback in self.quality_callbacks:
            self.quality_callbacks.remove(callback)

    def get_sync_statistics(self) -> dict[str, Any]:
        """获取同步统计信息"""
        return {
            "metrics": {
                "total_synced": self.sync_metrics.total_synced,
                "avg_latency_ms": self.sync_metrics.avg_latency_ms,
                "max_latency_ms": self.sync_metrics.max_latency_ms,
                "sync_success_rate": self.sync_metrics.sync_success_rate,
                "interpolated_count": self.sync_metrics.interpolated_count,
            },
            "quality_distribution": self.sync_metrics.quality_distribution,
            "configuration": {
                "sync_tolerance_ms": self.sync_tolerance_ms,
                "sync_window_ms": self.sync_window_ms,
                "adaptive_window": self.adaptive_window,
                "enable_interpolation": self.enable_interpolation,
            },
            "buffer_status": {
                buffer_key: {
                    "size": len(buffer.data),
                    "last_update": buffer.last_update.isoformat()
                    if buffer.last_update
                    else None,
                }
                for buffer_key, buffer in self.buffers.items()
            },
        }

    def configure_sync_parameters(
        self,
        tolerance_ms: int | None = None,
        window_ms: int | None = None,
        adaptive: bool | None = None,
        interpolation: bool | None = None,
    ) -> None:
        """配置同步参数"""
        if tolerance_ms is not None:
            self.sync_tolerance_ms = tolerance_ms
            logger.info(f"同步容忍度设置为: {tolerance_ms}ms")

        if window_ms is not None:
            self.sync_window_ms = window_ms
            logger.info(f"同步窗口设置为: {window_ms}ms")

        if adaptive is not None:
            self.adaptive_window = adaptive
            logger.info(f"自适应窗口: {'启用' if adaptive else '禁用'}")

        if interpolation is not None:
            self.enable_interpolation = interpolation
            logger.info(f"数据插值: {'启用' if interpolation else '禁用'}")

    async def force_sync(
        self, symbol: str, timestamp: datetime | None = None
    ) -> SyncedData | None:
        """强制同步指定symbol的数据"""
        if timestamp is None:
            timestamp = datetime.now(UTC)

        await self._sync_symbol_data(symbol, timestamp)

        # 返回最近的同步结果（这里简化处理）
        return None

    def clear_buffers(self, symbol: str | None = None) -> None:
        """清空缓冲区"""
        if symbol:
            # 清空特定symbol的缓冲区
            keys_to_clear = [key for key in self.buffers if key.endswith(f":{symbol}")]
            for key in keys_to_clear:
                self.buffers[key].data.clear()
                logger.info(f"清空缓冲区: {key}")
        else:
            # 清空所有缓冲区
            for buffer in self.buffers.values():
                buffer.data.clear()
            logger.info("清空所有缓冲区")


class _OptionSurfaceAggregator:
    """期权面聚合器：按到期桶×delta/moneyness 桶聚合IV/OI/Greeks。"""

    def __init__(self):
        self._inst: dict[str, dict] = {}
        self._buckets: dict[tuple, dict] = {}
        self._prev_snapshot: dict[tuple, dict] = {}

    def _expiry_bucket(self, expiry: datetime, now: datetime) -> str:
        days = (expiry - now).total_seconds() / 86400.0
        if days <= 7:
            return "near"
        if days <= 30:
            return "mid"
        if days <= 90:
            return "far"
        return "ultra"

    def _moneyness_bucket(
        self,
        option_type: str,
        strike: float | None,
        spot_price: float | None,
        delta: float | None,
    ) -> str:
        try:
            if delta is not None:
                d = float(delta)
                if option_type.lower().startswith("c"):
                    if d >= 0.4:
                        return "call_atm"
                    elif d >= 0.25:
                        return "call_25d"
                    else:
                        return "call_10d"
                else:
                    if d <= -0.4:
                        return "put_atm"
                    elif d <= -0.25:
                        return "put_25d"
                    else:
                        return "put_10d"
        except Exception:
            pass
        try:
            if spot_price and strike:
                ks = float(strike) / float(spot_price)
                if option_type.lower().startswith("c"):
                    if 0.97 <= ks <= 1.03:
                        return "call_atm"
                    elif ks < 0.97:
                        return "call_itm"
                    else:
                        return "call_otm"
                else:
                    if 0.97 <= ks <= 1.03:
                        return "put_atm"
                    elif ks > 1.03:
                        return "put_itm"
                    else:
                        return "put_otm"
        except Exception:
            pass
        return "unknown"

    def _bucket_id(self, expiry_bucket: str, m_bucket: str) -> tuple:
        return (expiry_bucket, m_bucket)

    def update_option(
        self, option_event: dict, event_ts: datetime, spot_price: float | None
    ) -> None:
        inst = option_event.get("instrument_name") or option_event.get("symbol")
        if not inst:
            return
        opt_type = option_event.get("option_type") or (
            "call"
            if str(inst).endswith("-C")
            else ("put" if str(inst).endswith("-P") else "?")
        )
        strike = option_event.get("strike")
        expiry = event_ts
        try:
            exp_s = option_event.get("expiry")
            if isinstance(exp_s, str):
                from datetime import datetime as _dt

                for fmt in ("%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S", "%Y-%m-%d"):
                    try:
                        expiry = _dt.strptime(exp_s, fmt).replace(tzinfo=UTC)
                        break
                    except Exception:
                        continue
        except Exception:
            pass

        iv = option_event.get("iv") or option_event.get("mark_iv")
        delta = option_event.get("delta") or option_event.get("greeks", {}).get("delta")
        gamma = option_event.get("gamma") or option_event.get("greeks", {}).get("gamma")
        vega = option_event.get("vega") or option_event.get("greeks", {}).get("vega")
        mark = option_event.get("mark_price") or option_event.get("last_price")
        oi = option_event.get("open_interest") or 0

        now = event_ts
        e_bucket = self._expiry_bucket(expiry, now)
        m_bucket = self._moneyness_bucket(opt_type, strike, spot_price, delta)
        b_id = self._bucket_id(e_bucket, m_bucket)

        old = self._inst.get(inst)
        if old is not None and old.get("bucket") != b_id:
            self._apply_contribution(old, negate=True)

        st = {
            "bucket": b_id,
            "iv": _to_float(iv),
            "delta": _to_float(delta),
            "gamma": _to_float(gamma),
            "vega": _to_float(vega),
            "mark": _to_float(mark),
            "oi": float(oi) if oi is not None else 0.0,
            "spot": float(spot_price) if spot_price is not None else 0.0,
        }
        self._inst[inst] = st
        self._apply_contribution(st, negate=False)

    def _apply_contribution(self, st: dict, negate: bool) -> None:
        b_id = st["bucket"]
        agg = self._buckets.get(b_id)
        if agg is None:
            agg = {
                "w": 0.0,
                "ivw": 0.0,
                "oi": 0.0,
                "d_exp": 0.0,
                "g_exp": 0.0,
                "v_exp": 0.0,
                "mw": 0.0,
            }
            self._buckets[b_id] = agg
        sgn = -1.0 if negate else 1.0
        w = float(st.get("oi") or 0.0)
        agg["w"] += sgn * w
        agg["ivw"] += sgn * w * (st.get("iv") or 0.0)
        agg["oi"] += sgn * w
        agg["d_exp"] += sgn * (st.get("delta") or 0.0) * w
        agg["g_exp"] += (
            sgn * (st.get("gamma") or 0.0) * (st.get("spot") or 0.0) ** 2 * w
        )
        agg["v_exp"] += sgn * (st.get("vega") or 0.0) * w
        agg["mw"] += sgn * w * (st.get("mark") or 0.0)

    def snapshot_features(self, spot_price: float) -> dict:
        snap: dict[tuple, dict] = {}
        out: dict[str, dict] = {}
        for b_id, agg in self._buckets.items():
            w = max(agg.get("w", 0.0), 0.0)
            iv_vwap = (agg.get("ivw", 0.0) / w) if w > 0 else 0.0
            mark_vwap = (agg.get("mw", 0.0) / w) if w > 0 else 0.0
            cur = {
                "iv_vwap": iv_vwap,
                "oi_sum": agg.get("oi", 0.0),
                "delta_exposure": agg.get("d_exp", 0.0),
                "gamma_exposure": agg.get("g_exp", 0.0),
                "vega_exposure": agg.get("v_exp", 0.0),
                "mark_vwap": mark_vwap,
            }
            prev = self._prev_snapshot.get(b_id)
            if prev:
                cur["d_iv"] = cur["iv_vwap"] - prev.get("iv_vwap", 0.0)
                cur["d_oi"] = cur["oi_sum"] - prev.get("oi_sum", 0.0)
                cur["d_gex"] = cur["gamma_exposure"] - prev.get("gamma_exposure", 0.0)
                cur["d_vega"] = cur["vega_exposure"] - prev.get("vega_exposure", 0.0)
            else:
                cur["d_iv"] = 0.0
                cur["d_oi"] = 0.0
                cur["d_gex"] = 0.0
                cur["d_vega"] = 0.0
            snap[b_id] = cur
            key = f"{b_id[0]}_{b_id[1]}"
            out[key] = cur
        self._prev_snapshot = snap
        return out


def _to_float(x) -> float:
    try:
        if x is None:
            return 0.0
        return float(x)
    except Exception:
        return 0.0
