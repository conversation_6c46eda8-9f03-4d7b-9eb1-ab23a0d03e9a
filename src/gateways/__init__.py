"""
API网关层 - API Gateway Layer

统一管理外部API接入，提供数据标准化和错误处理：
- BinanceClient: Binance WebSocket连接管理
- DeribitClient: Deribit WebSocket连接管理
- DataSynchronizer: 多源数据时间同步
"""

# 导出所有网关客户端
from .binance_client import BinanceClient as BinanceClient
from .data_synchronizer import DataSynchronizer as DataSynchronizer
from .deribit_client import DeribitClient as DeribitClient
from .ibkr_client import IBKRClient as IBKRClient
from .ibkr_client import OrderSide as OrderSide
from .ibkr_client import OrderType as OrderType
