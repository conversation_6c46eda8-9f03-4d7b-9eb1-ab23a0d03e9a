"""
Binance WebSocket客户端模块
实现现货、永续合约数据订阅和交易执行功能
"""

import asyncio
import contextlib
import hashlib
import hmac
import json
import logging
import time
from collections import deque
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from typing import Any
from urllib.parse import urlencode

import aiohttp
import websockets

from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)
from src.core.celery_manager import CeleryTaskManager
from src.core.config_manager import ConfigManager
from src.core.event_bus import DataUpdateEvent, EventBus
from src.data.cache_manager import CacheManager
from src.utils.data_converter import safe_float_conversion

logger = logging.getLogger(__name__)


@dataclass
class BinanceMarketData:
    """Binance市场数据模型"""

    symbol: str
    price: Decimal
    volume: Decimal
    timestamp: datetime
    exchange: str = "binance"
    metadata: dict[str, Any] = field(default_factory=dict)


@dataclass
class BinanceTrade:
    """Binance交易数据模型"""

    symbol: str
    price: Decimal
    quantity: Decimal
    is_buyer_maker: bool
    timestamp: datetime
    trade_id: int


@dataclass
class BinanceOrderBook:
    """Binance订单簿数据模型"""

    symbol: str
    bids: list[list[str]]
    asks: list[list[str]]
    timestamp: datetime


@dataclass
class BinanceOrder:
    """Binance订单数据模型"""

    symbol: str
    order_id: int
    client_order_id: str
    side: str
    order_type: str
    quantity: Decimal
    price: Decimal | None
    status: str
    executed_qty: Decimal
    cumulative_quote_qty: Decimal
    timestamp: datetime


@dataclass
class RateLimitInfo:
    """限频信息"""

    limit_type: str
    interval: str
    limit: int
    count: int


class BinanceClient(BaseComponent):
    """
    Binance WebSocket客户端
    负责连接Binance WebSocket API，获取现货和永续合约数据
    """

    # 类型注解 - 供依赖注入器使用
    event_bus: EventBus | None
    config_manager: ConfigManager | None
    cache_manager: CacheManager | None

    def __init__(
        self,
        api_key: str | None = None,
        api_secret: str | None = None,
        celery_manager: CeleryTaskManager | None = None,
        config_manager: ConfigManager | None = None,
        cache_manager: CacheManager | None = None,
        event_bus: EventBus | None = None,
    ):
        super().__init__("binance_client")

        # API认证信息 - 从环境变量获取
        import os

        if not api_key:
            api_key = os.getenv("BINANCE_API_KEY")
        if not api_secret:
            api_secret = os.getenv("BINANCE_API_SECRET")

        self.api_key = api_key
        self.api_secret = api_secret

        # 公共模式检测 - 类似DeribitClient的逻辑
        self._public_mode = False
        if not self.api_key or not self.api_secret:
            self._public_mode = True
            logger.info("BinanceClient 运行在公共模式，仅支持市场数据获取")
        else:
            logger.info("BinanceClient 运行在认证模式，支持完整功能")

        # 基础设施组件
        self.celery_manager = celery_manager
        self.config_manager = config_manager
        self.event_bus = event_bus

        # WebSocket连接配置 - 基于连接测试结果，使用可工作的端点
        self.spot_ws_urls = [
            "wss://stream.binance.com:443/ws",  # 测试证明443端口可用
        ]
        self.futures_ws_urls = [
            "wss://fstream.binance.com/ws",  # 只保留主要期货端点
        ]
        # 当前使用的端点索引
        self._spot_url_index = 0
        self._futures_url_index = 0
        self.rest_api_url = "https://api.binance.com"
        self.futures_api_url = "https://fapi.binance.com"

        # 连接管理
        self.spot_ws = None
        self.futures_ws = None
        self.session = None
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 2  # 进一步减少重连次数，避免频率限制
        self._reconnect_delay = 120.0  # 增加重连延迟到2分钟，避免频率限制
        self._last_reconnect_reset = time.time()  # 记录上次重置时间
        self._rate_limit_backoff = 600.0  # 频率限制后的退避时间（10分钟）
        self._last_rate_limit_time = 0  # 记录上次rate limit时间
        self._reconnect_lock = asyncio.Lock()
        self._is_reconnecting = False
        self._last_reconnect_time = 0  # 记录上次重连时间
        self._connection_ban_time = 0  # 记录被禁止连接的时间
        self._connection_start_time = 0  # 记录连接开始时间，用于24小时断开检测

        # 订阅管理
        self.subscribed_streams = set()
        self.stream_callbacks = {}

        # 缓存管理器
        self.cache_manager = cache_manager

        # 数据缓存（仅保留必要的实时交易缓冲区）
        self.trade_buffer = deque(maxlen=1000)
        self.max_trade_buffer_size = 1000

        # 任务管理
        self.message_handlers = {}
        self.heartbeat_tasks = []

        # 限频控制
        self.rate_limits = {}
        self.last_request_time = {}

        # 连接活动监控
        self._last_spot_message = time.time()
        self._last_futures_message = time.time()
        self._message_timeout = 120  # 2分钟无消息认为连接有问题（降低超时时间）

        # 连接限制管理
        self._connection_attempts = 0
        self._last_connection_attempt = 0
        self._connection_limit_window = 300  # 5分钟窗口
        self._max_connections_per_window = 200  # 官方限制300，设置200保留安全边际
        self.request_weights = {
            "spot": {
                "order": 1,
                "cancel_order": 1,
                "query_order": 2,
                "account_info": 10,
            },
            "futures": {
                "order": 1,
                "cancel_order": 1,
                "query_order": 1,
                "account_info": 5,
            },
        }

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            # 创建HTTP会话
            self.session = aiohttp.ClientSession()

            # 连接WebSocket
            await self._connect_websockets()

            logger.info("BinanceClient初始化完成")
            return True
        except Exception as e:
            logger.error(f"BinanceClient初始化失败: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            logger.info("开始启动BinanceClient...")

            # 确保清理之前的任务，避免并发冲突
            await self._cleanup_existing_tasks()

            # 使用更长的超时时间和重试机制确保连接
            connection_timeout = 60.0  # 增加到60秒
            max_retries = 3
            retry_delay = 5.0

            connection_ok = False
            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(
                            f"🔄 重试WebSocket连接 (第{attempt + 1}/{max_retries}次)..."
                        )
                        await asyncio.sleep(retry_delay)
                    else:
                        logger.info("🔗 尝试建立WebSocket连接...")

                    # 调用连接保障
                    connection_ok = await asyncio.wait_for(
                        self._ensure_connection(), timeout=connection_timeout
                    )
                    # 记录连接结果

                    if connection_ok:
                        import time

                        self._connection_start_time = time.time()
                        logger.info("✅ WebSocket连接建立成功")
                        logger.info(
                            f"📅 连接有效期：24小时（至 {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self._connection_start_time + 24 * 3600))}）"
                        )
                        break
                    else:
                        logger.warning(
                            f"⚠️ WebSocket连接失败 (尝试 {attempt + 1}/{max_retries})"
                        )

                except TimeoutError:
                    logger.warning(
                        f"⏰ WebSocket连接超时 (尝试 {attempt + 1}/{max_retries})"
                    )
                except Exception as e:
                    logger.warning(
                        f"⚠️ WebSocket连接异常 (尝试 {attempt + 1}/{max_retries}): {e}"
                    )

            if not connection_ok:
                logger.error("❌ WebSocket连接最终失败，但继续启动（后台会持续重试）")

            # 启动连接监控任务
            logger.info("🔄 启动连接监控任务...")
            self.message_handlers["connection_monitor"] = asyncio.create_task(
                self._connection_monitor()
            )

            # 启动数据流健康检查任务
            logger.info("🔄 启动数据流健康检查任务...")
            self.message_handlers["stream_health_monitor"] = asyncio.create_task(
                self._stream_health_monitor()
            )

            # 启动消息处理任务 - 确保只启动一个任务避免并发冲突
            logger.info("📡 启动消息处理任务...")
            self.message_handlers["spot"] = asyncio.create_task(
                self._handle_spot_messages()
            )
            self.message_handlers["futures"] = asyncio.create_task(
                self._handle_futures_messages()
            )

            # 启动订阅任务 - 确保即使初始连接失败也会在重连后自动订阅
            logger.info("🎯 启动订阅管理器...")
            self.message_handlers["subscription"] = asyncio.create_task(
                self._subscription_manager()
            )

            # 如果连接成功，立即尝试订阅
            if connection_ok and self.is_connected:
                logger.info("立即订阅基本数据流...")
                await asyncio.sleep(2)  # 增加等待时间确保WebSocket连接稳定

                try:
                    await self._subscribe_default_streams()
                except Exception as e:
                    logger.warning(f"⚠️ 初始订阅失败: {e}，后续会自动重试")
            else:
                logger.info("📶 连接未就绪，订阅将在连接建立后进行")

            # 启动心跳任务和pong任务
            self.heartbeat_tasks.append(asyncio.create_task(self._heartbeat_task()))
            self.heartbeat_tasks.append(asyncio.create_task(self._pong_task()))

            logger.info("BinanceClient启动成功")
            return True
        except Exception as e:
            logger.error(f"BinanceClient启动失败: {e}")
            return False

    async def _cleanup_existing_tasks(self) -> None:
        """清理现有的任务，避免并发冲突"""
        try:
            # 取消现有的消息处理任务
            for task_name, task in list(self.message_handlers.items()):
                if task and not task.done():
                    task.cancel()
                    with contextlib.suppress(TimeoutError, asyncio.CancelledError):
                        await asyncio.wait_for(task, timeout=2.0)

            # 清空任务字典
            self.message_handlers.clear()

            # 取消心跳任务
            for task in self.heartbeat_tasks:
                if task and not task.done():
                    task.cancel()
                    with contextlib.suppress(TimeoutError, asyncio.CancelledError):
                        await asyncio.wait_for(task, timeout=2.0)

            # 清空心跳任务列表
            self.heartbeat_tasks.clear()

            logger.debug("任务清理完成")
        except Exception as e:
            logger.debug(f"任务清理异常: {e}")

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            # 取消所有任务
            for _task_name, task in self.message_handlers.items():
                if task and not task.done():
                    task.cancel()
                    with contextlib.suppress(asyncio.CancelledError, TimeoutError):
                        await asyncio.wait_for(task, timeout=2.0)

            for task in self.heartbeat_tasks:
                if task and not task.done():
                    task.cancel()
                    with contextlib.suppress(asyncio.CancelledError, TimeoutError):
                        await asyncio.wait_for(task, timeout=2.0)

            # 关闭WebSocket连接（检查状态避免重复关闭）
            if self.spot_ws and not self.spot_ws.closed:
                await self.spot_ws.close()
                self.spot_ws = None
            if self.futures_ws and not self.futures_ws.closed:
                await self.futures_ws.close()
                self.futures_ws = None

            # 关闭HTTP会话（检查状态避免重复关闭）
            if self.session and not self.session.closed:
                await self.session.close()
                self.session = None

            logger.info("BinanceClient已停止")
            return True
        except Exception as e:
            logger.error(f"BinanceClient停止失败: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""

        # 检查连接状态 - 简化逻辑，如果WebSocket对象存在就认为连接正常
        spot_connected = self.spot_ws is not None
        futures_connected = self.futures_ws is not None

        # 如果需要更精确的检查，可以尝试检查closed属性
        if spot_connected:
            try:
                # 对于websockets库，检查是否closed
                closed = getattr(self.spot_ws, "closed", False)
                spot_connected = not closed
            except Exception:
                # 如果检查失败，只要对象存在就认为连接正常
                spot_connected = True

        if futures_connected:
            try:
                # 对于websockets库，检查是否closed
                closed = getattr(self.futures_ws, "closed", False)
                futures_connected = not closed
            except Exception:
                # 如果检查失败，只要对象存在就认为连接正常
                futures_connected = True

        # 确定健康状态
        if spot_connected and futures_connected:
            status = HealthStatus.HEALTHY
            message = "All connections active"
        elif spot_connected or futures_connected:
            status = HealthStatus.DEGRADED
            message = "Partial connections active"
        else:
            status = HealthStatus.UNHEALTHY
            message = "No active connections"

        details = {
            "spot_ws_connected": spot_connected,
            "futures_ws_connected": futures_connected,
            "subscribed_streams": len(self.subscribed_streams),
            "reconnect_attempts": self._reconnect_attempts,
            "trade_buffer_size": len(self.trade_buffer),
            "cache_manager_connected": self.cache_manager is not None,
        }

        return HealthCheckResult(status=status, message=message, details=details)

    @property
    def is_connected(self) -> bool:
        """检查连接状态"""
        spot_connected = self.spot_ws is not None
        futures_connected = self.futures_ws is not None

        if spot_connected:
            try:
                closed = getattr(self.spot_ws, "closed", False)
                spot_connected = not closed
            except Exception:
                spot_connected = True

        if futures_connected:
            try:
                closed = getattr(self.futures_ws, "closed", False)
                futures_connected = not closed
            except Exception:
                futures_connected = True

        return spot_connected or futures_connected

    async def _connect_websockets(self) -> None:
        """连接WebSocket - 使用备选端点重试"""
        # 连接现货WebSocket
        spot_connected = False
        for i in range(len(self.spot_ws_urls)):
            current_url = self.spot_ws_urls[self._spot_url_index]
            try:
                logger.info(f"正在连接现货WebSocket: {current_url}")
                # 添加30秒超时保护，防止连接无限期阻塞
                self.spot_ws = await asyncio.wait_for(
                    websockets.connect(
                        current_url,
                        ping_interval=None,  # 禁用自动ping，服务器会发送ping
                        ping_timeout=None,  # 禁用ping超时，让服务器管理
                        close_timeout=10,
                        max_size=2**20,  # 1MB 最大消息大小
                        max_queue=32,  # 最大队列大小
                    ),
                    timeout=30.0,  # 30秒连接超时
                )
                logger.info(f"现货WebSocket连接成功: {current_url}")
                spot_connected = True
                break
            except Exception as e:
                logger.warning(f"现货WebSocket连接失败 ({current_url}): {e}")
                # 尝试下一个端点
                self._spot_url_index = (self._spot_url_index + 1) % len(
                    self.spot_ws_urls
                )
                if i == len(self.spot_ws_urls) - 1:  # 所有端点都试过了
                    logger.error("所有现货WebSocket端点连接失败")

        # 检查是否启用期货订阅
        futures_enabled = False
        if self.config_manager and self.config_manager.config:
            try:
                config = self.config_manager.config
                binance_config = config.get("exchanges", {}).get("binance", {})
                subscriptions = binance_config.get("subscriptions", {})
                futures_config = subscriptions.get("futures", {})
                futures_enabled = futures_config.get("enabled", False)
                logger.info(f"期货订阅配置: enabled={futures_enabled}")
            except Exception as e:
                logger.warning(f"读取期货配置失败: {e}")

        # 连接期货WebSocket（仅在启用时）
        futures_connected = False
        if futures_enabled:
            for i in range(len(self.futures_ws_urls)):
                current_url = self.futures_ws_urls[self._futures_url_index]
                try:
                    logger.info(f"正在连接期货WebSocket: {current_url}")
                    # 添加30秒超时保护，防止连接无限期阻塞
                    self.futures_ws = await asyncio.wait_for(
                        websockets.connect(
                            current_url,
                            ping_interval=None,  # 禁用自动ping，服务器会发送ping
                            ping_timeout=None,  # 禁用ping超时，让服务器管理
                            close_timeout=10,
                            max_size=2**20,  # 1MB 最大消息大小
                            max_queue=32,  # 最大队列大小
                        ),
                        timeout=30.0,  # 30秒连接超时
                    )
                    logger.info(f"期货WebSocket连接成功: {current_url}")
                    futures_connected = True
                    break
                except Exception as e:
                    logger.warning(f"期货WebSocket连接失败 ({current_url}): {e}")
                    # 尝试下一个端点
                    self._futures_url_index = (self._futures_url_index + 1) % len(
                        self.futures_ws_urls
                    )
                    if i == len(self.futures_ws_urls) - 1:  # 所有端点都试过了
                        logger.error("所有期货WebSocket端点连接失败")
        else:
            logger.info("期货订阅已禁用，跳过期货WebSocket连接")

        # 检查连接结果
        if not spot_connected and not futures_connected:
            if futures_enabled:
                raise ConnectionError("所有WebSocket连接尝试均失败")
            else:
                raise ConnectionError("现货WebSocket连接失败")
        elif not spot_connected:
            logger.warning("只有期货WebSocket连接成功，现货连接失败")
        elif not futures_connected and futures_enabled:
            logger.warning("只有现货WebSocket连接成功，期货连接失败")

        # 连接成功，重置重连计数器
        self._reconnect_attempts = 0

    async def _close_websockets(self) -> None:
        """安全关闭WebSocket连接"""
        # 先取消所有消息处理任务
        for _task_name, task in self.message_handlers.items():
            if task and not task.done():
                task.cancel()
                with contextlib.suppress(asyncio.CancelledError, TimeoutError):
                    await asyncio.wait_for(task, timeout=2.0)

        # 取消心跳任务
        for task in self.heartbeat_tasks:
            if task and not task.done():
                task.cancel()
                with contextlib.suppress(asyncio.CancelledError, TimeoutError):
                    await asyncio.wait_for(task, timeout=2.0)

        # 关闭WebSocket连接
        close_tasks = []
        if self.spot_ws and not getattr(self.spot_ws, "closed", True):
            try:
                close_tasks.append(
                    asyncio.create_task(self._close_single_ws(self.spot_ws, "现货"))
                )
            except Exception as e:
                logger.debug(f"关闭现货WebSocket时出错: {e}")

        if self.futures_ws and not getattr(self.futures_ws, "closed", True):
            try:
                close_tasks.append(
                    asyncio.create_task(self._close_single_ws(self.futures_ws, "期货"))
                )
            except Exception as e:
                logger.debug(f"关闭期货WebSocket时出错: {e}")

        # 等待所有关闭操作完成
        if close_tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*close_tasks, return_exceptions=True), timeout=3.0
                )
            except TimeoutError:
                logger.warning("WebSocket关闭操作超时")

        # 清理引用
        self.spot_ws = None
        self.futures_ws = None

    async def _close_websockets_for_reconnect(self) -> None:
        """重连时关闭WebSocket连接（不取消心跳任务）"""
        # 只取消消息处理任务
        for _task_name, task in self.message_handlers.items():
            if task and not task.done():
                task.cancel()
                with contextlib.suppress(asyncio.CancelledError, TimeoutError):
                    await asyncio.wait_for(task, timeout=2.0)

        # 直接关闭WebSocket连接，避免create_task
        if self.spot_ws and not getattr(self.spot_ws, "closed", True):
            try:
                await self._close_single_ws(self.spot_ws, "现货")
            except Exception as e:
                logger.debug(f"关闭现货WebSocket时出错: {e}")

        if self.futures_ws and not getattr(self.futures_ws, "closed", True):
            try:
                await self._close_single_ws(self.futures_ws, "期货")
            except Exception as e:
                logger.debug(f"关闭期货WebSocket时出错: {e}")

        # 清理连接引用
        self.spot_ws = None
        self.futures_ws = None

    async def _cancel_message_handlers(self) -> None:
        """取消消息处理任务并等待完成（不取消connection_monitor避免递归问题）"""
        tasks_to_cancel = {
            k: v for k, v in self.message_handlers.items() if k != "connection_monitor"
        }

        for task_name, task in tasks_to_cancel.items():
            if task and not task.done():
                logger.debug(f"取消消息处理任务: {task_name}")
                task.cancel()
                try:
                    await asyncio.wait_for(task, timeout=3.0)
                except (TimeoutError, asyncio.CancelledError):
                    logger.debug(f"任务 {task_name} 取消完成")
                except Exception as e:
                    logger.warning(f"取消任务 {task_name} 时出错: {e}")

        # 只清除已取消的任务
        for task_name in tasks_to_cancel:
            self.message_handlers.pop(task_name, None)

    async def _connection_monitor(self) -> None:
        """连接监控任务，定期检查连接状态并重连"""
        while self.is_running or self.state.name == "STARTING":
            try:
                await asyncio.sleep(10)  # 缩短检查间隔到10秒，更快发现连接问题

                # 检查WebSocket连接状态
                spot_ok = self.spot_ws and not getattr(self.spot_ws, "closed", True)
                futures_ok = self.futures_ws and not getattr(
                    self.futures_ws, "closed", True
                )

                # 检查消息活跃度
                current_time = time.time()
                spot_message_stale = (
                    current_time - self._last_spot_message
                ) > 120  # 2分钟无消息认为异常
                futures_message_stale = (
                    current_time - self._last_futures_message
                ) > 600  # 10分钟无消息认为异常

                if (
                    not spot_ok
                    or not futures_ok
                    or spot_message_stale
                    or futures_message_stale
                ):
                    connection_issues = []
                    if not spot_ok:
                        connection_issues.append("现货连接断开")
                    if not futures_ok:
                        connection_issues.append("期货连接断开")
                    if spot_message_stale:
                        connection_issues.append("现货消息超时")
                    if futures_message_stale:
                        connection_issues.append("期货消息超时")

                    logger.info(
                        f"🔄 检测到连接问题: {', '.join(connection_issues)}，开始重连..."
                    )

                    # 执行重连
                    if not self._is_reconnecting:  # 避免重复重连
                        success = await self._reconnect_websockets()
                        if success:
                            logger.info("✅ WebSocket重连成功")
                        else:
                            logger.warning("⚠️ WebSocket重连失败，将在下次检查时重试")

            except asyncio.CancelledError:
                logger.debug("连接监控任务被取消")
                break
            except Exception as e:
                logger.error(f"连接监控任务错误: {e}")
                await asyncio.sleep(5)  # 出错后等待5秒再继续

    async def _close_single_ws(self, ws, name: str) -> None:
        """关闭单个WebSocket连接"""
        try:
            logger.debug(f"正在关闭{name}WebSocket连接")
            await ws.close()
            logger.debug(f"{name}WebSocket连接已关闭")
        except Exception as e:
            logger.debug(f"关闭{name}WebSocket时出错: {e}")

    def _check_connection_limits(self) -> bool:
        """检查连接限制，防止触发Binance的连接限制"""
        current_time = time.time()

        # 清理过期的连接尝试记录
        if current_time - self._last_connection_attempt > self._connection_limit_window:
            self._connection_attempts = 0

        # 检查是否超过连接限制
        if self._connection_attempts >= self._max_connections_per_window:
            remaining_time = self._connection_limit_window - (
                current_time - self._last_connection_attempt
            )
            logger.warning(f"连接尝试过多，需等待 {remaining_time:.0f} 秒后再试")
            return False

        return True

    async def _reconnect_websockets(self) -> bool:
        """重连WebSocket - 改进的重连策略，防止并发重连和连接限制"""
        async with self._reconnect_lock:
            # 如果已经在重连中，直接返回等待结果
            if self._is_reconnecting:
                logger.debug("重连已在进行中，等待完成")
                # 等待重连完成
                while self._is_reconnecting and self.is_running:
                    await asyncio.sleep(0.5)
                return self.spot_ws is not None or self.futures_ws is not None

            self._is_reconnecting = True

            try:
                # 检查连接限制
                if not self._check_connection_limits():
                    # 等待连接限制窗口重置
                    current_time = time.time()
                    wait_time = self._connection_limit_window - (
                        current_time - self._last_connection_attempt
                    )
                    if wait_time > 0:
                        logger.info(f"等待连接限制重置: {wait_time:.0f}秒")
                        await asyncio.sleep(wait_time)

                # 检查是否需要重置重连计数器（每30分钟重置一次）
                current_time = time.time()
                if current_time - self._last_reconnect_reset > 1800:  # 30分钟
                    logger.info("重置重连计数器，允许继续重连尝试")
                    self._reconnect_attempts = 0
                    self._last_reconnect_reset = current_time

                if self._reconnect_attempts >= self._max_reconnect_attempts:
                    logger.warning(
                        f"达到单轮最大重连次数({self._max_reconnect_attempts})，等待5分钟后重置计数器"
                    )
                    # 等待5分钟后重置计数器，允许继续重连
                    await asyncio.sleep(300)
                    self._reconnect_attempts = 0
                    self._last_reconnect_reset = time.time()
                    logger.info("重连计数器已重置，继续重连尝试")

                self._reconnect_attempts += 1
                # 检测是否是频率限制导致的断开
                is_rate_limited = self._is_rate_limited_disconnect()
                if is_rate_limited:
                    # 记录rate limit时间
                    self._last_rate_limit_time = time.time()
                    # 频率限制时使用更长的退避时间
                    delay = self._rate_limit_backoff
                    logger.warning(
                        f"检测到频率限制，使用长退避时间: {delay}秒 ({delay / 60:.1f}分钟)"
                    )
                else:
                    # 检查是否在rate limit冷却期内
                    if (
                        hasattr(self, "_last_rate_limit_time")
                        and self._last_rate_limit_time > 0
                    ):
                        time_since_rate_limit = time.time() - self._last_rate_limit_time
                        if time_since_rate_limit < self._rate_limit_backoff:
                            # 仍在冷却期内，使用较长的延迟
                            delay = max(self._reconnect_delay * 2, 180)
                            logger.info(
                                f"仍在rate limit冷却期内，使用延长延迟: {delay}秒"
                            )
                        else:
                            # 冷却期已过，使用正常延迟
                            delay = min(
                                self._reconnect_delay
                                * (1.5 ** (self._reconnect_attempts - 1)),
                                180,
                            )
                    else:
                        # 正常的退避策略
                        delay = min(
                            self._reconnect_delay
                            * (1.5 ** (self._reconnect_attempts - 1)),
                            180,
                        )

                logger.info(
                    f"尝试重连WebSocket (第{self._reconnect_attempts}次)，等待{delay}秒"
                )
                await asyncio.sleep(delay)

                # 记录连接尝试
                self._connection_attempts += 1
                self._last_connection_attempt = time.time()

                try:
                    # 先关闭现有连接（重连时不取消心跳任务）
                    await self._close_websockets_for_reconnect()

                    # 简单的网络连通性检查
                    try:
                        async with (
                            aiohttp.ClientSession() as session,
                            session.get(
                                "https://api.binance.com/api/v3/ping",
                                timeout=aiohttp.ClientTimeout(total=5),
                            ) as response,
                        ):
                            if response.status != 200:
                                logger.warning(
                                    f"Binance API连通性检查失败，状态码: {response.status}"
                                )
                    except Exception as e:
                        logger.warning(f"网络连通性检查失败: {e}")

                    # 重新连接
                    await self._connect_websockets()

                    # 确保旧任务已完全取消后再创建新任务
                    await self._cancel_message_handlers()

                    # 重新创建消息处理任务（不要重新创建connection_monitor，因为我们就在其中）
                    self.message_handlers["spot"] = asyncio.create_task(
                        self._handle_spot_messages()
                    )
                    self.message_handlers["futures"] = asyncio.create_task(
                        self._handle_futures_messages()
                    )

                    # 重新订阅之前的流；如果此前未订阅过（例如首次订阅失败），则订阅默认流
                    try:
                        if self.subscribed_streams:
                            logger.debug("重连后重新订阅已有流...")
                            await self._resubscribe_streams()
                        else:
                            logger.debug("重连后订阅默认流...")
                            await self._subscribe_default_streams(
                                skip_connection_check=True
                            )
                        logger.debug("重连后订阅完成")
                    except Exception as subscribe_error:
                        logger.error(f"重新订阅数据流失败: {subscribe_error}")
                        import traceback

                        logger.error(f"订阅错误详情: {traceback.format_exc()}")
                        # 订阅失败不应该阻止重连成功

                    # 重连成功，重置计数器
                    self._reconnect_attempts = 0
                    logger.info("WebSocket重连成功")
                    return True

                except Exception as e:
                    logger.error(
                        f"WebSocket重连失败 (尝试{self._reconnect_attempts}): {e}"
                    )
                    return False

            finally:
                self._is_reconnecting = False

    def _reset_reconnect_state(self) -> None:
        """重置重连状态，允许立即重连"""
        self._reconnect_attempts = 0
        self._last_reconnect_reset = time.time()
        self._is_reconnecting = False
        logger.info("重连状态已重置，允许立即重连")

    def _is_rate_limited_disconnect(self) -> bool:
        """检测是否是频率限制导致的断开"""
        # 检查最近的WebSocket关闭原因
        # code=1008 通常表示"Too many requests"
        # 这里可以根据实际的断开原因进行判断
        disconnect_reason = str(getattr(self, "_last_disconnect_reason", ""))
        return any(
            phrase in disconnect_reason.lower()
            for phrase in [
                "too many requests",
                "rate limit",
                "frequency limit",
                "code=1008",  # WebSocket policy violation code
            ]
        )

    async def _stream_health_monitor(self) -> None:
        """数据流健康检查任务，监控数据流的连续性和质量"""
        check_interval = 30  # 每30秒检查一次

        while self.is_running or self.state.name == "STARTING":
            try:
                await asyncio.sleep(check_interval)

                current_time = time.time()

                # 检查现货数据流健康状态
                spot_healthy = self._check_stream_health("spot", current_time)

                # 检查期货数据流健康状态（如果启用）
                futures_enabled = (
                    self.config_manager
                    and self.config_manager.config.get("exchanges", {})
                    .get("binance", {})
                    .get("subscriptions", {})
                    .get("futures", {})
                    .get("enabled", True)
                )
                futures_healthy = True
                if futures_enabled:
                    futures_healthy = self._check_stream_health("futures", current_time)

                # 如果数据流不健康，触发重连
                if not spot_healthy or (futures_enabled and not futures_healthy):
                    unhealthy_streams = []
                    if not spot_healthy:
                        unhealthy_streams.append("现货")
                    if futures_enabled and not futures_healthy:
                        unhealthy_streams.append("期货")

                    logger.warning(
                        f"🚨 数据流健康检查失败: {', '.join(unhealthy_streams)}"
                    )

                    # 触发重连
                    if not self._is_reconnecting:
                        logger.info("数据流健康检查触发重连")
                        asyncio.create_task(self._reconnect_websockets())

            except asyncio.CancelledError:
                logger.debug("数据流健康检查任务被取消")
                break
            except Exception as e:
                logger.error(f"数据流健康检查任务错误: {e}")
                await asyncio.sleep(10)

    def _check_stream_health(self, market_type: str, current_time: float) -> bool:
        """检查特定市场的数据流健康状态"""
        try:
            if market_type == "spot":
                ws = self.spot_ws
                last_message_time = self._last_spot_message
                # 现货市场应该有频繁的数据更新
                max_silence_time = 90  # 90秒无数据认为不健康
            elif market_type == "futures":
                ws = self.futures_ws
                last_message_time = self._last_futures_message
                # 期货市场数据更新频率较低
                max_silence_time = 600  # 10分钟无数据认为不健康
            else:
                return False

            # 检查WebSocket连接状态
            if not ws or ws.closed:
                logger.debug(f"{market_type}数据流不健康：连接已断开")
                return False

            # 检查数据流活跃度
            silence_time = current_time - last_message_time
            if silence_time > max_silence_time:
                logger.debug(
                    f"{market_type}数据流不健康：{silence_time:.1f}秒无数据（阈值：{max_silence_time}秒）"
                )
                return False

            return True

        except Exception as e:
            logger.debug(f"检查{market_type}数据流健康状态时出错: {e}")
            return False

    async def _ensure_connection(self) -> bool:
        """确保WebSocket连接可用，如果需要会尝试重连"""
        logger.debug("_ensure_connection() 开始检查连接状态")

        # 只检查现货连接，暂时跳过期货以简化问题
        spot_ok = self.spot_ws and not getattr(self.spot_ws, "closed", True)
        logger.debug(
            f"现货连接状态: spot_ws存在={self.spot_ws is not None}, closed={getattr(self.spot_ws, 'closed', True) if self.spot_ws else 'N/A'}"
        )

        # 暂时跳过期货检查

        if not spot_ok and not self._is_reconnecting:
            logger.debug("现货连接不正常，尝试重连...")
            return await self._reconnect_websockets()

        logger.debug(f"_ensure_connection() 完成，返回: {spot_ok}")
        return spot_ok

    async def _resubscribe_streams(self) -> None:
        """重新订阅流（结构化存储：('spot'|'futures', stream)）"""
        try:
            streams_list = list(self.subscribed_streams)
            for item in streams_list:
                if isinstance(item, tuple) and len(item) == 2:
                    market_type, stream = item
                    if market_type == "futures":
                        await self._subscribe_futures_stream(
                            stream, skip_connection_check=True
                        )
                        logger.debug(f"重订阅期货流: {stream}")
                    else:
                        await self._subscribe_spot_stream(
                            stream, skip_connection_check=True
                        )
                        logger.debug(f"重订阅现货流: {stream}")
                elif isinstance(item, str):
                    # 兼容旧格式
                    if item.startswith("futures:"):
                        await self._subscribe_futures_stream(
                            item.split(":", 1)[1], skip_connection_check=True
                        )
                    else:
                        await self._subscribe_spot_stream(
                            item, skip_connection_check=True
                        )

            logger.info(f"重新订阅{len(streams_list)}个数据流")

        except Exception as e:
            logger.error(f"重新订阅失败: {e}")

    async def _subscribe_default_streams(
        self, skip_connection_check: bool = False
    ) -> None:
        """订阅配置文件中指定的数据流"""
        try:
            logger.info("开始订阅配置中指定的数据流...")
            await asyncio.sleep(1)  # 等待WebSocket连接稳定

            # 从配置中获取订阅设置（必须存在配置管理器）
            if not self.config_manager:
                logger.error("配置管理器未找到，无法订阅数据流")
                return

            try:
                config = self.config_manager.config
                binance_config = config.get("exchanges", {}).get("binance", {})
                subscriptions = binance_config.get("subscriptions", {})
                spot_config = subscriptions.get("spot", {})

                symbols = spot_config.get("symbols", ["BTCUSDT"])
                data_types = spot_config.get(
                    "data_types", ["ticker", "trades", "klines"]
                )  # 修复默认值，包含klines
                kline_intervals = spot_config.get("kline_intervals", ["1m"])

                logger.info(
                    f"配置订阅: symbols={symbols}, data_types={data_types}, kline_intervals={kline_intervals}"
                )

                # 额外调试：检查配置文件路径
                # 可选：logger.debug 输出配置路径

                # 检查原始配置数据
                # 可选：logger.debug 输出原始配置数据类型

                # 订阅每个交易对的数据
                for symbol in symbols:
                    for data_type in data_types:
                        logger.debug(f"处理订阅: {symbol} - {data_type}")
                        if data_type == "ticker":
                            await self.subscribe_ticker(
                                symbol,
                                "spot",
                                skip_connection_check=skip_connection_check,
                            )
                            logger.info(f"✅ 已订阅ticker: {symbol}")
                        elif data_type == "trades":
                            await self.subscribe_trades(
                                symbol,
                                "spot",
                                skip_connection_check=skip_connection_check,
                            )
                            logger.info(f"✅ 已订阅trades: {symbol}")
                        elif data_type == "klines":
                            # 订阅K线数据
                            for interval in kline_intervals:
                                await self.subscribe_klines(
                                    [symbol],
                                    [interval],
                                    "spot",
                                    skip_connection_check=skip_connection_check,
                                )
                                logger.info(f"✅ 已订阅klines: {symbol} {interval}")

                logger.info("✅ 配置数据流订阅完成")

            except Exception as config_error:
                logger.error(f"读取配置失败: {config_error}")

        except Exception as e:
            logger.warning(f"⚠️ 数据流订阅失败: {e}")

    # 已移除回退订阅以保持配置驱动的单一行为

    async def _subscription_manager(self):
        """订阅管理器 - 监控连接状态并自动订阅"""
        try:
            logger.info("🎯 启动订阅管理器...")
            subscription_attempted = False

            while True:
                try:
                    # 每10秒检查一次连接状态
                    await asyncio.sleep(10)

                    # 检查连接状态
                    if self.is_connected:
                        # 如果连接正常但还没有订阅过，或者订阅列表为空，则尝试订阅
                        if not subscription_attempted or not self.subscribed_streams:
                            logger.info("🔄 检测到连接正常，尝试订阅默认数据流...")
                            try:
                                await self._subscribe_default_streams()
                                subscription_attempted = True
                                logger.info("✅ 订阅管理器：数据流订阅成功")
                            except Exception as e:
                                logger.warning(f"⚠️ 订阅管理器：订阅失败: {e}")
                                subscription_attempted = False
                    else:
                        # 连接断开，重置订阅状态
                        if subscription_attempted:
                            logger.info("🔌 连接断开，重置订阅状态")
                            subscription_attempted = False

                except asyncio.CancelledError:
                    logger.info("🛑 订阅管理器被取消")
                    break
                except Exception as e:
                    logger.warning(f"⚠️ 订阅管理器异常: {e}")

        except Exception as e:
            logger.error(f"❌ 订阅管理器启动失败: {e}")

    async def _handle_spot_messages(self) -> None:
        """处理现货WebSocket消息"""
        task_id = id(asyncio.current_task())
        logger.debug(f"现货消息处理任务启动 (task_id: {task_id})")

        try:
            while self.is_running or self.state.name == "STARTING":
                try:
                    if not self.spot_ws or getattr(self.spot_ws, "closed", True):
                        logger.warning("现货WebSocket连接断开，停止消息处理任务")
                        break

                    # 添加并发保护，确保只有一个协程在接收消息
                    try:
                        # 现货：服务器每20秒发ping，1分钟无pong断开，设置70秒超时合理
                        message = await asyncio.wait_for(
                            self.spot_ws.recv(), timeout=70.0
                        )
                    except RuntimeError as e:
                        if "another coroutine is already waiting" in str(e):
                            logger.warning(
                                f"现货WebSocket并发接收冲突，任务退出 (task_id: {task_id})"
                            )
                            break
                        else:
                            raise

                    data = json.loads(message)
                    await self._process_spot_message(data)

                    # 更新最后消息时间
                    self._last_spot_message = time.time()

                except TimeoutError:
                    logger.warning("⏰ 现货WebSocket接收超时70秒，可能连接异常")
                    # 不立即退出，让连接监控处理重连
                    continue
                except websockets.exceptions.ConnectionClosed as e:
                    logger.warning(
                        f"现货WebSocket连接已关闭: code={e.code}, reason={e.reason}"
                    )
                    # 记录断开原因用于频率限制检测
                    self._last_disconnect_reason = f"code={e.code}, reason={e.reason}"
                    break
                except json.JSONDecodeError as e:
                    logger.warning(f"现货消息JSON解析失败: {e}")
                    continue
                except Exception as e:
                    # 检查是否是WebSocket并发接收冲突错误
                    if (
                        "cannot call recv while another coroutine is already waiting"
                        in str(e)
                    ):
                        logger.warning(
                            f"现货WebSocket并发接收冲突 (一般异常捕获)，任务退出 (task_id: {task_id})"
                        )
                        break
                    else:
                        logger.error(f"处理现货消息失败: {e}")
                        await asyncio.sleep(1)
        finally:
            logger.debug(f"现货消息处理任务结束 (task_id: {task_id})")

    async def _handle_futures_messages(self) -> None:
        """处理期货WebSocket消息"""
        task_id = id(asyncio.current_task())
        logger.debug(f"期货消息处理任务启动 (task_id: {task_id})")

        try:
            while self.is_running or self.state.name == "STARTING":
                try:
                    if not self.futures_ws or getattr(self.futures_ws, "closed", True):
                        logger.warning("期货WebSocket连接断开，停止消息处理任务")
                        break

                    # 添加并发保护，确保只有一个协程在接收消息
                    try:
                        # 期货：服务器每3分钟发ping，10分钟无pong断开，设置8分钟超时合理
                        message = await asyncio.wait_for(
                            self.futures_ws.recv(),
                            timeout=480.0,  # 8分钟
                        )
                    except RuntimeError as e:
                        if "another coroutine is already waiting" in str(e):
                            logger.warning(
                                f"期货WebSocket并发接收冲突，任务退出 (task_id: {task_id})"
                            )
                            break
                        else:
                            raise

                    data = json.loads(message)
                    await self._process_futures_message(data)

                    # 更新最后消息时间
                    self._last_futures_message = time.time()

                except TimeoutError:
                    logger.warning("⏰ 期货WebSocket接收超时8分钟，可能连接异常")
                    # 不立即退出，让连接监控处理重连
                    continue
                except websockets.exceptions.ConnectionClosed as e:
                    logger.warning(
                        f"期货WebSocket连接已关闭: code={e.code}, reason={e.reason}"
                    )
                    # 记录断开原因用于频率限制检测
                    self._last_disconnect_reason = f"code={e.code}, reason={e.reason}"
                    break
                except json.JSONDecodeError as e:
                    logger.warning(f"期货消息JSON解析失败: {e}")
                    continue
                except Exception as e:
                    # 检查是否是WebSocket并发接收冲突错误
                    if (
                        "cannot call recv while another coroutine is already waiting"
                        in str(e)
                    ):
                        logger.warning(
                            f"期货WebSocket并发接收冲突 (一般异常捕获)，任务退出 (task_id: {task_id})"
                        )
                        break
                    else:
                        logger.error(f"处理期货消息失败: {e}")
                        await asyncio.sleep(1)
        finally:
            logger.debug(f"期货消息处理任务结束 (task_id: {task_id})")

    async def _process_spot_message(self, data: dict) -> None:
        """处理现货消息"""
        try:
            # 检查是否是订阅确认消息
            if "result" in data:
                logger.debug(f"现货订阅确认: {data}")
                return

            # 处理流格式消息 (多流连接)
            if "stream" in data and "data" in data:
                stream = data.get("stream", "")
                event_data = data.get("data", {})

                if "@ticker" in stream:
                    await self._handle_ticker_data(event_data, "spot")
                elif "@trade" in stream:
                    await self._handle_trade_data(event_data, "spot")
                elif "@depth" in stream:
                    await self._handle_depth_data(event_data, "spot")
                elif "@kline_" in stream:
                    await self._handle_kline_data(event_data, "spot")

            # 处理直接事件格式消息 (单流连接)
            elif "e" in data:
                event_type = data.get("e", "")

                if event_type == "24hrTicker":
                    await self._handle_ticker_data(data, "spot")
                elif event_type == "trade":
                    await self._handle_trade_data(data, "spot")
                elif event_type == "depthUpdate":
                    await self._handle_depth_data(data, "spot")
                elif event_type == "kline":
                    await self._handle_kline_data(data, "spot")

        except Exception as e:
            # 检查是否是WebSocket并发相关错误
            if "cannot call recv while another coroutine is already waiting" in str(e):
                logger.warning(f"现货消息处理中检测到WebSocket并发冲突: {e}")
            else:
                logger.error(f"处理现货消息失败: {e}")

    async def _process_futures_message(self, data: dict) -> None:
        """处理期货消息"""
        try:
            # 检查是否是订阅确认消息
            if "result" in data:
                logger.debug(f"期货订阅确认: {data}")
                return

            # 处理流格式消息 (多流连接)
            if "stream" in data and "data" in data:
                stream = data.get("stream", "")
                event_data = data.get("data", {})

                if "@ticker" in stream:
                    await self._handle_ticker_data(event_data, "futures")
                elif "@aggTrade" in stream:
                    await self._handle_trade_data(event_data, "futures")
                elif "@depth" in stream:
                    await self._handle_depth_data(event_data, "futures")
                elif "@kline_" in stream:
                    await self._handle_kline_data(event_data, "futures")

            # 处理直接事件格式消息 (单流连接)
            elif "e" in data:
                event_type = data.get("e", "")

                if event_type == "24hrTicker":
                    await self._handle_ticker_data(data, "futures")
                elif event_type == "aggTrade":
                    await self._handle_trade_data(data, "futures")
                elif event_type == "depthUpdate":
                    await self._handle_depth_data(data, "futures")
                elif event_type == "kline":
                    await self._handle_kline_data(data, "futures")

        except Exception as e:
            # 检查是否是WebSocket并发相关错误
            if "cannot call recv while another coroutine is already waiting" in str(e):
                logger.warning(f"期货消息处理中检测到WebSocket并发冲突: {e}")
            else:
                logger.error(f"处理期货消息失败: {e}")

    async def _handle_ticker_data(self, data: dict, market_type: str) -> None:
        """处理ticker数据"""
        try:
            # 更新最后接收消息时间
            if market_type == "spot":
                self._last_spot_message = time.time()
            elif market_type == "futures":
                self._last_futures_message = time.time()
            symbol = data.get("s", "")
            price = Decimal(str(data.get("c", "0")))
            volume = Decimal(str(data.get("v", "0")))
            timestamp = datetime.fromtimestamp(data.get("E", 0) / 1000, UTC)

            market_data = BinanceMarketData(
                symbol=symbol,
                price=price,
                volume=volume,
                timestamp=timestamp,
                metadata={
                    "market_type": market_type,
                    "high": data.get("h"),
                    "low": data.get("l"),
                    "open": data.get("o"),
                    "change": data.get("P"),
                    "count": data.get("n"),
                },
            )

            # 缓存数据到CacheManager
            if self.cache_manager:
                cache_key = f"binance:{market_type}:{symbol}:ticker"
                await self.cache_manager.set(cache_key, market_data, ttl=30)

            # 发布到EventBus - 添加验证
            if self.event_bus:
                try:
                    event = DataUpdateEvent(
                        data={
                            "symbol": symbol,
                            "price": safe_float_conversion(price, 0.0),
                            "volume": safe_float_conversion(volume, 0.0),
                            "timestamp": timestamp.isoformat(),
                            "market_type": market_type,
                            "high": data.get("h"),
                            "low": data.get("l"),
                            "open": data.get("o"),
                            "change": data.get("P"),
                            "count": data.get("n"),
                        },
                        data_type="ticker",
                        exchange="binance",
                        source=f"binance_{market_type}",
                    )
                    await self.event_bus.publish(event)
                    # 指标计数
                    from contextlib import suppress

                    with suppress(Exception):
                        self.metrics.custom_metrics["events_published"] = (
                            self.metrics.custom_metrics.get("events_published", 0) + 1
                        )
                    logger.debug(
                        f"✅ 发布Binance ticker数据: {symbol} @ ${float(price)}"
                    )
                except Exception as e:
                    logger.error(f"❌ EventBus发布失败: {e}")
            else:
                logger.warning(f"⚠️ EventBus未注入，无法发布ticker数据: {symbol}")

            # 调用回调函数
            callback_key = f"ticker:{market_type}:{symbol}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(market_data)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理ticker数据失败: {e}")

    async def _handle_trade_data(self, data: dict, market_type: str) -> None:
        """处理交易数据"""
        try:
            # 更新最后接收消息时间
            if market_type == "spot":
                self._last_spot_message = time.time()
            elif market_type == "futures":
                self._last_futures_message = time.time()
            symbol = data.get("s", "")
            price = Decimal(str(data.get("p", "0")))
            quantity = Decimal(str(data.get("q", "0")))
            is_buyer_maker = data.get("m", False)
            timestamp = datetime.fromtimestamp(data.get("T", 0) / 1000, UTC)
            trade_id = data.get("t", 0)

            trade = BinanceTrade(
                symbol=symbol,
                price=price,
                quantity=quantity,
                is_buyer_maker=is_buyer_maker,
                timestamp=timestamp,
                trade_id=trade_id,
            )

            # 添加到交易缓冲区（deque自动维护maxlen）
            self.trade_buffer.append(trade)

            # 发布到EventBus - 添加验证
            if self.event_bus:
                try:
                    event = DataUpdateEvent(
                        data={
                            "symbol": symbol,
                            "price": safe_float_conversion(price, 0.0),
                            "quantity": safe_float_conversion(quantity, 0.0),
                            "is_buyer_maker": is_buyer_maker,
                            "timestamp": timestamp.isoformat(),
                            "trade_id": trade_id,
                        },
                        data_type="trade",
                        exchange="binance",
                        source=f"binance_{market_type}",
                    )
                    await self.event_bus.publish(event)
                    from contextlib import suppress

                    with suppress(Exception):
                        self.metrics.custom_metrics["events_published"] = (
                            self.metrics.custom_metrics.get("events_published", 0) + 1
                        )
                    logger.debug(f"✅ 发布Binance trade数据: {symbol}")
                except Exception as e:
                    logger.error(f"❌ EventBus发布失败: {e}")
            else:
                logger.warning(f"⚠️ EventBus未注入，无法发布trade数据: {symbol}")

            # 调用回调函数
            callback_key = f"trade:{market_type}:{symbol}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(trade)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理交易数据失败: {e}")

    async def _handle_depth_data(self, data: dict, market_type: str) -> None:
        """处理深度数据"""
        try:
            # 更新最后接收消息时间
            if market_type == "spot":
                self._last_spot_message = time.time()
            elif market_type == "futures":
                self._last_futures_message = time.time()
            symbol = data.get("s", "")
            bids = data.get("b", [])
            asks = data.get("a", [])
            timestamp = datetime.fromtimestamp(data.get("E", 0) / 1000, UTC)

            orderbook = BinanceOrderBook(
                symbol=symbol, bids=bids, asks=asks, timestamp=timestamp
            )

            # 缓存订单簿数据到CacheManager
            if self.cache_manager:
                cache_key = f"binance:{market_type}:{symbol}:orderbook"
                await self.cache_manager.set(cache_key, orderbook, ttl=5)

            # 发布到EventBus - 添加验证
            if self.event_bus:
                try:
                    event = DataUpdateEvent(
                        data={
                            "symbol": symbol,
                            "bids": bids,
                            "asks": asks,
                            "timestamp": timestamp.isoformat(),
                        },
                        data_type="orderbook",
                        exchange="binance",
                        source=f"binance_{market_type}",
                    )
                    await self.event_bus.publish(event)
                    from contextlib import suppress

                    with suppress(Exception):
                        self.metrics.custom_metrics["events_published"] = (
                            self.metrics.custom_metrics.get("events_published", 0) + 1
                        )
                    logger.debug(f"✅ 发布Binance orderbook数据: {symbol}")
                except Exception as e:
                    logger.error(f"❌ EventBus发布失败: {e}")
            else:
                logger.warning(f"⚠️ EventBus未注入，无法发布orderbook数据: {symbol}")

            # 调用回调函数
            callback_key = f"depth:{market_type}:{symbol}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(orderbook)
                    except Exception as e:
                        logger.error(f"回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理深度数据失败: {e}")

    async def _handle_kline_data(self, data: dict, market_type: str) -> None:
        """处理K线数据"""
        try:
            # 更新最后接收消息时间
            if market_type == "spot":
                self._last_spot_message = time.time()
            elif market_type == "futures":
                self._last_futures_message = time.time()

            # K线数据可能在 'k' 字段中（单流）或直接在data中（多流）
            kline_data = data.get("k", data) if "k" in data else data

            symbol = kline_data.get("s", "")
            interval = kline_data.get("i", "")
            open_time = kline_data.get("t", 0)
            close_time = kline_data.get("T", 0)
            open_price = safe_float_conversion(kline_data.get("o"), 0.0)
            high_price = safe_float_conversion(kline_data.get("h"), 0.0)
            low_price = safe_float_conversion(kline_data.get("l"), 0.0)
            close_price = safe_float_conversion(kline_data.get("c"), 0.0)
            volume = safe_float_conversion(kline_data.get("v"), 0.0)
            quote_volume = safe_float_conversion(kline_data.get("q"), 0.0)
            trades_count = int(kline_data.get("n", 0))
            taker_buy_volume = safe_float_conversion(kline_data.get("V"), 0.0)
            taker_buy_quote_volume = safe_float_conversion(kline_data.get("Q"), 0.0)
            is_closed = kline_data.get("x", False)  # K线是否完成

            timestamp = datetime.fromtimestamp(open_time / 1000, UTC)

            # 构建K线数据对象
            kline_event_data = {
                "symbol": symbol,
                "interval": interval,
                "open_time": open_time,
                "close_time": close_time,
                "open": open_price,
                "high": high_price,
                "low": low_price,
                "close": close_price,
                "volume": volume,
                "quote_volume": quote_volume,
                "trades_count": trades_count,
                "taker_buy_volume": taker_buy_volume,
                "taker_buy_quote_volume": taker_buy_quote_volume,
                "is_closed": is_closed,
                "timestamp": timestamp.isoformat(),
            }

            # 缓存K线数据到CacheManager
            if self.cache_manager:
                cache_key = f"binance:{market_type}:{symbol}:kline:{interval}"
                await self.cache_manager.set(cache_key, kline_event_data, ttl=60)

            # 发布到EventBus
            if self.event_bus:
                try:
                    event = DataUpdateEvent(
                        data=kline_event_data,
                        data_type="kline",
                        exchange="binance",
                        source=f"binance_{market_type}",
                        timestamp=timestamp,
                    )
                    await self.event_bus.publish(event)
                    from contextlib import suppress

                    with suppress(Exception):
                        self.metrics.custom_metrics["events_published"] = (
                            self.metrics.custom_metrics.get("events_published", 0) + 1
                        )
                    if is_closed:  # 只在K线完成时记录日志，避免刷屏
                        logger.info(
                            f"✅ 发布Binance K线数据: {symbol} {interval} {close_price}"
                        )
                except Exception as e:
                    logger.error(f"❌ EventBus发布失败: {e}")
            else:
                logger.warning(f"⚠️ EventBus未注入，无法发布K线数据: {symbol}")

            # 调用回调函数
            callback_key = f"kline:{market_type}:{symbol}:{interval}"
            if callback_key in self.stream_callbacks:
                for callback in self.stream_callbacks[callback_key]:
                    try:
                        await callback(kline_event_data)
                    except Exception as e:
                        logger.error(f"K线回调函数执行失败: {e}")

        except Exception as e:
            logger.error(f"处理K线数据失败: {e}")

    async def _heartbeat_task(self) -> None:
        """心跳任务 - 优化连接状态检查和重连逻辑"""
        consecutive_failures = 0
        max_consecutive_failures = 3
        current_time = time.time()

        while self.is_running:
            try:
                # 每15秒检查连接状态，更频繁地检测问题
                await asyncio.sleep(15)
                current_time = time.time()

                # 检查现货连接状态
                spot_disconnected = self._is_connection_broken("spot", current_time)

                # 检查期货连接状态
                futures_disconnected = self._is_connection_broken(
                    "futures", current_time
                )

                # 更积极的重连策略
                if spot_disconnected or futures_disconnected:
                    consecutive_failures += 1
                    connection_status = []
                    if spot_disconnected:
                        connection_status.append("现货")
                    if futures_disconnected:
                        connection_status.append("期货")

                    logger.warning(
                        f"WebSocket连接断开: {','.join(connection_status)} (连续失败次数: {consecutive_failures})"
                    )

                    # 如果连续失败次数过多，强制重置重连状态
                    if consecutive_failures > max_consecutive_failures:
                        logger.warning(
                            f"连续失败{consecutive_failures}次，强制重置重连状态"
                        )
                        self._reset_reconnect_state()
                        consecutive_failures = 0

                    # 立即触发重连，不等待connection_monitor
                    if not self._is_reconnecting:
                        logger.info("心跳任务检测到连接问题，立即触发重连")
                        asyncio.create_task(self._reconnect_websockets())
                else:
                    # 连接正常，重置失败计数
                    if consecutive_failures > 0:
                        logger.info("WebSocket连接恢复正常")
                        consecutive_failures = 0

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"心跳任务失败: {e}")
                consecutive_failures += 1

    def _is_connection_broken(self, market_type: str, current_time: float) -> bool:
        """检查连接是否断开 - 使用更敏感的检测方法"""
        try:
            if market_type == "spot":
                ws = self.spot_ws
                last_message_time = self._last_spot_message
                # 现货市场活跃，60秒无消息就认为异常
                message_timeout = 60
            elif market_type == "futures":
                ws = self.futures_ws
                last_message_time = self._last_futures_message
                # 期货市场相对不活跃，300秒无消息认为异常
                message_timeout = 300
            else:
                return True

            # 1. 检查WebSocket对象是否存在且未关闭
            if not ws or ws.closed:
                logger.debug(f"{market_type}连接：WebSocket对象无效或已关闭")
                return True

            # 2. 检查最近是否有消息活动（更敏感的检测）
            time_since_last_message = current_time - last_message_time
            if time_since_last_message > message_timeout:
                logger.debug(
                    f"{market_type}连接：{time_since_last_message:.1f}秒内无消息（阈值：{message_timeout}秒），可能连接异常"
                )
                return True

            return False

        except (AttributeError, Exception) as e:
            logger.debug(f"检查{market_type}连接状态时出错: {e}")
            return True

    async def _pong_task(self) -> None:
        """Binance官方要求：定期发送空pong保持连接活跃"""
        while self.is_running:
            try:
                await asyncio.sleep(60)  # 每60秒发送一次空pong

                # 向现货WebSocket发送空pong
                if self.spot_ws and not getattr(self.spot_ws, "closed", True):
                    try:
                        await self.spot_ws.pong()  # 发送空负载pong
                        logger.debug("发送现货WebSocket空pong")
                    except Exception as e:
                        logger.debug(f"发送现货pong失败: {e}")

                # 向期货WebSocket发送空pong
                if self.futures_ws and not getattr(self.futures_ws, "closed", True):
                    try:
                        await self.futures_ws.pong()  # 发送空负载pong
                        logger.debug("发送期货WebSocket空pong")
                    except Exception as e:
                        logger.debug(f"发送期货pong失败: {e}")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Pong任务异常: {e}")
                await asyncio.sleep(5)  # 出错后等待5秒继续

    # 公共API方法

    async def subscribe_ticker(
        self,
        symbol: str,
        market_type: str = "spot",
        callback: Callable = None,
        skip_connection_check: bool = False,
    ) -> bool:
        """订阅ticker数据（结构化存储订阅项）"""
        try:
            stream = f"{symbol.lower()}@ticker"

            if market_type == "spot":
                success = await self._subscribe_spot_stream(
                    stream, skip_connection_check=skip_connection_check
                )
                if success:
                    self.subscribed_streams.add(("spot", stream))
            else:
                success = await self._subscribe_futures_stream(
                    stream, skip_connection_check=skip_connection_check
                )
                if success:
                    self.subscribed_streams.add(("futures", stream))

            if success and callback:
                callback_key = f"ticker:{market_type}:{symbol}"
                if callback_key not in self.stream_callbacks:
                    self.stream_callbacks[callback_key] = []
                self.stream_callbacks[callback_key].append(callback)

            return success

        except Exception as e:
            logger.error(f"订阅ticker失败: {e}")
            return False

    async def subscribe_trades(
        self,
        symbol: str,
        market_type: str = "spot",
        callback: Callable = None,
        skip_connection_check: bool = False,
    ) -> bool:
        """订阅交易数据（结构化存储订阅项）"""
        try:
            if market_type == "spot":
                stream = f"{symbol.lower()}@trade"
                success = await self._subscribe_spot_stream(
                    stream, skip_connection_check=skip_connection_check
                )
                if success:
                    self.subscribed_streams.add(("spot", stream))
            else:
                stream = f"{symbol.lower()}@aggTrade"
                success = await self._subscribe_futures_stream(
                    stream, skip_connection_check=skip_connection_check
                )
                if success:
                    self.subscribed_streams.add(("futures", stream))

            if success and callback:
                callback_key = f"trade:{market_type}:{symbol}"
                if callback_key not in self.stream_callbacks:
                    self.stream_callbacks[callback_key] = []
                self.stream_callbacks[callback_key].append(callback)

            return success

        except Exception as e:
            logger.error(f"订阅交易数据失败: {e}")
            return False

    async def subscribe_orderbook(
        self,
        symbol: str,
        market_type: str = "spot",
        callback: Callable = None,
        skip_connection_check: bool = False,
    ) -> bool:
        """订阅订单簿数据（结构化存储订阅项）"""
        try:
            stream = f"{symbol.lower()}@depth@100ms"

            if market_type == "spot":
                success = await self._subscribe_spot_stream(
                    stream, skip_connection_check=skip_connection_check
                )
                if success:
                    self.subscribed_streams.add(("spot", stream))
            else:
                success = await self._subscribe_futures_stream(
                    stream, skip_connection_check=skip_connection_check
                )
                if success:
                    self.subscribed_streams.add(("futures", stream))

            if success and callback:
                callback_key = f"depth:{market_type}:{symbol}"
                if callback_key not in self.stream_callbacks:
                    self.stream_callbacks[callback_key] = []
                self.stream_callbacks[callback_key].append(callback)

            return success

        except Exception as e:
            logger.error(f"订阅订单簿失败: {e}")
            return False

    async def _subscribe_spot_stream(
        self, stream: str, skip_connection_check: bool = False
    ) -> bool:
        """订阅现货流"""
        try:
            # 检查WebSocket连接状态（除非明确跳过检查）
            if not skip_connection_check:
                spot_closed = True
                try:
                    spot_closed = getattr(self.spot_ws, "closed", True)
                except (AttributeError, Exception):
                    spot_closed = True

                if not self.spot_ws or spot_closed:
                    logger.warning(f"现货WebSocket连接不可用，跳过订阅: {stream}")
                    return False

            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [stream],
                "id": int(time.time()),
            }

            await self.spot_ws.send(json.dumps(subscribe_msg))
            # 集合结构化由上层subscribe_*维护，这里不再重复写入
            logger.info(f"订阅现货流成功: {stream}")
            return True

        except Exception as e:
            logger.error(f"订阅现货流失败: {e}")
            return False

    async def _subscribe_futures_stream(
        self, stream: str, skip_connection_check: bool = False
    ) -> bool:
        """订阅期货流"""
        try:
            # 检查WebSocket连接状态（除非明确跳过检查）
            if not skip_connection_check:
                futures_closed = True
                try:
                    futures_closed = getattr(self.futures_ws, "closed", True)
                except (AttributeError, Exception):
                    futures_closed = True

                if not self.futures_ws or futures_closed:
                    logger.warning(f"期货WebSocket连接不可用，跳过订阅: {stream}")
                    return False

            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [stream],
                "id": int(time.time()),
            }

            await self.futures_ws.send(json.dumps(subscribe_msg))
            # 集合结构化由上层subscribe_*维护，这里不再重复写入
            logger.info(f"订阅期货流成功: {stream}")
            return True

        except Exception as e:
            logger.error(f"订阅期货流失败: {e}")
            return False

    async def _analyze_disconnect_reason(
        self, market_type: str, code: int, reason: str
    ) -> None:
        """分析WebSocket断开原因并提供建议（基于Binance官方文档）"""
        try:
            # 基于Binance官方文档的断开原因分析
            logger.error(f"🔍 {market_type}市场WebSocket断开分析:")
            logger.error(f"   错误代码: {code}")
            if reason:
                logger.error(f"   错误原因: {reason}")

            # 根据Binance实际情况分析断开原因
            if code == 1000:
                logger.info("💡 正常关闭 - 可能原因:")

                # 检查是否是24小时断开
                if self._connection_start_time > 0:
                    import time

                    connection_duration = time.time() - self._connection_start_time
                    hours = connection_duration / 3600
                    logger.info(f"   • 连接时长: {hours:.1f}小时")

                    if hours >= 23.5:  # 接近24小时
                        logger.info("   ✅ 24小时连接期限到达（Binance正常断开）")
                        logger.info("   • 这是预期的行为，无需担心")
                    else:
                        logger.info("   • 服务器维护或其他原因")
                else:
                    logger.info("   • 24小时连接期限到达（Binance自动断开）")
                    logger.info("   • 服务器维护")
                    logger.info("   • 客户端主动关闭")
            elif code == 1001:
                logger.error("💡 端点离线 - 建议:")
                logger.error("   • 检查网络连接")
                logger.error("   • 验证Binance API状态")
                logger.error("   • 检查防火墙设置")
            elif code == 1006:
                logger.error("💡 异常关闭（无关闭帧）- 常见原因:")
                logger.error("   • 网络连接不稳定")
                logger.error("   • 触发了Binance的连接限制")
                logger.error("   • 服务器负载过高")
            elif code == 1008:
                logger.error("💡 违反策略 - 可能原因:")
                logger.error("   • 超过5条消息/秒限制")
                logger.error("   • IP被临时限制")
                logger.error("   • 订阅了超过1024个流")
            elif code == 1011:
                logger.error("💡 服务器内部错误 - 建议:")
                logger.error("   • 稍后重试连接")
                logger.error("   • 使用指数退避策略")
            else:
                # 通用WebSocket错误码
                common_codes = {
                    1002: "协议错误",
                    1003: "不支持的数据类型",
                    1007: "无效UTF-8数据",
                    1009: "消息过大",
                    1010: "缺少扩展",
                    1015: "TLS握手失败",
                }
                desc = common_codes.get(code, f"未知错误码: {code}")
                logger.error(f"💡 {desc}")

            # 记录重连策略
            logger.info(f"🔄 将根据Binance规范延迟后重连{market_type}市场:")
            if market_type == "spot":
                logger.info("   • 现货市场：考虑20秒ping周期和60秒pong超时")
            else:
                logger.info("   • 期货市场：考虑3分钟ping周期和10分钟pong超时")

            # 检查是否需要网络诊断
            if code in [1001, 1006, 1011, 1015]:
                await self._check_network_connectivity()

        except Exception as e:
            logger.error(f"分析断开原因时出错: {e}")

    async def _check_network_connectivity(self) -> None:
        """检查网络连通性"""
        try:
            import aiohttp

            logger.info("🌐 正在检查网络连通性...")

            # 检查DNS解析
            test_urls = [
                "https://api.binance.com/api/v3/ping",
                "https://fapi.binance.com/fapi/v1/ping",
                "https://8.8.8.8",  # Google DNS
            ]

            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=10)
            ) as session:
                for url in test_urls:
                    try:
                        async with session.get(url) as response:
                            if response.status == 200:
                                logger.info(f"✅ 网络连通性正常: {url}")
                            else:
                                logger.warning(
                                    f"⚠️ 网络响应异常: {url} - status={response.status}"
                                )
                    except Exception as e:
                        logger.error(f"❌ 网络连接失败: {url} - {e}")

        except Exception as e:
            logger.error(f"网络连通性检查失败: {e}")

    async def get_latest_price(
        self, symbol: str, market_type: str = "spot"
    ) -> Decimal | None:
        """获取最新价格"""
        # 从CacheManager获取缓存价格
        if self.cache_manager:
            cache_key = f"binance:{market_type}:{symbol}:ticker"
            cached_data = await self.cache_manager.get(cache_key)
            if cached_data and hasattr(cached_data, "price"):
                return cached_data.price
        return None

    async def get_recent_trades(
        self, symbol: str, limit: int = 100
    ) -> list[BinanceTrade]:
        """获取最近交易记录"""
        symbol_trades = [trade for trade in self.trade_buffer if trade.symbol == symbol]
        return symbol_trades[-limit:] if symbol_trades else []

    async def get_orderbook(
        self, symbol: str, market_type: str = "spot"
    ) -> BinanceOrderBook | None:
        """获取订单簿"""
        # 从CacheManager获取缓存OrderBook
        if self.cache_manager:
            cache_key = f"binance:{market_type}:{symbol}:orderbook"
            return await self.cache_manager.get(cache_key)
        return None

    async def calculate_taker_flow(
        self, symbol: str, window_minutes: int = 5
    ) -> dict[str, Any]:
        """计算Taker Flow数据（通过CeleryManager分布式计算）"""
        try:
            current_time = datetime.now(UTC)
            window_start = current_time.timestamp() - (window_minutes * 60)

            # 获取时间窗口内的交易数据
            recent_trades = [
                {
                    "symbol": trade.symbol,
                    "quantity": float(trade.quantity),
                    "is_buyer_maker": trade.is_buyer_maker,
                    "timestamp": trade.timestamp.isoformat(),
                }
                for trade in self.trade_buffer
                if (
                    trade.symbol == symbol
                    and trade.timestamp.timestamp() >= window_start
                )
            ]

            # 通过CeleryManager提交Taker Flow计算任务
            if hasattr(self, "celery_manager") and self.celery_manager:
                task_id = await self.celery_manager.submit_taker_flow_calculation(
                    recent_trades, window_minutes
                )
                if task_id:
                    # 等待任务完成（最多等待10秒）
                    result = await self.celery_manager.get_task_result(
                        task_id, timeout=10
                    )
                    if result and result.status == "SUCCESS":
                        return result.result
                    else:
                        logger.warning(
                            f"Taker Flow calculation task failed or timed out: {task_id}"
                        )

            # CeleryManager不可用时返回空结果
            logger.error("CeleryManager not available, cannot calculate Taker Flow")
            return {
                "buy_volume": 0,
                "sell_volume": 0,
                "net_flow": 0,
                "buy_ratio": 0,
                "trade_count": 0,
                "window_minutes": window_minutes,
                "error": "CeleryManager not available",
            }

        except Exception as e:
            logger.error(f"计算Taker Flow失败: {e}")
            return {}

    async def get_funding_rate(self, symbol: str) -> Decimal | None:
        """获取资金费率（期货）"""
        try:
            if not self.session:
                return None

            url = f"{self.futures_api_url}/fapi/v1/premiumIndex"
            params = {"symbol": symbol}

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    return Decimal(str(data.get("lastFundingRate", "0")))

        except Exception as e:
            logger.error(f"获取资金费率失败: {e}")

        return None

    def _generate_signature(self, query_string: str) -> str:
        """生成API签名"""
        if not self.api_secret:
            raise ValueError("API secret is required for signed requests")

        return hmac.new(
            self.api_secret.encode("utf-8"),
            query_string.encode("utf-8"),
            hashlib.sha256,
        ).hexdigest()

    async def _check_rate_limit(self, market_type: str, endpoint: str) -> bool:
        """检查限频"""
        try:
            current_time = time.time()
            weight = self.request_weights.get(market_type, {}).get(endpoint, 1)

            # 检查上次请求时间，确保不超过限频
            last_time = self.last_request_time.get(f"{market_type}:{endpoint}", 0)
            min_interval = weight / 10  # 基于权重计算最小间隔

            if current_time - last_time < min_interval:
                wait_time = min_interval - (current_time - last_time)
                await asyncio.sleep(wait_time)

            self.last_request_time[f"{market_type}:{endpoint}"] = time.time()
            return True

        except Exception as e:
            logger.error(f"限频检查失败: {e}")
            return False

    async def _make_signed_request(
        self, method: str, endpoint: str, params: dict = None, market_type: str = "spot"
    ) -> dict | None:
        """发送签名请求"""
        try:
            if not self.api_key or not self.api_secret:
                if self._public_mode:
                    raise ValueError("该操作需要API认证，但当前运行在公共模式")
                else:
                    raise ValueError("API credentials required for signed requests")

            # 检查限频
            await self._check_rate_limit(market_type, endpoint.split("/")[-1])

            # 准备参数
            params = params or {}
            params["timestamp"] = int(time.time() * 1000)
            params["recvWindow"] = 5000

            # 生成查询字符串和签名
            query_string = urlencode(params)
            signature = self._generate_signature(query_string)
            params["signature"] = signature

            # 选择API基础URL
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            url = f"{base_url}{endpoint}"

            # 设置请求头
            headers = {
                "X-MBX-APIKEY": self.api_key,
                "Content-Type": "application/x-www-form-urlencoded",
            }

            # 发送请求
            if method.upper() == "GET":
                async with self.session.get(
                    url, params=params, headers=headers
                ) as response:
                    return await self._handle_response(response)
            elif method.upper() == "POST":
                async with self.session.post(
                    url, data=params, headers=headers
                ) as response:
                    return await self._handle_response(response)
            elif method.upper() == "DELETE":
                async with self.session.delete(
                    url, data=params, headers=headers
                ) as response:
                    return await self._handle_response(response)

        except Exception as e:
            logger.error(f"签名请求失败: {e}")
            return None

    async def _handle_response(self, response: aiohttp.ClientResponse) -> dict | None:
        """处理API响应"""
        try:
            # 更新限频信息
            rate_limit_headers = [
                "x-mbx-used-weight",
                "x-mbx-used-weight-1m",
                "x-mbx-order-count-10s",
                "x-mbx-order-count-1m",
            ]

            for header in rate_limit_headers:
                if header in response.headers:
                    self.rate_limits[header] = int(response.headers[header])

            if response.status == 200:
                return await response.json()
            elif response.status == 429:
                logger.warning("API rate limit exceeded")
                # 等待后重试
                await asyncio.sleep(1)
                return None
            else:
                error_data = await response.text()
                logger.error(f"API请求失败: {response.status} - {error_data}")
                return None

        except Exception as e:
            logger.error(f"处理响应失败: {e}")
            return None

    # 注意: 交易执行方法已移除
    # 因为所有策略交易都通过IBKR执行IBIT期权，不再使用Binance进行交易
    # Binance客户端现在仅用于获取BTC市场数据

    async def query_order(
        self,
        symbol: str,
        order_id: int | None = None,
        client_order_id: str | None = None,
        market_type: str = "spot",
    ) -> dict | None:
        """查询订单"""
        try:
            if not order_id and not client_order_id:
                raise ValueError("Must provide either order_id or client_order_id")

            endpoint = "/api/v3/order" if market_type == "spot" else "/fapi/v1/order"

            params = {"symbol": symbol}
            if order_id:
                params["orderId"] = order_id
            if client_order_id:
                params["origClientOrderId"] = client_order_id

            result = await self._make_signed_request(
                "GET", endpoint, params, market_type
            )
            return result

        except Exception as e:
            logger.error(f"查询订单异常: {e}")
            return None

    async def get_open_orders(
        self, symbol: str | None = None, market_type: str = "spot"
    ) -> list[dict] | None:
        """获取未成交订单"""
        try:
            endpoint = (
                "/api/v3/openOrders" if market_type == "spot" else "/fapi/v1/openOrders"
            )

            params = {}
            if symbol:
                params["symbol"] = symbol

            result = await self._make_signed_request(
                "GET", endpoint, params, market_type
            )
            return result if result else []

        except Exception as e:
            logger.error(f"获取未成交订单异常: {e}")
            return []

    async def get_account_info(self, market_type: str = "spot") -> dict | None:
        """获取账户信息"""
        try:
            endpoint = (
                "/api/v3/account" if market_type == "spot" else "/fapi/v2/account"
            )

            result = await self._make_signed_request("GET", endpoint, {}, market_type)
            return result

        except Exception as e:
            logger.error(f"获取账户信息异常: {e}")
            return None

    async def get_position_info(self, symbol: str | None = None) -> list[dict] | None:
        """获取持仓信息（期货）"""
        try:
            endpoint = "/fapi/v2/positionRisk"

            params = {}
            if symbol:
                params["symbol"] = symbol

            result = await self._make_signed_request("GET", endpoint, params, "futures")
            return result if result else []

        except Exception as e:
            logger.error(f"获取持仓信息异常: {e}")
            return []

    async def get_exchange_info(self, market_type: str = "spot") -> dict | None:
        """获取交易所信息"""
        try:
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            endpoint = (
                "/api/v3/exchangeInfo"
                if market_type == "spot"
                else "/fapi/v1/exchangeInfo"
            )
            url = f"{base_url}{endpoint}"

            async with self.session.get(url) as response:
                if response.status == 200:
                    return await response.json()

        except Exception as e:
            logger.error(f"获取交易所信息失败: {e}")

        return None

    async def get_24hr_ticker(
        self, symbol: str | None = None, market_type: str = "spot"
    ) -> dict | list[dict] | None:
        """获取24小时价格变动统计"""
        try:
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            endpoint = (
                "/api/v3/ticker/24hr"
                if market_type == "spot"
                else "/fapi/v1/ticker/24hr"
            )
            url = f"{base_url}{endpoint}"

            params = {}
            if symbol:
                params["symbol"] = symbol

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()

        except Exception as e:
            logger.error(f"获取24小时统计失败: {e}")

        return None

    async def get_klines(
        self,
        symbol: str,
        interval: str = "1m",
        limit: int = 500,
        start_time: int | None = None,
        end_time: int | None = None,
        market_type: str = "spot",
    ) -> list[dict] | None:
        """获取K线数据

        Args:
            symbol: 交易对符号，如 'BTCUSDT'
            interval: K线间隔，支持：1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1M
            limit: 返回数据条数，最大1000
            start_time: 起始时间戳（毫秒）
            end_time: 结束时间戳（毫秒）
            market_type: 市场类型，'spot'为现货，'futures'为期货

        Returns:
            K线数据列表，每个元素包含：
            - timestamp: 时间戳
            - open: 开盘价
            - high: 最高价
            - low: 最低价
            - close: 收盘价
            - volume: 成交量
        """
        try:
            if not self.session:
                logger.warning("HTTP会话未初始化")
                return None

            # 选择API端点
            base_url = (
                self.rest_api_url if market_type == "spot" else self.futures_api_url
            )
            endpoint = "/api/v3/klines" if market_type == "spot" else "/fapi/v1/klines"
            url = f"{base_url}{endpoint}"

            # 构建请求参数
            params = {
                "symbol": symbol.upper(),
                "interval": interval,
                "limit": min(limit, 1000),  # Binance限制最大1000
            }

            if start_time:
                params["startTime"] = start_time
            if end_time:
                params["endTime"] = end_time

            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    raw_data = await response.json()

                    # 转换为标准格式
                    klines = []
                    for item in raw_data:
                        kline = {
                            "timestamp": int(item[0]),  # 开盘时间
                            "open": float(item[1]),  # 开盘价
                            "high": float(item[2]),  # 最高价
                            "low": float(item[3]),  # 最低价
                            "close": float(item[4]),  # 收盘价
                            "volume": float(item[5]),  # 成交量
                            "close_time": int(item[6]),  # 收盘时间
                            "quote_volume": float(item[7]),  # 成交额
                            "trades_count": int(item[8]),  # 成交笔数
                            "taker_buy_volume": float(item[9]),  # 主动买入成交量
                            "taker_buy_quote_volume": float(item[10]),  # 主动买入成交额
                            "symbol": symbol,
                            "interval": interval,
                        }
                        klines.append(kline)

                    logger.info(f"获取K线数据成功: {symbol} {interval} {len(klines)}条")
                    return klines
                else:
                    logger.error(f"获取K线数据失败: HTTP {response.status}")

        except Exception as e:
            logger.error(f"获取K线数据异常: {e}")

        return None

    async def subscribe_klines(
        self,
        symbols: list[str],
        intervals: list[str] | None = None,
        market_type: str = "spot",
        skip_connection_check: bool = False,
    ) -> bool:
        """订阅K线数据流

        Args:
            symbols: 交易对列表
            intervals: K线间隔列表，默认为['1m']
            market_type: 市场类型

        Returns:
            是否订阅成功
        """
        try:
            if not intervals:
                intervals = ["1m"]

            success_count = 0
            total_count = 0

            # 为每个交易对和间隔订阅K线数据
            for symbol in symbols:
                for interval in intervals:
                    total_count += 1
                    stream = f"{symbol.lower()}@kline_{interval}"

                    if market_type == "spot":
                        success = await self._subscribe_spot_stream(
                            stream, skip_connection_check=skip_connection_check
                        )
                        if success:
                            self.subscribed_streams.add(("spot", stream))
                            success_count += 1
                            logger.info(f"✅ 订阅现货K线成功: {symbol} {interval}")
                    else:
                        success = await self._subscribe_futures_stream(
                            stream, skip_connection_check=skip_connection_check
                        )
                        if success:
                            self.subscribed_streams.add(("futures", stream))
                            success_count += 1
                            logger.info(f"✅ 订阅期货K线成功: {symbol} {interval}")

                    if not success:
                        logger.warning(f"⚠️ 订阅K线失败: {symbol} {interval}")

            logger.info(f"K线订阅完成: {success_count}/{total_count}")
            return success_count > 0

        except Exception as e:
            logger.error(f"订阅K线数据流失败: {e}")
            return False
