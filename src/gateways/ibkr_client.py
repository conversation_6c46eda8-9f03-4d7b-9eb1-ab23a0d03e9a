from __future__ import annotations

import asyncio
import contextlib
import random
import threading
import time
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from ibapi.client import EClient
from ibapi.common import TickAttrib, TickerId
from ibapi.contract import Contract, ContractDetails
from ibapi.order import Order
from ibapi.ticktype import TickType
from ibapi.wrapper import EWrapper
import logging as _logging
_log = _logging.getLogger(__name__)

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import BaseEvent
from src.core.order_types import OrderSide


class OrderType(str, Enum):
    MARKET = "MARKET"
    LIMIT = "LIMIT"


class TimeInForce(str, Enum):
    DAY = "DAY"
    GTC = "GTC"


class Right(str, Enum):
    CALL = "C"
    PUT = "P"


@dataclass
class OptionContract:
    underlying: str
    expiry: date
    strike: Decimal
    right: Right
    multiplier: int = 100


@dataclass
class PlacedOrder:
    order_id: int
    status: str
    filled_qty: int = 0
    limit_price: Decimal | None = None
    avg_fill_price: Decimal | None = None
    remaining_qty: int = 0
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


@dataclass
class OptionChainContract:
    """期权链合约信息"""

    symbol: str
    con_id: int
    strike: Decimal
    expiry: date
    right: Right
    exchange: str = "SMART"
    currency: str = "USD"
    multiplier: int = 100


@dataclass
class Quote:
    """报价信息"""

    symbol: str
    bid: Decimal | None = None
    ask: Decimal | None = None
    last: Decimal | None = None
    bid_size: int = 0
    ask_size: int = 0
    last_size: int = 0
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))

    def spread_bps(self) -> float:
        """计算买卖价差(基点)"""
        if self.bid and self.ask and self.bid > 0:
            return float((self.ask - self.bid) / self.bid * 10000)
        return float("inf")


class IBKREvent(BaseEvent):
    """IBKR事件基类"""

    pass


class OrderStatusEvent(IBKREvent):
    """订单状态事件"""

    def __init__(
        self,
        order_id: int,
        status: str,
        filled: int,
        remaining: int,
        avg_fill_price: float,
    ):
        super().__init__()
        self.order_id = order_id
        self.status = status
        self.filled = filled
        self.remaining = remaining
        self.avg_fill_price = avg_fill_price


class PositionEvent(IBKREvent):
    """持仓事件"""

    def __init__(
        self, account: str, contract: Contract, position: Decimal, avg_cost: float
    ):
        super().__init__()
        self.account = account
        self.contract = contract
        self.position = position
        self.avg_cost = avg_cost


class TickPriceEvent(IBKREvent):
    """价格tick事件"""

    def __init__(
        self, req_id: int, tick_type: TickType, price: float, attrib: TickAttrib
    ):
        super().__init__()
        self.req_id = req_id
        self.tick_type = tick_type
        self.price = price
        self.attrib = attrib


class IBKRAsyncWrapper(EWrapper):
    """完整的异步IBKR API包装类"""

    def __init__(self, client_instance):
        super().__init__()
        self.client_instance = client_instance  # 引用主客户端
        self.connected = False
        self.next_order_id = None
        self.accounts = []
        self.connection_error = None

        # 数据存储
        self.positions: dict[str, dict] = {}
        self.orders: dict[int, PlacedOrder] = {}
        self.contract_details: dict[int, list[ContractDetails]] = {}
        self.market_data: dict[int, Quote] = {}
        self.account_values: dict[str, dict[str, str]] = {}

        # 请求ID管理
        self._req_id_counter = 1000
        self._pending_requests: dict[int, str] = {}  # req_id -> request_type

        # 异步事件
        self._connection_event = asyncio.Event()
        self._next_order_id_event = asyncio.Event()

        # 连接监控相关
        self.last_activity_time = time.time()
        self.market_data_farm_status = "unknown"  # unknown, active, inactive
        self.connection_quality_score = 100.0  # 0-100, 100 is perfect
        self.last_heartbeat_time: datetime | None = None
        self.reconnection_attempts = 0
        self.max_reconnection_attempts = 10

        # 性能监控
        self.message_count = 0
        self.last_message_time: datetime | None = None
        self.connection_latency_ms = 0.0

    def get_next_req_id(self) -> int:
        """获取下一个请求ID"""
        self._req_id_counter += 1
        return self._req_id_counter

    def update_activity(self):
        """更新活动时间和消息计数"""
        self.last_activity_time = time.time()
        self.message_count += 1
        self.last_message_time = datetime.now(UTC)

    def is_connection_stale(self, timeout_seconds: int = 300) -> bool:
        """检查连接是否过期（默认5分钟）"""
        return time.time() - self.last_activity_time > timeout_seconds

    def get_connection_health(self) -> dict:
        """获取连接健康状态"""
        return {
            "connected": self.connected,
            "market_data_farm_status": self.market_data_farm_status,
            "quality_score": self.connection_quality_score,
            "last_activity": self.last_activity_time,
            "message_count": self.message_count,
            "reconnection_attempts": self.reconnection_attempts,
            "is_stale": self.is_connection_stale(),
            "latency_ms": self.connection_latency_ms,
        }

    def _degrade_connection_quality(self, points: float):
        """降低连接质量评分"""
        self.connection_quality_score = max(0.0, self.connection_quality_score - points)

    def _improve_connection_quality(self, points: float):
        """提高连接质量评分"""
        self.connection_quality_score = min(
            100.0, self.connection_quality_score + points
        )

    def reset_connection_metrics(self):
        """重置连接指标（在新连接时调用）"""
        self.last_activity_time = time.time()
        self.market_data_farm_status = "unknown"
        self.connection_quality_score = 80.0  # 起始评分
        self.message_count = 0
        self.connection_latency_ms = 0.0

    # ============= 连接相关回调 =============
    def connectAck(self):
        """连接确认回调"""
        _log.info("[IBKR] 连接确认成功")
        self.connected = True
        self.update_activity()
        self.last_heartbeat_time = datetime.now(UTC)
        self.reconnection_attempts = 0  # 重置重连计数
        self._connection_event.set()

    def nextValidId(self, orderId: int):
        """接收下一个有效订单ID"""
        _log.info(f"[IBKR] NextValidId = {orderId}")
        self.update_activity()
        self.next_order_id = orderId
        self._next_order_id_event.set()

    def managedAccounts(self, accountsList: str):
        """接收管理的账户列表"""
        _log.info(f"[IBKR] 管理账户 = {accountsList}")
        self.update_activity()
        self.accounts = accountsList.split(",") if accountsList else []

    def error(self, reqId: int, errorCode: int, errorString: str):
        """增强的错误和信息消息处理"""
        # 更新活动时间
        self.update_activity()

        # 市场数据农场状态监控
        if errorCode == 2103:  # 市场数据农场连接中断
            _log.warning(
                f"[IBKR] 警告 {errorCode}: 市场数据农场连接中断: {errorString}"
            )
            self.market_data_farm_status = "inactive"
            self._degrade_connection_quality(30)
            if hasattr(self, "client_instance") and self.client_instance:
                with contextlib.suppress(Exception):
                    asyncio.create_task(
                        self.client_instance._handle_market_farm_down(errorString)
                    )

        elif errorCode in [2104, 2106]:  # 市场数据农场连接正常/恢复
            farm_status = (
                "HMDS data farm connection is OK"
                if "HMDS" in errorString
                else "Market data farm connection is OK"
            )
            _log.info(f"[IBKR] 信息 {errorCode}: {farm_status}: {errorString}")
            self.market_data_farm_status = "active"
            self._improve_connection_quality(10)
            if hasattr(self, "client_instance") and self.client_instance:
                with contextlib.suppress(Exception):
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            loop.create_task(
                                self.client_instance._handle_market_farm_recovered(
                                    errorString
                                )
                            )
                    except RuntimeError:
                        pass

        elif errorCode == 1102:  # 连接恢复通知
            print(f"[IBKR] 信息 {errorCode}: 连接恢复: {errorString}")
            self._improve_connection_quality(20)

        elif errorCode in [2158, 2119, 2157]:  # 其他信息性消息
            _log.info(f"[IBKR] 信息 {errorCode}: {errorString}")

        elif errorCode in [502, 504]:  # 连接相关错误
            _log.error(f"[IBKR] 连接错误 {errorCode}: {errorString}")
            self.connection_error = (errorCode, errorString)
            self._degrade_connection_quality(50)

        elif errorCode == 326:  # 客户端ID冲突
            _log.warning(f"[IBKR] 客户端ID冲突 {errorCode}: {errorString}")
            self.connection_error = (errorCode, errorString)

        elif errorCode in [10147, 10148]:  # 市场数据权限问题
            _log.error(f"[IBKR] 市场数据权限错误 {errorCode}: {errorString}")

        elif errorCode >= 2000:  # 警告信息（非致命错误）
            _log.warning(f"[IBKR] 警告 {errorCode}: {errorString}")

        else:  # 其他错误
            _log.error(f"[IBKR] 错误 {errorCode}: {errorString}")
            if errorCode < 1000:  # 严重错误
                self._degrade_connection_quality(40)

    def connectionClosed(self):
        """连接关闭回调"""
        _log.info("[IBKR] 连接已关闭")
        self.connected = False
        self.market_data_farm_status = "unknown"
        self.connection_quality_score = 0.0
        self._connection_event.clear()

    # ============= 订单相关回调 =============
    def orderStatus(
        self,
        orderId: int,
        status: str,
        filled: Decimal,
        remaining: Decimal,
        avgFillPrice: float,
        _permId: int,
        _parentId: int,
        _lastFillPrice: float,
        _clientId: int,
        _whyHeld: str,
        _mktCapPrice: float,
    ):
        """订单状态回调"""
        print(
            f"[IBKR] 订单状态 - ID:{orderId}, 状态:{status}, 已成交:{filled}, 剩余:{remaining}"
        )
        self.update_activity()

        # 更新订单状态
        if orderId in self.orders:
            order = self.orders[orderId]
            order.status = status
            order.filled_qty = int(filled)
            order.remaining_qty = int(remaining)
            order.avg_fill_price = (
                Decimal(str(avgFillPrice)) if avgFillPrice > 0 else None
            )

        # 发布事件
        if hasattr(self.client_instance, "_event_callbacks"):
            event = OrderStatusEvent(
                orderId, status, int(filled), int(remaining), avgFillPrice
            )
            self.client_instance._publish_event(event)

    def openOrder(self, orderId: int, contract: Contract, order: Order, _orderState):
        """开放订单回调"""
        print(
            f"[IBKR] 开放订单 - ID:{orderId}, 合约:{contract.symbol}, 操作:{order.action}"
        )

    def openOrderEnd(self):
        """开放订单结束回调"""
        print("[IBKR] 开放订单查询结束")

    # ============= 持仓相关回调 =============
    def position(
        self, account: str, contract: Contract, position: Decimal, avgCost: float
    ):
        """持仓回调"""
        self.update_activity()
        symbol_key = f"{contract.symbol}_{contract.secType}"
        if contract.secType == "OPT":
            symbol_key += f"_{contract.lastTradeDateOrContractMonth}_{contract.strike}_{contract.right}"

        self.positions[symbol_key] = {
            "account": account,
            "contract": contract,
            "position": position,
            "avgCost": avgCost,
        }

            _log.info(f"[IBKR] 持仓更新 - {symbol_key}: {position}@{avgCost}")

        # 发布事件
        if hasattr(self.client_instance, "_event_callbacks"):
            event = PositionEvent(account, contract, position, avgCost)
            self.client_instance._publish_event(event)

    def positionEnd(self):
        """持仓查询结束回调"""
        print("[IBKR] 持仓查询结束")

    # ============= 账户相关回调 =============
    def updateAccountValue(self, key: str, val: str, currency: str, accountName: str):
        """账户数值更新回调"""
        if accountName not in self.account_values:
            self.account_values[accountName] = {}
        self.account_values[accountName][key] = val

    def updateAccountTime(self, _timeStamp: str):
        """账户时间更新回调"""
        pass

    def accountDownloadEnd(self, accountName: str):
        """账户下载结束回调"""
        print(f"[IBKR] 账户 {accountName} 数据下载完成")

    # ============= 市场数据回调 =============
    def tickPrice(
        self, reqId: TickerId, tickType: TickType, price: float, attrib: TickAttrib
    ):
        """价格tick回调"""
        self.update_activity()
        if reqId not in self.market_data:
            self.market_data[reqId] = Quote(symbol=f"req_{reqId}")

        quote = self.market_data[reqId]

        if tickType == 1:  # TickType.BID
            quote.bid = Decimal(str(price))
        elif tickType == 2:  # TickType.ASK
            quote.ask = Decimal(str(price))
        elif tickType == 4:  # TickType.LAST
            quote.last = Decimal(str(price))

        # 发布价格事件
        if hasattr(self.client_instance, "_event_callbacks"):
            event = TickPriceEvent(reqId, tickType, price, attrib)
            self.client_instance._publish_event(event)

    def tickSize(self, reqId: TickerId, tickType: TickType, size: Decimal):
        """数量tick回调"""
        self.update_activity()
        if reqId not in self.market_data:
            self.market_data[reqId] = Quote(symbol=f"req_{reqId}")

        quote = self.market_data[reqId]

        if tickType == 0:  # TickType.BID_SIZE
            quote.bid_size = int(size)
        elif tickType == 3:  # TickType.ASK_SIZE
            quote.ask_size = int(size)
        elif tickType == 5:  # TickType.LAST_SIZE
            quote.last_size = int(size)

    # ============= 合约详情回调 =============
    def contractDetails(self, reqId: int, contractDetails: ContractDetails):
        """合约详情回调"""
        if reqId not in self.contract_details:
            self.contract_details[reqId] = []
        self.contract_details[reqId].append(contractDetails)

        print(
            f"[IBKR] 合约详情 - {contractDetails.contract.symbol} {contractDetails.contract.strike} {contractDetails.contract.right}"
        )

    def contractDetailsEnd(self, reqId: int):
        """合约详情查询结束回调"""
        print(f"[IBKR] 合约详情查询结束 - reqId: {reqId}")


class IBKRClient(BaseComponent):
    """重构的异步IBKR客户端"""

    def __init__(
        self,
        host="127.0.0.1",
        port=7497,
        client_id=None,  # 动态分配客户端ID，避免冲突
        account_id: str = "",
        config_manager=None,
        event_bus=None,
    ):
        super().__init__("IBKRClient", config_manager)

        # 从配置管理器或环境变量读取配置
        if config_manager:
            self.host = config_manager.get("exchanges.ibkr.host", host)
            self.port = int(config_manager.get("exchanges.ibkr.port", port))
            self.client_id = int(
                config_manager.get(
                    "exchanges.ibkr.client_id", client_id or self._generate_client_id()
                )
            )
            self.account_id = config_manager.get(
                "exchanges.ibkr.account_id", account_id
            )
            # 读取行情重试与等待配置
            try:
                self.quote_max_attempts = int(
                    config_manager.get("exchanges.ibkr.quotes.max_attempts", 3)
                )
                self.quote_wait_seconds = float(
                    config_manager.get("exchanges.ibkr.quotes.per_attempt_wait", 3.0)
                )
            except Exception:
                self.quote_max_attempts = 3
                self.quote_wait_seconds = 3.0
        else:
            self.host = host
            self.port = int(port)
            self.client_id = int(client_id or self._generate_client_id())
            self.account_id = account_id
            self.quote_max_attempts = 3
            self.quote_wait_seconds = 3.0

        # 维护订阅映射：req_id -> {'symbol':..., 'contract_type':..., 'contract': Contract}
        self._active_subscriptions: dict[int, dict[str, Any]] = {}
        # 上次市场数据场状态，用于抑制重复操作
        self._market_farm_down: bool = False

        # 使用注入的EventBus或创建新的（依赖注入将提供单例）
        self.event_bus = event_bus

        # 使用完整的异步Wrapper
        self.wrapper = IBKRAsyncWrapper(self)
        self.client = EClient(self.wrapper)
        self.api_thread: threading.Thread | None = None

        self._assignment_callbacks: list[Callable] = []

        # 连接和重连状态
        self._is_connecting = False
        self._last_connection_attempt = None
        self._reconnection_task: asyncio.Task | None = None
        self._heartbeat_task: asyncio.Task | None = None
        self._connection_monitor_task: asyncio.Task | None = None

        # 重连策略参数
        self.base_retry_delay = 1.0  # 基础重试延迟（秒）
        self.max_retry_delay = 300.0  # 最大重试延迟（5分钟）
        self.backoff_multiplier = 2.0  # 指数退避倍数
        self.jitter_factor = 0.1  # 随机抖动因子

        # 心跳相关
        self.heartbeat_interval = 300  # 5分钟发送一次心跳
        self.connection_timeout = 600  # 10分钟连接超时

    def _generate_client_id(self) -> int:
        """生成唯一的客户端ID，避免冲突"""
        # 使用时间戳和随机数生成唯一ID，范围1000-9999
        return int((time.time() % 10000) * random.randint(100, 999)) % 9000 + 1000

    def calculate_retry_delay(self, attempt: int) -> float:
        """计算指数退避重试延迟"""
        # 指数退避: base_delay * (multiplier ^ attempt)
        delay = self.base_retry_delay * (self.backoff_multiplier**attempt)
        delay = min(delay, self.max_retry_delay)  # 限制最大延迟

        # 添加随机抖动避免惊群效应
        jitter = delay * self.jitter_factor * random.random()
        return delay + jitter

    async def start_connection_monitor(self):
        """启动连接监控任务"""
        if self._connection_monitor_task and not self._connection_monitor_task.done():
            return

        self._connection_monitor_task = asyncio.create_task(
            self._connection_monitor_loop()
        )

    async def _connection_monitor_loop(self):
        """连接监控循环"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒检查一次

                if not self.is_connected():
                    print("[IBKR] 连接监控: 检测到连接中断，尝试重连")
                    await self._attempt_reconnection()
                elif self.wrapper.is_connection_stale():
                    print("[IBKR] 连接监控: 连接过期，发送心跳")
                    await self._send_heartbeat()

            except Exception as e:
                print(f"[IBKR] 连接监控循环错误: {e}")
                await asyncio.sleep(60)  # 错误时稍长一些延迟

    async def _send_heartbeat(self):
        """发送心跳信号保持连接活跃"""
        try:
            # 发送一个简单的请求来保持连接活跃
            await asyncio.get_event_loop().run_in_executor(
                None, self.client.reqCurrentTime
            )
            self.wrapper.last_heartbeat_time = datetime.now(UTC)
            print("[IBKR] 心跳信号已发送")
        except Exception as e:
            print(f"[IBKR] 发送心跳失败: {e}")

    async def _attempt_reconnection(self):
        """尝试重新连接（使用指数退避）"""
        if self._reconnection_task and not self._reconnection_task.done():
            return  # 已有重连任务在运行

        self._reconnection_task = asyncio.create_task(self._reconnection_loop())

    async def _reconnection_loop(self):
        """重连循环（指数退避）"""
        attempt = 0

        while attempt < self.wrapper.max_reconnection_attempts:
            try:
                delay = self.calculate_retry_delay(attempt)
                print(f"[IBKR] 等待 {delay:.1f} 秒后进行第 {attempt + 1} 次重连尝试")

                await asyncio.sleep(delay)

                print(f"[IBKR] 开始第 {attempt + 1} 次重连尝试")
                if await self._connect_internal():
                    print("[IBKR] 重连成功")
                    # 重新订阅之前的市场数据
                    await self._resubscribe_market_data()
                    return

                attempt += 1
                self.wrapper.reconnection_attempts = attempt

            except Exception as e:
                print(f"[IBKR] 重连尝试 {attempt + 1} 失败: {e}")
                attempt += 1

        print(
            f"[IBKR] 达到最大重连尝试次数 {self.wrapper.max_reconnection_attempts}，停止重连"
        )

    async def _resubscribe_market_data(self):
        """重新订阅之前的市场数据"""
        try:
            for _req_id, sub_info in self._active_subscriptions.items():
                print(f"[IBKR] 重新订阅市场数据: {sub_info.get('symbol', 'unknown')}")
                # 这里可以添加具体的重新订阅逻辑
                # 根据之前的订阅信息重新订阅
        except Exception as e:
            print(f"[IBKR] 重新订阅市场数据失败: {e}")

    async def connect_async(self) -> bool:
        """异步连接方法"""
        return await self._connect_internal()

    async def _connect_internal(self) -> bool:
        """内部异步连接实现"""

        # 尝试多个客户端ID直到找到可用的
        max_retries = 5
        original_client_id = self.client_id

        for retry in range(max_retries):
            current_client_id = original_client_id + retry

            # 先检查这个client_id是否已被占用的标志
            client_id_conflict = False
            connection_failed = False

            try:
                print(
                    f"[IBKR] 尝试连接 {self.host}:{self.port} client_id={current_client_id} (第{retry + 1}次尝试)"
                )
                self._is_connecting = True
                self._last_connection_attempt = datetime.now(UTC)

                # 确保断开之前的连接
                if self.client.isConnected():
                    await self._disconnect_internal()
                    await asyncio.sleep(1)

                # 重置连接状态和事件
                self.wrapper.connected = False
                self.wrapper.next_order_id = None
                self.wrapper.connection_error = None
                self.wrapper._connection_event.clear()
                self.wrapper._next_order_id_event.clear()

                # 使用当前客户端ID连接（放到线程并加超时，防止阻塞事件循环）
                try:
                    await asyncio.wait_for(
                        asyncio.to_thread(
                            self.client.connect, self.host, self.port, current_client_id
                        ),
                        timeout=5.0,
                    )
                except TimeoutError:
                    print("[IBKR][ERROR] connect() 调用超时，可能网络不通或网关无响应")
                    connection_failed = True
                    # 提前进入下一次重试
                    await self._disconnect_internal()
                    if retry == max_retries - 1:
                        return False
                    continue

                # 启动API消息循环线程
                self.api_thread = threading.Thread(target=self.client.run, daemon=True)
                self.api_thread.start()

                # 等待连接建立（使用事件而不是轮询）
                print("[IBKR] 等待连接建立...")
                try:
                    await asyncio.wait_for(
                        self.wrapper._connection_event.wait(), timeout=15.0
                    )
                except TimeoutError:
                    print("[IBKR][ERROR] 连接超时")
                    connection_failed = True

                # 优先检查客户端ID冲突，无论是否连接成功
                if self.wrapper.connection_error:
                    error_code, error_msg = self.wrapper.connection_error
                    print(f"[IBKR][ERROR] 检测到错误 {error_code}: {error_msg}")

                    if error_code == 326:  # 客户号码已被使用
                        print(
                            f"[IBKR][INFO] 客户端ID {current_client_id} 被占用，尝试下一个..."
                        )
                        client_id_conflict = True
                    elif error_code in [502, 504]:
                        print("[IBKR][INFO] 连接错误，尝试下一个客户端ID...")
                        connection_failed = True

                # 如果是客户端ID冲突，立即尝试下一个ID
                if client_id_conflict:
                    await self._disconnect_internal()
                    continue  # 跳到下一个client_id

                # 如果是其他连接失败，也尝试下一个ID（除非是最后一次尝试）
                if connection_failed:
                    await self._disconnect_internal()
                    if retry == max_retries - 1:  # 最后一次尝试失败
                        return False
                    continue

                print("[IBKR] ✅ 连接成功")

                # 等待nextValidId
                print("[IBKR] 等待API就绪...")
                try:
                    await asyncio.wait_for(
                        self.wrapper._next_order_id_event.wait(), timeout=10.0
                    )
                    print(f"[IBKR] API就绪！NextOrderId: {self.wrapper.next_order_id}")
                except TimeoutError:
                    print("[IBKR][WARN] 未能获取nextValidId - API可能未完全就绪")
                    # 再次检查是否是客户端ID冲突导致的
                    if (
                        self.wrapper.connection_error
                        and self.wrapper.connection_error[0] == 326
                    ):
                        print("[IBKR][INFO] 由于客户端ID冲突，尝试下一个ID...")
                        await self._disconnect_internal()
                        continue  # 跳到下一个client_id

                # 请求账户信息
                try:
                    await self._request_account_data()
                except Exception as account_error:
                    print(f"[IBKR][WARN] 请求账户信息失败: {account_error}")
                    # 账户信息请求失败不影响连接成功

                # 更新成功使用的客户端ID
                self.client_id = current_client_id

                # 重置连接指标并启动监控
                self.wrapper.reset_connection_metrics()

                # 启动连接监控任务
                await self.start_connection_monitor()

                print(
                    f"[IBKR][SUCCESS] 连接成功！使用client_id={self.client_id}，监控已启动"
                )
                return True

            except Exception as e:
                print(f"[IBKR][ERROR] 连接异常 (client_id={current_client_id}): {e}")
                await self._disconnect_internal()

                if retry == max_retries - 1:  # 最后一次尝试
                    await self._print_connection_diagnostics()
                    return False

                print("[IBKR][INFO] 尝试下一个客户端ID...")
                continue
            finally:
                self._is_connecting = False

        print(
            f"[IBKR][ERROR] 所有客户端ID ({original_client_id}-{original_client_id + max_retries - 1}) 都无法连接"
        )
        return False

    async def _request_account_data(self):
        """请求账户数据"""
        try:
            if not self.client or not self.client.isConnected():
                print("[IBKR][WARN] 客户端未连接，跳过账户数据请求")
                return

            if self.account_id:
                self.client.reqAccountUpdates(True, self.account_id)
            else:
                self.client.reqAccountUpdates(True, "")
            self.client.reqPositions()
            print("[IBKR] 已请求账户信息")
        except Exception as e:
            print(f"[IBKR][WARN] 请求账户信息失败: {e}")

    async def _print_connection_diagnostics(self):
        """打印连接诊断信息"""
        print("[IBKR][DIAGNOSE] 连接失败诊断:")
        print("[IBKR][DIAGNOSE] 1. 确认TWS/Gateway正在运行并已登录")
        print("[IBKR][DIAGNOSE] 2. TWS: Edit → Global Configuration → API → Settings")
        print("[IBKR][DIAGNOSE] 3. Gateway: Configure → API → Settings")
        print("[IBKR][DIAGNOSE] 4. 确认以下设置:")
        print("[IBKR][DIAGNOSE]    - 'Enable ActiveX and Socket Clients' 已勾选")
        print("[IBKR][DIAGNOSE]    - 'Read-Only API' 已取消勾选（如需下单）")
        print(f"[IBKR][DIAGNOSE]    - Socket端口设置为{self.port}")
        print("[IBKR][DIAGNOSE] 5. 重启TWS/Gateway")

    def disconnect(self) -> None:
        """同步断开连接"""
        asyncio.run(self._disconnect_internal())

    async def disconnect_async(self) -> None:
        """异步断开连接"""
        await self._disconnect_internal()

    async def _disconnect_internal(self) -> None:
        """内部断开连接实现"""
        try:
            if self.client.isConnected():
                self.client.disconnect()

            if self.api_thread and self.api_thread.is_alive():
                # 给线程时间优雅关闭
                await asyncio.sleep(0.5)
                if self.api_thread.is_alive():
                    print("[IBKR][WARN] API线程未及时关闭")

            self.wrapper.connected = False
            print("[IBKR] 连接已断开")

        except Exception as e:
            print(f"[IBKR][WARN] 断开连接时异常: {e}")

    def is_connected(self) -> bool:
        """检查连接状态"""
        # 严格的连接状态检查
        if not self.client.isConnected():
            return False

        if not self.wrapper.connected:
            return False

        # 检查API线程是否还在运行
        if not self.api_thread or not self.api_thread.is_alive():
            return False

        # 检查是否有有效的订单ID（表示API已就绪）
        return self.wrapper.next_order_id is not None

    # ============= 测试辅助方法（仅测试用） =============
    def simulate_assignment_for_test(self, contract: OptionContract, qty: int):
        """仅用于测试：模拟行权分配，更新持仓。生产代码不会调用。"""
        try:
            symbol = (
                f"{contract.underlying}_{contract.right.value}{int(contract.strike)}"
            )
            # 简单更新 positions 字典
            pos = self.wrapper.positions.get(symbol) or {
                "position": 0,
                "avg_cost": 0.0,
                "contract": None,
            }
            pos["position"] += qty

            # 创建mock合约对象，以便get_positions可以正确提取symbol
            if pos["contract"] is None:
                from types import SimpleNamespace

                pos["contract"] = SimpleNamespace(symbol=contract.underlying)

            self.wrapper.positions[symbol] = pos

            # 触发assignment回调
            if hasattr(self, "_assignment_callbacks"):
                for callback in self._assignment_callbacks:
                    try:
                        from datetime import datetime

                        callback(contract, qty, datetime.now())
                    except Exception:
                        pass
        except Exception:
            pass

    async def ensure_connected(self) -> bool:
        """确保连接状态"""
        if not self.is_connected():
            print("[IBKR] 检测到连接断开，尝试重连...")
            return await self._connect_internal()
        return True

    # ============= 账户查询方法 =============

    def get_account_buying_power(self) -> Decimal:
        """获取账户购买力"""
        account_name = self.account_id or (
            self.wrapper.accounts[0] if self.wrapper.accounts else ""
        )
        if account_name in self.wrapper.account_values:
            buying_power = self.wrapper.account_values[account_name].get(
                "BuyingPower", "0"
            )
            return Decimal(buying_power)
        return Decimal("0")

    def get_positions(self) -> dict[str, int]:
        """获取持仓信息"""
        positions = {}
        for _symbol_key, pos_data in self.wrapper.positions.items():
            # 只返回非零持仓
            position_qty = int(pos_data["position"])
            if position_qty != 0:
                # 检查合约是否存在，从symbol_key提取标准symbol
                contract = pos_data.get("contract")
                if contract and hasattr(contract, "symbol"):
                    symbol = contract.symbol
                    positions[symbol] = position_qty
                else:
                    # 使用symbol_key作为fallback
                    positions[_symbol_key] = position_qty
        return positions

    async def get_account_summary(self) -> dict[str, Any]:
        """获取账户摘要"""
        if not await self.ensure_connected():
            return {}

        account_name = self.account_id or (
            self.wrapper.accounts[0] if self.wrapper.accounts else ""
        )
        return self.wrapper.account_values.get(account_name, {})

    # ============= 合约构建辅助方法 =============

    def _build_option_contract(self, c: OptionContract) -> Contract:
        """构建期权合约"""
        contract = Contract()
        contract.symbol = c.underlying
        contract.secType = "OPT"
        contract.currency = "USD"
        contract.exchange = "SMART"
        contract.lastTradeDateOrContractMonth = c.expiry.strftime("%Y%m%d")
        contract.strike = float(c.strike)
        contract.right = c.right.value
        contract.multiplier = str(c.multiplier)
        return contract

    def _build_stock_contract(self, symbol: str) -> Contract:
        """构建股票合约"""
        contract = Contract()
        contract.symbol = symbol
        contract.secType = "STK"
        contract.currency = "USD"
        contract.exchange = "SMART"
        return contract

    def _build_contract_from_option_chain(
        self, option_chain_contract: OptionChainContract
    ) -> Contract:
        """从期权链合约构建IBKR合约"""
        contract = Contract()
        contract.symbol = option_chain_contract.symbol.split("_")[0]  # 提取underlying
        contract.secType = "OPT"
        contract.currency = option_chain_contract.currency
        contract.exchange = option_chain_contract.exchange
        contract.lastTradeDateOrContractMonth = option_chain_contract.expiry.strftime(
            "%Y%m%d"
        )
        contract.strike = float(option_chain_contract.strike)
        contract.right = option_chain_contract.right.value
        contract.multiplier = str(option_chain_contract.multiplier)
        if hasattr(option_chain_contract, "con_id") and option_chain_contract.con_id:
            contract.conId = option_chain_contract.con_id
        return contract

    # ============= 市场数据方法 =============

    async def get_market_data(self, symbol: str) -> dict[str, Any] | None:
        """获取股票/ETF的实时市场数据"""
        try:
            if not await self.ensure_connected():
                raise RuntimeError("IBKR not connected")

            # 创建股票合约
            contract = self._build_stock_contract(symbol)

            # 请求市场数据
            req_id = len(self.wrapper.market_data) + 1000

            # 清理之前的数据
            if req_id in self.wrapper.market_data:
                del self.wrapper.market_data[req_id]

            # 请求实时数据
            self.client.reqMktData(req_id, contract, "", False, False, [])

            # 等待数据返回
            max_wait = 5.0  # 最多等待5秒
            start_time = asyncio.get_event_loop().time()

            while (asyncio.get_event_loop().time() - start_time) < max_wait:
                if req_id in self.wrapper.market_data:
                    quote = self.wrapper.market_data[req_id]

                    # 检查是否有有效的价格数据
                    if quote.last and quote.last > 0:
                        result = {
                            "symbol": symbol,
                            "last_price": float(quote.last),
                            "bid": float(quote.bid) if quote.bid else None,
                            "ask": float(quote.ask) if quote.ask else None,
                            "timestamp": datetime.now(UTC).isoformat(),
                        }

                        # 取消数据订阅
                        self.client.cancelMktData(req_id)

                        return result

                await asyncio.sleep(0.1)

            # 超时，取消订阅
            self.client.cancelMktData(req_id)

            if self.logger:
                await self.logger.warning(f"Market data timeout for {symbol}")

            return None

        except Exception as e:
            print(f"[IBKR][ERROR] 获取市场数据失败 {symbol}: {e}")
            return None

    async def get_option_chain(
        self, underlying: str, expiry: date | None = None
    ) -> dict[str, Any] | None:
        """获取期权链数据"""
        try:
            if not await self.ensure_connected():
                raise RuntimeError("IBKR not connected")

            try:
                # 创建底层资产合约
                underlying_contract = self._build_stock_contract(underlying)

                # 请求期权链数据
                req_id = len(getattr(self.wrapper, "option_chains", {})) + 2000

                # 创建期权链请求
                self.client.reqSecDefOptParams(
                    req_id,
                    underlying_contract.symbol,
                    "",
                    underlying_contract.secType,
                    underlying_contract.conId
                    if hasattr(underlying_contract, "conId")
                    else 0,
                )

                # 等待期权链数据返回
                max_wait = 10.0  # 期权链数据可能需要更长时间
                wait_time = 0.0
                step = 0.1

                while wait_time < max_wait:
                    if (
                        hasattr(self.wrapper, "option_chains")
                        and req_id in self.wrapper.option_chains
                    ):
                        option_data = self.wrapper.option_chains[req_id]

                        # 处理期权链数据，转换为标准格式
                        formatted_chain = self._format_option_chain_data(
                            option_data, expiry
                        )

                        if hasattr(self, "logger") and self.logger:
                            await self.logger.info(
                                f"Retrieved option chain for {underlying}"
                            )

                        return formatted_chain

                    await asyncio.sleep(step)
                    wait_time += step

                # 超时处理
                if hasattr(self, "logger") and self.logger:
                    await self.logger.warning(
                        f"Option chain request timeout for {underlying}"
                    )
                return None

            except Exception as api_error:
                if hasattr(self, "logger") and self.logger:
                    await self.logger.error(
                        f"Option chain API error for {underlying}: {api_error}"
                    )
                return None

        except Exception as e:
            print(f"[IBKR][ERROR] 获取期权链失败 {underlying}: {e}")
            return None

    def _format_option_chain_data(
        self, option_data: dict, expiry: date | None = None
    ) -> dict[str, Any]:
        """格式化期权链数据为标准格式"""
        try:
            # 实现IBKR期权链数据格式化逻辑
            formatted_options = []

            # 处理IBKR secDefOptParams响应数据
            if "option_chains" in option_data:
                chains = option_data["option_chains"]

                for chain in chains:
                    # 提取期权参数
                    strikes = chain.get("strikes", [])
                    expirations = chain.get("expirations", [])
                    rights = chain.get("rights", ["C", "P"])

                    # 为每个执行价和到期日组合创建期权
                    for strike in strikes:
                        for exp in expirations:
                            # 如果指定了特定到期日，只处理匹配的
                            if expiry and exp != expiry.strftime("%Y%m%d"):
                                continue

                            for right in rights:
                                option_info = {
                                    "strike": float(strike),
                                    "expiry": exp,
                                    "right": "CALL" if right == "C" else "PUT",
                                    "exchange": chain.get("exchange", "SMART"),
                                    "multiplier": chain.get("multiplier", 100),
                                    "currency": chain.get("currency", "USD"),
                                    # 添加合约标识信息
                                    "local_symbol": f"{chain.get('underlying', '')}_{exp}_{strike}_{right}",
                                    "trading_class": chain.get("trading_class", ""),
                                }
                                formatted_options.append(option_info)

            # 获取底层价格信息
            underlying_symbol = option_data.get("underlying", "")
            spot_price = option_data.get("spot_price", 0.0)

            # 如果没有现货价格，先设为0（后续在调用时异步获取）
            # 同步函数中不能使用await，现货价格需要在调用时单独获取
            if spot_price == 0.0:
                spot_price = 0.0  # 将在调用方异步获取

            return {
                "underlying": underlying_symbol,
                "spot_price": spot_price,
                "expiry": expiry,
                "options": formatted_options,
                "total_options": len(formatted_options),
                "timestamp": datetime.now(UTC).isoformat(),
            }

        except Exception as e:
            # 同步函数中不能使用await，使用print作为替代
            print(f"[IBKR][ERROR] Format option chain data failed: {e}")
            return None

    # ============= 订单管理方法 =============

    async def place_order(
        self,
        contract: OptionContract | str,
        side: OrderSide,
        qty: int,
        order_type: OrderType,
        limit_price: Decimal | None = None,
        tif: TimeInForce = TimeInForce.DAY,
    ) -> PlacedOrder:
        """异步下单方法"""
        # 检查连接状态
        if not await self.ensure_connected():
            raise RuntimeError("IBKR not connected")
        # 获取订单ID
        if not self.wrapper.next_order_id or self.wrapper.next_order_id is None:
            raise RuntimeError("No valid order ID available")

        order_id = self.wrapper.next_order_id
        self.wrapper.next_order_id += 1

        # 构造合约
        if isinstance(contract, OptionContract):
            ib_contract = self._build_option_contract(contract)
        else:
            ib_contract = self._build_stock_contract(str(contract))

        # 构造订单
        order = Order()
        order.orderId = order_id
        order.action = side.value.upper()
        order.totalQuantity = int(qty)
        order.tif = tif.value

        if order_type == OrderType.MARKET:
            order.orderType = "MKT"
        elif order_type == OrderType.LIMIT:
            if limit_price is None:
                raise ValueError("limit_price required for LIMIT order")
            order.orderType = "LMT"
            order.lmtPrice = float(limit_price)
        else:
            raise ValueError(f"Unsupported order type: {order_type}")

        print(
            f"[IBKR] 提交订单 symbol={ib_contract.symbol} action={side.value} qty={qty} orderType={order.orderType}"
        )

        try:
            # 创建订单记录
            placed_order = PlacedOrder(
                order_id=order_id,
                status="PENDING",
                filled_qty=0,
                remaining_qty=qty,
                limit_price=limit_price,
            )
            self.wrapper.orders[order_id] = placed_order

            # 提交订单
            self.client.placeOrder(order_id, ib_contract, order)

            # 等待初始状态更新
            await asyncio.sleep(0.5)

            # 返回更新后的订单状态
            return self.wrapper.orders.get(order_id, placed_order)

        except Exception as e:
            print(f"[IBKR][ERROR] 下单异常: {e}")
            if order_id in self.wrapper.orders:
                del self.wrapper.orders[order_id]
            raise RuntimeError(f"Place order failed: {e}") from e

    async def get_order_status(self, order_id: int) -> PlacedOrder | None:
        """获取订单状态"""
        return self.wrapper.orders.get(order_id)

    async def cancel_order(self, order_id: int) -> bool:
        """取消订单"""
        if not await self.ensure_connected():
            return False

        try:
            self.client.cancelOrder(order_id)
            await asyncio.sleep(0.2)
            return True
        except Exception as e:
            print(f"[IBKR][ERROR] 取消订单失败: {e}")
            return False

    # ============= 市场数据订阅方法 =============

    async def subscribe_market_data(
        self, symbol: str, contract_type: str = "STK"
    ) -> int:
        """订阅市场数据"""
        if not await self.ensure_connected():
            raise RuntimeError("IBKR not connected")

        req_id = self.wrapper.get_next_req_id()
        self.wrapper._pending_requests[req_id] = f"market_data_{symbol}"

        # 构造合约
        if contract_type == "STK":
            contract = self._build_stock_contract(symbol)
        else:
            raise ValueError(f"Unsupported contract type: {contract_type}")

        try:
            # 请求市场数据
            self.client.reqMktData(req_id, contract, "", False, False, [])
            print(f"[IBKR] 已订阅市场数据 - {symbol}, req_id: {req_id}")
            return req_id
        except Exception as e:
            print(f"[IBKR][ERROR] 订阅市场数据失败: {e}")

            if req_id in self.wrapper._pending_requests:
                del self.wrapper._pending_requests[req_id]
            raise

    async def unsubscribe_market_data(self, req_id: int) -> bool:
        """取消市场数据订阅"""
        if not await self.ensure_connected():
            return False

        try:
            self.client.cancelMktData(req_id)
            if req_id in self.wrapper.market_data:
                del self.wrapper.market_data[req_id]
            if req_id in self.wrapper._pending_requests:
                del self.wrapper._pending_requests[req_id]
            print(f"[IBKR] 已取消市场数据订阅 - req_id: {req_id}")
            return True
        except Exception as e:
            print(f"[IBKR][ERROR] 取消市场数据订阅失败: {e}")
            return False

    def get_market_data_by_req_id(self, req_id: int) -> Quote | None:
        """根据请求ID获取市场数据"""
        return self.wrapper.market_data.get(req_id)

    async def get_quotes(
        self,
        contracts: list[OptionContract | str],
        *,
        max_attempts: int | None = None,
        per_attempt_wait: float | None = None,
    ) -> dict[str, Quote]:
        """获取多个合约的报价（增强：重试 + 更长等待 + 有效性校验）"""
        quotes: dict[str, Quote] = {}
        # 采用实例级配置作为默认
        if max_attempts is None:
            max_attempts = getattr(self, "quote_max_attempts", 3)
        if per_attempt_wait is None:
            per_attempt_wait = getattr(self, "quote_wait_seconds", 3.0)
        req_ids: list[int] = []

        try:
            # 为每个合约订阅市场数据
            for contract in contracts:
                if isinstance(contract, OptionContract):
                    symbol = f"{contract.underlying}_{contract.expiry}_{contract.strike}_{contract.right.value}"

                    try:
                        ib_contract = self._build_option_contract(contract)
                        valid = False

                        for _attempt in range(max_attempts):
                            req_id = self.wrapper.get_next_req_id()
                            try:
                                self.client.reqMktData(
                                    req_id, ib_contract, "", False, False, []
                                )
                            except Exception:
                                await asyncio.sleep(0.1)
                                continue

                            waited = 0.0
                            step = 0.1
                            while waited < per_attempt_wait:
                                qd = self.wrapper.market_data.get(req_id)
                                if qd and qd.bid and qd.ask:
                                    try:
                                        bid_v = float(qd.bid)
                                        ask_v = float(qd.ask)
                                        if bid_v > 0 and ask_v >= bid_v:
                                            quotes[symbol] = Quote(
                                                symbol=symbol,
                                                bid=qd.bid,
                                                ask=qd.ask,
                                                last=qd.last or qd.bid,
                                                bid_size=qd.bid_size,
                                                ask_size=qd.ask_size,
                                                timestamp=datetime.now(UTC),
                                            )
                                            valid = True
                                            break
                                    except Exception:
                                        pass
                                await asyncio.sleep(step)
                                waited += step

                                # 取消订阅本次尝试
                                from contextlib import suppress

                                with suppress(Exception):
                                    self.client.cancelMktData(req_id)

                            if valid:
                                break
                            await asyncio.sleep(0.2)  # 短暂退避

                        if not valid:
                            # 未获取到有效报价，提示可能的权限/OPRA问题，并返回空报价
                            try:
                                if hasattr(self, "logger") and self.logger:
                                    await self.logger.warning(
                                        f"No valid option quote for {symbol}. Check US Options/OPRA real-time market data permissions on the IBKR account."
                                    )
                            except Exception:
                                pass
                            quotes[symbol] = Quote(symbol=symbol)

                    except Exception as e:
                        # 期权报价获取失败：返回空报价
                        if hasattr(self, "logger") and self.logger:
                            await self.logger.warning(
                                f"Failed to get option quote for {symbol}: {e}"
                            )
                        quotes[symbol] = Quote(symbol=symbol)
                else:
                    symbol = str(contract)
                    req_id = await self.subscribe_market_data(symbol)
                    req_ids.append(req_id)

                    # 等待数据到达
                    await asyncio.sleep(0.5)

                    quote = self.get_market_data_by_req_id(req_id)
                    if quote:
                        quote.symbol = symbol
                        quotes[symbol] = quote
                    else:
                        # 创建默认报价
                        quotes[symbol] = Quote(symbol=symbol)

        finally:
            # 清理通过 subscribe_market_data() 订阅的通道
            for req_id in req_ids:
                await self.unsubscribe_market_data(req_id)

        return quotes

    # ============= 行权事件处理方法 =============

    def subscribe_assignment_events(
        self, callback: Callable[[OptionContract, int, datetime], None]
    ):
        """
        订阅行权/被行权事件

        Args:
            callback: 回调函数，参数为 (contract, qty, timestamp)
        """
        if not hasattr(self, "_assignment_callbacks"):
            self._assignment_callbacks = []
        self._assignment_callbacks.append(callback)

    # ============= BaseComponent接口实现 =============
    async def _initialize_impl(self) -> bool:
        """初始化IBKR客户端"""
        try:
            if self.logger:
                await self.logger.info("初始化IBKR客户端")

            # 进行预连接检查
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"IBKR客户端初始化失败: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动IBKR客户端"""
        try:
            _log.debug("[DEBUG] IBKRClient._start_impl() 开始")
            if self.logger:
                await self.logger.info("启动IBKR客户端连接...")

            # 使用总体超时来避免启动阻塞
            # 考虑重试机制：5次尝试 × (15秒连接 + 10秒API等待) = 125秒，给60秒应该足够前2-3次尝试
            startup_timeout = 60.0  # 60秒总体超时，给重试机制充足时间

            try:
                if self.logger:
                    await self.logger.info(
                        f"🔗 尝试连接IBKR (超时{startup_timeout}秒)..."
                    )

                # 为整个连接过程设置超时
                success = await asyncio.wait_for(
                    self.connect_async(), timeout=startup_timeout
                )

                if success and self.logger:
                    await self.logger.info("✅ IBKR客户端启动成功")
                elif not success and self.logger:
                    await self.logger.warning("⚠️ IBKR客户端连接失败，但系统继续运行")

                _log.debug(f"[DEBUG] IBKRClient._start_impl() 连接结果: {success}")
                return success

            except TimeoutError:
                if self.logger:
                    await self.logger.warning(
                        f"⏰ IBKR客户端连接超时({startup_timeout}秒)，跳过连接以避免阻塞系统启动"
                    )
                _log.debug("[DEBUG] IBKRClient._start_impl() 超时，返回True")
                # 返回True让系统继续启动，IBKR连接失败不应阻塞整个系统
                return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ IBKR客户端启动失败: {e}")
            _log.debug(f"[DEBUG] IBKRClient._start_impl() 异常: {e}")
            # 即使IBKR失败，也返回True让系统继续启动
            return True

    async def _stop_impl(self) -> bool:
        """停止IBKR客户端"""
        try:
            if self.logger:
                await self.logger.info("停止IBKR客户端...")

            # 停止所有监控任务
            await self.stop_all_monitors()

            # 断开连接
            await self.disconnect_async()

            if self.logger:
                await self.logger.info("IBKR客户端已停止")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"IBKR客户端停止失败: {e}")
            return False

    async def _handle_market_farm_down(self, message: str):
        """处理市场数据农场连接中断"""
        print(f"[IBKR] 处理市场数据农场中断: {message}")
        self._market_farm_down = True

        # 降低连接质量评分
        self.wrapper._degrade_connection_quality(20)

        # 等待一段时间后尝试重新订阅市场数据
        await asyncio.sleep(5)
        await self._resubscribe_market_data()

    async def _handle_market_farm_recovered(self, message: str):
        """处理市场数据农场连接恢复"""
        print(f"[IBKR] 处理市场数据农场恢复: {message}")
        self._market_farm_down = False

        # 提高连接质量评分
        self.wrapper._improve_connection_quality(15)

        # 確保所有订阅都正常工作
        await self._verify_subscriptions()

    async def _verify_subscriptions(self):
        """验证并修复市场数据订阅"""
        try:
            for req_id, sub_info in self._active_subscriptions.items():
                # 检查每个订阅是否正常接收数据
                symbol = sub_info.get("symbol", "unknown")
                print(f"[IBKR] 验证订阅: {symbol} (req_id: {req_id})")
        except Exception as e:
            print(f"[IBKR] 验证订阅失败: {e}")

    def get_connection_health_report(self) -> dict:
        """获取详细的连接健康报告"""
        health = self.wrapper.get_connection_health()

        # 添加额外信息
        health.update(
            {
                "client_id": self.client_id,
                "host": self.host,
                "port": self.port,
                "account_id": self.account_id,
                "market_farm_down": self._market_farm_down,
                "active_subscriptions_count": len(self._active_subscriptions),
                "is_connecting": self._is_connecting,
                "last_connection_attempt": self._last_connection_attempt.isoformat()
                if self._last_connection_attempt
                else None,
            }
        )

        return health

    async def stop_all_monitors(self):
        """停止所有监控任务"""
        tasks_to_cancel = [
            self._connection_monitor_task,
            self._reconnection_task,
            self._heartbeat_task,
        ]

        for task in tasks_to_cancel:
            if task and not task.done():
                task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await task

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            if self.is_connected():
                return HealthCheckResult(
                    status=HealthStatus.HEALTHY, message="IBKR连接正常"
                )
            else:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY, message="IBKR连接断开"
                )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"IBKR健康检查失败: {e}"
            )
