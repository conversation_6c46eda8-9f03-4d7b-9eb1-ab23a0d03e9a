"""
Telegram Bot通知和查询服务 - TelegramBotHandler

提供完整的Telegram Bot功能，支持：
- 查询命令：/status、/positions、/risk、/pnl、/signals
- 控制命令：/pause、/resume、/emergency
- 用户授权验证和权限控制
- 实时告警推送
- 友好的Markdown格式消息展示
"""

import asyncio
import contextlib
import fcntl
import json
import os
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from enum import Enum
from typing import Any

from telegram import Bot, BotCommand, Update
from telegram.constants import ParseMode
from telegram.error import TelegramError
from telegram.ext import (
    Application,
    CommandHandler,
    ContextTypes,
    MessageHandler,
    filters,
)

from src.analysis.common_types import AlertLevel
from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.config_manager import ConfigManager
from src.core.event_bus import EventBus

# 告警级别到emoji的映射
ALERT_LEVEL_EMOJIS = {
    AlertLevel.CRITICAL: "🚨",  # 严重
    AlertLevel.ERROR: "❌",  # 错误
    AlertLevel.WARNING: "⚠️",  # 警告
    AlertLevel.INFO: "ℹ️",  # 信息
    AlertLevel.STATUS: "📊",  # 状态更新
}


class UserPermission(Enum):
    """用户权限级别"""

    ADMIN = "admin"  # 管理员 - 所有权限
    TRADER = "trader"  # 交易员 - 查询和基本控制
    VIEWER = "viewer"  # 观察者 - 仅查询权限


@dataclass
class TelegramUser:
    """Telegram用户信息"""

    user_id: int
    username: str | None = None
    first_name: str | None = None
    last_name: str | None = None
    permission: UserPermission = UserPermission.VIEWER
    is_active: bool = True
    last_activity: datetime = field(default_factory=lambda: datetime.now(UTC))

    @property
    def display_name(self) -> str:
        """显示名称"""
        if self.username:
            return f"@{self.username}"
        elif self.first_name:
            name = self.first_name
            if self.last_name:
                name += f" {self.last_name}"
            return name
        else:
            return f"User_{self.user_id}"


@dataclass
class CommandResult:
    """命令执行结果"""

    success: bool
    message: str
    data: dict[str, Any] | None = None
    alert_level: AlertLevel = AlertLevel.INFO

    # 由依赖注入自动注入的事件总线（类级属性用于属性注入）
    event_bus: EventBus | None = None


class TelegramBotHandler(BaseComponent):
    """
    Telegram Bot处理器

    提供完整的Telegram Bot功能，包括查询、控制、告警等
    """

    def __init__(
        self,
        config_manager: ConfigManager | None = None,
        bot_token: str | None = None,
        authorized_users: list[int] = None,
        admin_users: list[int] = None,
        enable_notifications: bool = True,
    ):
        super().__init__(component_name="TelegramBotHandler")

        # 存储配置管理器
        self.config_manager = config_manager

        # 从环境变量或配置获取参数
        import os

        from dotenv import load_dotenv

        load_dotenv()

        self.bot_token = bot_token or os.getenv("TELEGRAM_BOT_TOKEN")
        chat_id = os.getenv("TELEGRAM_CHAT_ID")

        # 设置授权用户
        if authorized_users is None and chat_id:
            authorized_users = [int(chat_id)]
        if admin_users is None and chat_id:
            admin_users = [int(chat_id)]

        self.authorized_users = set(authorized_users or [])
        self.admin_users = set(admin_users or [])
        self.enable_notifications = enable_notifications

        self.application: Application | None = None
        self.bot: Bot | None = None
        self.users: dict[int, TelegramUser] = {}
        self.command_handlers: dict[str, Callable] = {}
        self.notification_subscribers: dict[AlertLevel, list[int]] = {
            level: [] for level in AlertLevel
        }

        # 外部服务接口
        self.system_status_provider: Callable | None = None
        self.position_provider: Callable | None = None
        self.risk_provider: Callable | None = None
        self.pnl_provider: Callable | None = None
        self.signal_provider: Callable | None = None
        self.strategy_controller: Callable | None = None

        self._is_running = False
        # 事件总线（由依赖注入注入）
        self.event_bus: EventBus | None = None

    async def _register_command_handlers(self):
        """注册命令处理器"""
        # 查询命令
        self.application.add_handler(CommandHandler("start", self._handle_start))
        self.application.add_handler(CommandHandler("help", self._handle_help))
        self.application.add_handler(CommandHandler("status", self._handle_status))
        self.application.add_handler(
            CommandHandler("positions", self._handle_positions)
        )
        self.application.add_handler(CommandHandler("risk", self._handle_risk))
        self.application.add_handler(CommandHandler("pnl", self._handle_pnl))
        self.application.add_handler(CommandHandler("signals", self._handle_signals))

        # 控制命令
        self.application.add_handler(CommandHandler("pause", self._handle_pause))
        self.application.add_handler(CommandHandler("resume", self._handle_resume))
        self.application.add_handler(
            CommandHandler("emergency", self._handle_emergency)
        )

        # 设置命令
        self.application.add_handler(
            CommandHandler("subscribe", self._handle_subscribe)
        )
        self.application.add_handler(
            CommandHandler("unsubscribe", self._handle_unsubscribe)
        )

        # 未知命令处理器
        self.application.add_handler(
            MessageHandler(filters.COMMAND, self._handle_unknown_command)
        )

        # 普通消息处理器
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_message)
        )

    async def _setup_bot_commands(self):
        """设置Bot命令菜单"""
        commands = [
            BotCommand("start", "开始使用Bot"),
            BotCommand("help", "显示帮助信息"),
            BotCommand("status", "查看系统状态"),
            BotCommand("positions", "查看持仓信息"),
            BotCommand("risk", "查看风险指标"),
            BotCommand("pnl", "查看损益统计"),
            BotCommand("signals", "查看交易信号"),
            BotCommand("pause", "暂停策略"),
            BotCommand("resume", "恢复策略"),
            BotCommand("emergency", "紧急停止"),
            BotCommand("subscribe", "订阅通知"),
            BotCommand("unsubscribe", "取消订阅"),
        ]

        await self.bot.set_my_commands(commands)

    async def _initialize_users(self):
        """初始化用户信息"""
        # 初始化授权用户
        for user_id in self.authorized_users:
            permission = (
                UserPermission.ADMIN
                if user_id in self.admin_users
                else UserPermission.TRADER
            )
            self.users[user_id] = TelegramUser(user_id=user_id, permission=permission)

            # 默认订阅所有通知
            for level in AlertLevel:
                if user_id not in self.notification_subscribers[level]:
                    self.notification_subscribers[level].append(user_id)

    def _check_authorization(self, user_id: int) -> bool:
        """检查用户授权"""
        return user_id in self.authorized_users

    def _check_permission(
        self, user_id: int, required_permission: UserPermission
    ) -> bool:
        """检查用户权限"""
        if not self._check_authorization(user_id):
            return False

        user = self.users.get(user_id)
        if not user or not user.is_active:
            return False

        # 权限级别：ADMIN > TRADER > VIEWER
        permission_levels = {
            UserPermission.VIEWER: 0,
            UserPermission.TRADER: 1,
            UserPermission.ADMIN: 2,
        }

        return permission_levels.get(user.permission, 0) >= permission_levels.get(
            required_permission, 0
        )

    async def _update_user_activity(self, update: Update):
        """更新用户活动时间"""
        user_id = update.effective_user.id
        if user_id in self.users:
            user = self.users[user_id]
            user.last_activity = datetime.now(UTC)

            # 更新用户信息
            if update.effective_user.username:
                user.username = update.effective_user.username
            if update.effective_user.first_name:
                user.first_name = update.effective_user.first_name
            if update.effective_user.last_name:
                user.last_name = update.effective_user.last_name

    async def _send_unauthorized_message(self, update: Update):
        """发送未授权消息"""
        message = (
            "🚫 *访问被拒绝*\n\n"
            "您没有权限使用此Bot。\n"
            f"用户ID: `{update.effective_user.id}`\n\n"
            "如需访问权限，请联系管理员。"
        )
        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def _send_permission_denied_message(
        self, update: Update, required_permission: str
    ):
        """发送权限不足消息"""
        message = (
            "⚠️ *权限不足*\n\n"
            f"此操作需要 `{required_permission}` 权限。\n"
            "请联系管理员升级您的权限。"
        )
        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    # 命令处理器
    async def _handle_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/start命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        user = self.users[user_id]
        message = (
            f"👋 欢迎使用BTC期权网格交易系统Bot！\n\n"
            f"用户：{user.display_name}\n"
            f"权限：{user.permission.value}\n\n"
            f"使用 /help 查看可用命令。"
        )

        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def _handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/help命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        help_text = (
            "📖 *命令帮助*\n\n"
            "*查询命令：*\n"
            "/status - 查看系统状态\n"
            "/positions - 查看持仓信息\n"
            "/risk - 查看风险指标\n"
            "/pnl - 查看损益统计\n"
            "/signals - 查看交易信号\n\n"
        )

        if self._check_permission(user_id, UserPermission.TRADER):
            help_text += "*控制命令：*\n/pause - 暂停策略\n/resume - 恢复策略\n"

        if self._check_permission(user_id, UserPermission.ADMIN):
            help_text += "/emergency - 紧急停止\n"

        help_text += (
            "\n*设置命令：*\n"
            "/subscribe - 订阅通知\n"
            "/unsubscribe - 取消订阅\n\n"
            "💡 使用命令时，Bot会实时查询系统状态并返回最新信息。"
        )

        await update.message.reply_text(help_text, parse_mode=ParseMode.MARKDOWN)

    async def _handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/status命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        try:
            # 获取系统状态
            if self.system_status_provider:
                status_data = await self.system_status_provider()
            else:
                status_data = {
                    "status": "unknown",
                    "message": "Status provider not configured",
                }

            # 格式化状态消息
            message = self._format_status_message(status_data)

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling status command: {e}")
            await update.message.reply_text(
                "❌ 获取系统状态时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_positions(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """处理/positions命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        try:
            # 获取持仓信息
            if self.position_provider:
                position_data = await self.position_provider()
            else:
                position_data = {
                    "positions": [],
                    "message": "Position provider not configured",
                }

            # 格式化持仓消息
            message = self._format_positions_message(position_data)

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling positions command: {e}")
            await update.message.reply_text(
                "❌ 获取持仓信息时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_risk(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/risk命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        try:
            # 获取风险指标
            if self.risk_provider:
                risk_data = await self.risk_provider()
            else:
                risk_data = {
                    "risk_metrics": {},
                    "message": "Risk provider not configured",
                }

            # 格式化风险消息
            message = self._format_risk_message(risk_data)

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling risk command: {e}")
            await update.message.reply_text(
                "❌ 获取风险指标时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_pnl(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/pnl命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        try:
            # 获取损益统计
            if self.pnl_provider:
                pnl_data = await self.pnl_provider()
            else:
                pnl_data = {"pnl_summary": {}, "message": "PnL provider not configured"}

            # 格式化PnL消息
            message = self._format_pnl_message(pnl_data)

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling pnl command: {e}")
            await update.message.reply_text(
                "❌ 获取损益统计时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_signals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/signals命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        try:
            # 获取交易信号
            if self.signal_provider:
                signal_data = await self.signal_provider()
            else:
                signal_data = {
                    "signals": [],
                    "message": "Signal provider not configured",
                }

            # 格式化信号消息
            message = self._format_signals_message(signal_data)

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling signals command: {e}")
            await update.message.reply_text(
                "❌ 获取交易信号时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_pause(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/pause命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        if not self._check_permission(user_id, UserPermission.TRADER):
            await self._send_permission_denied_message(update, "TRADER")
            return

        await self._update_user_activity(update)

        try:
            # 执行暂停操作
            if self.strategy_controller:
                result = await self.strategy_controller("pause")
                if result.get("success", False):
                    message = "⏸️ *策略已暂停*\n\n所有交易策略已暂停执行。"
                else:
                    message = f"❌ 暂停失败：{result.get('message', '未知错误')}"
            else:
                message = "❌ 策略控制器未配置"

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling pause command: {e}")
            await update.message.reply_text(
                "❌ 执行暂停操作时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_resume(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理/resume命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        if not self._check_permission(user_id, UserPermission.TRADER):
            await self._send_permission_denied_message(update, "TRADER")
            return

        await self._update_user_activity(update)

        try:
            # 执行恢复操作
            if self.strategy_controller:
                result = await self.strategy_controller("resume")
                if result.get("success", False):
                    message = "▶️ *策略已恢复*\n\n所有交易策略已恢复执行。"
                else:
                    message = f"❌ 恢复失败：{result.get('message', '未知错误')}"
            else:
                message = "❌ 策略控制器未配置"

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling resume command: {e}")
            await update.message.reply_text(
                "❌ 执行恢复操作时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_emergency(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """处理/emergency命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        if not self._check_permission(user_id, UserPermission.ADMIN):
            await self._send_permission_denied_message(update, "ADMIN")
            return

        await self._update_user_activity(update)

        try:
            # 执行紧急停止操作
            if self.strategy_controller:
                result = await self.strategy_controller("emergency")
                if result.get("success", False):
                    message = "🛑 *紧急停止已执行*\n\n系统已进入紧急停止状态，所有交易活动已停止。"
                else:
                    message = f"❌ 紧急停止失败：{result.get('message', '未知错误')}"
            else:
                message = "❌ 策略控制器未配置"

            await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

            # 通知所有管理员
            await self.send_notification(
                f"🛑 紧急停止已由 {self.users[user_id].display_name} 执行",
                AlertLevel.CRITICAL,
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling emergency command: {e}")
            await update.message.reply_text(
                "❌ 执行紧急停止时发生错误，请稍后重试。", parse_mode=ParseMode.MARKDOWN
            )

    async def _handle_subscribe(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """处理/subscribe命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        # 订阅所有级别的通知
        subscribed_levels = []
        for level in AlertLevel:
            if user_id not in self.notification_subscribers[level]:
                self.notification_subscribers[level].append(user_id)
                subscribed_levels.append(level.value)

        if subscribed_levels:
            message = f"✅ 已订阅通知：{' '.join(subscribed_levels)}"
        else:
            message = "ℹ️ 您已订阅所有通知"

        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def _handle_unsubscribe(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """处理/unsubscribe命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        await self._update_user_activity(update)

        # 取消订阅所有级别的通知
        unsubscribed_levels = []
        for level in AlertLevel:
            if user_id in self.notification_subscribers[level]:
                self.notification_subscribers[level].remove(user_id)
                unsubscribed_levels.append(level.value)

        if unsubscribed_levels:
            message = f"✅ 已取消订阅：{' '.join(unsubscribed_levels)}"
        else:
            message = "ℹ️ 您未订阅任何通知"

        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def _handle_unknown_command(
        self, update: Update, context: ContextTypes.DEFAULT_TYPE
    ):
        """处理未知命令"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        message = "❓ 未知命令\n\n使用 /help 查看可用命令列表。"
        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    async def _handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理普通消息"""
        user_id = update.effective_user.id

        if not self._check_authorization(user_id):
            await self._send_unauthorized_message(update)
            return

        message = "💬 收到您的消息\n\n请使用命令与Bot交互，输入 /help 查看可用命令。"
        await update.message.reply_text(message, parse_mode=ParseMode.MARKDOWN)

    # 消息格式化方法
    def _format_status_message(self, status_data: dict[str, Any]) -> str:
        """格式化系统状态消息"""
        status = status_data.get("status", "unknown")
        uptime = status_data.get("uptime", "N/A")
        active_strategies = status_data.get("active_strategies", 0)
        total_positions = status_data.get("total_positions", 0)

        status_emoji = {
            "running": "🟢",
            "paused": "🟡",
            "stopped": "🔴",
            "error": "❌",
        }.get(status, "⚪")

        message = (
            f"📊 *系统状态*\n\n"
            f"状态：{status_emoji} {status.upper()}\n"
            f"运行时间：{uptime}\n"
            f"活跃策略：{active_strategies}\n"
            f"总持仓数：{total_positions}\n\n"
            f"更新时间：{datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}"
        )

        return message

    def _format_positions_message(self, position_data: dict[str, Any]) -> str:
        """格式化持仓信息消息"""
        positions = position_data.get("positions", [])
        total_value = position_data.get("total_value", 0)

        if not positions:
            return "📈 *持仓信息*\n\n暂无持仓"

        message = "📈 *持仓信息*\n\n"

        for pos in positions[:10]:  # 限制显示前10个持仓
            symbol = pos.get("symbol", "N/A")
            size = pos.get("size", 0)
            pnl = pos.get("unrealized_pnl", 0)
            pnl_emoji = "🟢" if pnl >= 0 else "🔴"

            message += f"{symbol}: {size:+.4f} {pnl_emoji} {pnl:+.2f}\n"

        if len(positions) > 10:
            message += f"... 还有 {len(positions) - 10} 个持仓\n"

        message += f"\n总价值：${total_value:,.2f}"

        return message

    def _format_risk_message(self, risk_data: dict[str, Any]) -> str:
        """格式化风险指标消息"""
        metrics = risk_data.get("risk_metrics", {})

        delta = metrics.get("total_delta", 0)
        gamma = metrics.get("total_gamma", 0)
        margin_usage = metrics.get("margin_usage_rate", 0)
        var_1d = metrics.get("var_1d", 0)

        # 风险等级判断
        risk_level = "🟢 低"
        if margin_usage > 0.7 or abs(delta) > 0.5:
            risk_level = "🔴 高"
        elif margin_usage > 0.5 or abs(delta) > 0.3:
            risk_level = "🟡 中"

        message = (
            f"⚠️ *风险指标*\n\n"
            f"风险等级：{risk_level}\n"
            f"Delta敞口：{delta:+.4f}\n"
            f"Gamma敞口：{gamma:+.6f}\n"
            f"保证金使用率：{margin_usage:.1%}\n"
            f"1日VaR：${var_1d:,.2f}\n\n"
            f"更新时间：{datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}"
        )

        return message

    def _format_pnl_message(self, pnl_data: dict[str, Any]) -> str:
        """格式化损益统计消息"""
        summary = pnl_data.get("pnl_summary", {})

        total_pnl = summary.get("total_pnl", 0)
        realized_pnl = summary.get("realized_pnl", 0)
        unrealized_pnl = summary.get("unrealized_pnl", 0)
        daily_pnl = summary.get("daily_pnl", 0)

        total_emoji = "🟢" if total_pnl >= 0 else "🔴"
        daily_emoji = "🟢" if daily_pnl >= 0 else "🔴"

        message = (
            f"💰 *损益统计*\n\n"
            f"总损益：{total_emoji} ${total_pnl:+,.2f}\n"
            f"已实现：${realized_pnl:+,.2f}\n"
            f"未实现：${unrealized_pnl:+,.2f}\n"
            f"今日损益：{daily_emoji} ${daily_pnl:+,.2f}\n\n"
            f"更新时间：{datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}"
        )

        return message

    def _format_signals_message(self, signal_data: dict[str, Any]) -> str:
        """格式化交易信号消息"""
        signals = signal_data.get("signals", [])

        if not signals:
            return "📡 *交易信号*\n\n暂无活跃信号"

        message = "📡 *交易信号*\n\n"

        for signal in signals[:5]:  # 限制显示前5个信号
            signal_type = signal.get("type", "unknown")
            strength = signal.get("strength", 0)
            timestamp = signal.get("timestamp", "")

            strength_emoji = (
                "🔴" if strength > 0.8 else "🟡" if strength > 0.5 else "🟢"
            )

            message += f"{strength_emoji} {signal_type}: {strength:.2f}\n"
            message += f"   时间: {timestamp}\n\n"

        return message

    async def _safe_log(self, log_call, *args, **kwargs):
        """安全调用logger，兼容同步/异步方法并吞掉异常（仅用于日志）。"""
        try:
            res = log_call(*args, **kwargs)
            if asyncio.iscoroutine(res):
                await res
        except Exception:
            pass

    # 通知发送方法
    async def send_notification(
        self,
        message: str,
        alert_level: AlertLevel = AlertLevel.INFO,
        data: dict[str, Any] | None = None,
        target_users: list[int] | None = None,
    ) -> bool:
        """发送通知消息"""
        if self.logger:
            await self._safe_log(
                self.logger.debug, f"📧 尝试发送通知: {message[:50]}..."
            )
            await self._safe_log(
                self.logger.debug,
                f"   通知启用: {self.enable_notifications}, 运行状态: {self._is_running}",
            )

        if not self.enable_notifications:
            if self.logger:
                await self._safe_log(
                    self.logger.warning, "⚠️ 通知功能已禁用，跳过通知发送"
                )
            return False

        if not self._is_running:
            if self.logger:
                await self._safe_log(
                    self.logger.warning, "⚠️ Telegram Bot未运行，跳过通知发送"
                )
            return False

        try:
            # 确定目标用户
            if target_users:
                recipients = target_users
            else:
                recipients = self.notification_subscribers.get(alert_level, [])

            if self.logger:
                await self._safe_log(self.logger.debug, f"   目标用户: {recipients}")

            if not recipients:
                if self.logger:
                    await self.logger.warning("⚠️ 没有目标用户，跳过通知发送")
                return True

            # 格式化消息
            emoji = ALERT_LEVEL_EMOJIS.get(alert_level, "ℹ️")
            formatted_message = f"{emoji} {message}"

            if data:
                formatted_message += f"\n\n```json\n{json.dumps(data, indent=2, ensure_ascii=False)}\n```"

            formatted_message += (
                f"\n\n⏰ {datetime.now(UTC).strftime('%Y-%m-%d %H:%M:%S UTC')}"
            )

            # 发送给所有目标用户
            success_count = 0
            for user_id in recipients:
                try:
                    await self.bot.send_message(
                        chat_id=user_id,
                        text=formatted_message,
                        parse_mode=None,
                    )
                    success_count += 1
                except TelegramError as e:
                    if self.logger:
                        await self._safe_log(
                            self.logger.error,
                            f"Failed to send notification to user {user_id}: {e}",
                        )
                except Exception as e:
                    if self.logger:
                        await self._safe_log(
                            self.logger.error,
                            f"Unexpected error sending to user {user_id}: {e}",
                        )

            if self.logger:
                await self._safe_log(
                    self.logger.info,
                    f"Notification attempted to {len(recipients)} users, success={success_count}",
                )
            return success_count > 0

        except Exception as e:
            if self.logger:
                await self._safe_log(
                    self.logger.error, f"Error sending notification: {e}"
                )
            return False

    async def send_alert(
        self,
        alert_type: str,
        severity: str,
        description: str,
        data: dict[str, Any] | None = None,
    ) -> bool:
        """发送告警消息"""
        alert_level = {
            "critical": AlertLevel.CRITICAL,
            "high": AlertLevel.ERROR,
            "medium": AlertLevel.WARNING,
            "low": AlertLevel.INFO,
        }.get(severity.lower(), AlertLevel.INFO)

        message = f"*{alert_type.upper()}*\n\n{description}"

        return await self.send_notification(message, alert_level, data)

    async def _handle_system_event(self, event):
        """处理系统事件（用于关闭/启动通知）"""
        try:
            et = getattr(event, "event_type", "")
            if et != "SystemEvent":
                return

            action = getattr(event, "action", None)
            status = getattr(event, "status", None)

            if (action == "shutdown" and status in {"stopping", "stopped"}) or (
                action == "start" and status == "running"
            ):
                # 将系统事件转换为友好通知
                msg = "⏹️ 系统正在停止" if action == "shutdown" else "🚀 系统已启动"
                await self.send_notification(msg, AlertLevel.STATUS)
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"_handle_system_event error: {e}")

    async def _handle_risk_warning_event(self, event):
        try:
            data = getattr(event, "data", {}) or {}
            symbol = data.get("symbol")
            msg = "Risk warning triggered"
            if symbol:
                msg += f" for {symbol}"
            await self.send_alert("risk_warning", "medium", msg, data)
        except Exception:
            pass

    async def _handle_expiry_alert_event(self, event):
        try:
            data = getattr(event, "data", {}) or {}
            level = str(data.get("risk_level", "medium"))
            symbol = data.get("symbol", "")
            title = f"Expiry alert ({level})"
            if symbol:
                title += f": {symbol}"
            await self.send_alert("expiry_alert", level, title, data)
        except Exception:
            pass

    async def _handle_expiry_action_event(self, event):
        try:
            data = getattr(event, "data", {}) or {}
            symbol = data.get("symbol", "")
            action = data.get("action", "execute")
            title = f"Expiry action: {action}"
            if symbol:
                title += f" for {symbol}"
            await self.send_alert("expiry_action", "low", title, data)
        except Exception:
            pass

    async def _handle_expiry_backup_event(self, event):
        try:
            data = getattr(event, "data", {}) or {}
            symbol = data.get("symbol", "")
            backup = data.get("backup_action", "backup")
            title = f"Expiry backup: {backup}"
            if symbol:
                title += f" for {symbol}"
            await self.send_alert("expiry_backup", "medium", title, data)
        except Exception:
            pass

    # 服务提供者注册方法
    def register_system_status_provider(self, provider: Callable):
        """注册系统状态提供者"""
        self.system_status_provider = provider

    def register_position_provider(self, provider: Callable):
        """注册持仓信息提供者"""
        self.position_provider = provider

    def register_risk_provider(self, provider: Callable):
        """注册风险指标提供者"""
        self.risk_provider = provider

    def register_pnl_provider(self, provider: Callable):
        """注册损益统计提供者"""
        self.pnl_provider = provider

    def register_signal_provider(self, provider: Callable):
        """注册交易信号提供者"""
        self.signal_provider = provider

    def register_strategy_controller(self, controller: Callable):
        """注册策略控制器"""
        self.strategy_controller = controller

    # 用户管理方法
    def add_authorized_user(
        self, user_id: int, permission: UserPermission = UserPermission.VIEWER
    ):
        """添加授权用户"""
        self.authorized_users.add(user_id)
        self.users[user_id] = TelegramUser(user_id=user_id, permission=permission)

        # 默认订阅通知
        for level in AlertLevel:
            if user_id not in self.notification_subscribers[level]:
                self.notification_subscribers[level].append(user_id)

    def remove_authorized_user(self, user_id: int):
        """移除授权用户"""
        self.authorized_users.discard(user_id)
        if user_id in self.users:
            del self.users[user_id]

        # 移除通知订阅
        for level in AlertLevel:
            if user_id in self.notification_subscribers[level]:
                self.notification_subscribers[level].remove(user_id)

    def update_user_permission(self, user_id: int, permission: UserPermission):
        """更新用户权限"""
        if user_id in self.users:
            self.users[user_id].permission = permission

    def get_user_info(self, user_id: int) -> TelegramUser | None:
        """获取用户信息"""
        return self.users.get(user_id)

    def list_users(self) -> list[TelegramUser]:
        """列出所有用户"""
        return list(self.users.values())

    # BaseComponent抽象方法实现
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("🚀 开始初始化TelegramBotHandler...")
                await self.logger.info(
                    f"   Bot Token: {'已配置' if self.bot_token else '未配置'}"
                )
                await self.logger.info(f"   授权用户: {list(self.authorized_users)}")

            # 订阅系统事件与风险/到期事件（在初始化阶段绑定回调）
            try:
                if self.event_bus:
                    from src.core.event_bus import EventType as _ET

                    await self.event_bus.subscribe(
                        event_types="SystemEvent",
                        callback=self._handle_system_event,
                        subscriber_id="telegram_bot_handler",
                    )
                    await self.event_bus.subscribe(
                        _ET.RISK_WARNING, self._handle_risk_warning_event
                    )
                    await self.event_bus.subscribe(
                        _ET.EXPIRY_ALERT, self._handle_expiry_alert_event
                    )
                    await self.event_bus.subscribe(
                        _ET.EXPIRY_ACTION_EXECUTE, self._handle_expiry_action_event
                    )
                    await self.event_bus.subscribe(
                        _ET.EXPIRY_BACKUP_ACTION, self._handle_expiry_backup_event
                    )
                    if self.logger:
                        await self.logger.info(
                            "已订阅 System/Risk/Expiry 事件以接收通知"
                        )
            except Exception as sub_e:
                if self.logger:
                    await self.logger.warning(f"订阅 SystemEvent 失败: {sub_e}")

            # 检查必要配置
            if not self.bot_token:
                if self.logger:
                    await self.logger.error("❌ Telegram Bot Token未配置")
                return False

            if not self.authorized_users and self.logger:
                await self.logger.warning("⚠️ 没有授权用户配置")

            if self.logger:
                await self.logger.info("📱 创建Telegram Bot应用...")
            # 使用简单构建链以兼容测试中的Mock（不调用 request()）
            self.application = Application.builder().token(self.bot_token).build()
            self.bot = self.application.bot

            if self.logger:
                await self.logger.info("📋 注册命令处理器...")
            # 注册命令处理器
            await self._register_command_handlers()

            if self.logger:
                await self.logger.info("⚙️ 设置Bot命令菜单...")
            # 设置Bot命令菜单
            await self._setup_bot_commands()

            if self.logger:
                await self.logger.info("👥 初始化用户信息...")
            # 初始化用户信息
            await self._initialize_users()

            if self.logger:
                await self.logger.info("✅ TelegramBotHandler初始化成功！")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ TelegramBotHandler初始化失败: {e}")
                import traceback

                await self.logger.error(f"初始化异常堆栈: {traceback.format_exc()}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现 - 增强冲突处理"""
        if self.logger:
            await self.logger.info("🚀 开始启动TelegramBotHandler...")

        if not self.application:
            if self.logger:
                await self.logger.error(
                    "❌ TelegramBotHandler未初始化 - application为空"
                )
            raise RuntimeError("TelegramBotHandler not initialized")

        try:
            if self.logger:
                await self.logger.info("📱 初始化Telegram应用程序...")
            # 初始化应用程序
            await self.application.initialize()

            if self.logger:
                await self.logger.info("▶️ 启动Telegram应用程序...")
            # 启动应用程序
            await self.application.start()

            if self.logger:
                await self.logger.info("📡 创建Telegram轮询任务...")
            # 启动轮询任务，但不阻塞
            import asyncio

            self._polling_task = asyncio.create_task(self._run_polling_with_retry())

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ Telegram应用启动失败: {e}")
            return False

        if self.logger:
            await self.logger.info("✅ Telegram应用启动成功，设置运行状态...")
        self._is_running = True

        if self.logger:
            await self.logger.info(
                f"📢 准备发送启动通知到用户: {list(self.authorized_users)}"
            )

        # 发送启动通知（异步非阻塞）
        if self.logger:
            await self.logger.info("📤 安排启动通知发送任务...")

        # 创建异步任务发送启动通知，不阻塞启动过程
        async def send_startup_notification():
            try:
                # 等待一小段时间确保系统完全就绪
                await asyncio.sleep(0.5)

                # 检查发送条件
                if not self.enable_notifications:
                    if self.logger:
                        await self.logger.warning("⚠️ 通知功能已禁用，跳过启动通知")
                    return
                elif not self.authorized_users:
                    if self.logger:
                        await self.logger.warning("⚠️ 没有授权用户，跳过启动通知")
                    return

                if self.logger:
                    await self.logger.info(
                        f"📤 发送启动通知到 {len(self.authorized_users)} 个用户..."
                    )

                ok = await self.send_notification(
                    "🚀 BTC期权网格交易系统已启动",
                    AlertLevel.STATUS,
                    target_users=list(self.authorized_users),
                )
                if self.logger:
                    if ok:
                        await self.logger.info("✅ Telegram启动通知发送成功！")
                    else:
                        await self.logger.warning(
                            "⚠️ Telegram启动通知发送失败（返回False）"
                        )

            except Exception as e:
                if self.logger:
                    await self.logger.error(f"❌ Telegram启动通知发送异常: {e}")

        # 启动异步通知任务，不等待完成
        self._startup_notification_task = asyncio.create_task(
            send_startup_notification()
        )

        if self.logger:
            await self.logger.info("🎉 TelegramBotHandler启动完成！")
        return True

    async def _run_polling(self):
        """运行轮询任务"""
        try:
            # 获取更新
            updater = self.application.updater
            if updater:
                await updater.start_polling()
                # 保持运行直到取消
                while not self._polling_task.cancelled():
                    await asyncio.sleep(1)
            else:
                # 如果没有updater，使用bot直接轮询
                offset = 0
                while not self._polling_task.cancelled():
                    try:
                        updates = await self.bot.get_updates(offset=offset, timeout=30)
                        for update in updates:
                            offset = update.update_id + 1
                            # 处理更新
                            await self.application.process_update(update)
                        if not updates:
                            await asyncio.sleep(1)
                    except Exception as e:
                        if self.logger:
                            await self.logger.error(f"Polling error: {e}")
                        await asyncio.sleep(5)
        except asyncio.CancelledError:
            pass

    async def _run_polling_with_retry(self):
        """运行轮询任务 - 增强冲突处理和重试机制"""
        retry_count = 0
        max_retries = 5
        base_delay = 2

        if self.logger:
            await self.logger.info("🔄 开始Telegram轮询任务...")

        while retry_count < max_retries and not self._polling_task.cancelled():
            try:
                # 首先尝试停止任何现有的轮询
                try:
                    updater = self.application.updater
                    if updater and updater.running:
                        if self.logger:
                            await self.logger.info("🛑 停止现有的Telegram轮询...")
                        await updater.stop()
                        await asyncio.sleep(1)  # 等待完全停止
                except Exception as e:
                    if self.logger:
                        await self.logger.debug(f"停止现有轮询时出错（可忽略）: {e}")

                # 在启动轮询前，先清理可能的冲突状态
                try:
                    if self.logger:
                        await self.logger.info("🧹 清理Telegram API状态...")
                    # 发送一个简单的API调用来清理状态
                    await self.bot.get_me()
                    # 删除webhook（如果存在）
                    await self.bot.delete_webhook(drop_pending_updates=True)
                    await asyncio.sleep(0.5)
                except Exception as e:
                    if self.logger:
                        await self.logger.debug(f"清理API状态时出错（可忽略）: {e}")

                # 使用单一的轮询机制
                updater = self.application.updater
                if updater:
                    # 使用超时来避免轮询启动阻塞
                    if self.logger:
                        await self.logger.info("🔗 启动Telegram轮询连接...")
                    try:
                        await asyncio.wait_for(updater.start_polling(), timeout=10.0)
                        if self.logger:
                            await self.logger.info("✅ Telegram轮询连接成功")

                        # 保持运行直到取消
                        while not self._polling_task.cancelled():
                            await asyncio.sleep(1)

                        # 正常退出，不需要重试
                        break

                    except Exception as e:
                        if "Conflict" in str(
                            e
                        ) or "terminated by other getUpdates" in str(e):
                            if self.logger:
                                await self.logger.warning(
                                    "⚠️ 检测到Telegram Bot冲突，停止当前实例轮询"
                                )
                            # 检测到冲突，停止轮询任务
                            return
                        elif "TimeoutError" in str(type(e).__name__):
                            if self.logger:
                                await self.logger.warning(
                                    "⚠️ Telegram轮询启动超时，将重试..."
                                )
                            raise e  # 让外层重试逻辑处理
                        else:
                            raise e
                else:
                    if self.logger:
                        await self.logger.error("❌ Telegram updater不可用")
                    return

                # 正常路径已在上方处理，无需额外中断

            except Exception as e:
                retry_count += 1
                delay = base_delay * (2**retry_count)  # 指数退避

                if "Conflict" in str(e) or "terminated by other getUpdates" in str(e):
                    if self.logger:
                        await self.logger.warning(
                            f"Telegram conflict detected (attempt {retry_count}/{max_retries}): {e}. "
                            f"Retrying in {delay} seconds..."
                        )
                else:
                    if self.logger:
                        await self.logger.error(
                            f"Telegram polling error (attempt {retry_count}/{max_retries}): {e}. "
                            f"Retrying in {delay} seconds..."
                        )

                if retry_count >= max_retries:
                    if self.logger:
                        await self.logger.error(
                            "Max retries exceeded. Stopping Telegram bot."
                        )
                    break

                await asyncio.sleep(delay)

            except asyncio.CancelledError:
                if self.logger:
                    await self.logger.info("Telegram轮询任务被取消")
                break

        # 最终清理
        try:
            updater = self.application.updater
            if updater:
                await updater.stop()
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error stopping updater: {e}")

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.application and self._is_running:
                if self.logger:
                    await self.logger.info("📤 发送Telegram停止通知...")
                # 发送停止通知
                try:
                    ok = await self.send_notification(
                        "⏹️ BTC期权网格交易系统正在停止", AlertLevel.STATUS
                    )
                    if self.logger:
                        if ok:
                            await self.logger.info("✅ Telegram停止通知发送成功！")
                        else:
                            await self.logger.warning(
                                "⚠️ Telegram停止通知发送失败（返回False）"
                            )
                except Exception as notify_error:
                    if self.logger:
                        await self.logger.error(
                            f"❌ Telegram停止通知发送异常: {notify_error}"
                        )

                # 停止Bot - 清理逻辑
                try:
                    # 取消polling任务
                    if hasattr(self, "_polling_task") and self._polling_task:
                        self._polling_task.cancel()
                        with contextlib.suppress(asyncio.CancelledError):
                            await self._polling_task

                    # 停止应用程序
                    if self.application:
                        await self.application.stop()
                        await self.application.shutdown()

                    # 给一点时间让连接完全关闭
                    await asyncio.sleep(0.5)

                except Exception as stop_error:
                    if self.logger:
                        await self.logger.warning(
                            f"Error during bot cleanup: {stop_error}"
                        )

                self._is_running = False

            if self.logger:
                await self.logger.info("TelegramBotHandler stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop TelegramBotHandler: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            is_healthy = (
                self.application is not None
                and self._is_running
                and self.bot is not None
            )

            if is_healthy:
                # 尝试获取Bot信息来验证连接 - 添加3秒超时
                try:
                    bot_info = await asyncio.wait_for(self.bot.get_me(), timeout=3.0)
                    status = HealthStatus.HEALTHY
                    message = f"Bot @{bot_info.username} is running"
                except TimeoutError:
                    status = HealthStatus.UNHEALTHY
                    message = "Health check failed: Timed out"
                except Exception as e:
                    status = HealthStatus.UNHEALTHY
                    message = f"Health check failed: {e}"
            else:
                status = HealthStatus.UNHEALTHY
                message = "Bot is not running or not initialized"

            details = {
                "bot_running": self._is_running,
                "application_initialized": self.application is not None,
                "authorized_users_count": len(self.authorized_users),
                "admin_users_count": len(self.admin_users),
                "registered_users_count": len(self.users),
                "subscribed_to_system_event": bool(self.event_bus),
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                details={"error": str(e)},
            )


class TelegramBotSingleton:
    """Telegram Bot 单例管理器 - 增强冲突检测"""

    _instance: TelegramBotHandler | None = None
    _lock = asyncio.Lock()
    _bot_token: str | None = None
    _lock_file_path = "/tmp/btc_option_grid_bot_telegram.lock"
    _lock_file_handle: int | None = None

    @classmethod
    def _acquire_file_lock(cls) -> bool:
        """获取文件锁以防止多实例运行"""
        try:
            cls._lock_file_handle = os.open(
                cls._lock_file_path, os.O_CREAT | os.O_WRONLY | os.O_TRUNC
            )
            fcntl.flock(cls._lock_file_handle, fcntl.LOCK_EX | fcntl.LOCK_NB)
            # 写入PID
            os.write(cls._lock_file_handle, f"{os.getpid()}\n".encode())
            return True
        except OSError:
            if cls._lock_file_handle:
                with contextlib.suppress(OSError):
                    os.close(cls._lock_file_handle)
                cls._lock_file_handle = None
            return False

    @classmethod
    def _release_file_lock(cls):
        """释放文件锁"""
        if cls._lock_file_handle:
            try:
                fcntl.flock(cls._lock_file_handle, fcntl.LOCK_UN)
                os.close(cls._lock_file_handle)
                os.unlink(cls._lock_file_path)
            except OSError:
                pass
            cls._lock_file_handle = None

    @classmethod
    async def get_instance(
        cls,
        bot_token: str = None,
        authorized_users: list[int] = None,
        admin_users: list[int] = None,
        force_single_instance: bool = True,
    ) -> TelegramBotHandler | None:
        """获取或创建Telegram Bot处理器实例"""
        async with cls._lock:
            # 如果强制单实例模式，检查文件锁
            if force_single_instance and not cls._acquire_file_lock():
                try:
                    from src.utils.async_logger import get_logger
                    _log = await get_logger()
                    await _log.info("另一个Telegram bot实例正在运行，跳过启动以避免冲突")
                except Exception:
                    pass
                return None

            # 如果没有实例或bot_token变化，创建新实例
            if cls._instance is None or (bot_token and cls._bot_token != bot_token):
                # 停止之前的实例（如果存在）
                if cls._instance:
                    try:
                        await cls._instance.stop()
                    except Exception as e:
                        try:
                            from src.utils.async_logger import get_logger
                            _log = await get_logger()
                            await _log.warning(
                                f"Warning: Failed to stop previous Telegram bot instance: {e}"
                            )
                        except Exception:
                            pass

                # 创建新实例
                if bot_token:
                    cls._instance = TelegramBotHandler(
                        bot_token=bot_token,
                        authorized_users=authorized_users or [],
                        admin_users=admin_users or [],
                    )
                    cls._bot_token = bot_token
                else:
                    cls._instance = None
                    cls._bot_token = None
                    if force_single_instance:
                        cls._release_file_lock()

            return cls._instance

    @classmethod
    async def clear_instance(cls):
        """清理实例"""
        async with cls._lock:
            if cls._instance:
                with contextlib.suppress(Exception):
                    await cls._instance.stop()
                cls._instance = None
                cls._bot_token = None
            # 释放文件锁
            cls._release_file_lock()


async def get_telegram_handler() -> TelegramBotHandler | None:
    """获取Telegram Bot处理器实例"""
    return await TelegramBotSingleton.get_instance()


async def initialize_telegram_handler(
    bot_token: str, authorized_users: list[int] = None, admin_users: list[int] = None
) -> TelegramBotHandler:
    """初始化Telegram Bot处理器"""
    return await TelegramBotSingleton.get_instance(
        bot_token=bot_token, authorized_users=authorized_users, admin_users=admin_users
    )
