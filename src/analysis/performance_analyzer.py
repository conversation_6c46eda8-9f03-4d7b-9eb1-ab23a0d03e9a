"""
策略性能评估分析器

实现完整的期权策略性能分析系统，包括：
1. 收益率分析和风险调整收益率
2. 最大回撤和夏普比率计算
3. 希腊字母风险分析
4. 策略归因分析
5. 基准比较和相对表现
"""

import asyncio
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from typing import Any

import numpy as np

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import EventBus
from src.data.cache_manager import CacheManager
from src.monitoring.strict_metrics import inc_missing
from src.services.market_data_service import MarketDataService


@dataclass
class PerformanceMetrics:
    """性能指标"""

    # 收益指标
    total_return: float = 0.0
    annualized_return: float = 0.0
    cumulative_return: float = 0.0

    # 风险指标
    volatility: float = 0.0
    max_drawdown: float = 0.0
    var_95: float = 0.0
    var_99: float = 0.0

    # 风险调整收益率
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # 交易统计
    win_rate: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0
    profit_factor: float = 0.0

    # 期权特定指标
    premium_capture_rate: float = 0.0
    time_decay_alpha: float | None = None
    volatility_alpha: float | None = None

    # 统计期间
    start_date: datetime = field(default_factory=lambda: datetime.now(UTC))
    end_date: datetime = field(default_factory=lambda: datetime.now(UTC))
    analysis_days: int = 0


@dataclass
class StrategyAttribution:
    """策略归因分析"""

    # 收益来源分解
    premium_income: float = 0.0  # 权利金收入
    time_decay_pnl: float = 0.0  # 时间价值收益
    delta_pnl: float = 0.0  # Delta收益
    gamma_pnl: float = 0.0  # Gamma收益
    vega_pnl: float = 0.0  # Vega收益

    # 策略类型归因
    accumulation_pnl: float = 0.0  # 积累模式收益
    distribution_pnl: float = 0.0  # 分发模式收益
    sideways_pnl: float = 0.0  # 横盘模式收益

    # 市场环境归因
    trending_market_pnl: float = 0.0  # 趋势市场收益
    ranging_market_pnl: float = 0.0  # 震荡市场收益
    volatile_market_pnl: float = 0.0  # 高波动市场收益


@dataclass
class RiskBreakdown:
    """风险分解分析"""

    # Greeks风险贡献
    delta_risk: float = 0.0
    gamma_risk: float = 0.0
    theta_risk: float = 0.0
    vega_risk: float = 0.0

    # 期限结构风险
    short_term_risk: float = 0.0  # <30天
    medium_term_risk: float = 0.0  # 30-90天
    long_term_risk: float = 0.0  # >90天

    # 价格区间风险
    otm_risk: float = 0.0  # 虚值期权风险
    atm_risk: float = 0.0  # 平值期权风险
    itm_risk: float = 0.0  # 实值期权风险


class PerformanceAnalyzer(BaseComponent):
    """策略性能评估分析器"""

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("PerformanceAnalyzer", config)
        self.config = config or {}

        # 核心组件依赖
        self.cache_manager: CacheManager | None = None
        self.event_bus: EventBus | None = None

        # 性能数据存储
        self.performance_history: list[dict[str, Any]] = []
        self.daily_pnl: list[float] = []
        self.portfolio_values: list[float] = []
        self.timestamps: list[datetime] = []

        # 基准数据
        self.benchmark_returns: list[float] = []
        self.risk_free_rate = 0.02  # 2%无风险利率（适合加密货币市场）

        # 分析配置
        self.analysis_config = {
            "lookback_days": 252,  # 分析回看天数
            "min_data_points": 30,  # 最小数据点数
            "benchmark_symbol": "BTC",  # 基准符号
            "rebalance_frequency": "daily",  # 重新平衡频率
            "commission_rate": 0.002,  # 手续费率（适合加密货币期权）
            "slippage_bp": 10.0,  # 滑点基点（适合加密货币市场）
        }

        # 当前分析结果
        self.current_metrics: PerformanceMetrics | None = None
        self.current_attribution: StrategyAttribution | None = None
        self.current_risk_breakdown: RiskBreakdown | None = None

        # 统计信息
        self.analysis_count = 0
        self.last_analysis_time: datetime | None = None

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            if self.logger:
                await self.logger.info("Initializing PerformanceAnalyzer")

            # 加载历史性能数据
            await self._load_performance_history()

            # 初始化基准数据
            await self._initialize_benchmark_data()

            if self.logger:
                await self.logger.info("PerformanceAnalyzer initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"PerformanceAnalyzer initialization failed: {e}"
                )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            # 启动定期分析任务
            asyncio.create_task(self._periodic_analysis())

            if self.logger:
                await self.logger.info("PerformanceAnalyzer started")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"PerformanceAnalyzer start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("PerformanceAnalyzer stopped")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"PerformanceAnalyzer stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查数据完整性
            if len(self.daily_pnl) < self.analysis_config["min_data_points"]:
                issues.append(f"Insufficient data points: {len(self.daily_pnl)}")

            # 检查分析频率
            if self.last_analysis_time:
                time_since_analysis = datetime.now(UTC) - self.last_analysis_time
                if time_since_analysis.total_seconds() > 3600:  # 1小时
                    issues.append("Analysis not updated recently")

            if not issues:
                status = HealthStatus.HEALTHY
                message = "PerformanceAnalyzer operating normally"
            else:
                status = HealthStatus.DEGRADED
                message = f"PerformanceAnalyzer issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "data_points": len(self.daily_pnl),
                    "analysis_count": self.analysis_count,
                    "last_analysis": self.last_analysis_time.isoformat()
                    if self.last_analysis_time
                    else None,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def analyze_performance(
        self, start_date: datetime | None = None, end_date: datetime | None = None
    ) -> PerformanceMetrics:
        """分析策略性能"""
        try:
            if self.logger:
                await self.logger.info("Starting performance analysis")

            # 设置分析时间范围
            end_date = end_date or datetime.now(UTC)
            start_date = start_date or (
                end_date - timedelta(days=self.analysis_config["lookback_days"])
            )

            # 获取时间范围内的数据
            pnl_data, portfolio_data, timestamp_data = await self._get_data_in_range(
                start_date, end_date
            )

            if len(pnl_data) < self.analysis_config["min_data_points"]:
                raise ValueError(
                    f"Insufficient data for analysis: {len(pnl_data)} < {self.analysis_config['min_data_points']}"
                )

            # 计算基础收益指标
            returns = np.array(pnl_data) / np.array(portfolio_data[:-1])  # 日收益率
            cumulative_return = (portfolio_data[-1] / portfolio_data[0] - 1) * 100

            # 年化指标
            trading_days = len(returns)
            annualized_return = (1 + cumulative_return / 100) ** (
                252 / trading_days
            ) - 1
            volatility = np.std(returns) * np.sqrt(252)

            # 计算最大回撤
            max_drawdown = await self._calculate_max_drawdown(portfolio_data)

            # 计算VaR
            var_95 = np.percentile(returns, 5) * 100
            var_99 = np.percentile(returns, 1) * 100

            # 计算风险调整收益率
            sharpe_ratio = (
                (annualized_return - self.risk_free_rate) / volatility
                if volatility > 0
                else 0
            )
            sortino_ratio = await self._calculate_sortino_ratio(
                returns, self.risk_free_rate
            )
            calmar_ratio = (
                annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            )

            # 计算交易统计
            (
                win_rate,
                avg_win,
                avg_loss,
                profit_factor,
            ) = await self._calculate_trading_stats(pnl_data)

            # 计算期权特定指标
            premium_capture_rate = await self._calculate_premium_capture_rate(
                start_date, end_date
            )
            time_decay_alpha = await self._calculate_time_decay_alpha(
                start_date, end_date
            )
            volatility_alpha = await self._calculate_volatility_alpha(
                start_date, end_date
            )

            # 构建性能指标
            self.current_metrics = PerformanceMetrics(
                total_return=sum(pnl_data),
                annualized_return=annualized_return * 100,
                cumulative_return=cumulative_return,
                volatility=volatility * 100,
                max_drawdown=max_drawdown,
                var_95=var_95,
                var_99=var_99,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                profit_factor=profit_factor,
                premium_capture_rate=premium_capture_rate,
                time_decay_alpha=time_decay_alpha,
                volatility_alpha=volatility_alpha,
                start_date=start_date,
                end_date=end_date,
                analysis_days=trading_days,
            )

            # 更新统计
            self.analysis_count += 1
            self.last_analysis_time = datetime.now(UTC)

            if self.logger:
                await self.logger.info(
                    f"Performance analysis completed: Sharpe={sharpe_ratio:.3f}, Max DD={max_drawdown:.2f}%"
                )

            return self.current_metrics

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Performance analysis failed: {e}")
            raise

    async def analyze_attribution(
        self, start_date: datetime | None = None, end_date: datetime | None = None
    ) -> StrategyAttribution:
        """分析策略归因"""
        try:
            end_date = end_date or datetime.now(UTC)
            start_date = start_date or (end_date - timedelta(days=30))

            # 获取交易数据
            trades_data = await self._get_trades_in_range(start_date, end_date)
            positions_data = await self._get_positions_in_range(start_date, end_date)

            # 初始化归因结果
            attribution = StrategyAttribution()

            # 分析权利金收入
            attribution.premium_income = await self._calculate_premium_income(
                trades_data
            )

            # 分析Greeks贡献
            attribution.time_decay_pnl = await self._calculate_theta_pnl(positions_data)
            attribution.delta_pnl = await self._calculate_delta_pnl(positions_data)
            attribution.gamma_pnl = await self._calculate_gamma_pnl(positions_data)
            attribution.vega_pnl = await self._calculate_vega_pnl(positions_data)

            # 分析策略类型贡献
            attribution.accumulation_pnl = await self._calculate_strategy_pnl(
                trades_data, "accumulation"
            )
            attribution.distribution_pnl = await self._calculate_strategy_pnl(
                trades_data, "distribution"
            )
            attribution.sideways_pnl = await self._calculate_strategy_pnl(
                trades_data, "sideways"
            )

            # 分析市场环境贡献
            attribution.trending_market_pnl = (
                await self._calculate_market_environment_pnl(trades_data, "trending")
            )
            attribution.ranging_market_pnl = (
                await self._calculate_market_environment_pnl(trades_data, "ranging")
            )
            attribution.volatile_market_pnl = (
                await self._calculate_market_environment_pnl(trades_data, "volatile")
            )

            self.current_attribution = attribution
            return attribution

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Attribution analysis failed: {e}")
            raise

    async def analyze_risk_breakdown(self) -> RiskBreakdown:
        """分析风险分解"""
        try:
            # 优先通过 MarketDataService 获取
            mds = await self.dependency_injector.resolve(MarketDataService)
            positions_data = await mds.get_positions()
            if not positions_data:
                return RiskBreakdown()

            risk_breakdown = RiskBreakdown()

            # 分析Greeks风险贡献
            for symbol, position in positions_data.items():
                if not isinstance(position, dict):
                    continue

                size = position.get("size", 0)
                if abs(size) < 0.001:
                    continue

                # 获取Greeks数据
                greeks_data = await mds.get_greeks(symbol)
                if not greeks_data:
                    continue

                # 计算风险贡献
                delta = greeks_data.get("delta", 0)
                gamma = greeks_data.get("gamma", 0)
                theta = greeks_data.get("theta", 0)
                vega = greeks_data.get("vega", 0)

                risk_breakdown.delta_risk += abs(size * delta * 50000)  # 假设BTC价格
                risk_breakdown.gamma_risk += abs(
                    size * gamma * 50000 * 0.01
                )  # 1%价格变动
                risk_breakdown.theta_risk += abs(size * theta)
                risk_breakdown.vega_risk += abs(size * vega * 0.01)  # 1%波动率变动

                # 期限结构风险分析
                expiry_str = position.get("expiry", "")
                if expiry_str:
                    try:
                        expiry_date = datetime.fromisoformat(
                            expiry_str.replace("Z", "+00:00")
                        )
                        days_to_expiry = (expiry_date - datetime.now(UTC)).days

                        position_risk = abs(size * position.get("market_value", 0))

                        if days_to_expiry <= 30:
                            risk_breakdown.short_term_risk += position_risk
                        elif days_to_expiry <= 90:
                            risk_breakdown.medium_term_risk += position_risk
                        else:
                            risk_breakdown.long_term_risk += position_risk
                    except Exception:
                        continue

                # 价格区间风险分析（基于Delta）
                position_risk = abs(size * position.get("market_value", 0))
                if abs(delta) < 0.2:
                    risk_breakdown.otm_risk += position_risk
                elif abs(delta) < 0.6:
                    risk_breakdown.atm_risk += position_risk
                else:
                    risk_breakdown.itm_risk += position_risk

            self.current_risk_breakdown = risk_breakdown
            return risk_breakdown

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Risk breakdown analysis failed: {e}")
            raise

    async def _calculate_max_drawdown(self, portfolio_values: list[float]) -> float:
        """计算最大回撤"""
        if len(portfolio_values) < 2:
            return 0.0

        peak = portfolio_values[0]
        max_drawdown = 0.0

        for value in portfolio_values:
            if value > peak:
                peak = value

            drawdown = (peak - value) / peak * 100
            if drawdown > max_drawdown:
                max_drawdown = drawdown

        return max_drawdown

    async def _calculate_sortino_ratio(
        self, returns: np.ndarray, risk_free_rate: float
    ) -> float:
        """计算Sortino比率"""
        excess_returns = returns - risk_free_rate / 252  # 日化无风险收益率
        negative_returns = excess_returns[excess_returns < 0]

        if len(negative_returns) == 0:
            return float("inf")

        downside_deviation = np.std(negative_returns) * np.sqrt(252)
        if downside_deviation == 0:
            return 0.0

        return (np.mean(excess_returns) * 252) / downside_deviation

    async def _calculate_trading_stats(
        self, pnl_data: list[float]
    ) -> tuple[float, float, float, float]:
        """计算交易统计"""
        if not pnl_data:
            return 0.0, 0.0, 0.0, 0.0

        winning_trades = [pnl for pnl in pnl_data if pnl > 0]
        losing_trades = [pnl for pnl in pnl_data if pnl < 0]

        win_rate = len(winning_trades) / len(pnl_data) * 100
        avg_win = np.mean(winning_trades) if winning_trades else 0.0
        avg_loss = np.mean(losing_trades) if losing_trades else 0.0

        total_wins = sum(winning_trades)
        total_losses = abs(sum(losing_trades))
        profit_factor = total_wins / total_losses if total_losses > 0 else float("inf")

        return win_rate, avg_win, avg_loss, profit_factor

    async def _calculate_premium_capture_rate(
        self, start_date: datetime, end_date: datetime
    ) -> float:
        """计算权利金捕获率 - 实际收到的权利金 vs 理论权利金"""
        try:
            if not self.timescale_manager or not self.timescale_manager.pool:
                return 0.0

            async with self.timescale_manager.pool.acquire() as conn:
                # 获取期权卖出交易的实际权利金收入
                actual_premium_query = """
                    SELECT SUM(
                        CASE
                            WHEN t.side = 'sell' AND t.metadata->>'instrument_type' = 'option'
                            THEN t.size * t.price
                            ELSE 0
                        END
                    ) as actual_premium
                    FROM trades t
                    WHERE t.time BETWEEN $1 AND $2
                    AND t.side = 'sell'
                    AND t.metadata->>'instrument_type' = 'option'
                """

                actual_premium = await conn.fetchval(
                    actual_premium_query, start_date, end_date
                )
                if not actual_premium:
                    actual_premium = 0.0

                # 获取对应的理论权利金（基于期权定价模型）
                theoretical_premium_query = """
                    SELECT SUM(
                        t.size * COALESCE((t.metadata->>'theoretical_price')::numeric, t.price)
                    ) as theoretical_premium
                    FROM trades t
                    WHERE t.time BETWEEN $1 AND $2
                    AND t.side = 'sell'
                    AND t.metadata->>'instrument_type' = 'option'
                    AND t.metadata->>'theoretical_price' IS NOT NULL
                """

                theoretical_premium = await conn.fetchval(
                    theoretical_premium_query, start_date, end_date
                )
                if not theoretical_premium:
                    theoretical_premium = actual_premium  # 回退到实际价格

                # 计算捕获率
                if theoretical_premium > 0:
                    capture_rate = (actual_premium / theoretical_premium) * 100
                    return min(capture_rate, 200.0)  # 限制在200%以内（防止异常数据）

                return 0.0

        except Exception as e:
            if self.logger:
                await self.logger.warning(
                    f"Error calculating premium capture rate: {e}"
                )
            return 0.0

    async def _calculate_time_decay_alpha(
        self, start_date: datetime, end_date: datetime
    ) -> float | None:
        """计算时间价值Alpha - Theta收益 vs 市场时间价值衰减"""
        try:
            if not self.timescale_manager or not self.timescale_manager.pool:
                inc_missing("PerformanceAnalyzer", "time_decay_alpha")
                return None

            async with self.timescale_manager.pool.acquire() as conn:
                # 获取实际的Theta P&L
                theta_pnl_query = """
                    SELECT SUM(
                        CASE
                            WHEN p.metadata->>'theta' IS NOT NULL AND p.size != 0
                            THEN (p.metadata->>'theta')::numeric * p.size * (EXTRACT(EPOCH FROM $2 - $1) / 86400.0)
                            ELSE 0
                        END
                    ) as actual_theta_pnl
                    FROM position_snapshots p
                    WHERE p.time BETWEEN $1 AND $2
                    AND p.metadata->>'instrument_type' = 'option'
                """

                actual_theta_pnl = await conn.fetchval(
                    theta_pnl_query, start_date, end_date
                )
                if actual_theta_pnl is None:
                    inc_missing("PerformanceAnalyzer", "time_decay_alpha")
                    return None

                # 获取理论时间价值衰减（基于期权定价模型）
                theoretical_decay_query = """
                    SELECT SUM(
                        CASE
                            WHEN p.metadata->>'theoretical_theta' IS NOT NULL AND p.size != 0
                            THEN (p.metadata->>'theoretical_theta')::numeric * p.size * (EXTRACT(EPOCH FROM $2 - $1) / 86400.0)
                            ELSE COALESCE((p.metadata->>'theta')::numeric, 0) * p.size * (EXTRACT(EPOCH FROM $2 - $1) / 86400.0)
                        END
                    ) as theoretical_theta_pnl
                    FROM position_snapshots p
                    WHERE p.time BETWEEN $1 AND $2
                    AND p.metadata->>'instrument_type' = 'option'
                """

                theoretical_theta_pnl = await conn.fetchval(
                    theoretical_decay_query, start_date, end_date
                )
                if theoretical_theta_pnl is None or float(theoretical_theta_pnl) == 0.0:
                    inc_missing("PerformanceAnalyzer", "time_decay_alpha")
                    return None

                # 计算Alpha（超额收益率）
                alpha = actual_theta_pnl / abs(theoretical_theta_pnl)
                # 如果是卖方策略（收取时间价值），Alpha > 1表示超额收益
                # 如果是买方策略（支付时间价值），Alpha < 1表示相对节省
                return float(alpha)

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Error calculating time decay alpha: {e}")
            inc_missing("PerformanceAnalyzer", "time_decay_alpha")
            return None

    async def _calculate_volatility_alpha(
        self, start_date: datetime, end_date: datetime
    ) -> float | None:
        """计算波动率Alpha - 实际波动率 vs 隐含波动率的收益"""
        try:
            if not self.timescale_manager or not self.timescale_manager.pool:
                inc_missing("PerformanceAnalyzer", "volatility_alpha")
                return None

            async with self.timescale_manager.pool.acquire() as conn:
                # 获取交易时的隐含波动率和实际实现波动率
                volatility_query = """
                    WITH trade_vol AS (
                        SELECT
                            t.time,
                            t.symbol,
                            (t.metadata->>'implied_volatility')::numeric as implied_volatility,
                            t.size as quantity,
                            t.side,
                            -- 计算该期权到期时的实现波动率
                            CASE
                                WHEN t.metadata->>'instrument_type' = 'option'
                                THEN (
                                    SELECT STDDEV(LOG(m2.price / LAG(m2.price) OVER (ORDER BY m2.time))) * SQRT(252)
                                    FROM market_data m2
                                    WHERE m2.symbol = SPLIT_PART(t.symbol, '-', 1)
                                    AND m2.time BETWEEN t.time AND t.time + INTERVAL '30 days'
                                )
                                ELSE NULL
                            END as realized_volatility
                        FROM trades t
                        WHERE t.time BETWEEN $1 AND $2
                        AND t.metadata->>'instrument_type' = 'option'
                        AND t.metadata->>'implied_volatility' IS NOT NULL
                    )
                    SELECT
                        AVG(
                            CASE
                                WHEN side = 'sell' AND realized_volatility < implied_volatility
                                THEN (implied_volatility - realized_volatility) / implied_volatility
                                WHEN side = 'buy' AND realized_volatility > implied_volatility
                                THEN (realized_volatility - implied_volatility) / implied_volatility
                                ELSE 0
                            END
                        ) as volatility_alpha,
                        COUNT(*) as trade_count
                    FROM trade_vol
                    WHERE realized_volatility IS NOT NULL
                """

                result = await conn.fetchrow(volatility_query, start_date, end_date)

                if (
                    result
                    and result["trade_count"] > 0
                    and result["volatility_alpha"] is not None
                ):
                    return float(result["volatility_alpha"])

                # 备用计算：基于Vega P&L分析
                vega_analysis_query = """
                    SELECT
                        SUM(
                            CASE
                                WHEN p.metadata->>'vega' IS NOT NULL AND p.size != 0
                                THEN (p.metadata->>'vega')::numeric * p.size *
                                     ((p.metadata->>'current_iv')::numeric - (p.metadata->>'entry_iv')::numeric) /
                                     NULLIF((p.metadata->>'entry_iv')::numeric, 0)
                                ELSE 0
                            END
                        ) as vega_pnl,
                        COUNT(*) as position_count
                    FROM position_snapshots p
                    WHERE p.time BETWEEN $1 AND $2
                    AND p.metadata->>'instrument_type' = 'option'
                    AND p.metadata->>'entry_iv' IS NOT NULL
                    AND p.metadata->>'current_iv' IS NOT NULL
                """

                backup_result = await conn.fetchrow(
                    vega_analysis_query, start_date, end_date
                )

                if backup_result and backup_result["position_count"] > 0:
                    # 根据Vega P&L推算波动率Alpha
                    vega_pnl = backup_result["vega_pnl"]
                    if vega_pnl is not None:
                        # 正的Vega P&L通常意味着波动率预测正确
                        return min(
                            max(float(vega_pnl) / 1000.0, -1.0), 1.0
                        )  # 标准化到[-1, 1]

                inc_missing("PerformanceAnalyzer", "volatility_alpha")
                return None

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Error calculating volatility alpha: {e}")
            inc_missing("PerformanceAnalyzer", "volatility_alpha")
            return None

    # 数据获取和处理方法
    async def _load_performance_history(self):
        """加载历史性能数据"""
        try:
            mds = await self.dependency_injector.resolve(MarketDataService)
            history_data = await mds.get_cached("performance_history")
            if history_data:
                self.performance_history = history_data
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Failed to load performance history: {e}")

    async def _initialize_benchmark_data(self):
        """初始化基准数据"""
        try:
            mds = await self.dependency_injector.resolve(MarketDataService)
            benchmark_data = await mds.get_cached("btc_returns")
            if benchmark_data:
                self.benchmark_returns = benchmark_data
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Failed to initialize benchmark data: {e}")

    async def _get_data_in_range(
        self, start_date: datetime, end_date: datetime
    ) -> tuple[list[float], list[float], list[datetime]]:
        """获取时间范围内的实际交易数据"""
        try:
            if not self.timescale_manager or not self.timescale_manager.pool:
                # 无数据库连接时返回空数据
                return [], [], []

            async with self.timescale_manager.pool.acquire() as conn:
                # 获取每日P&L数据
                pnl_query = """
                    SELECT
                        DATE(t.time) as trade_date,
                        SUM(
                            CASE
                                WHEN t.side = 'sell' THEN t.size * t.price
                                WHEN t.side = 'buy' THEN -t.size * t.price
                                ELSE 0
                            END
                        ) as daily_pnl
                    FROM trades t
                    WHERE t.time BETWEEN $1 AND $2
                    GROUP BY DATE(t.time)
                    ORDER BY trade_date
                """

                pnl_rows = await conn.fetch(pnl_query, start_date, end_date)

                # 获取组合价值变化（基于持仓快照）
                portfolio_query = """
                    SELECT DISTINCT
                        DATE(time) as value_date,
                        FIRST_VALUE(
                            SUM(size * mark_price) OVER (
                                PARTITION BY DATE(time)
                                ORDER BY time DESC
                                ROWS UNBOUNDED PRECEDING
                            )
                        ) OVER (
                            PARTITION BY DATE(time)
                            ORDER BY time DESC
                        ) as daily_portfolio_value
                    FROM position_snapshots
                    WHERE time BETWEEN $1 AND $2
                    ORDER BY value_date
                """

                portfolio_rows = await conn.fetch(portfolio_query, start_date, end_date)

                # 处理数据
                pnl_data = []
                portfolio_data = []
                timestamp_data = []

                # 创建日期范围
                current_date = start_date.date()
                end_date_only = end_date.date()

                # 构建完整的日期序列
                pnl_dict = {
                    row["trade_date"]: float(row["daily_pnl"]) for row in pnl_rows
                }
                portfolio_dict = {
                    row["value_date"]: float(row["daily_portfolio_value"])
                    for row in portfolio_rows
                }

                # 获取初始组合价值
                initial_portfolio_value = 100000.0  # 默认值
                if portfolio_dict:
                    initial_portfolio_value = list(portfolio_dict.values())[0]

                current_portfolio_value = initial_portfolio_value

                while current_date <= end_date_only:
                    timestamp_data.append(
                        datetime.combine(current_date, datetime.min.time())
                    )

                    # 获取当日P&L
                    daily_pnl = pnl_dict.get(current_date, 0.0)
                    pnl_data.append(daily_pnl)

                    # 更新组合价值
                    if current_date in portfolio_dict:
                        current_portfolio_value = portfolio_dict[current_date]
                    else:
                        current_portfolio_value += daily_pnl

                    portfolio_data.append(current_portfolio_value)

                    current_date += timedelta(days=1)

                return pnl_data, portfolio_data, timestamp_data

        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Error getting data in range: {e}")

            # 回退到空数据而不是模拟数据
            return [], [], []

    async def _get_trades_in_range(
        self, start_date: datetime, end_date: datetime
    ) -> list[dict[str, Any]]:
        """获取时间范围内的交易数据"""
        mds = await self.dependency_injector.resolve(MarketDataService)
        trades_data = await mds.get_cached("trades_history")
        if trades_data:
            return [
                trade
                for trade in trades_data
                if start_date
                <= datetime.fromisoformat(trade.get("timestamp", ""))
                <= end_date
            ]
        return []

    async def _get_positions_in_range(
        self, start_date: datetime, end_date: datetime
    ) -> list[dict[str, Any]]:
        """获取时间范围内的仓位数据"""
        mds = await self.dependency_injector.resolve(MarketDataService)
        positions_data = await mds.get_cached("positions_history")
        if positions_data:
            return [
                pos
                for pos in positions_data
                if start_date
                <= datetime.fromisoformat(pos.get("timestamp", ""))
                <= end_date
            ]
        return []

    # 归因分析辅助方法
    async def _calculate_premium_income(
        self, trades_data: list[dict[str, Any]]
    ) -> float:
        """计算权利金收入"""
        premium_income = 0.0
        for trade in trades_data:
            if trade.get("trade_type") == "sell_option":
                premium_income += trade.get("premium_received", 0)
        return premium_income

    async def _calculate_theta_pnl(self, positions_data: list[dict[str, Any]]) -> float:
        """计算Theta损益 - 基于时间价值变化的实际损益"""
        try:
            total_theta_pnl = 0.0

            for position in positions_data:
                # 获取持仓的基本信息
                quantity = position.get("quantity", 0)
                theta = position.get("theta", 0.0)

                if quantity == 0 or theta == 0.0:
                    continue

                # 计算持仓时间（天数）
                entry_time = position.get("entry_timestamp")
                current_time = position.get("current_timestamp")

                if entry_time and current_time:
                    if isinstance(entry_time, str):
                        entry_time = datetime.fromisoformat(
                            entry_time.replace("Z", "+00:00")
                        )
                    if isinstance(current_time, str):
                        current_time = datetime.fromisoformat(
                            current_time.replace("Z", "+00:00")
                        )

                    days_held = (current_time - entry_time).total_seconds() / 86400.0
                else:
                    days_held = 1.0  # 默认1天

                # Theta损益 = Theta * 持仓量 * 持仓天数
                position_theta_pnl = theta * quantity * days_held
                total_theta_pnl += position_theta_pnl

                # 如果有实际的theta_pnl记录，优先使用
                if "theta_pnl" in position:
                    recorded_theta_pnl = position["theta_pnl"]
                    if (
                        isinstance(recorded_theta_pnl, (int | float))
                        and recorded_theta_pnl != 0
                    ):
                        total_theta_pnl += (
                            recorded_theta_pnl - position_theta_pnl
                        )  # 替换计算值

            return total_theta_pnl

        except Exception as e:
            if hasattr(self, "logger") and self.logger:
                await self.logger.warning(f"Error calculating theta PnL: {e}")
            # 回退到简单求和
            return sum(pos.get("theta_pnl", 0) for pos in positions_data)

    async def _calculate_delta_pnl(self, positions_data: list[dict[str, Any]]) -> float:
        """计算Delta损益"""
        return sum(pos.get("delta_pnl", 0) for pos in positions_data)

    async def _calculate_gamma_pnl(self, positions_data: list[dict[str, Any]]) -> float:
        """计算Gamma损益"""
        return sum(pos.get("gamma_pnl", 0) for pos in positions_data)

    async def _calculate_vega_pnl(self, positions_data: list[dict[str, Any]]) -> float:
        """计算Vega损益"""
        return sum(pos.get("vega_pnl", 0) for pos in positions_data)

    async def _calculate_strategy_pnl(
        self, trades_data: list[dict[str, Any]], strategy_type: str
    ) -> float:
        """计算策略类型损益"""
        return sum(
            trade.get("pnl", 0)
            for trade in trades_data
            if trade.get("strategy_type") == strategy_type
        )

    async def _calculate_market_environment_pnl(
        self, trades_data: list[dict[str, Any]], market_env: str
    ) -> float:
        """计算市场环境损益"""
        return sum(
            trade.get("pnl", 0)
            for trade in trades_data
            if trade.get("market_environment") == market_env
        )

    async def _periodic_analysis(self):
        """定期分析任务"""
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时分析一次
                await self.analyze_performance()
                await self.analyze_attribution()
                await self.analyze_risk_breakdown()
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Periodic analysis failed: {e}")

    # 公共接口方法
    async def get_performance_summary(self) -> dict[str, Any]:
        """获取性能摘要"""
        if not self.current_metrics:
            await self.analyze_performance()

        return {
            "metrics": {
                "total_return": self.current_metrics.total_return,
                "annualized_return": self.current_metrics.annualized_return,
                "sharpe_ratio": self.current_metrics.sharpe_ratio,
                "max_drawdown": self.current_metrics.max_drawdown,
                "win_rate": self.current_metrics.win_rate,
                "profit_factor": self.current_metrics.profit_factor,
            },
            "attribution": {
                "premium_income": self.current_attribution.premium_income
                if self.current_attribution
                else 0,
                "time_decay_pnl": self.current_attribution.time_decay_pnl
                if self.current_attribution
                else 0,
                "delta_pnl": self.current_attribution.delta_pnl
                if self.current_attribution
                else 0,
            }
            if self.current_attribution
            else {},
            "risk_breakdown": {
                "delta_risk": self.current_risk_breakdown.delta_risk
                if self.current_risk_breakdown
                else 0,
                "gamma_risk": self.current_risk_breakdown.gamma_risk
                if self.current_risk_breakdown
                else 0,
                "theta_risk": self.current_risk_breakdown.theta_risk
                if self.current_risk_breakdown
                else 0,
            }
            if self.current_risk_breakdown
            else {},
            "analysis_info": {
                "last_analysis": self.last_analysis_time.isoformat()
                if self.last_analysis_time
                else None,
                "analysis_count": self.analysis_count,
                "data_points": len(self.daily_pnl),
            },
        }

    def set_cache_manager(self, cache_manager: CacheManager):
        """设置缓存管理器"""
        self.cache_manager = cache_manager

    def set_event_bus(self, event_bus: EventBus):
        """设置事件总线"""
        self.event_bus = event_bus
