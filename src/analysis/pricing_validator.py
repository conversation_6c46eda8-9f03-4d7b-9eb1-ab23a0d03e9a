"""
期权定价验证模块

实现Black-Scholes期权定价模型、Deribit价格合理性验证、隐含波动率计算和价格异常检测
"""

import asyncio
import contextlib
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import Any

# 科学计算库采用懒加载以避免启动时阻塞
# import numpy as np
# from scipy.optimize import brentq
# from scipy.stats import norm
from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import BaseEvent, EventType


@dataclass
class OptionPricing:
    """期权定价数据"""

    symbol: str
    timestamp: datetime

    # 市场数据
    market_price: Decimal = Decimal("0")
    bid: Decimal = Decimal("0")
    ask: Decimal = Decimal("0")

    # 期权参数
    underlying_price: Decimal = Decimal("0")
    strike_price: Decimal = Decimal("0")
    time_to_expiry: float = 0.0  # 年化时间
    option_type: str = "call"  # call/put

    # 波动率数据
    market_iv: float = 0.0  # 市场隐含波动率
    calculated_iv: float = 0.0  # 计算得出的隐含波动率
    historical_vol: float = 0.0  # 历史波动率

    # 理论价格
    bs_price: Decimal = Decimal("0")  # Black-Scholes理论价格
    price_deviation: float = 0.0  # 价格偏差百分比

    # Greeks
    delta: float = 0.0
    gamma: float = 0.0
    theta: float = 0.0
    vega: float = 0.0
    rho: float = 0.0

    # 验证结果
    is_valid: bool = True
    validation_score: float = 100.0  # 0-100分
    anomaly_flags: list[str] = field(default_factory=list)


@dataclass
class PricingAlert:
    """定价告警"""

    symbol: str
    alert_type: str  # price_deviation, iv_anomaly, arbitrage_opportunity
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime
    current_value: float
    expected_value: float
    deviation_percentage: float


class BlackScholesCalculator:
    """Black-Scholes期权定价计算器"""

    @staticmethod
    def calculate_option_price(
        S: float, K: float, T: float, r: float, sigma: float, option_type: str = "call"
    ) -> float:
        """
        计算期权理论价格

        Args:
            S: 标的资产价格
            K: 行权价
            T: 到期时间(年)
            r: 无风险利率
            sigma: 波动率
            option_type: 期权类型 ("call" or "put")
        """
        # 懒加载科学计算库
        import numpy as np
        from scipy.stats import norm

        if T <= 0:
            # 到期时的内在价值
            if option_type.lower() == "call":
                return max(S - K, 0)
            else:
                return max(K - S, 0)

        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)

        if option_type.lower() == "call":
            price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        else:
            price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)

        return max(price, 0)

    @staticmethod
    def calculate_greeks(
        S: float, K: float, T: float, r: float, sigma: float, option_type: str = "call"
    ) -> dict[str, float]:
        """计算期权Greeks"""
        # 懒加载科学计算库
        import numpy as np
        from scipy.stats import norm

        if T <= 0:
            return {"delta": 0, "gamma": 0, "theta": 0, "vega": 0, "rho": 0}

        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)

        # Delta
        delta = norm.cdf(d1) if option_type.lower() == "call" else norm.cdf(d1) - 1

        # Gamma
        gamma = norm.pdf(d1) / (S * sigma * np.sqrt(T))

        # Theta
        if option_type.lower() == "call":
            theta = (
                -S * norm.pdf(d1) * sigma / (2 * np.sqrt(T))
                - r * K * np.exp(-r * T) * norm.cdf(d2)
            ) / 365
        else:
            theta = (
                -S * norm.pdf(d1) * sigma / (2 * np.sqrt(T))
                + r * K * np.exp(-r * T) * norm.cdf(-d2)
            ) / 365

        # Vega
        vega = S * norm.pdf(d1) * np.sqrt(T) / 100

        # Rho
        if option_type.lower() == "call":
            rho = K * T * np.exp(-r * T) * norm.cdf(d2) / 100
        else:
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100

        return {
            "delta": delta,
            "gamma": gamma,
            "theta": theta,
            "vega": vega,
            "rho": rho,
        }

    @staticmethod
    def calculate_implied_volatility(
        market_price: float,
        S: float,
        K: float,
        T: float,
        r: float,
        option_type: str = "call",
    ) -> float:
        """计算隐含波动率"""
        # 懒加载科学计算库
        from scipy.optimize import brentq

        if T <= 0:
            return 0.0

        def objective(sigma):
            return (
                BlackScholesCalculator.calculate_option_price(
                    S, K, T, r, sigma, option_type
                )
                - market_price
            )

        try:
            # 使用Brent方法求解
            iv = brentq(objective, 0.001, 5.0, xtol=1e-6)
            return iv
        except (ValueError, RuntimeError) as e:
            # 严格模式：求解失败即失败
            raise e


class OptionPricingValidator(BaseComponent):
    """期权定价验证器"""

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("OptionPricingValidator", config)
        # 使用默认配置如果没有提供配置
        self.config = config or {
            "enabled": True,
            "price_deviation_max": 10.0,
            "iv_deviation_max": 20.0,
            "min_time_to_expiry": 1 / 365,
            "max_iv": 3.0,
            "min_iv": 0.05,
        }

        # 定价数据存储
        self.pricing_data: dict[str, OptionPricing] = {}
        self.pricing_history: dict[str, list[OptionPricing]] = {}

        # 验证配置
        self.validation_thresholds = {
            "price_deviation_max": 10.0,  # 最大价格偏差10%
            "iv_deviation_max": 20.0,  # 最大隐含波动率偏差20%
            "min_time_to_expiry": 1 / 365,  # 最小到期时间1天
            "max_iv": 3.0,  # 最大隐含波动率300%
            "min_iv": 0.05,  # 最小隐含波动率5%
        }

        # 市场参数
        self.risk_free_rate = 0.02  # 2%无风险利率（适合加密货币市场）

        # 活跃告警
        self.active_alerts: list[PricingAlert] = []

        # Black-Scholes计算器
        self.bs_calculator = BlackScholesCalculator()

        # 定期任务
        self._periodic_task: asyncio.Task | None = None

    async def _initialize_impl(self):
        """具体初始化实现"""
        try:
            if self.logger:
                await self.logger.info("OptionPricingValidator 开始初始化...")

            # 验证配置
            if not self.config:
                if self.logger:
                    await self.logger.error("OptionPricingValidator: 配置为空")
                return False

            if self.logger:
                await self.logger.info(
                    f"OptionPricingValidator: 配置验证通过，验证阈值: {self.validation_thresholds}"
                )

            # 初始化数据存储
            if self.logger:
                await self.logger.info("OptionPricingValidator: 初始化数据存储...")

            self.pricing_data = {}
            self.pricing_history = {}
            self.active_alerts = []

            if self.logger:
                await self.logger.info("OptionPricingValidator: 数据存储初始化完成")

            # 初始化 Black-Scholes 计算器
            self.bs_calculator = BlackScholesCalculator()
            if self.logger:
                await self.logger.info(
                    "OptionPricingValidator: Black-Scholes 计算器初始化完成"
                )

            # 尝试从依赖注入器获取 EventBus
            if (
                (not hasattr(self, "event_bus") or self.event_bus is None)
                and hasattr(self, "dependency_injector")
                and self.dependency_injector
            ):
                try:
                    from src.core.event_bus import EventBus

                    self.event_bus = await self.dependency_injector.resolve(EventBus)
                    if self.logger:
                        await self.logger.info(
                            "OptionPricingValidator: EventBus 已从依赖注入器获取"
                        )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"OptionPricingValidator: 无法从依赖注入器获取 EventBus: {e}"
                        )

            # 订阅市场数据事件 - 如果事件总线可用
            try:
                if hasattr(self, "event_bus") and self.event_bus:
                    await self.event_bus.subscribe(
                        "option_data_update", self._handle_option_data
                    )
                    await self.event_bus.subscribe(
                        "underlying_price_update", self._handle_underlying_price
                    )

                    if self.logger:
                        await self.logger.info("OptionPricingValidator: 事件订阅成功")
                else:
                    if self.logger:
                        await self.logger.warning(
                            "OptionPricingValidator: EventBus 未可用，将无法接收实时数据事件"
                        )
            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"OptionPricingValidator: 事件订阅失败，但继续初始化: {e}"
                    )

            # 注意：定期验证任务将在start阶段启动，避免在初始化时阻塞
            if self.logger:
                await self.logger.info(
                    "OptionPricingValidator: 初始化完成，定期验证任务将在启动时开始..."
                )

            if self.logger:
                await self.logger.info("✅ OptionPricingValidator 初始化成功完成")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ OptionPricingValidator 初始化失败: {e}")
                import traceback

                await self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            if self.logger:
                await self.logger.info("OptionPricingValidator starting...")

            # 启动定期验证任务
            if self.logger:
                await self.logger.info("OptionPricingValidator: 启动定期验证任务...")

            self._periodic_task = asyncio.create_task(self._periodic_validation())

            if self.logger:
                await self.logger.info("✅ OptionPricingValidator started successfully")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"OptionPricingValidator start failed: {e}")
            return False

    async def _stop_impl(self):
        """具体停止实现"""
        try:
            # 停止定期验证任务
            if self._periodic_task:
                self._periodic_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._periodic_task
                self._periodic_task = None

            if self.logger:
                await self.logger.info("OptionPricingValidator stopped")
        except Exception as e:
            if self.logger:
                await self.logger.error(f"OptionPricingValidator stop error: {e}")

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查基本状态
            if not self.is_initialized:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY, message="Component not initialized"
                )

            # 检查定价数据状态
            if not self.pricing_data:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED, message="No pricing data available"
                )

            # 检查告警状态
            critical_alerts = [
                alert for alert in self.active_alerts if alert.severity == "critical"
            ]
            if critical_alerts:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Critical pricing alerts: {len(critical_alerts)}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="OptionPricingValidator operating normally",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _handle_option_data(self, event: BaseEvent):
        """处理期权数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol or not self._is_option_symbol(symbol):
                return

            # 解析期权信息
            option_info = self._parse_option_symbol(symbol)
            if not option_info:
                return

            # 创建或更新定价数据
            await self._update_pricing_data(symbol, data, option_info)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling option data: {e}")

    async def _handle_underlying_price(self, event: BaseEvent):
        """处理标的资产价格更新"""
        try:
            data = event.data
            underlying_symbol = data.get("symbol")
            underlying_price = Decimal(str(data.get("price", 0)))

            # 更新相关期权的标的价格
            for symbol, pricing in self.pricing_data.items():
                if underlying_symbol in symbol:  # 简化匹配逻辑
                    pricing.underlying_price = underlying_price
                    await self._validate_option_pricing(symbol)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling underlying price update: {e}")

    def _is_option_symbol(self, symbol: str) -> bool:
        """判断是否为期权符号"""
        return "-C" in symbol or "-P" in symbol

    def _parse_option_symbol(self, symbol: str) -> dict[str, Any] | None:
        """解析期权符号"""
        try:
            # 示例: BTC-07JAN25-45000-C
            parts = symbol.split("-")
            if len(parts) != 4:
                return None

            underlying = parts[0]
            expiry_str = parts[1]
            strike_str = parts[2]
            option_type = "call" if parts[3] == "C" else "put"

            # 解析到期日
            expiry_date = datetime.strptime(
                f"20{expiry_str[5:]}-{expiry_str[2:5]}-{expiry_str[:2]}", "%Y-%b-%d"
            ).replace(tzinfo=UTC)

            # 解析行权价
            strike_price = Decimal(strike_str)

            return {
                "underlying": underlying,
                "expiry_date": expiry_date,
                "strike_price": strike_price,
                "option_type": option_type,
            }

        except Exception:
            if self.logger:
                # 注意：这是一个同步函数，不能使用await
                # 如果需要异步日志记录，应该将此函数改为async
                pass  # 暂时跳过日志记录
            return None

    async def _update_pricing_data(
        self, symbol: str, data: dict[str, Any], option_info: dict[str, Any]
    ):
        """更新定价数据"""
        try:
            # 获取或创建定价对象
            if symbol not in self.pricing_data:
                self.pricing_data[symbol] = OptionPricing(
                    symbol=symbol, timestamp=datetime.now(UTC)
                )

            pricing = self.pricing_data[symbol]

            # 更新市场数据
            pricing.market_price = Decimal(str(data.get("price", 0)))
            pricing.bid = Decimal(str(data.get("bid", 0)))
            pricing.ask = Decimal(str(data.get("ask", 0)))
            pricing.market_iv = data.get("iv", 0.0)

            # 更新期权参数
            pricing.strike_price = option_info["strike_price"]
            pricing.option_type = option_info["option_type"]

            # 计算到期时间
            now = datetime.now(UTC)
            expiry_date = option_info["expiry_date"]
            time_to_expiry = (expiry_date - now).total_seconds() / (365.25 * 24 * 3600)
            pricing.time_to_expiry = max(time_to_expiry, 0)

            pricing.timestamp = now

            # 执行定价验证
            await self._validate_option_pricing(symbol)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating pricing data for {symbol}: {e}"
                )

    async def _validate_option_pricing(self, symbol: str):
        """验证期权定价"""
        try:
            pricing = self.pricing_data.get(symbol)
            if not pricing or pricing.underlying_price <= 0:
                return

            # 计算理论价格
            S = float(pricing.underlying_price)
            K = float(pricing.strike_price)
            T = pricing.time_to_expiry
            r = self.risk_free_rate

            # 使用市场隐含波动率计算理论价格
            if pricing.market_iv > 0:
                bs_price = self.bs_calculator.calculate_option_price(
                    S, K, T, r, pricing.market_iv, pricing.option_type
                )
                pricing.bs_price = Decimal(str(bs_price))

                # 计算Greeks
                greeks = self.bs_calculator.calculate_greeks(
                    S, K, T, r, pricing.market_iv, pricing.option_type
                )
                pricing.delta = greeks["delta"]
                pricing.gamma = greeks["gamma"]
                pricing.theta = greeks["theta"]
                pricing.vega = greeks["vega"]
                pricing.rho = greeks["rho"]

            # 计算隐含波动率
            if pricing.market_price > 0:
                calculated_iv = self.bs_calculator.calculate_implied_volatility(
                    float(pricing.market_price), S, K, T, r, pricing.option_type
                )
                pricing.calculated_iv = calculated_iv

            # 计算价格偏差
            if pricing.bs_price > 0:
                price_deviation = float(
                    abs(pricing.market_price - pricing.bs_price)
                    / pricing.bs_price
                    * 100
                )
                pricing.price_deviation = price_deviation

            # 执行异常检测
            await self._detect_pricing_anomalies(symbol)

            # 计算验证评分
            pricing.validation_score = await self._calculate_validation_score(symbol)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error validating pricing for {symbol}: {e}")

    async def _detect_pricing_anomalies(self, symbol: str):
        """检测定价异常"""
        try:
            pricing = self.pricing_data[symbol]
            anomaly_flags = []

            # 检查价格偏差
            if (
                pricing.price_deviation
                > self.validation_thresholds["price_deviation_max"]
            ):
                anomaly_flags.append("high_price_deviation")
                await self._create_pricing_alert(
                    symbol,
                    "price_deviation",
                    "high",
                    f"Price deviation {pricing.price_deviation:.2f}% exceeds threshold",
                    pricing.price_deviation,
                    self.validation_thresholds["price_deviation_max"],
                )

            # 检查隐含波动率异常
            iv_deviation = (
                abs(pricing.market_iv - pricing.calculated_iv)
                / pricing.calculated_iv
                * 100
                if pricing.calculated_iv > 0
                else 0
            )
            if iv_deviation > self.validation_thresholds["iv_deviation_max"]:
                anomaly_flags.append("iv_anomaly")
                await self._create_pricing_alert(
                    symbol,
                    "iv_anomaly",
                    "medium",
                    f"IV deviation {iv_deviation:.2f}% exceeds threshold",
                    iv_deviation,
                    self.validation_thresholds["iv_deviation_max"],
                )

            # 检查隐含波动率范围
            if pricing.market_iv > self.validation_thresholds["max_iv"]:
                anomaly_flags.append("iv_too_high")
            elif pricing.market_iv < self.validation_thresholds["min_iv"]:
                anomaly_flags.append("iv_too_low")

            # 检查到期时间
            if (
                pricing.time_to_expiry
                < self.validation_thresholds["min_time_to_expiry"]
            ):
                anomaly_flags.append("near_expiry")

            # 检查套利机会
            if await self._check_arbitrage_opportunity(symbol):
                anomaly_flags.append("arbitrage_opportunity")
                await self._create_pricing_alert(
                    symbol,
                    "arbitrage_opportunity",
                    "critical",
                    "Potential arbitrage opportunity detected",
                    0,
                    0,
                )

            pricing.anomaly_flags = anomaly_flags
            pricing.is_valid = len(anomaly_flags) == 0

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error detecting anomalies for {symbol}: {e}")

    async def _check_arbitrage_opportunity(self, symbol: str) -> bool:
        """检查套利机会"""
        try:
            pricing = self.pricing_data[symbol]

            # 简化的套利检查：市场价格与理论价格偏差过大
            if pricing.bs_price > 0:
                deviation = abs(float(pricing.market_price - pricing.bs_price)) / float(
                    pricing.bs_price
                )
                return deviation > 0.2  # 20%偏差认为存在套利机会

            return False

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error checking arbitrage for {symbol}: {e}")
            return False

    async def _calculate_validation_score(self, symbol: str) -> float:
        """计算验证评分"""
        try:
            pricing = self.pricing_data[symbol]
            score = 100.0

            # 价格偏差扣分
            if pricing.price_deviation > 0:
                score -= min(pricing.price_deviation * 2, 50)  # 最多扣50分

            # 异常标志扣分
            score -= len(pricing.anomaly_flags) * 10

            # 数据完整性加分
            if pricing.market_iv > 0:
                score += 5
            if pricing.bs_price > 0:
                score += 5

            return max(score, 0)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error calculating validation score for {symbol}: {e}"
                )
            return 0.0

    async def _create_pricing_alert(
        self,
        symbol: str,
        alert_type: str,
        severity: str,
        message: str,
        current_value: float,
        expected_value: float,
    ):
        """创建定价告警"""
        alert = PricingAlert(
            symbol=symbol,
            alert_type=alert_type,
            severity=severity,
            message=message,
            timestamp=datetime.now(UTC),
            current_value=current_value,
            expected_value=expected_value,
            deviation_percentage=abs(current_value - expected_value)
            / expected_value
            * 100
            if expected_value > 0
            else 0,
        )

        # 检查是否已存在相同告警
        existing = any(
            a.symbol == alert.symbol
            and a.alert_type == alert.alert_type
            and (datetime.now(UTC) - a.timestamp).seconds < 300  # 5分钟内
            for a in self.active_alerts
        )

        if not existing:
            self.active_alerts.append(alert)

            # 发布告警事件
            if self.event_bus:
                await self.event_bus.publish(
                    BaseEvent(
                        event_type=EventType.PRICING_ALERT,
                        data={
                            "symbol": alert.symbol,
                            "alert_type": alert.alert_type,
                            "severity": alert.severity,
                            "message": alert.message,
                            "current_value": alert.current_value,
                            "expected_value": alert.expected_value,
                        },
                    )
                )

            if self.logger:
                await self.logger.warning(f"Pricing alert: {alert.message}")

        return alert

    async def _periodic_validation(self):
        """定期验证任务"""
        while True:
            try:
                await asyncio.sleep(30)  # 每30秒执行一次

                # 重新验证所有期权定价
                for symbol in list(self.pricing_data.keys()):
                    await self._validate_option_pricing(symbol)

                # 保存历史数据
                for symbol, pricing in self.pricing_data.items():
                    if symbol not in self.pricing_history:
                        self.pricing_history[symbol] = []

                    self.pricing_history[symbol].append(pricing)

                    # 保留最近24小时的数据
                    cutoff_time = datetime.now(UTC) - timedelta(hours=24)
                    self.pricing_history[symbol] = [
                        p
                        for p in self.pricing_history[symbol]
                        if p.timestamp >= cutoff_time
                    ]

                # 清理过期告警
                current_time = datetime.now(UTC)
                self.active_alerts = [
                    alert
                    for alert in self.active_alerts
                    if (current_time - alert.timestamp).seconds
                    < 3600  # 保留1小时内的告警
                ]

            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Periodic validation error: {e}")

    # 公共接口方法

    async def get_option_pricing(self, symbol: str) -> OptionPricing | None:
        """获取期权定价数据"""
        return self.pricing_data.get(symbol)

    async def get_all_option_pricing(self) -> dict[str, OptionPricing]:
        """获取所有期权定价数据"""
        return self.pricing_data.copy()

    async def get_pricing_alerts(self, symbol: str | None = None) -> list[PricingAlert]:
        """获取定价告警"""
        if symbol:
            return [alert for alert in self.active_alerts if alert.symbol == symbol]
        return self.active_alerts.copy()

    async def validate_option_price(
        self, symbol: str, market_price: float
    ) -> dict[str, Any]:
        """验证单个期权价格 - 集成Monte Carlo验证"""
        try:
            pricing = self.pricing_data.get(symbol)
            if not pricing:
                return {"valid": False, "reason": "Option not found"}

            # 计算Black-Scholes理论价格
            S = float(pricing.underlying_price)
            K = float(pricing.strike_price)
            T = pricing.time_to_expiry
            r = self.risk_free_rate
            sigma = pricing.market_iv if pricing.market_iv > 0 else 0.5  # 默认50%波动率

            bs_theoretical_price = self.bs_calculator.calculate_option_price(
                S, K, T, r, sigma, pricing.option_type
            )

            # 尝试Monte Carlo验证（用于复杂或高价值期权）
            mc_theoretical_price = None
            mc_confidence_interval = None
            use_monte_carlo = False

            try:
                # 判断是否需要Monte Carlo验证
                option_value = market_price * S  # 估算期权总价值
                high_value_threshold = 1000  # 高价值期权阈值

                complex_option = (
                    abs(sigma - 0.5) > 0.3  # 高波动率或低波动率
                    or T > 0.25  # 长期期权（3个月以上）
                    or option_value > high_value_threshold  # 高价值期权
                )

                if complex_option:
                    # 导入Monte Carlo引擎
                    from .monte_carlo_engine import (
                        MarketParameters,
                        MonteCarloEngine,
                        OptionContract,
                        SimulationConfig,
                    )

                    mc_engine = MonteCarloEngine()
                    await mc_engine.initialize()

                    # 市场参数
                    market_params = MarketParameters(
                        spot_price=S,
                        risk_free_rate=r,
                        volatility=sigma,
                        dividend_yield=0.0,
                    )

                    # 模拟配置
                    sim_config = SimulationConfig(
                        num_simulations=5000,  # 平衡精度和性能
                        num_steps=100,
                        antithetic_variates=True,
                        parallel_execution=True,
                        num_threads=2,
                    )

                    # 创建期权合约
                    contract = OptionContract(
                        option_type=pricing.option_type,
                        strike=K,
                        time_to_expiry=T,
                        option_side="long",
                        quantity=1.0,
                    )

                    # Monte Carlo定价
                    mc_result = await mc_engine.price_option(
                        contract, market_params, sim_config
                    )
                    mc_theoretical_price = mc_result.option_price
                    mc_confidence_interval = mc_result.confidence_interval
                    use_monte_carlo = True

                    if self.logger:
                        await self.logger.debug(
                            f"Monte Carlo validation for {symbol}: MC={mc_theoretical_price:.4f}, BS={bs_theoretical_price:.4f}"
                        )

            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"Monte Carlo validation failed for {symbol}: {e}"
                    )
                # Monte Carlo失败时使用Black-Scholes
                pass

            # 选择最终的理论价格
            if use_monte_carlo and mc_theoretical_price is not None:
                # 使用Monte Carlo和Black-Scholes的加权平均
                # Monte Carlo权重更高用于复杂期权
                mc_weight = 0.7
                bs_weight = 0.3
                final_theoretical_price = (
                    mc_weight * mc_theoretical_price + bs_weight * bs_theoretical_price
                )
                pricing_method = "monte_carlo_weighted"
            else:
                final_theoretical_price = bs_theoretical_price
                pricing_method = "black_scholes"

            # 计算偏差
            deviation = (
                abs(market_price - final_theoretical_price)
                / final_theoretical_price
                * 100
                if final_theoretical_price > 0
                else 100
            )

            # 验证有效性
            is_valid = deviation <= self.validation_thresholds["price_deviation_max"]

            # 如果使用了Monte Carlo，检查市场价格是否在置信区间内
            within_confidence_interval = True
            if use_monte_carlo and mc_confidence_interval:
                within_confidence_interval = (
                    mc_confidence_interval[0]
                    <= market_price
                    <= mc_confidence_interval[1]
                )
                if not within_confidence_interval:
                    # 如果超出置信区间，降低有效性判断的严格程度
                    adjusted_threshold = (
                        self.validation_thresholds["price_deviation_max"] * 1.5
                    )
                    is_valid = deviation <= adjusted_threshold

            result = {
                "valid": is_valid,
                "theoretical_price": final_theoretical_price,
                "market_price": market_price,
                "deviation_percentage": deviation,
                "threshold": self.validation_thresholds["price_deviation_max"],
                "pricing_method": pricing_method,
                "bs_price": bs_theoretical_price,
            }

            # 添加Monte Carlo特定信息
            if use_monte_carlo:
                result.update(
                    {
                        "mc_price": mc_theoretical_price,
                        "mc_confidence_interval": mc_confidence_interval,
                        "within_confidence_interval": within_confidence_interval,
                    }
                )

            return result

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error validating option price for {symbol}: {e}"
                )
            return {"valid": False, "reason": str(e)}

    async def calculate_fair_value(
        self, symbol: str, volatility: float | None = None
    ) -> float | None:
        """计算期权公允价值"""
        try:
            pricing = self.pricing_data.get(symbol)
            if not pricing or pricing.underlying_price <= 0:
                return None

            S = float(pricing.underlying_price)
            K = float(pricing.strike_price)
            T = pricing.time_to_expiry
            r = self.risk_free_rate
            sigma = volatility or pricing.market_iv or 0.5

            return self.bs_calculator.calculate_option_price(
                S, K, T, r, sigma, pricing.option_type
            )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error calculating fair value for {symbol}: {e}"
                )
            return None
