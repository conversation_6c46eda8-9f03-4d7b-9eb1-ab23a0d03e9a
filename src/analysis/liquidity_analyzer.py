"""
流动性分析模块

提供bid-ask spread监控、成交量分析、市场深度评估和流动性风险预警功能
"""

import asyncio
from collections import deque
from dataclasses import dataclass
from datetime import UTC, datetime, timedelta
from decimal import Decimal
from typing import TYPE_CHECKING, Any

import numpy as np

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import BaseEvent, DataUpdateEvent, EventType

if TYPE_CHECKING:
    from src.core.cache_manager import CacheManager
    from src.data.timescale_manager import TimescaleDBManager


@dataclass
class LiquidityMetrics:
    """流动性指标"""

    symbol: str
    timestamp: datetime

    # 价差指标
    bid_ask_spread: Decimal = Decimal("0")
    spread_percentage: float = 0.0

    # 成交量指标
    volume_24h: Decimal = Decimal("0")
    volume_1h: Decimal = Decimal("0")
    avg_trade_size: Decimal = Decimal("0")

    # 持仓量指标
    open_interest: Decimal = Decimal("0")
    oi_change_24h: Decimal = Decimal("0")

    # 市场深度指标
    bid_depth_5: Decimal = Decimal("0")  # 5档买单深度
    ask_depth_5: Decimal = Decimal("0")  # 5档卖单深度
    total_depth: Decimal = Decimal("0")

    # 流动性评分
    liquidity_score: float = 0.0  # 0-100分
    liquidity_grade: str = "Unknown"  # A, B, C, D, F

    # 风险指标
    price_impact_1k: float = 0.0  # 1000美元订单的价格冲击
    price_impact_10k: float = 0.0  # 10000美元订单的价格冲击


@dataclass
class LiquidityAlert:
    """流动性告警"""

    symbol: str
    alert_type: str  # spread_high, volume_low, depth_low, price_impact_high
    severity: str  # low, medium, high, critical
    message: str
    timestamp: datetime
    current_value: float
    threshold: float


class LiquidityAnalyzer(BaseComponent):
    """流动性分析器"""

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("LiquidityAnalyzer", config)
        # 使用默认配置如果没有提供配置
        self.config = config or {
            "enabled": True,
            "spread_threshold": 15.0,
            "volume_threshold": 1000,
            "depth_threshold": 5000,
        }

        # 流动性数据存储
        self.liquidity_metrics: dict[str, LiquidityMetrics] = {}
        self.metrics_history: dict[str, deque] = {}
        self.max_history_size = 1440  # 24小时历史数据

        # 告警配置
        self.alert_thresholds = {
            "spread_percentage": 15.0,  # 价差超过15%（适合加密货币期权）
            "volume_24h_min": 1000,  # 24小时最小成交量
            "depth_min": 5000,  # 最小市场深度
            "price_impact_1k_max": 0.5,  # 1k订单最大价格冲击0.5%
            "price_impact_10k_max": 2.0,  # 10k订单最大价格冲击2%
        }

        # 流动性评分权重
        self.score_weights = {
            "spread": 0.3,  # 价差权重
            "volume": 0.25,  # 成交量权重
            "depth": 0.25,  # 市场深度权重
            "stability": 0.2,  # 稳定性权重
        }

        # 活跃告警
        self.active_alerts: list[LiquidityAlert] = []

        # 数据源依赖 - 用于获取历史数据 (类型注解用于依赖注入)
        self.timescale_manager: TimescaleDBManager = None
        self.cache_manager: CacheManager = None

    async def _initialize_impl(self):
        """具体初始化实现"""
        try:
            if self.logger:
                await self.logger.info("LiquidityAnalyzer 开始初始化...")

            # 验证配置
            if not self.config:
                if self.logger:
                    await self.logger.error("LiquidityAnalyzer: 配置为空")
                return False

            if self.logger:
                await self.logger.info(
                    f"LiquidityAnalyzer: 配置验证通过，阈值配置: {self.alert_thresholds}"
                )

            # 尝试从依赖注入器获取 EventBus
            if (
                (not hasattr(self, "event_bus") or self.event_bus is None)
                and hasattr(self, "dependency_injector")
                and self.dependency_injector
            ):
                try:
                    from src.core.event_bus import EventBus

                    self.event_bus = await self.dependency_injector.resolve(EventBus)
                    if self.logger:
                        await self.logger.info(
                            "LiquidityAnalyzer: EventBus 已从依赖注入器获取"
                        )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"LiquidityAnalyzer: 无法从依赖注入器获取 EventBus: {e}"
                        )

            # 订阅市场数据事件 - 如果事件总线可用
            try:
                if hasattr(self, "event_bus") and self.event_bus:
                    await self.event_bus.subscribe(
                        event_types={EventType.DATA_UPDATE},
                        callback=self._handle_market_data,
                        subscriber_id=f"{self.component_name}_market_data_handler",
                    )

                    if self.logger:
                        await self.logger.info("LiquidityAnalyzer: 事件订阅成功")
                else:
                    if self.logger:
                        await self.logger.warning(
                            "LiquidityAnalyzer: EventBus 未可用，将无法接收实时数据事件"
                        )
            except Exception as e:
                if self.logger:
                    await self.logger.warning(
                        f"LiquidityAnalyzer: 事件订阅失败，但继续初始化: {e}"
                    )

            # 初始化数据存储
            if self.logger:
                await self.logger.info("LiquidityAnalyzer: 初始化数据存储...")

            self.liquidity_metrics = {}
            self.metrics_history = {}
            self.active_alerts = []

            if self.logger:
                await self.logger.info("LiquidityAnalyzer: 数据存储初始化完成")

            # 启动定期分析任务
            if self.logger:
                await self.logger.info("LiquidityAnalyzer: 启动定期分析任务...")

            asyncio.create_task(self._periodic_analysis())

            if self.logger:
                await self.logger.info("✅ LiquidityAnalyzer 初始化成功完成")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"❌ LiquidityAnalyzer 初始化失败: {e}")
                import traceback

                await self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            if self.logger:
                await self.logger.info("LiquidityAnalyzer started")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"LiquidityAnalyzer start failed: {e}")
            return False

    async def _stop_impl(self):
        """具体停止实现"""
        if self.logger:
            await self.logger.info("LiquidityAnalyzer stopped")

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查基本状态
            if not self.is_initialized:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY, message="Component not initialized"
                )

            # 检查数据更新状态
            if not self.liquidity_metrics:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED, message="No liquidity data available"
                )

            # 检查告警状态
            critical_alerts = [
                alert for alert in self.active_alerts if alert.severity == "critical"
            ]
            if critical_alerts:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"Critical liquidity alerts: {len(critical_alerts)}",
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="LiquidityAnalyzer operating normally",
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def _handle_market_data(self, event: BaseEvent):
        """处理市场数据更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol:
                return

            # 更新基础价格数据
            await self._update_price_metrics(symbol, data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling market data: {e}")

    async def _handle_orderbook_update(self, event: BaseEvent):
        """处理订单簿更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol:
                return

            # 更新市场深度指标
            await self._update_depth_metrics(symbol, data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling orderbook update: {e}")

    async def _handle_trade_update(self, event: BaseEvent):
        """处理交易更新"""
        try:
            data = event.data
            symbol = data.get("symbol")

            if not symbol:
                return

            # 更新成交量指标
            await self._update_volume_metrics(symbol, data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling trade update: {e}")

    async def _update_price_metrics(self, symbol: str, data: dict[str, Any]):
        """更新价格相关指标"""
        try:
            bid = Decimal(str(data.get("bid", 0)))
            ask = Decimal(str(data.get("ask", 0)))

            if bid > 0 and ask > 0:
                # 计算价差
                spread = ask - bid
                mid_price = (bid + ask) / 2
                spread_percentage = (
                    float(spread / mid_price * 100) if mid_price > 0 else 0
                )

                # 获取或创建流动性指标
                if symbol not in self.liquidity_metrics:
                    self.liquidity_metrics[symbol] = LiquidityMetrics(
                        symbol=symbol, timestamp=datetime.now(UTC)
                    )

                metrics = self.liquidity_metrics[symbol]
                metrics.bid_ask_spread = spread
                metrics.spread_percentage = spread_percentage
                metrics.timestamp = datetime.now(UTC)

                # 检查价差告警
                await self._check_spread_alert(symbol, spread_percentage)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating price metrics for {symbol}: {e}"
                )

    async def _update_depth_metrics(self, symbol: str, data: dict[str, Any]):
        """更新市场深度指标"""
        try:
            bids = data.get("bids", [])
            asks = data.get("asks", [])

            # 计算5档深度
            bid_depth_5 = sum(Decimal(str(bid[1])) for bid in bids[:5])
            ask_depth_5 = sum(Decimal(str(ask[1])) for ask in asks[:5])
            total_depth = bid_depth_5 + ask_depth_5

            # 更新指标
            if symbol in self.liquidity_metrics:
                metrics = self.liquidity_metrics[symbol]
                metrics.bid_depth_5 = bid_depth_5
                metrics.ask_depth_5 = ask_depth_5
                metrics.total_depth = total_depth

                # 计算价格冲击
                metrics.price_impact_1k = await self._calculate_price_impact(
                    symbol, 1000, bids, asks
                )
                metrics.price_impact_10k = await self._calculate_price_impact(
                    symbol, 10000, bids, asks
                )

                # 检查深度告警
                await self._check_depth_alert(symbol, float(total_depth))
                await self._check_price_impact_alert(
                    symbol, metrics.price_impact_1k, metrics.price_impact_10k
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating depth metrics for {symbol}: {e}"
                )

    async def _update_volume_metrics(self, symbol: str, data: dict[str, Any]):
        """更新成交量指标 - 基于实际市场数据"""
        try:
            if symbol not in self.liquidity_metrics:
                return

            metrics = self.liquidity_metrics[symbol]

            # 从实际数据中提取成交量信息
            current_volume = data.get("volume", 0)
            current_trade_size = data.get("trade_size", 0)
            current_oi = data.get("open_interest", 0)

            # 优先使用直接传入的字段（用于测试或API数据）
            if "volume_24h" in data:
                metrics.volume_24h = Decimal(str(data["volume_24h"]))
            elif historical_data := await self._get_historical_volume_data(symbol):
                metrics.volume_24h = Decimal(
                    str(historical_data.get("volume_24h", current_volume))
                )
            else:
                metrics.volume_24h = Decimal(str(current_volume))

            if "volume_1h" in data:
                metrics.volume_1h = Decimal(str(data["volume_1h"]))
            elif historical_data:
                metrics.volume_1h = Decimal(
                    str(historical_data.get("volume_1h", current_volume / 24))
                )
            else:
                metrics.volume_1h = Decimal(str(current_volume / 24))

            if "avg_trade_size" in data:
                metrics.avg_trade_size = Decimal(str(data["avg_trade_size"]))
            elif historical_data:
                total_volume = historical_data.get("total_volume", current_volume)
                total_trades = historical_data.get("total_trades", 1)
                metrics.avg_trade_size = Decimal(
                    str(total_volume / max(total_trades, 1))
                )
            else:
                metrics.avg_trade_size = Decimal(str(current_trade_size or 50))

            # 更新持仓量数据
            if "open_interest" in data:
                metrics.open_interest = Decimal(str(data["open_interest"]))
            elif historical_data:
                metrics.open_interest = Decimal(
                    str(current_oi or historical_data.get("open_interest", 0))
                )
            else:
                metrics.open_interest = Decimal(str(current_oi or 0))

            # 计算持仓量变化
            if historical_data:
                prev_oi = historical_data.get("prev_open_interest", current_oi)
                metrics.oi_change_24h = Decimal(str((current_oi or 0) - (prev_oi or 0)))
            else:
                metrics.oi_change_24h = Decimal("0")  # 无法计算变化

                # 检查成交量告警
                await self._check_volume_alert(symbol, metrics.volume_24h)

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating volume metrics for {symbol}: {e}"
                )

    async def _calculate_price_impact(
        self, symbol: str, order_size_usd: float, bids: list, asks: list
    ) -> float:
        """计算价格冲击"""
        try:
            # 获取当前中间价
            if not bids or not asks:
                return 0.0

            best_bid = Decimal(str(bids[0][0]))
            best_ask = Decimal(str(asks[0][0]))
            mid_price = (best_bid + best_ask) / 2

            # 计算需要的数量
            order_quantity = Decimal(str(order_size_usd)) / mid_price

            # 模拟市价单执行，计算平均成交价
            remaining_qty = order_quantity
            total_cost = Decimal("0")

            # 假设买单，遍历卖单
            for price_str, qty_str in asks:
                if remaining_qty <= 0:
                    break

                price = Decimal(str(price_str))
                qty = Decimal(str(qty_str))

                executed_qty = min(remaining_qty, qty)
                total_cost += executed_qty * price
                remaining_qty -= executed_qty

            if remaining_qty > 0:
                # 流动性不足
                return 10.0  # 返回高价格冲击

            avg_price = total_cost / order_quantity
            price_impact = float(abs(avg_price - mid_price) / mid_price * 100)

            return price_impact

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating price impact: {e}")
            # 严格模式：失败即失败，返回高冲击以阻止交易
            return 100.0

    async def _estimate_price_impact_fallback(
        self, order_size_usd: float, symbol: str
    ) -> float:
        """基于订单大小估算价格冲击的后备方法"""
        try:
            # 方法1：基于历史价格冲击模型
            if hasattr(self, "timescale_manager") and self.timescale_manager:
                # 查询类似大小订单的历史价格冲击
                from datetime import timedelta

                end_time = datetime.now(UTC)
                start_time = end_time - timedelta(days=7)

                historical_impacts = await self.timescale_manager.query_price_impacts(
                    symbol=symbol,
                    start_time=start_time,
                    end_time=end_time,
                    order_size_range=(order_size_usd * 0.8, order_size_usd * 1.2),
                )

                if historical_impacts:
                    avg_impact = sum(
                        impact["price_impact"] for impact in historical_impacts
                    ) / len(historical_impacts)
                    return min(avg_impact, 15.0)  # 限制最大15%

            # 方法2：基于订单大小的经验公式
            # 对于期权交易，价格冲击通常与订单大小的平方根成正比
            import math

            # 获取标的资产的市值或日均成交量作为参考
            daily_volume_usd = 1000000.0  # 默认100万美元日成交量

            # 尝试从配置或历史数据获取真实的日均成交量
            if hasattr(self, "config_manager") and self.config_manager:
                symbol_config = await self.config_manager.get_symbol_config(symbol)
                if symbol_config and "avg_daily_volume_usd" in symbol_config:
                    daily_volume_usd = float(symbol_config["avg_daily_volume_usd"])

            # 价格冲击 = 系数 × sqrt(订单大小 / 日均成交量)
            # 系数根据资产类型调整，期权通常比现货流动性更差
            coefficient = 2.0  # 期权系数
            if "BTC" in symbol.upper():
                coefficient = 1.5  # BTC期权流动性相对较好
            elif "ETH" in symbol.upper():
                coefficient = 1.8

            impact = coefficient * math.sqrt(order_size_usd / daily_volume_usd) * 100

            # 限制价格冲击在合理范围内
            return min(max(impact, 0.1), 20.0)  # 最小0.1%，最大20%

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error in price impact fallback calculation: {e}"
                )

            # 最后的保守估计：基于订单大小的简单分级
            if order_size_usd < 1000:
                return 0.5  # 小单0.5%
            elif order_size_usd < 10000:
                return 1.5  # 中单1.5%
            elif order_size_usd < 50000:
                return 3.0  # 大单3%
            else:
                return 5.0  # 特大单5%

    async def _get_historical_volume_data(self, symbol: str) -> dict[str, Any] | None:
        """获取历史成交量数据"""
        try:
            # 优先从缓存获取
            cache_key = f"historical_volume:{symbol}:24h"
            if self.cache_manager:
                from src.services.market_data_service import MarketDataService

                mds = await self.dependency_injector.resolve(MarketDataService)
                cached_data = await mds.get_cached(cache_key)
                if cached_data:
                    return cached_data

            # 如果缓存没有，尝试从数据库获取
            if hasattr(self, "timescale_manager") and self.timescale_manager:
                from datetime import timedelta

                # 获取过去24小时的数据
                end_time = datetime.now(UTC)
                start_time = end_time - timedelta(hours=24)
                start_time_1h = end_time - timedelta(hours=1)

                # 查询24小时数据
                market_data_24h = await self.timescale_manager.query_market_data(
                    symbol=symbol, start_time=start_time, end_time=end_time, limit=10000
                )

                # 查询1小时数据
                market_data_1h = await self.timescale_manager.query_market_data(
                    symbol=symbol,
                    start_time=start_time_1h,
                    end_time=end_time,
                    limit=1000,
                )

                if market_data_24h:
                    # 计算24小时成交量统计
                    total_volume_24h = sum(
                        float(data.get("volume", 0)) for data in market_data_24h
                    )
                    total_trades_24h = len(market_data_24h)

                    # 计算1小时成交量统计
                    total_volume_1h = (
                        sum(float(data.get("volume", 0)) for data in market_data_1h)
                        if market_data_1h
                        else total_volume_24h / 24
                    )

                    # 获取前一天的持仓量（如果有）
                    prev_start = start_time - timedelta(hours=24)
                    prev_data = await self.timescale_manager.query_market_data(
                        symbol=symbol,
                        start_time=prev_start,
                        end_time=start_time,
                        limit=1,
                    )
                    prev_oi = (
                        float(prev_data[0].get("metadata", {}).get("open_interest", 0))
                        if prev_data
                        else 0
                    )
                    current_oi = (
                        float(
                            market_data_24h[-1]
                            .get("metadata", {})
                            .get("open_interest", 0)
                        )
                        if market_data_24h
                        else 0
                    )

                    historical_data = {
                        "volume_24h": total_volume_24h,
                        "volume_1h": total_volume_1h,
                        "total_volume": total_volume_24h,
                        "total_trades": total_trades_24h,
                        "avg_trade_size": total_volume_24h / max(total_trades_24h, 1),
                        "open_interest": current_oi,
                        "prev_open_interest": prev_oi,
                        "timestamp": end_time.isoformat(),
                    }

                    # 缓存结果5分钟
                    if self.cache_manager:
                        await self.cache_manager.set(
                            cache_key,
                            historical_data,
                            ttl=300,  # 5分钟缓存
                        )

                    if self.logger:
                        await self.logger.debug(
                            f"获取 {symbol} 历史数据成功: 24h成交量={total_volume_24h:.0f}, 交易数={total_trades_24h}"
                        )

                    return historical_data

            # 如果数据库查询失败，返回默认估算值
            if self.logger:
                await self.logger.warning(
                    f"无法从数据库获取 {symbol} 历史数据，使用估算值"
                )

            return {
                "volume_24h": 50000,  # 默认24小时成交量
                "volume_1h": 2000,  # 默认1小时成交量
                "total_volume": 50000,
                "total_trades": 1000,
                "avg_trade_size": 50,
                "open_interest": 0,
                "prev_open_interest": 0,
                "timestamp": datetime.now(UTC).isoformat(),
                "estimated": True,
            }

        except Exception as e:
            if self.logger:
                await self.logger.error(f"获取 {symbol} 历史成交量数据失败: {e}")
            return None

    async def _calculate_liquidity_score(self, symbol: str) -> tuple[float, str]:
        """计算流动性评分"""
        try:
            if symbol not in self.liquidity_metrics:
                return 0.0, "F"

            metrics = self.liquidity_metrics[symbol]

            # 价差评分 (0-100)
            spread_score = max(0, 100 - metrics.spread_percentage * 20)

            # 成交量评分 (0-100)
            volume_score = min(100, float(metrics.volume_24h) / 10000 * 100)

            # 市场深度评分 (0-100)
            depth_score = min(100, float(metrics.total_depth) / 50000 * 100)

            # 稳定性评分 (基于历史数据波动性)
            stability_score = await self._calculate_stability_score(symbol)

            # 加权总分
            total_score = (
                spread_score * self.score_weights["spread"]
                + volume_score * self.score_weights["volume"]
                + depth_score * self.score_weights["depth"]
                + stability_score * self.score_weights["stability"]
            )

            # 评级
            if total_score >= 90:
                grade = "A"
            elif total_score >= 80:
                grade = "B"
            elif total_score >= 70:
                grade = "C"
            elif total_score >= 60:
                grade = "D"
            else:
                grade = "F"

            return total_score, grade

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating liquidity score: {e}")
            return 0.0, "F"

    async def _calculate_stability_score(self, symbol: str) -> float:
        """计算稳定性评分"""
        try:
            # 获取历史数据
            if symbol not in self.metrics_history:
                return 50.0  # 默认中等评分

            history = self.metrics_history[symbol]
            if len(history) < 10:
                return 50.0

            # 计算价差波动性
            spreads = [m.spread_percentage for m in history]
            spread_volatility = np.std(spreads) if spreads else 0

            # 稳定性评分 (波动性越低，评分越高)
            stability_score = max(0, 100 - spread_volatility * 10)

            return stability_score

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error calculating stability score: {e}")
            return 50.0

    async def _check_spread_alert(self, symbol: str, spread_percentage: float):
        """检查价差告警"""
        threshold = self.alert_thresholds["spread_percentage"]

        if spread_percentage > threshold:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="spread_high",
                severity="high" if spread_percentage > threshold * 2 else "medium",
                message=f"Bid-ask spread {spread_percentage:.2f}% exceeds threshold {threshold}%",
                timestamp=datetime.now(UTC),
                current_value=spread_percentage,
                threshold=threshold,
            )

            await self._add_alert(alert)

    async def _check_volume_alert(self, symbol: str, volume_24h: Decimal):
        """检查成交量告警"""
        threshold = self.alert_thresholds["volume_24h_min"]

        if volume_24h < threshold:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="volume_low",
                severity="medium",
                message=f"24h volume {volume_24h:.0f} below threshold {threshold}",
                timestamp=datetime.now(UTC),
                current_value=volume_24h,
                threshold=threshold,
            )

            await self._add_alert(alert)

    async def _check_depth_alert(self, symbol: str, total_depth: float):
        """检查市场深度告警"""
        threshold = self.alert_thresholds["depth_min"]

        if total_depth < threshold:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="depth_low",
                severity="medium",
                message=f"Market depth {total_depth:.0f} below threshold {threshold}",
                timestamp=datetime.now(UTC),
                current_value=total_depth,
                threshold=threshold,
            )

            await self._add_alert(alert)

    async def _check_price_impact_alert(
        self, symbol: str, impact_1k: float, impact_10k: float
    ):
        """检查价格冲击告警"""
        threshold_1k = self.alert_thresholds["price_impact_1k_max"]
        threshold_10k = self.alert_thresholds["price_impact_10k_max"]

        if impact_1k > threshold_1k:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="price_impact_high",
                severity="medium",
                message=f"1K order price impact {impact_1k:.2f}% exceeds threshold {threshold_1k}%",
                timestamp=datetime.now(UTC),
                current_value=impact_1k,
                threshold=threshold_1k,
            )
            await self._add_alert(alert)

        if impact_10k > threshold_10k:
            alert = LiquidityAlert(
                symbol=symbol,
                alert_type="price_impact_high",
                severity="high",
                message=f"10K order price impact {impact_10k:.2f}% exceeds threshold {threshold_10k}%",
                timestamp=datetime.now(UTC),
                current_value=impact_10k,
                threshold=threshold_10k,
            )
            await self._add_alert(alert)

    async def _add_alert(self, alert: LiquidityAlert):
        """添加告警"""
        # 检查是否已存在相同告警
        existing = any(
            a.symbol == alert.symbol
            and a.alert_type == alert.alert_type
            and (datetime.now(UTC) - a.timestamp).seconds < 300  # 5分钟内
            for a in self.active_alerts
        )

        if not existing:
            self.active_alerts.append(alert)

            # 发布告警事件
            if self.event_bus:
                await self.event_bus.publish(
                    DataUpdateEvent(
                        event_type=EventType.LIQUIDITY_ALERT,
                        data={
                            "symbol": alert.symbol,
                            "alert_type": alert.alert_type,
                            "severity": alert.severity,
                            "message": alert.message,
                            "current_value": alert.current_value,
                            "threshold": alert.threshold,
                        },
                    )
                )

            if self.logger:
                await self.logger.warning(f"Liquidity alert: {alert.message}")

    async def _periodic_analysis(self):
        """定期分析任务"""
        while True:
            try:
                await asyncio.sleep(60)  # 每分钟执行一次

                # 更新所有符号的流动性评分
                for symbol in self.liquidity_metrics:
                    score, grade = await self._calculate_liquidity_score(symbol)

                    metrics = self.liquidity_metrics[symbol]
                    metrics.liquidity_score = score
                    metrics.liquidity_grade = grade

                    # 保存历史数据
                    if symbol not in self.metrics_history:
                        self.metrics_history[symbol] = deque(
                            maxlen=self.max_history_size
                        )

                    self.metrics_history[symbol].append(metrics)

                # 清理过期告警
                current_time = datetime.now(UTC)
                self.active_alerts = [
                    alert
                    for alert in self.active_alerts
                    if (current_time - alert.timestamp).seconds
                    < 3600  # 保留1小时内的告警
                ]

            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Periodic analysis error: {e}")

    # 公共接口方法

    async def get_liquidity_metrics(self, symbol: str) -> LiquidityMetrics | None:
        """获取流动性指标"""
        return self.liquidity_metrics.get(symbol)

    async def get_all_liquidity_metrics(self) -> dict[str, LiquidityMetrics]:
        """获取所有流动性指标"""
        return self.liquidity_metrics.copy()

    async def get_liquidity_alerts(
        self, symbol: str | None = None
    ) -> list[LiquidityAlert]:
        """获取流动性告警"""
        if symbol:
            return [alert for alert in self.active_alerts if alert.symbol == symbol]
        return self.active_alerts.copy()

    async def get_liquidity_history(
        self, symbol: str, hours: int = 24
    ) -> list[LiquidityMetrics]:
        """获取流动性历史数据"""
        if symbol not in self.metrics_history:
            return []

        cutoff_time = datetime.now(UTC) - timedelta(hours=hours)
        history = self.metrics_history[symbol]

        return [metrics for metrics in history if metrics.timestamp >= cutoff_time]

    async def is_symbol_liquid(self, symbol: str) -> bool:
        """检查符号是否具有足够流动性"""
        metrics = await self.get_liquidity_metrics(symbol)

        if not metrics:
            return False

        # 检查关键流动性指标
        spread_ok = (
            metrics.spread_percentage <= self.alert_thresholds["spread_percentage"]
        )
        volume_ok = float(metrics.volume_24h) >= self.alert_thresholds["volume_24h_min"]
        depth_ok = float(metrics.total_depth) >= self.alert_thresholds["depth_min"]

        return spread_ok and volume_ok and depth_ok
