"""
通用数据类型定义
统一所有分析器使用的公共数据结构，减少重复的dataclass定义
"""

from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from enum import Enum
from typing import Any

from src.core.order_types import OrderSide


class OptionType(Enum):
    """期权类型"""

    CALL = "call"
    PUT = "put"


class SignalStrength(Enum):
    """信号强度级别"""

    WEAK = "weak"  # 0.0-0.3
    MODERATE = "moderate"  # 0.3-0.6
    STRONG = "strong"  # 0.6-0.8
    VERY_STRONG = "very_strong"  # 0.8-1.0


class DataQuality(Enum):
    """统一数据质量等级"""

    HIGH = "high"  # <50ms延迟，优质数据
    MEDIUM = "medium"  # 50-200ms延迟，良好数据
    LOW = "low"  # 200-500ms延迟，可用数据
    STALE = "stale"  # >500ms延迟，过期数据


@dataclass
class BaseOptionData:
    """基础期权数据结构 - 统一所有期权相关的公共字段"""

    symbol: str
    strike: Decimal
    expiry: datetime
    option_type: OptionType
    mark_price: Decimal
    bid: Decimal
    ask: Decimal
    volume: int = 0  # 当前成交量
    volume_24h: int = 0  # 24小时成交量
    open_interest: int = 0
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))

    @property
    def bid_ask_spread(self) -> Decimal:
        """买卖价差"""
        return self.ask - self.bid

    @property
    def mid_price(self) -> Decimal:
        """中间价"""
        return (self.bid + self.ask) / 2


@dataclass
class GreeksData:
    """期权希腊字母数据"""

    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float = 0.0
    implied_vol: float = 0.0


@dataclass
class OptionDataWithGreeks(BaseOptionData):
    """包含希腊字母的期权数据"""

    greeks: GreeksData = field(default_factory=lambda: GreeksData(0, 0, 0, 0))

    @property
    def delta(self) -> float:
        return self.greeks.delta

    @property
    def gamma(self) -> float:
        return self.greeks.gamma

    @property
    def theta(self) -> float:
        return self.greeks.theta

    @property
    def vega(self) -> float:
        return self.greeks.vega

    @property
    def implied_vol(self) -> float:
        return self.greeks.implied_vol

    @property
    def spread_ratio(self) -> float:
        """价差比例"""
        if self.mark_price > 0:
            return float(self.bid_ask_spread / self.mark_price)
        return 0.0


@dataclass
class MarketFlowData:
    """市场流向数据 - 统一Taker Flow等数据结构"""

    buy_volume: float
    sell_volume: float
    net_flow: float
    timestamp: datetime

    @property
    def buy_ratio(self) -> float:
        """买入占比"""
        total = self.buy_volume + self.sell_volume
        return self.buy_volume / total if total > 0 else 0.0

    @property
    def sell_ratio(self) -> float:
        """卖出占比"""
        total = self.buy_volume + self.sell_volume
        return self.sell_volume / total if total > 0 else 0.0


@dataclass
class GeneralVolatilityData:
    """通用波动率数据结构"""

    timestamp: datetime
    realized_vol: float
    implied_vol: float | None = None
    vol_surface_data: dict[str, Any] | None = None
    funding_rate: float | None = None


@dataclass
class UnifiedSignal:
    """统一信号数据结构 - 合并了BaseSignal和CausalSignal的功能"""

    signal_type: str  # 信号类型，支持字符串和枚举
    strength: float  # 0-1之间的信号强度
    confidence: float  # 0-1之间的置信度
    timestamp: datetime
    metadata: dict[str, Any] = field(default_factory=dict)

    @property
    def strength_level(self) -> SignalStrength:
        """信号强度级别"""
        if self.strength < 0.3:
            return SignalStrength.WEAK
        elif self.strength < 0.6:
            return SignalStrength.MODERATE
        elif self.strength < 0.8:
            return SignalStrength.STRONG
        else:
            return SignalStrength.VERY_STRONG

    def is_actionable(self, min_confidence: float = 0.6) -> bool:
        """判断信号是否可操作"""
        return self.confidence >= min_confidence

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        # 处理signal_type可能是枚举的情况
        signal_type_value = (
            self.signal_type.value
            if hasattr(self.signal_type, "value")
            else str(self.signal_type)
        )

        return {
            "signal_type": signal_type_value,
            "strength": self.strength,
            "confidence": self.confidence,
            "strength_level": self.strength_level.value,
            "timestamp": self.timestamp.isoformat(),
            "is_actionable": self.is_actionable(),
            "metadata": self.metadata,
        }


@dataclass
class PriceLevel:
    """价格水平数据"""

    price: Decimal
    volume: float
    open_interest: float
    gamma_exposure: float = 0.0
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


@dataclass
class UnifiedMarketData:
    """统一的市场数据结构 - 替代各种重复的MarketData定义"""

    # 基础标识
    symbol: str
    exchange: str
    timestamp: datetime

    # 价格数据
    price: Decimal  # 当前价格或标记价格
    bid: Decimal = Decimal("0")
    ask: Decimal = Decimal("0")

    # 成交量数据
    volume: int = 0  # 当前成交量
    volume_24h: int = 0  # 24小时成交量

    # 期权特定字段（可选）
    strike: Decimal | None = None
    expiry: datetime | None = None
    option_type: OptionType | None = None
    open_interest: int = 0

    # 希腊字母（可选）
    greeks: GreeksData | None = None

    # 其他元数据
    metadata: dict[str, Any] = field(default_factory=dict)

    @property
    def bid_ask_spread(self) -> Decimal:
        """买卖价差"""
        return self.ask - self.bid if self.ask > 0 and self.bid > 0 else Decimal("0")

    @property
    def mid_price(self) -> Decimal:
        """中间价"""
        return (
            (self.bid + self.ask) / 2 if self.ask > 0 and self.bid > 0 else self.price
        )

    @property
    def is_option(self) -> bool:
        """是否为期权数据"""
        return (
            self.strike is not None
            and self.expiry is not None
            and self.option_type is not None
        )

    def to_base_option_data(self) -> "BaseOptionData":
        """转换为BaseOptionData格式（期权专用）"""
        if not self.is_option:
            raise ValueError("Cannot convert non-option data to BaseOptionData")

        return BaseOptionData(
            symbol=self.symbol,
            strike=self.strike,
            expiry=self.expiry,
            option_type=self.option_type,
            mark_price=self.price,
            bid=self.bid,
            ask=self.ask,
            volume=self.volume,
            volume_24h=self.volume_24h,
            open_interest=self.open_interest,
            timestamp=self.timestamp,
        )


@dataclass
class OrderData:
    """订单数据统一结构"""

    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: Decimal
    price: Decimal
    order_type: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    order_id: str | None = None
    status: str = "pending"

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            "symbol": self.symbol,
            "side": self.side,
            "quantity": float(self.quantity),
            "price": float(self.price),
            "order_type": self.order_type,
            "timestamp": self.timestamp.isoformat(),
            "order_id": self.order_id,
            "status": self.status,
        }


# =====================================
# 策略模式统一数据结构
# =====================================


class StrategyMode(Enum):
    """策略模式类型"""

    ACCUMULATION = "accumulation"  # 抄底模式
    DISTRIBUTION = "distribution"  # 卖出模式
    SIDEWAYS = "sideways"  # 震荡模式


class StrategyType(Enum):
    """策略类型"""

    SHORT_STRANGLE = "short_strangle"
    IRON_CONDOR = "iron_condor"
    COVERED_CALL = "covered_call"
    CASH_SECURED_PUT = "cash_secured_put"


class ExerciseHandling(Enum):
    """行权处理方式"""

    AUTO_CONVERT = "auto_convert"  # 自动转换为现货
    AUTO_RELEASE = "auto_release"  # 自动释放
    MANUAL = "manual"  # 手动处理


class AlertLevel(Enum):
    """统一告警级别"""

    CRITICAL = "critical"  # 严重告警
    ERROR = "error"  # 错误告警
    WARNING = "warning"  # 警告告警
    INFO = "info"  # 信息告警
    STATUS = "status"  # 状态更新


@dataclass
class StrategyOrderBase:
    """策略订单基类"""

    order_id: str
    strategy_mode: StrategyMode
    size: Decimal
    target_premium: Decimal
    batch_number: int
    created_at: datetime
    status: str = "pending"
    filled_premium: Decimal | None = None
    risk_metrics: dict[str, float] = field(default_factory=dict)
    metadata: dict[str, Any] = field(default_factory=dict)


@dataclass
class AccumulationOrder(StrategyOrderBase):
    """抄底模式订单 - 基于StrategyOrderBase"""

    put_option: OptionDataWithGreeks | None = field(default=None)
    max_delta: float = 0.2
    reason: str = ""
    expected_pnl: Decimal = Decimal("0")

    def __post_init__(self):
        if not hasattr(self, "strategy_mode"):
            self.strategy_mode = StrategyMode.ACCUMULATION


@dataclass
class DistributionOrder(StrategyOrderBase):
    """卖出模式订单 - 基于StrategyOrderBase"""

    call_option: OptionDataWithGreeks | None = field(default=None)
    covered_position: Decimal = Decimal("0")

    def __post_init__(self):
        if not hasattr(self, "strategy_mode"):
            self.strategy_mode = StrategyMode.DISTRIBUTION


@dataclass
class SidewaysOrder(StrategyOrderBase):
    """震荡模式订单 - 基于StrategyOrderBase"""

    strategy_type: StrategyType = StrategyType.SHORT_STRANGLE
    legs: list = field(default_factory=list)  # 期权腿列表
    net_premium: Decimal = Decimal("0")
    max_risk: Decimal = Decimal("0")
    max_profit: Decimal = Decimal("0")
    break_even_lower: Decimal = Decimal("0")
    break_even_upper: Decimal = Decimal("0")
    delta_neutral: bool = False

    def __post_init__(self):
        if not hasattr(self, "strategy_mode"):
            self.strategy_mode = StrategyMode.SIDEWAYS

    @property
    def profit_risk_ratio(self) -> float:
        """收益风险比"""
        if self.max_risk > 0:
            return float(self.max_profit / self.max_risk)
        return 0.0

    @property
    def days_to_expiry(self) -> int:
        """距离到期天数"""
        if self.legs:
            # 从期权腿中获取最早到期日期
            expiry_dates = []
            for leg in self.legs:
                if (
                    hasattr(leg, "option_data")
                    and leg.option_data
                    and hasattr(leg.option_data, "expiry")
                ):
                    expiry_dates.append(leg.option_data.expiry)

            if expiry_dates:
                earliest_expiry = min(expiry_dates)
                return (earliest_expiry.date() - datetime.now(UTC).date()).days
        return 0


@dataclass
class OptionLeg:
    """期权腿数据 - 用于多腿策略"""

    option_data: OptionDataWithGreeks
    side: OrderSide  # 'buy' or 'sell'
    quantity: int = 1

    @property
    def symbol(self) -> str:
        return self.option_data.symbol

    @property
    def option_type(self) -> OptionType:
        return self.option_data.option_type

    @property
    def strike(self) -> Decimal:
        return self.option_data.strike

    @property
    def premium(self) -> Decimal:
        return self.option_data.mark_price

    @property
    def delta(self) -> float:
        return self.option_data.delta

    @property
    def gamma(self) -> float:
        return self.option_data.gamma

    @property
    def theta(self) -> float:
        return self.option_data.theta

    @property
    def vega(self) -> float:
        return self.option_data.vega

    @property
    def bid_ask_spread(self) -> Decimal:
        return self.option_data.bid_ask_spread

    @property
    def spread_ratio(self) -> float:
        """价差比例"""
        if self.option_data.mark_price > 0:
            return float(self.bid_ask_spread / self.option_data.mark_price)
        return 0.0


@dataclass
class StrategyMetricsBase:
    """策略指标基类"""

    # BaseComponent.ComponentMetrics 字段
    start_time: datetime | None = None
    last_health_check: datetime | None = None
    uptime_seconds: float = 0.0
    error_count: int = 0
    warning_count: int = 0
    restart_count: int = 0
    custom_metrics: dict[str, Any] = field(default_factory=dict)

    # 策略特定指标
    total_orders: int = 0
    total_premium_collected: Decimal = Decimal("0")
    successful_orders: int = 0
    failed_orders: int = 0
    avg_premium_rate: float = 0.0
    success_rate: float = 0.0

    @property
    def success_rate_calculated(self) -> float:
        """计算成功率"""
        if self.total_orders == 0:
            return 0.0
        return self.successful_orders / self.total_orders


@dataclass
class AccumulationMetrics(StrategyMetricsBase):
    """抄底模式指标"""

    total_puts_sold: int = 0
    exercised_puts: int = 0
    expired_worthless: int = 0
    avg_delta: float = 0.0
    total_spot_acquired: Decimal = Decimal("0")
    avg_acquisition_price: Decimal = Decimal("0")
    current_exposure: Decimal = Decimal("0")


@dataclass
class DistributionMetrics(StrategyMetricsBase):
    """卖出模式指标"""

    total_calls_sold: int = 0
    exercised_calls: int = 0
    expired_worthless: int = 0
    avg_delta: float = 0.0
    total_spot_sold: Decimal = Decimal("0")
    avg_sale_price: Decimal = Decimal("0")
    current_coverage: float = 0.0


@dataclass
class SidewaysMetrics(StrategyMetricsBase):
    """震荡模式指标"""

    strangles_created: int = 0
    condors_created: int = 0
    profitable_closes: int = 0
    early_closes: int = 0
    avg_profit_loss: Decimal = Decimal("0")
    avg_days_held: float = 0.0
    max_loss: Decimal = Decimal("0")
    win_rate: float = 0.0
    total_gamma_exposure: float = 0.0
    max_gamma_exposure: float = 0.0
    total_strategies_created: int = 0
    current_delta_exposure: float = 0.0
