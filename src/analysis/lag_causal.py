from __future__ import annotations

from collections import deque
from collections.abc import Iterable
from dataclasses import dataclass
from datetime import datetime
from typing import Any

import numpy as np
from scipy import stats


def _flatten_features(d: dict[str, Any], prefix: str = "") -> dict[str, float]:
    out: dict[str, float] = {}
    for k, v in d.items():
        key = f"{prefix}.{k}" if prefix else str(k)
        if isinstance(v, dict):
            out.update(_flatten_features(v, key))
        else:
            try:
                out[key] = float(v)
            except Exception:
                continue
    return out


@dataclass
class LagCausalConfig:
    lags: int = 3
    ridge_lambda: float = 1e-3
    batch_size: int = 32
    max_buffer: int = 2000
    train_interval_sec: float = 3.0
    target: str = "spot.return"  # computed from spot.price
    features: list[str] | None = None


class LagCausalTrainer:
    """Rolling linear VAR-like trainer with lagged exogenous features.

    Model: y_t = sum_{i=1..p} a_i y_{t-i} + sum_{i=1..p} b_i^T x_{t-i} + e_t
    Fitted via ridge-regularized OLS over a rolling window.
    """

    def __init__(self, cfg: LagCausalConfig, feature_names: Iterable[str]):
        self.cfg = cfg
        self.feature_names = list(feature_names)
        self.p = int(max(1, cfg.lags))
        self.window = int(max(100, cfg.max_buffer))

        self.ts_buf: deque[datetime] = deque(maxlen=self.window)
        self.y_buf: deque[float] = deque(maxlen=self.window)
        self.x_buf: deque[np.ndarray] = deque(maxlen=self.window)

        self.beta: np.ndarray | None = None
        self.r2_full: float | None = None
        self.r2_restricted: float | None = None
        self.f_stat: float | None = None
        self.p_value: float | None = None
        self.lag_corrs: dict[str, dict[int, float]] = {}

    @property
    def n_features(self) -> int:
        return len(self.feature_names)

    def add_sample(self, ts: datetime, y: float, x: dict[str, float]) -> None:
        vec = np.array(
            [float(x.get(name, 0.0)) for name in self.feature_names], dtype=float
        )
        self.ts_buf.append(ts)
        self.y_buf.append(float(y))
        self.x_buf.append(vec)

    def _build_design(self) -> tuple[np.ndarray, np.ndarray] | tuple[None, None]:
        y = np.asarray(self.y_buf, dtype=float)
        if y.size <= self.p:
            return None, None
        X_exo = np.vstack(self.x_buf) if len(self.x_buf) > 0 else None
        T = y.size
        rows = T - self.p
        # Design: [y_{t-1..p}, x_{t-1..p}]
        X_parts = []
        # Endogenous lags
        for lag in range(1, self.p + 1):
            X_parts.append(y[self.p - lag : T - lag].reshape(rows, 1))
        # Exogenous feature lags
        if X_exo is None:
            return None, None
        for lag in range(1, self.p + 1):
            X_parts.append(X_exo[self.p - lag : T - lag, :])
        X = np.hstack(X_parts)
        y_t = y[self.p :]
        return X, y_t

    def fit(self) -> bool:
        Xy = self._build_design()
        if Xy == (None, None):
            return False
        X, y = Xy  # type: ignore
        n, k = X.shape
        lam = float(max(0.0, self.cfg.ridge_lambda))
        XtX = X.T @ X + lam * np.eye(k)
        Xty = X.T @ y
        try:
            beta = np.linalg.solve(XtX, Xty)
        except np.linalg.LinAlgError:
            beta = np.linalg.lstsq(XtX, Xty, rcond=None)[0]
        y_hat = X @ beta
        ssr = float(np.sum((y - y_hat) ** 2))
        sst = float(np.sum((y - np.mean(y)) ** 2))
        r2_full = 1.0 - ssr / sst if sst > 0 else 0.0

        # Restricted model: only y lags
        Xr = X[:, : self.p]
        try:
            br = np.linalg.lstsq(Xr, y, rcond=None)[0]
        except np.linalg.LinAlgError:
            br = np.zeros(self.p)
        y_r = Xr @ br
        ssr_r = float(np.sum((y - y_r) ** 2))
        r2_restricted = 1.0 - ssr_r / sst if sst > 0 else 0.0

        # Granger-style F-test for added exogenous lags
        k_added = k - self.p
        df1 = k_added
        df2 = max(1, n - k)
        num = (ssr_r - ssr) / max(1e-12, df1)
        den = ssr / max(1e-12, df2)
        f_stat = num / max(1e-12, den)
        try:
            p_val = 1.0 - stats.f.cdf(f_stat, df1, df2)
        except Exception:
            p_val = float("nan")

        # Lagged cross-correlations y vs each feature
        lag_corrs: dict[str, dict[int, float]] = {}
        X_exo = np.vstack(self.x_buf)
        y_full = np.asarray(self.y_buf, dtype=float)
        for idx, name in enumerate(self.feature_names):
            lag_corrs[name] = {}
            series = X_exo[:, idx]
            for lag in range(1, self.p + 1):
                y_aligned = y_full[self.p :]
                x_aligned = series[self.p - lag : len(series) - lag]
                if y_aligned.size == x_aligned.size and y_aligned.size > 2:
                    c = np.corrcoef(y_aligned, x_aligned)[0, 1]
                    if np.isfinite(c):
                        lag_corrs[name][lag] = float(c)

        self.beta = beta
        self.r2_full = r2_full
        self.r2_restricted = r2_restricted
        self.f_stat = f_stat
        self.p_value = p_val
        self.lag_corrs = lag_corrs
        return True

    def predict_next(self) -> float | None:
        if self.beta is None:
            return None
        if len(self.y_buf) <= self.p:
            return None
        y = np.asarray(self.y_buf, dtype=float)
        X_exo = np.vstack(self.x_buf)
        parts = []
        for lag in range(1, self.p + 1):
            parts.append([y[-lag]])
        for lag in range(1, self.p + 1):
            parts.append(list(X_exo[-lag]))
        x_vec = np.array([v for sub in parts for v in sub], dtype=float)
        return float(x_vec @ self.beta)

    def latest_metrics(self) -> dict[str, Any]:
        return {
            "lags": self.p,
            "n_features": self.n_features,
            "beta": self.beta.tolist() if self.beta is not None else None,
            "r2_full": self.r2_full,
            "r2_restricted": self.r2_restricted,
            "f_stat": self.f_stat,
            "p_value": self.p_value,
            "lag_corrs": self.lag_corrs,
        }


__all__ = [
    "LagCausalConfig",
    "LagCausalTrainer",
    "_flatten_features",
]
