"""
结构分歧分析器 - 重构版本

分析Binance Taker Flow与Deribit OI的相关性，识别主流资金与期权市场的方向差异。
实现资金流向分歧度量和评分算法，提供分歧信号的置信度计算。

核心功能：
1. 主流资金vs期权防御分析
2. Taker Buy/Sell Ratio与Call/Put OI变化率对比
3. 结构分歧信号生成和评分
4. 历史验证和成功率统计
"""

import asyncio
from collections import deque
from dataclasses import dataclass
from datetime import UTC, datetime
from typing import Any

import numpy as np

from src.core.config_manager import ConfigManager
from src.core.event_bus import EventType

from .base_analyzer import BaseAnalyzer
from .common_types import MarketFlowData, UnifiedSignal


@dataclass
class OptionOIData:
    """期权持仓量数据"""

    call_oi: float
    put_oi: float
    call_oi_change: float  # 变化率
    put_oi_change: float  # 变化率
    call_put_ratio: float  # call_oi / put_oi
    net_oi_change: float  # call_oi_change - put_oi_change
    timestamp: datetime


@dataclass
class StructureDivergenceSignal(UnifiedSignal):
    """结构分歧信号 - 继承UnifiedSignal"""

    correlation: float = 0.0  # Taker Flow与OI变化的相关性
    divergence_score: float = 0.0  # 分歧评分 (0-1)
    direction_conflict: bool = False  # 方向冲突标志
    strength_ratio: float = 0.0  # 强度比率

    def __post_init__(self):
        # 设置基类字段
        if not hasattr(self, "signal_type"):
            self.signal_type = "structure_divergence"
        if not hasattr(self, "strength"):
            self.strength = self.divergence_score
        if not hasattr(self, "confidence"):
            self.confidence = min(abs(self.correlation), 1.0)


class StructureDivergenceAnalyzer(BaseAnalyzer):
    """
    结构分歧分析器

    分析Binance现货/永续市场的主流资金流向与Deribit期权市场的防御性行为差异，
    识别市场结构性机会并生成交易信号。
    """

    def __init__(self, config_manager: ConfigManager):
        super().__init__("StructureDivergenceAnalyzer", config_manager)

        # 数据缓冲区
        self.taker_flow_buffer: deque = deque(maxlen=100)  # 最近100个数据点
        self.option_oi_buffer: deque = deque(maxlen=100)
        self.divergence_history: deque = deque(maxlen=500)  # 分歧历史

        # 注意：默认配置现在通过BaseAnalyzer._get_analyzer_defaults()管理

        # 最后信号时间
        self.last_signal_time: datetime | None = None

    def _get_analyzer_defaults(self) -> dict[str, Any]:
        """结构分歧分析器的特定默认配置"""
        return {
            "lookback_window": 20,  # 回看窗口大小
            "correlation_threshold": -0.3,  # 负相关阈值
            "divergence_threshold": 0.6,  # 分歧信号阈值
            "min_volume_threshold": 1000000,  # 最小成交量阈值（USD）
            "min_oi_change_threshold": 0.05,  # 最小OI变化阈值（5%）
            "confidence_decay_factor": 0.95,  # 置信度衰减因子
        }

    async def _initialize_analyzer(self) -> bool:
        """分析器特定的初始化逻辑"""
        try:
            # 验证配置参数
            print(f"🔍 StructureDivergenceAnalyzer: 当前配置: {self.config}")
            required_params = [
                "lookback_window",
                "correlation_threshold",
                "divergence_threshold",
            ]
            for param in required_params:
                if param not in self.config:
                    if self.logger:
                        await self.logger.error(f"缺少必需的配置参数: {param}")
                    else:
                        print(
                            f"❌ StructureDivergenceAnalyzer: 缺少必需的配置参数: {param}"
                        )
                    return False
                else:
                    print(
                        f"✅ StructureDivergenceAnalyzer: 配置参数 {param} = {self.config[param]}"
                    )

            # 初始化数据订阅
            print("🔧 StructureDivergenceAnalyzer: 开始设置数据订阅...")
            try:
                await self._setup_data_subscriptions()
                print("✅ StructureDivergenceAnalyzer: 数据订阅设置完成")
            except Exception as e:
                print(f"❌ StructureDivergenceAnalyzer: 数据订阅设置失败: {e}")
                import traceback

                print(f"数据订阅异常堆栈: {traceback.format_exc()}")
                raise

            if self.logger:
                await self.logger.info("结构分歧分析器初始化完成")
            else:
                print("✅ StructureDivergenceAnalyzer: 结构分歧分析器初始化完成")

            print("🔍 StructureDivergenceAnalyzer: _initialize_analyzer 即将返回 True")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"结构分歧分析器初始化失败: {e}")
            else:
                print(f"❌ StructureDivergenceAnalyzer: 结构分歧分析器初始化失败: {e}")
            return False

    async def _run_analysis_loop(self):
        """主要的分析循环"""
        while self._is_analyzing:
            try:
                await self._analyze_structure_divergence()
                await asyncio.sleep(self.config.get("analysis_interval", 60))
            except Exception as e:
                await self.logger.error(f"结构分歧分析循环异常: {e}")
                await asyncio.sleep(5)

    async def _setup_data_subscriptions(self):
        """设置数据订阅"""
        if self.event_bus:
            # 订阅数据更新事件以解析Taker Flow和期权OI数据
            await self.event_bus.subscribe(
                event_types={EventType.DATA_UPDATE},
                callback=self._on_data_update_event,
                subscriber_id="StructureAnalyzer",
            )

    async def _on_data_update_event(self, event):
        """处理数据更新事件，提取Taker Flow和期权OI数据"""
        try:
            if not event.data or not event.data_type or not event.exchange:
                return

            # 确保数据是字典格式，如果是字符串则尝试解析
            data = event.data
            if isinstance(data, str):
                try:
                    import json

                    data = json.loads(data)
                except (json.JSONDecodeError, ValueError):
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to parse data as JSON: {data[:100]}..."
                        )
                    return

            # 处理Binance交易数据以计算Taker Flow
            if event.exchange == "binance" and event.data_type == "trade":
                await self._process_taker_flow_data(data)

            # 处理Deribit期权数据以获取OI信息
            elif event.exchange == "deribit" and event.data_type in [
                "ticker",
                "option",
            ]:
                await self._process_option_oi_data(data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error processing data update event: {e}")

    async def _process_taker_flow_data(self, trade_data: dict[str, Any]):
        """处理交易数据计算Taker Flow"""
        try:
            # 类型检查和转换：确保trade_data是字典类型
            if not isinstance(trade_data, dict):
                if isinstance(trade_data, str):
                    # 如果是标识符字符串（如 "market_data"），直接跳过
                    if trade_data in [
                        "market_data",
                        "option_chain",
                        "greeks",
                        "trade_data",
                        "option_data",
                    ]:
                        if self.logger:
                            await self.logger.debug(
                                f"Skipping identifier string: {trade_data}"
                            )
                        return
                    try:
                        import json

                        trade_data = json.loads(trade_data)
                    except (json.JSONDecodeError, TypeError):
                        if self.logger:
                            await self.logger.warning(
                                f"Failed to parse trade_data string: {trade_data}"
                            )
                        return
                else:
                    if self.logger:
                        await self.logger.warning(
                            f"Invalid trade_data type: {type(trade_data)}, expected dict"
                        )
                    return

            is_buyer_maker = trade_data.get("is_buyer_maker", True)
            from src.utils.data_converter import safe_float_conversion

            quantity = safe_float_conversion(trade_data.get("quantity"))

            if not is_buyer_maker:  # Taker买入
                buy_volume = quantity
                sell_volume = 0
            else:  # Taker卖出
                buy_volume = 0
                sell_volume = quantity

            flow_data = MarketFlowData(
                buy_volume=buy_volume,
                sell_volume=sell_volume,
                net_flow=buy_volume - sell_volume,
                timestamp=datetime.fromisoformat(
                    trade_data.get("timestamp", datetime.now().isoformat())
                ),
            )
            self.taker_flow_buffer.append(flow_data)
        except Exception as e:
            await self.logger.warning(f"处理Taker Flow数据失败: {e}")

    async def _process_option_oi_data(self, option_data: dict[str, Any]):
        """处理期权数据获取OI信息"""
        try:
            # 类型检查和转换：确保option_data是字典类型
            if not isinstance(option_data, dict):
                if isinstance(option_data, str):
                    try:
                        import json

                        option_data = json.loads(option_data)
                    except (json.JSONDecodeError, TypeError):
                        await self.logger.warning(
                            f"Failed to parse option_data string: {option_data}"
                        )
                        return
                else:
                    await self.logger.warning(
                        f"Invalid option_data type: {type(option_data)}, expected dict"
                    )
                    return

            # 从期权数据中提取开放权益信息
            open_interest = option_data.get("open_interest", 0)
            if open_interest > 0:
                await self._on_option_oi_update(option_data)
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error processing option OI data: {e}")

    async def _on_option_oi_update(self, data: dict[str, Any]):
        """处理期权OI数据更新"""
        try:
            # 类型检查：确保data是字典类型
            if not isinstance(data, dict):
                await self.logger.warning(
                    f"Invalid data type in _on_option_oi_update: {type(data)}, expected dict"
                )
                return

            oi_data = OptionOIData(
                call_oi=data.get("call_oi", 0),
                put_oi=data.get("put_oi", 0),
                call_oi_change=data.get("call_oi_change", 0),
                put_oi_change=data.get("put_oi_change", 0),
                call_put_ratio=data.get("call_put_ratio", 1.0),
                net_oi_change=data.get("net_oi_change", 0),
                timestamp=datetime.fromisoformat(
                    data.get("timestamp", datetime.now().isoformat())
                ),
            )
            self.option_oi_buffer.append(oi_data)
        except Exception as e:
            await self.logger.warning(f"处理期权OI数据失败: {e}")

    async def _analyze_structure_divergence(self):
        """分析结构分歧"""
        if (
            len(self.taker_flow_buffer) < self.config["lookback_window"]
            or len(self.option_oi_buffer) < self.config["lookback_window"]
        ):
            return

        try:
            # 计算相关性
            correlation = await self._calculate_correlation()

            # 检查是否满足信号条件
            if abs(correlation) > abs(self.config["correlation_threshold"]):
                signal = await self._generate_divergence_signal(correlation)
                if signal and await self._should_emit_signal():
                    await self._emit_signal(signal)

        except Exception as e:
            await self.logger.error(f"结构分歧分析失败: {e}")

    async def _calculate_correlation(self) -> float:
        """计算Taker Flow与OI变化的相关性"""
        window = self.config["lookback_window"]

        # 获取最近的数据
        recent_flows = list(self.taker_flow_buffer)[-window:]
        recent_ois = list(self.option_oi_buffer)[-window:]

        if len(recent_flows) != len(recent_ois):
            return 0.0

        # 提取净流向和净OI变化
        net_flows = [flow.net_flow for flow in recent_flows]
        net_oi_changes = [oi.net_oi_change for oi in recent_ois]

        # 计算相关系数
        if len(net_flows) > 1 and len(net_oi_changes) > 1:
            correlation_matrix = np.corrcoef(net_flows, net_oi_changes)
            return (
                float(correlation_matrix[0, 1])
                if not np.isnan(correlation_matrix[0, 1])
                else 0.0
            )

        return 0.0

    async def _generate_divergence_signal(
        self, correlation: float
    ) -> StructureDivergenceSignal | None:
        """生成分歧信号"""
        try:
            divergence_score = abs(correlation)
            direction_conflict = correlation < 0

            # 计算强度比率
            recent_flow = self.taker_flow_buffer[-1] if self.taker_flow_buffer else None
            recent_oi = self.option_oi_buffer[-1] if self.option_oi_buffer else None

            if not recent_flow or not recent_oi:
                return None

            strength_ratio = abs(recent_flow.net_flow) / (
                abs(recent_oi.net_oi_change) + 1e-6
            )

            signal = StructureDivergenceSignal(
                signal_type="structure_divergence",
                strength=divergence_score,
                confidence=min(abs(correlation), 1.0),
                timestamp=datetime.now(UTC),
                correlation=correlation,
                divergence_score=divergence_score,
                direction_conflict=direction_conflict,
                strength_ratio=strength_ratio,
                metadata={
                    "recent_flow_net": recent_flow.net_flow,
                    "recent_oi_change": recent_oi.net_oi_change,
                    "lookback_window": self.config["lookback_window"],
                },
            )

            return signal

        except Exception as e:
            await self.logger.error(f"生成分歧信号失败: {e}")
            return None

    async def _should_emit_signal(self) -> bool:
        """检查是否应该发出信号（冷却时间等）"""
        if self.last_signal_time is None:
            return True

        cooldown_minutes = self.config.get("signal_cooldown_minutes", 30)
        time_since_last = datetime.now(UTC) - self.last_signal_time

        return time_since_last.total_seconds() > cooldown_minutes * 60

    async def _emit_signal(self, signal: StructureDivergenceSignal):
        """发出信号"""
        try:
            # 更新统计指标
            self.metrics.total_signals += 1
            self.metrics.last_signal_time = signal.timestamp
            self.last_signal_time = signal.timestamp

            # 添加到历史记录
            self.divergence_history.append(signal)

            # 通过事件总线发送信号
            if self.event_bus:
                await self.event_bus.publish(
                    "structure_divergence_signal", signal.to_dict()
                )

            await self.logger.info(
                f"发出结构分歧信号: 强度={signal.strength:.3f}, 置信度={signal.confidence:.3f}"
            )

        except Exception as e:
            await self.logger.error(f"发出信号失败: {e}")

    async def _check_analyzer_health(self) -> bool:
        """分析器特定的健康检查"""
        # 检查数据缓冲区是否有数据
        if not self.taker_flow_buffer or not self.option_oi_buffer:
            return False

        # 检查最近是否有数据更新（5分钟内）
        now = datetime.now(UTC)
        if self.taker_flow_buffer:
            last_flow_time = self.taker_flow_buffer[-1].timestamp
            if (now - last_flow_time).total_seconds() > 300:  # 5分钟
                return False

        return True

    def get_recent_signals(self, limit: int = 10) -> list[dict[str, Any]]:
        """获取最近的信号"""
        recent = list(self.divergence_history)[-limit:]
        return [signal.to_dict() for signal in recent]

    def get_divergence_stats(self) -> dict[str, Any]:
        """获取分歧统计信息"""
        stats = self.get_metrics()
        stats.update(
            {
                "buffer_sizes": {
                    "taker_flow": len(self.taker_flow_buffer),
                    "option_oi": len(self.option_oi_buffer),
                    "divergence_history": len(self.divergence_history),
                },
                "config": self.config,
            }
        )
        return stats

    # === 公共接口方法（供CausalEngine调用） ===

    async def analyze_divergence(
        self, market_data: dict[str, Any]
    ) -> dict[str, Any] | None:
        """
        分析结构分歧信号（供CausalEngine调用的标准接口）
        Args:
            market_data: 市场数据，包含Binance和Deribit数据
        Returns:
            分析结果字典，包含strength、confidence和metadata，格式与其他分析器一致
        """
        try:
            # 从market_data提取需要的数据并更新缓冲区
            await self._extract_and_update_from_market_data(market_data)

            # 检查是否有足够的数据进行分析
            if (
                len(self.taker_flow_buffer) < self.config["lookback_window"]
                or len(self.option_oi_buffer) < self.config["lookback_window"]
            ):
                return None

            # 计算相关性
            correlation = await self._calculate_correlation()

            # 检查是否满足信号条件
            if abs(correlation) > abs(self.config["correlation_threshold"]):
                signal = await self._generate_divergence_signal(correlation)
                if signal:
                    # 转换为标准格式返回给CausalEngine
                    return {
                        "strength": signal.strength,
                        "confidence": signal.confidence,
                        "signal_type": signal.signal_type,
                        "timestamp": signal.timestamp.isoformat(),
                        "metadata": {
                            "correlation": signal.correlation,
                            "divergence_score": signal.divergence_score,
                            "direction_conflict": signal.direction_conflict,
                            "strength_ratio": signal.strength_ratio,
                            **signal.metadata,
                        },
                    }

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in analyze_divergence: {e}")
            return None

    async def _extract_and_update_from_market_data(
        self, market_data: dict[str, Any]
    ) -> None:
        """
        从market_data中提取Taker Flow和期权OI数据，更新缓冲区
        """
        try:
            # 提取Binance Taker Flow数据
            binance_data = market_data.get("binance", {})
            if binance_data:
                await self._process_taker_flow_data(binance_data)

            # 提取Deribit期权OI数据
            deribit_data = market_data.get("deribit", {})
            if deribit_data:
                await self._process_option_oi_data(deribit_data)

            # 如果market_data包含通用的期权数据，也处理
            option_data = market_data.get("options", {})
            if option_data:
                await self._process_option_oi_data(option_data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error extracting data from market_data: {e}")
            raise
