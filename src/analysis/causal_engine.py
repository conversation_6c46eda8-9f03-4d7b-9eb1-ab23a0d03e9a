"""
跨交易所因果分析引擎

实现三大核心信号分析：
1. 结构分歧信号 - 主流资金vs期权防御分析
2. 波动率错配信号 - 融资成本vs隐含波动率套利
3. Gamma清算重叠信号 - 清算压力vs Gamma集中区域重叠

提供实时信号计算、动态阈值管理和机器学习优化
"""

import asyncio
import contextlib
from collections.abc import Callable
from dataclasses import dataclass
from datetime import UTC, datetime
from typing import Any

import numpy as np

from src.analysis.common_types import UnifiedSignal
from src.analysis.gamma_analyzer import GammaLiquidationAnalyzer
from src.analysis.lag_causal import (
    LagCausalConfig,
    LagCausalTrainer,
    _flatten_features,
)
from src.analysis.structure_analyzer import StructureDivergenceAnalyzer
from src.analysis.types import SignalType
from src.analysis.volatility_analyzer import VolatilityMismatchAnalyzer
from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.celery_manager import CeleryTaskManager
from src.core.config_manager import ConfigManager
from src.core.event_bus import EventBus, EventType, SignalEvent
from src.data.cache_manager import CacheManager
from src.data.timescale_manager import TimescaleDBManager


@dataclass
class ThresholdConfig:
    """动态阈值配置"""

    signal_threshold: float = 0.65  # 信号触发阈值
    confidence_threshold: float = 0.7  # 置信度阈值
    lookback_period: int = 14  # 历史数据回看天数
    adaptation_rate: float = 0.1  # 阈值自适应速率
    min_threshold: float = 0.5  # 最小阈值
    max_threshold: float = 0.9  # 最大阈值


@dataclass
class SignalMetrics:
    """信号统计指标"""

    total_signals: int = 0
    successful_signals: int = 0
    false_positives: int = 0
    avg_strength: float = 0.0
    avg_confidence: float = 0.0
    last_signal_time: datetime | None = None

    @property
    def success_rate(self) -> float:
        """成功率"""
        return (
            self.successful_signals / self.total_signals
            if self.total_signals > 0
            else 0.0
        )


class CausalEngine(BaseComponent):
    """
    跨交易所因果分析引擎

    核心功能：
    1. 三大信号分析器的框架和调度机制
    2. 信号强度计算算法（0-1标准化评分）
    3. 动态阈值管理（基于历史数据自适应调整）
    4. 历史数据回测和信号验证框架
    5. 机器学习模型优化信号识别
    6. 信号置信度计算和元数据记录
    """

    # 类级别的类型注解（用于依赖注入）
    event_bus: EventBus
    cache_manager: CacheManager
    db_manager: TimescaleDBManager

    def __init__(
        self,
        config_manager: ConfigManager,
        event_bus: EventBus | None = None,
        cache_manager: CacheManager | None = None,
        celery_manager: CeleryTaskManager | None = None,
    ):
        super().__init__("CausalEngine")
        self.config_manager = config_manager

        # 核心组件依赖 - 使用构造函数参数或等待属性注入
        self.event_bus: EventBus | None = event_bus
        self.cache_manager: CacheManager | None = cache_manager
        self.db_manager: TimescaleDBManager | None = None
        self.celery_manager: CeleryTaskManager | None = celery_manager
        # Debug信息改为低噪声：在有logger时由外部生命周期阶段输出

        # 信号分析器实例 - 延迟初始化以避免循环依赖
        self._config_manager = self.config_manager
        self.structure_analyzer = None
        self.volatility_analyzer = None
        self.gamma_analyzer = None

        # 配置和阈值管理
        self.threshold_config = ThresholdConfig()
        self.signal_metrics: dict[SignalType, SignalMetrics] = {
            signal_type: SignalMetrics() for signal_type in SignalType
        }

        # 信号历史和缓存
        self.signal_history: dict[SignalType, list[UnifiedSignal]] = {
            signal_type: [] for signal_type in SignalType
        }
        self.max_history_size = 1000

        # 机器学习模型（占位符，后续实现）
        self.ml_model = None
        self.feature_extractors: list[Callable] = []

        # 滞后因果（VAR风格）训练
        self._lag_cfg: LagCausalConfig | None = None
        self._lag_trainer: LagCausalTrainer | None = None
        self._feature_list: list[str] = []
        self._feat_queue: asyncio.Queue | None = None
        self._training_task: asyncio.Task | None = None
        self._last_spot_price: float | None = None
        self._last_spot_ts: datetime | None = None
        self._causal_metrics: dict[str, Any] = {}

        # 运行状态
        self._analysis_task: asyncio.Task | None = None
        self._threshold_update_task: asyncio.Task | None = None
        self._audit_task: asyncio.Task | None = None
        self.analysis_interval = 10  # 10秒分析间隔
        self._audit_interval = 600
        # 影子采样（低频）
        self._shadow_sampling_task: asyncio.Task | None = None
        self._shadow_sample_interval_sec: int = 300
        self._shadow_sampling_enabled: bool = True

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            if self.logger:
                await self.logger.debug("CausalEngine._initialize_impl() 开始执行")

            # 检查依赖
            if self.logger:
                await self.logger.debug(
                    f"cfg={self.config_manager is not None}, eb={self.event_bus is not None}, cache={self.cache_manager is not None}, db={self.db_manager is not None}"
                )

            # 初始化分析器实例
            if self.logger:
                await self.logger.debug("创建分析器实例…")
            self.structure_analyzer = StructureDivergenceAnalyzer(self.config_manager)
            self.volatility_analyzer = VolatilityMismatchAnalyzer(self.config_manager)
            self.gamma_analyzer = GammaLiquidationAnalyzer(self.config_manager)
            if self.logger:
                await self.logger.debug("分析器实例创建完成")

            # 加载配置
            if self.logger:
                await self.logger.debug("加载配置…")
            causal_config = self.config_manager.get_section("causal_engine")

            # 更新配置参数
            self.analysis_interval = causal_config.get("analysis_interval", 10)
            self.max_history_size = causal_config.get("max_history_size", 1000)
            self._audit_interval = causal_config.get("audit_interval", 600)

            # 更新阈值配置
            threshold_config = causal_config.get("thresholds", {})
            if threshold_config:
                self.threshold_config = ThresholdConfig(**threshold_config)

            # 影子采样配置
            shadow_cfg = (causal_config or {}).get("shadow_sampling", {}) or {}
            self._shadow_sampling_enabled = bool(shadow_cfg.get("enabled", True))
            self._shadow_sample_interval_sec = int(
                shadow_cfg.get("interval_sec", 300)
            )

            # 初始化分析器实例（已在构造函数中创建）
            if self.logger:
                await self.logger.debug("初始化分析器…")
            await self._initialize_analyzers()
            if self.logger:
                await self.logger.debug("分析器初始化完成")

            # 初始化特征提取器
            if self.logger:
                await self.logger.debug("初始化特征提取器…")
            self._initialize_feature_extractors()
            if self.logger:
                await self.logger.debug("特征提取器初始化完成")

            # 初始化滞后因果训练配置
            lag_section = (causal_config or {}).get("lag_causal", {})
            lags = int(lag_section.get("lags", 3))
            ridge_lambda = float(lag_section.get("ridge_lambda", 1e-3))
            batch_size = int(lag_section.get("batch_size", 32))
            max_buffer = int(lag_section.get("max_buffer", 2000))
            train_interval_sec = float(lag_section.get("train_interval_sec", 3.0))
            target = str(lag_section.get("target", "spot.return"))
            features = lag_section.get("features")
            if not features:
                features = [
                    # 近月ATM / 25d / 10d 变化与暴露
                    "options.near_call_atm.d_iv",
                    "options.near_put_atm.d_iv",
                    "options.near_call_25d.d_iv",
                    "options.near_put_25d.d_iv",
                    "options.near_call_10d.d_iv",
                    "options.near_put_10d.d_iv",
                    "options.near_call_atm.gamma_exposure",
                    "options.near_put_atm.gamma_exposure",
                ]
            self._feature_list = list(features)
            self._lag_cfg = LagCausalConfig(
                lags=lags,
                ridge_lambda=ridge_lambda,
                batch_size=batch_size,
                max_buffer=max_buffer,
                train_interval_sec=train_interval_sec,
                target=target,
                features=self._feature_list,
            )
            self._lag_trainer = LagCausalTrainer(self._lag_cfg, self._feature_list)
            self._feat_queue = asyncio.Queue(maxsize=max(256, batch_size * 8))

            if self.logger:
                await self.logger.info("CausalEngine initialized successfully")
                # 注册配置热更新回调
                try:
                    if hasattr(self.config_manager, "register_change_callback"):
                        self.config_manager.register_change_callback(
                            self._on_config_changes
                        )
                        await self.logger.info(
                            "CausalEngine registered config change callback"
                        )
                except Exception as _e:
                    import contextlib

                    with contextlib.suppress(Exception):
                        await self.logger.warning(
                            f"Failed to register config change callback: {_e}"
                        )

            if self.logger:
                await self.logger.debug("CausalEngine._initialize_impl() 成功完成")
            return True

        except Exception as e:
            # 即使没有logger也要记录错误
            error_msg = f"Failed to initialize CausalEngine: {e}"
            import traceback
            if self.logger:
                await self.logger.error(error_msg)
                await self.logger.debug(f"错误堆栈: {traceback.format_exc()}")
            if self.logger:
                await self.logger.error(error_msg)
            return False

    async def _initialize_analyzers(self) -> None:
        """初始化分析器实例"""
        try:
            if self.logger:
                await self.logger.debug("开始初始化分析器…")

            # 为分析器注入依赖
            for analyzer in [
                self.structure_analyzer,
                self.volatility_analyzer,
                self.gamma_analyzer,
            ]:
                if self.logger:
                    await self.logger.debug(f"为 {analyzer.component_name} 注入依赖…")

                # 注入所有必需的依赖
                analyzer.cache_manager = self.cache_manager
                analyzer.event_bus = self.event_bus
                analyzer.logger = self.logger
                analyzer.dependency_injector = self.dependency_injector

                if self.logger:
                    await self.logger.debug(
                        f"deps: cache={analyzer.cache_manager is not None}, eb={analyzer.event_bus is not None}, logger={analyzer.logger is not None}"
                    )

                # 初始化分析器 - 使用BaseComponent的initialize方法
                if self.logger:
                    await self.logger.debug(f"初始化 {analyzer.component_name}…")
                try:
                    init_result = await analyzer.initialize()
                    if self.logger:
                        await self.logger.debug(
                            f"{analyzer.component_name} 初始化结果: {init_result}"
                        )
                    if not init_result:
                        raise RuntimeError(
                            f"Failed to initialize {analyzer.component_name}"
                        )
                    if self.logger:
                        await self.logger.debug(
                            f"{analyzer.component_name} 初始化成功"
                        )
                except Exception as e:
                    import traceback
                    if self.logger:
                        await self.logger.debug(
                            f"{analyzer.component_name} 初始化异常: {e}"
                        )
                        await self.logger.debug(
                            f"初始化异常堆栈: {traceback.format_exc()}"
                        )
                    raise RuntimeError(
                        f"Failed to initialize {analyzer.component_name}: {e}"
                    ) from e

                # 启动分析器
                if self.logger:
                    await self.logger.debug(f"启动 {analyzer.component_name}…")
                try:
                    start_result = await analyzer.start()
                    if self.logger:
                        await self.logger.debug(
                            f"{analyzer.component_name} 启动结果: {start_result}"
                        )
                    if not start_result:
                        raise RuntimeError(f"Failed to start {analyzer.component_name}")
                    if self.logger:
                        await self.logger.debug(f"{analyzer.component_name} 启动成功")
                except Exception as e:
                    import traceback
                    if self.logger:
                        await self.logger.debug(
                            f"{analyzer.component_name} 启动异常: {e}"
                        )
                        await self.logger.debug(
                            f"启动异常堆栈: {traceback.format_exc()}"
                        )
                    raise RuntimeError(
                        f"Failed to start {analyzer.component_name}: {e}"
                    ) from e

            if self.logger:
                await self.logger.debug("所有分析器初始化和启动成功")
            if self.logger:
                await self.logger.info("All signal analyzers initialized successfully")

        except Exception as e:
            import traceback
            if self.logger:
                await self.logger.debug(f"分析器初始化失败: {e}")
                await self.logger.debug(f"错误堆栈: {traceback.format_exc()}")
            if self.logger:
                await self.logger.error(f"Failed to initialize analyzers: {e}")
            raise

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        if self.logger:
            await self.logger.debug("CausalEngine._start_impl() 调用")
        try:
            if self.logger:
                await self.logger.info("🚨 CausalEngine._start_impl() 开始执行")
                await self.logger.debug(
                    f"eb={self.event_bus is not None}, cache={self.cache_manager is not None}, db={getattr(self, 'db_manager', None) is not None}"
                )
        except Exception:
            pass

        try:
            # 添加详细的调试信息
            if self.logger:
                await self.logger.debug(
                    f"dependency_injector available: {hasattr(self, 'dependency_injector')}"
                )

            # 确保依赖已经注入
            if not self.event_bus:
                if self.logger:
                    await self.logger.warning(
                        "EventBus not injected, trying to resolve via dependency injector"
                    )
                if hasattr(self, "dependency_injector") and self.dependency_injector:
                    try:
                        self.event_bus = await self.dependency_injector.resolve(
                            EventBus
                        )
                    except Exception as e:
                        if self.logger:
                            await self.logger.error(f"Failed to resolve EventBus: {e}")
                else:
                    if self.logger:
                        await self.logger.debug(
                            "No dependency_injector available for EventBus"
                        )

            if not self.cache_manager:
                if self.logger:
                    await self.logger.warning(
                        "CacheManager not injected, trying to resolve via dependency injector"
                    )
                if hasattr(self, "dependency_injector") and self.dependency_injector:
                    try:
                        self.cache_manager = await self.dependency_injector.resolve(
                            CacheManager
                        )
                    except Exception as e:
                        if self.logger:
                            await self.logger.error(
                                f"Failed to resolve CacheManager: {e}"
                            )
                else:
                    if self.logger:
                        await self.logger.debug(
                            "No dependency_injector available for CacheManager"
                        )

            # 兜底：解析 TimescaleDBManager（用于信号持久化）
            if not getattr(self, "db_manager", None):
                try:
                    if hasattr(self, "dependency_injector") and self.dependency_injector:
                        from src.data.timescale_manager import TimescaleDBManager

                        self.db_manager = await self.dependency_injector.resolve(
                            TimescaleDBManager
                        )
                        if self.logger:
                            await self.logger.info(
                                "Successfully resolved TimescaleDBManager for CausalEngine"
                            )
                    else:
                        if self.logger:
                            await self.logger.debug(
                                "No dependency_injector available for TimescaleDBManager"
                            )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to resolve TimescaleDBManager: {e}"
                        )

            # 如果依赖注入器没有提供EventBus，尝试从依赖注入器获取
            if (
                not self.event_bus
                and hasattr(self, "dependency_injector")
                and self.dependency_injector
            ):
                try:
                    self.event_bus = await self.dependency_injector.resolve(EventBus)
                    if self.logger:
                        await self.logger.info(
                            "Successfully resolved EventBus from dependency injector"
                        )
                except Exception as e:
                    if self.logger:
                        await self.logger.error(f"Failed to resolve EventBus: {e}")

            # 如果依赖注入器没有提供CacheManager，尝试从依赖注入器获取
            if (
                not self.cache_manager
                and hasattr(self, "dependency_injector")
                and self.dependency_injector
            ):
                try:
                    self.cache_manager = await self.dependency_injector.resolve(
                        CacheManager
                    )
                    if self.logger:
                        await self.logger.info(
                            "Successfully resolved CacheManager from dependency injector"
                        )
                except Exception as e:
                    if self.logger:
                        await self.logger.warning(
                            f"Failed to resolve CacheManager: {e}"
                        )

            # 订阅EventBus数据更新事件以获取实时数据
            if self.logger:
                await self.logger.info(
                    f"🔍 CausalEngine EventBus check: event_bus={self.event_bus is not None}"
                )

            if self.event_bus:
                if self.logger:
                    await self.logger.info("🎯 CausalEngine开始订阅EventBus...")
                try:
                    subscription_id = await self.event_bus.subscribe(
                        event_types={EventType.DATA_UPDATE},
                        callback=self._handle_data_update_event,
                        subscriber_id="CausalEngine",
                    )
                    if self.logger:
                        await self.logger.info(
                            f"✅ CausalEngine成功订阅EventBus，subscription_id={subscription_id}"
                        )
                    # 订阅聚合特征事件
                    await self.event_bus.subscribe(
                        event_types={EventType.FEATURES},
                        callback=self._handle_features_event,
                        subscriber_id="CausalEngine_features",
                    )
                    if self.logger:
                        await self.logger.info("✅ CausalEngine订阅FeaturesEvent成功")
                except Exception as e:
                    if self.logger:
                        await self.logger.error(f"❌ CausalEngine订阅EventBus失败: {e}")
                    raise
            else:
                if self.logger:
                    await self.logger.warning(
                        "EventBus not available, signal analysis will be limited"
                    )

            # 启动分析任务
            self._analysis_task = asyncio.create_task(self._analysis_loop())

            # 启动阈值更新任务
            self._threshold_update_task = asyncio.create_task(
                self._threshold_update_loop()
            )

            # 启动审计日志任务
            self._audit_task = asyncio.create_task(self._audit_loop())

            # 启动低频影子采样任务
            if self._shadow_sampling_enabled:
                self._shadow_sampling_task = asyncio.create_task(
                    self._shadow_sampling_loop()
                )

            # 启动小批次异步训练任务
            if self._feat_queue is not None:
                self._training_task = asyncio.create_task(
                    self._mini_batch_training_loop()
                )

            if self.logger:
                await self.logger.info("CausalEngine started successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to start CausalEngine: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """具体停止实现"""
        try:
            # 停止分析任务
            if self._analysis_task and not self._analysis_task.done():
                self._analysis_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._analysis_task

            # 停止阈值更新任务
            if self._threshold_update_task and not self._threshold_update_task.done():
                self._threshold_update_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._threshold_update_task

            # 停止影子采样任务
            if self._shadow_sampling_task and not self._shadow_sampling_task.done():
                self._shadow_sampling_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._shadow_sampling_task

            # 停止审计任务
            if self._audit_task and not self._audit_task.done():
                self._audit_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._audit_task

            # 停止训练任务
            if self._training_task and not self._training_task.done():
                self._training_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._training_task

            if self.logger:
                await self.logger.info("CausalEngine stopped successfully")

            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Failed to stop CausalEngine: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            # 检查分析任务状态
            analysis_healthy = (
                self._analysis_task is not None and not self._analysis_task.done()
            )

            # 检查阈值更新任务状态
            threshold_healthy = (
                self._threshold_update_task is not None
                and not self._threshold_update_task.done()
            )

            # 检查信号生成情况
            recent_signals = any(
                metrics.last_signal_time
                and (datetime.now(UTC) - metrics.last_signal_time).total_seconds() < 300
                for metrics in self.signal_metrics.values()
            )

            if analysis_healthy and threshold_healthy:
                status = HealthStatus.HEALTHY
                message = "CausalEngine is running normally"
            elif analysis_healthy or threshold_healthy:
                status = HealthStatus.DEGRADED
                message = "CausalEngine is partially functional"
            else:
                status = HealthStatus.UNHEALTHY
                message = "CausalEngine tasks are not running"

            details = {
                "analysis_task_running": analysis_healthy,
                "threshold_task_running": threshold_healthy,
                "recent_signals": recent_signals,
                "signal_metrics": {
                    signal_type.value: {
                        "total_signals": metrics.total_signals,
                        "success_rate": metrics.success_rate,
                        "avg_strength": metrics.avg_strength,
                    }
                    for signal_type, metrics in self.signal_metrics.items()
                },
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    def _initialize_feature_extractors(self):
        """初始化特征提取器"""
        # 基础特征提取器
        self.feature_extractors = [
            self._extract_price_features,
            self._extract_volume_features,
            self._extract_volatility_features,
            self._extract_sentiment_features,
        ]

    async def _analysis_loop(self):
        """主分析循环"""
        while True:
            try:
                await self._run_analysis_cycle()
                await asyncio.sleep(self.analysis_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in analysis loop: {e}")
                await asyncio.sleep(self.analysis_interval)

    async def _threshold_update_loop(self):
        """阈值更新循环"""
        while True:
            try:
                await self._update_dynamic_thresholds()
                await asyncio.sleep(3600)  # 每小时更新一次阈值

            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in threshold update loop: {e}")
                await asyncio.sleep(3600)

    async def _audit_loop(self):
        """审计循环：输出滞后因果与阈值的关键参数，便于调参与审计"""
        while True:
            try:
                params = {
                    "analysis_interval": self.analysis_interval,
                    "signal_threshold": self.threshold_config.signal_threshold,
                    "confidence_threshold": self.threshold_config.confidence_threshold,
                }
                lag = {
                    "lags": getattr(self._lag_cfg, "lags", None),
                    "ridge_lambda": getattr(self._lag_cfg, "ridge_lambda", None),
                    "batch_size": getattr(self._lag_cfg, "batch_size", None),
                    "max_buffer": getattr(self._lag_cfg, "max_buffer", None),
                }
                metrics = self._causal_metrics or {}
                msg = (
                    f"CAUSAL audit: interval={params['analysis_interval']}s, "
                    f"thresholds={params}, lag={lag}, "
                    f"r2={float(metrics.get('r2_full', 0.0)):.3f}, "
                    f"F={float(metrics.get('f_stat', 0.0)):.2f}, p={metrics.get('p_value')}"
                )
                if self.logger:
                    await self.logger.info(msg)
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Causal audit error: {e}")
            finally:
                await asyncio.sleep(self._audit_interval)

    async def _shadow_sampling_loop(self):
        """低频采样信号影子数据（未达阈值也采样）用于自动调参"""
        while True:
            try:
                if not self.db_manager:
                    # 兜底解析 db_manager
                    if hasattr(self, "dependency_injector") and self.dependency_injector:
                        try:
                            from src.data.timescale_manager import TimescaleDBManager

                            self.db_manager = await self.dependency_injector.resolve(
                                TimescaleDBManager
                            )
                        except Exception:
                            pass

                now = datetime.now(UTC)
                for st in SignalType:
                    history = self.signal_history.get(st, [])
                    if history:
                        last_sig = history[-1]
                        strength = float(getattr(last_sig, "strength", 0.0))
                        confidence = float(getattr(last_sig, "confidence", 0.0))
                        meta = getattr(last_sig, "metadata", {}) or {}
                    else:
                        strength = 0.0
                        confidence = 0.0
                        meta = {}

                    payload = {
                        "time": now,
                        "signal_type": st.value,
                        "strength": float(strength),
                        "confidence": float(confidence),
                        "sample_source": "causal_engine_shadow",
                        "metadata": {
                            **meta,
                            "actionable": confidence
                            >= float(self.threshold_config.confidence_threshold),
                            "thresholds": {
                                "signal_threshold": float(
                                    self.threshold_config.signal_threshold
                                ),
                                "confidence_threshold": float(
                                    self.threshold_config.confidence_threshold
                                ),
                            },
                        },
                    }

                    if self.db_manager:
                        await self.db_manager.insert_signal_shadow(payload)
                # 等待下一个采样窗口
            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.warning(f"Shadow sampling error: {e}")
            finally:
                await asyncio.sleep(self._shadow_sample_interval_sec)

    async def _on_config_changes(self, changes):
        """处理配置热更新：causal_engine 与各 analyzer 配置变更"""
        try:
            for change in changes:
                path = str(getattr(change, "config_path", ""))
                if not path:
                    continue
                # CausalEngine 通用
                if path == "causal_engine.analysis_interval":
                    old = self.analysis_interval
                    self.analysis_interval = int(change.new_value)
                    if self.logger:
                        await self.logger.info(
                            f"CausalEngine.analysis_interval {old} -> {self.analysis_interval}"
                        )
                elif path == "causal_engine.max_history_size":
                    self.max_history_size = int(change.new_value)
                elif path.startswith("causal_engine.thresholds."):
                    key = path.split(".")[-1]
                    if hasattr(self.threshold_config, key):
                        old = getattr(self.threshold_config, key)
                        try:
                            val = float(change.new_value)
                        except Exception:
                            val = change.new_value
                        setattr(self.threshold_config, key, val)
                        if self.logger:
                            await self.logger.info(
                                f"CausalEngine.thresholds.{key} {old} -> {val}"
                            )
                elif path.startswith("causal_engine.lag_causal."):
                    key = path.split(".")[-1]
                    val = change.new_value
                    # 更新配置对象
                    if self._lag_cfg is None:
                        continue
                    old = getattr(self._lag_cfg, key, None)
                    try:
                        if key in {"lags", "batch_size", "max_buffer"}:
                            setattr(self._lag_cfg, key, int(val))
                        elif key in {"ridge_lambda", "train_interval_sec"}:
                            setattr(self._lag_cfg, key, float(val))
                        elif key == "features" and isinstance(val, list):
                            self._feature_list = list(val)
                            setattr(self._lag_cfg, key, list(val))
                        else:
                            setattr(self._lag_cfg, key, val)
                        # 重建trainer以应用参数
                        self._lag_trainer = LagCausalTrainer(
                            self._lag_cfg, self._feature_list
                        )
                        if self.logger:
                            await self.logger.info(
                                f"LagCausal {key} {old} -> {val}; trainer rebuilt"
                            )
                    except Exception as _e2:
                        if self.logger:
                            await self.logger.error(
                                f"LagCausal hot-update failed for {key}: {_e2}"
                            )
                # 分析器配置：直接更新其配置字典与关键属性
                elif (
                    path.startswith("volatility_analyzer.") and self.volatility_analyzer
                ):
                    key = path.split(".")[-1]
                    self.volatility_analyzer.config[key] = change.new_value
                    if key == "analysis_interval":
                        import contextlib

                        with contextlib.suppress(Exception):
                            self.volatility_analyzer.analysis_interval = int(
                                change.new_value
                            )
                    if self.logger:
                        await self.logger.info(
                            f"VolatilityAnalyzer.{key} -> {change.new_value}"
                        )
                elif path.startswith("gamma_analyzer.") and self.gamma_analyzer:
                    key = path.split(".")[-1]
                    self.gamma_analyzer.config[key] = (
                        change.new_value
                        if hasattr(self.gamma_analyzer, "config")
                        else change.new_value
                    )
                    if key == "analysis_interval":
                        import contextlib

                        with contextlib.suppress(Exception):
                            self.gamma_analyzer.analysis_interval = int(
                                change.new_value
                            )
                    if self.logger:
                        await self.logger.info(
                            f"GammaAnalyzer.{key} -> {change.new_value}"
                        )
                elif path.startswith("structure_analyzer.") and self.structure_analyzer:
                    key = path.split(".")[-1]
                    self.structure_analyzer.config[key] = (
                        change.new_value
                        if hasattr(self.structure_analyzer, "config")
                        else change.new_value
                    )
                    if self.logger:
                        await self.logger.info(
                            f"StructureAnalyzer.{key} -> {change.new_value}"
                        )

            # 记录热更新次数
            import contextlib

            with contextlib.suppress(Exception):
                self.metrics.custom_metrics["config_hot_updates"] = (
                    self.metrics.custom_metrics.get("config_hot_updates", 0) + 1
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"CausalEngine hot-update handler error: {e}")

    async def _handle_data_update_event(self, event):
        """处理实时数据更新事件"""
        try:
            # 添加调试日志
            if self.logger:
                await self.logger.info(
                    f"🎯 CAUSAL_ENGINE_METHOD_CALLED: type={type(event)}, event_type={getattr(event, 'event_type', 'N/A')}"
                )
                await self.logger.info(
                    f"🔥 CAUSAL_ENGINE_CALLBACK_INVOKED: event_id={getattr(event, 'event_id', 'N/A')}"
                )

            # 检查事件属性 - 使用source而不是exchange
            if (
                not hasattr(event, "data")
                or not hasattr(event, "data_type")
                or not hasattr(event, "source")
            ):
                if self.logger:
                    await self.logger.warning(
                        f"事件缺少必要属性: data={hasattr(event, 'data')}, data_type={hasattr(event, 'data_type')}, source={hasattr(event, 'source')}"
                    )
                return

            if not event.data or not event.data_type or not event.source:
                if self.logger:
                    await self.logger.warning(
                        f"事件属性为空: data={bool(event.data)}, data_type={event.data_type}, source={event.source}"
                    )
                return

            # 缓存最新数据以供分析使用 - 使用source而不是exchange
            if self.cache_manager:
                # 根据source确定exchange名称
                exchange_name = "binance" if "binance" in event.source else "deribit"
                cache_key = f"{exchange_name}:latest_{event.data_type}"
                await self.cache_manager.set(cache_key, event.data, ttl=300)

                if self.logger:
                    await self.logger.info(f"CAUSAL_ENGINE_CACHED: {cache_key}")

            # 如果是关键数据更新（价格、成交量、期权数据），触发分析
            if event.data_type in ["ticker", "trade", "option", "orderbook"]:
                # 异步触发分析（不阻塞数据流）
                asyncio.create_task(self._trigger_analysis_if_ready())
                if self.logger:
                    await self.logger.info(
                        f"CAUSAL_ENGINE_TRIGGERED: {event.source}:{event.data_type}"
                    )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling data update event: {e}")

    async def _handle_features_event(self, event):
        """处理聚合特征事件：抽取特征与目标，送入小批次训练队列"""
        try:
            payload = event.features if hasattr(event, "features") else None
            if not isinstance(payload, dict):
                return

            spot = (
                (payload.get("spot") or {})
                if isinstance(payload.get("spot"), dict)
                else {}
            )
            options = payload.get("options") or {}
            ts = getattr(event, "timestamp", datetime.now(UTC))

            # 目标：spot return（对数收益）
            spot_price = None
            try:
                sp = spot.get("price")
                if sp is not None:
                    spot_price = float(sp)
            except Exception:
                spot_price = None

            y_ret = None
            if (
                spot_price is not None
                and self._last_spot_price is not None
                and self._last_spot_price > 0
            ):
                try:
                    y_ret = float(np.log(spot_price / self._last_spot_price))
                except Exception:
                    y_ret = None
            self._last_spot_price = (
                spot_price if spot_price is not None else self._last_spot_price
            )
            self._last_spot_ts = ts

            # 特征展开
            flat_opt = {}
            if isinstance(options, dict):
                for k, v in options.items():
                    if isinstance(v, dict):
                        for kk, vv in v.items():
                            flat_opt[f"options.{k}.{kk}"] = vv
            flat_spot = _flatten_features({"spot": spot})
            flat = {**flat_spot, **flat_opt}

            if y_ret is None:
                return

            if self._feat_queue is not None:
                try:
                    await self._feat_queue.put((ts, y_ret, flat))
                except asyncio.QueueFull:
                    # 丢弃最旧项以腾出空间
                    _ = self._feat_queue.get_nowait()  # noqa: F841
                    await self._feat_queue.put((ts, y_ret, flat))
        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error handling FeaturesEvent: {e}")

    async def _mini_batch_training_loop(self):
        """小批次异步训练循环：基于FeaturesEvent增量更新滞后因果模型"""
        assert self._lag_trainer is not None
        assert self._feat_queue is not None
        batch: list[tuple[datetime, float, dict[str, float]]] = []
        last_train = datetime.now(UTC)
        while True:
            try:
                try:
                    item = await asyncio.wait_for(
                        self._feat_queue.get(),
                        timeout=self._lag_cfg.train_interval_sec
                        if self._lag_cfg
                        else 3.0,
                    )
                    batch.append(item)
                except TimeoutError:
                    pass

                # 触发训练条件：批量大小或时间间隔
                time_elapsed = (datetime.now(UTC) - last_train).total_seconds()
                trigger_by_size = (
                    self._lag_cfg and len(batch) >= self._lag_cfg.batch_size
                )
                trigger_by_time = (
                    self._lag_cfg and time_elapsed >= self._lag_cfg.train_interval_sec
                )
                if trigger_by_size or trigger_by_time:
                    for ts, y_ret, flat in batch:
                        x = {
                            name: float(flat.get(name, 0.0))
                            for name in self._feature_list
                        }
                        self._lag_trainer.add_sample(ts, y_ret, x)
                    ok = self._lag_trainer.fit()
                    if ok:
                        self._causal_metrics = self._lag_trainer.latest_metrics()
                        if self.logger:
                            await self.logger.info(
                                f"LagCausal updated: r2_full={self._causal_metrics.get('r2_full'):.3f}, "
                                f"F={self._causal_metrics.get('f_stat'):.2f}, p={self._causal_metrics.get('p_value')}"
                            )
                    batch.clear()
                    last_train = datetime.now(UTC)
            except asyncio.CancelledError:
                break
            except Exception as e:
                if self.logger:
                    await self.logger.error(f"Error in mini-batch training: {e}")
                await asyncio.sleep(0.5)

    async def _trigger_analysis_if_ready(self):
        """如果数据准备就绪，触发分析"""
        try:
            # 检查是否有足够的最新数据进行跨交易所分析
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            binance_ticker = await mds.get_latest_ticker("binance")
            # 对于Deribit，优先使用ticker数据，如果没有则使用期权数据作为替代
            deribit_ticker = await mds.get_latest_ticker("deribit")
            if not deribit_ticker:
                deribit_ticker = await mds.get_cached("deribit:latest_option")

                # 只有在同时有两个交易所数据时才进行分析
                if binance_ticker and deribit_ticker:
                    # 有足够的跨交易所数据，运行一次分析周期
                    await self._run_analysis_cycle()
                    if self.logger:
                        await self.logger.info(
                            "✅ 跨交易所分析已触发 - Binance ticker + Deribit option data"
                        )
                else:
                    # 数据不完整，记录缺失的数据源但不进行分析
                    missing_sources = []
                    if not binance_ticker:
                        missing_sources.append("Binance ticker")
                    if not deribit_ticker:
                        missing_sources.append("Deribit option")

                    if self.logger:
                        await self.logger.debug(
                            f"⏳ 跨交易所分析等待中 - 缺失数据源: {', '.join(missing_sources)}"
                        )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error triggering analysis: {e}")

    async def _run_analysis_cycle(self):
        """运行一次完整的分析周期"""
        try:
            # 获取市场数据
            market_data = await self._get_market_data()
            if not market_data:
                return

            # 并行运行三个信号分析器
            tasks = []

            # 结构分歧分析（任务16将实现）
            if self.structure_analyzer:
                tasks.append(self._analyze_structure_divergence(market_data))

            # 波动率错配分析（任务17将实现）
            if self.volatility_analyzer:
                tasks.append(self._analyze_volatility_mismatch(market_data))

            # Gamma清算重叠分析（任务18将实现）
            if self.gamma_analyzer:
                tasks.append(self._analyze_gamma_liquidation(market_data))

            # 等待所有分析完成
            if tasks:
                signals = await asyncio.gather(*tasks, return_exceptions=True)

                # 处理分析结果
                for signal in signals:
                    if isinstance(signal, UnifiedSignal):
                        await self._process_signal(signal)
                    elif isinstance(signal, Exception) and self.logger:
                        await self.logger.error(f"Signal analysis error: {signal}")

            # 运行机器学习优化（如果模型已训练）
            if self.ml_model:
                await self._run_ml_optimization(market_data)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error in analysis cycle: {e}")

    async def _get_market_data(self) -> dict[str, Any] | None:
        """获取分析所需的市场数据"""
        try:
            from src.services.market_data_service import MarketDataService

            mds = await self.dependency_injector.resolve(MarketDataService)
            market_data = {}

            # Binance数据
            binance_ticker = await mds.get_latest_ticker("binance")
            if binance_ticker:
                market_data["binance_ticker"] = binance_ticker

            binance_trade = await mds.get_cached("binance:latest_trade")
            if binance_trade:
                market_data["binance_trade"] = binance_trade

            # Deribit数据
            deribit_option = await mds.get_cached("deribit:latest_option")
            if deribit_option:
                market_data["deribit_option"] = deribit_option

            deribit_ticker = await mds.get_latest_ticker("deribit")
            if deribit_ticker:
                market_data["deribit_ticker"] = deribit_ticker

            # 如果有任何数据，记录日志并返回
            if market_data:
                if self.logger:
                    await self.logger.info(
                        f"📊 获取市场数据成功: {list(market_data.keys())}"
                    )
                return market_data
            else:
                if self.logger:
                    await self.logger.warning("⚠️ 未获取到任何市场数据")
                return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error getting market data: {e}")
            return None

    async def _analyze_structure_divergence(
        self, market_data: dict[str, Any]
    ) -> UnifiedSignal | None:
        """分析结构分歧信号（委托给专业分析器）"""
        try:
            if not self.structure_analyzer:
                return None

            # 委托给专业分析器进行分析
            analysis_result = await self.structure_analyzer.analyze_divergence(
                market_data
            )

            if analysis_result:
                # 将分析结果转换为CausalSignal
                signal = UnifiedSignal(
                    signal_type=SignalType.STRUCTURE_DIVERGENCE,
                    strength=analysis_result.get("strength", 0.0),
                    confidence=analysis_result.get("confidence", 0.0),
                    timestamp=datetime.now(UTC),
                    metadata=analysis_result.get("metadata", {}),
                )
                return signal

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Structure divergence analysis failed: {e}")
            return None

    async def _analyze_volatility_mismatch(
        self, market_data: dict[str, Any]
    ) -> UnifiedSignal | None:
        """分析波动率错配信号（委托给专业分析器）"""
        try:
            if not self.volatility_analyzer:
                return None

            # 委托给专业分析器进行分析
            analysis_result = await self.volatility_analyzer.analyze_mismatch(
                market_data
            )

            if analysis_result:
                # 将分析结果转换为CausalSignal
                signal = UnifiedSignal(
                    signal_type=SignalType.VOLATILITY_MISMATCH,
                    strength=analysis_result.get("strength", 0.0),
                    confidence=analysis_result.get("confidence", 0.0),
                    timestamp=datetime.now(UTC),
                    metadata=analysis_result.get("metadata", {}),
                )
                return signal

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Volatility mismatch analysis failed: {e}")
            return None

    async def _analyze_gamma_liquidation(
        self, market_data: dict[str, Any]
    ) -> UnifiedSignal | None:
        """分析Gamma清算重叠信号（委托给专业分析器）"""
        try:
            if not self.gamma_analyzer:
                return None

            # 委托给专业分析器进行分析
            analysis_result = await self.gamma_analyzer.analyze_liquidation_overlap(
                market_data
            )

            if analysis_result:
                # 将分析结果转换为CausalSignal
                signal = UnifiedSignal(
                    signal_type=SignalType.GAMMA_LIQUIDATION_OVERLAP,
                    strength=analysis_result.get("strength", 0.0),
                    confidence=analysis_result.get("confidence", 0.0),
                    timestamp=datetime.now(UTC),
                    metadata=analysis_result.get("metadata", {}),
                )
                return signal

            return None

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Gamma liquidation analysis failed: {e}")
            return None

    async def _process_signal(self, signal: UnifiedSignal):
        """处理生成的信号"""
        try:
            # 更新信号历史
            self._update_signal_history(signal)

            # 更新统计指标
            self._update_signal_metrics(signal)

            # 检查是否达到触发阈值
            if signal.is_actionable(self.threshold_config.confidence_threshold):
                # 发布信号事件
                await self._publish_signal_event(signal)

                # 缓存信号
                await self._cache_signal(signal)

            # 存储信号到数据库
            await self._store_signal_to_database(signal)

            # 记录信号
            if self.logger:
                await self.logger.info(
                    f"Signal processed: {signal.signal_type} "
                    f"strength={signal.strength:.3f} confidence={signal.confidence:.3f}"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error processing signal: {e}")

    async def _store_signal_to_database(self, signal: UnifiedSignal):
        """存储信号到数据库"""
        try:
            if not self.db_manager:
                if self.logger:
                    await self.logger.warning("⚠️ 数据库管理器未初始化，无法存储信号")
                return

            # 准备信号数据 - 使用TimescaleDBManager.insert_signal方法的格式
            # 统一将 signal_type 转为字符串，避免传入枚举导致 asyncpg 绑定失败
            signal_type_value = (
                signal.signal_type.value
                if hasattr(signal.signal_type, "value")
                else str(signal.signal_type)
            )
            signal_data = {
                "time": signal.timestamp,
                "signal_type": signal_type_value,
                "strength": signal.strength,
                "confidence": signal.confidence,
                "metadata": signal.metadata,
            }

            # 使用TimescaleDBManager的insert_signal方法
            success = await self.db_manager.insert_signal(signal_data)

            if success and self.logger:
                await self.logger.info(f"💾 信号已存储到数据库: {signal.signal_type}")
            elif not success and self.logger:
                await self.logger.warning(f"⚠️ 信号存储失败: {signal.signal_type}")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error storing signal to database: {e}")

    def _update_signal_history(self, signal: UnifiedSignal):
        """更新信号历史"""
        # 将字符串signal_type转换为SignalType枚举
        signal_type_enum = None
        for st in SignalType:
            if st.value == signal.signal_type:
                signal_type_enum = st
                break

        if signal_type_enum is None:
            return  # 未知信号类型，跳过

        history = self.signal_history[signal_type_enum]

        # 添加新信号
        history.append(signal)

        # 保持历史大小限制
        if len(history) > self.max_history_size:
            history.pop(0)

    def _update_signal_metrics(self, signal: UnifiedSignal):
        """更新信号统计指标"""
        # 将字符串signal_type转换为SignalType枚举
        signal_type_enum = None
        for st in SignalType:
            if st.value == signal.signal_type:
                signal_type_enum = st
                break

        if signal_type_enum is None:
            return  # 未知信号类型，跳过

        metrics = self.signal_metrics[signal_type_enum]

        # 更新计数
        metrics.total_signals += 1
        metrics.last_signal_time = signal.timestamp

        # 更新平均值
        total = metrics.total_signals
        metrics.avg_strength = (
            metrics.avg_strength * (total - 1) + signal.strength
        ) / total
        metrics.avg_confidence = (
            metrics.avg_confidence * (total - 1) + signal.confidence
        ) / total

    async def _publish_signal_event(self, signal: UnifiedSignal):
        """发布信号事件"""
        if not self.event_bus:
            return

        # 生成因果支持信息
        causal_support: dict[str, Any] = {}
        try:
            if self._lag_trainer is not None:
                pred = self._lag_trainer.predict_next()
                if pred is not None:
                    causal_support["pred_next_return"] = float(pred)
                    causal_support["pred_sign"] = (
                        "bullish"
                        if pred > 0
                        else ("bearish" if pred < 0 else "neutral")
                    )
            if self._causal_metrics:
                for key in ("p_value", "r2_full", "f_stat"):
                    if key in self._causal_metrics:
                        causal_support[key] = self._causal_metrics[key]
                if "lag_corrs" in self._causal_metrics:
                    causal_support["lag_corrs"] = self._causal_metrics["lag_corrs"]
        except Exception:
            pass

        event = SignalEvent(
            signal_data={
                "signal_type": signal.signal_type,
                "strength": signal.strength,
                "confidence": signal.confidence,
                "strength_level": signal.strength_level.value,
                "is_actionable": signal.is_actionable(
                    self.threshold_config.confidence_threshold
                ),
                "metadata": signal.metadata,
                "causal_support": causal_support,
            },
            signal_type=signal.signal_type,
            confidence=signal.confidence,
            source=self.component_name,
            timestamp=signal.timestamp,
        )

        await self.event_bus.publish(event)

    async def _cache_signal(self, signal: UnifiedSignal):
        """缓存信号数据"""
        if not self.cache_manager:
            return

        cache_key = (
            f"causal_signal:{signal.signal_type}:{int(signal.timestamp.timestamp())}"
        )
        signal_data = {
            "signal_type": signal.signal_type,
            "strength": signal.strength,
            "confidence": signal.confidence,
            "timestamp": signal.timestamp.isoformat(),
            "metadata": signal.metadata,
        }

        await self.cache_manager.set(cache_key, signal_data, ttl=300)

    async def _update_dynamic_thresholds(self):
        """更新动态阈值"""
        try:
            for signal_type in SignalType:
                await self._update_threshold_for_signal_type(signal_type)

        except Exception as e:
            if self.logger:
                await self.logger.error(f"Error updating dynamic thresholds: {e}")

    async def _update_threshold_for_signal_type(self, signal_type: SignalType):
        """为特定信号类型更新阈值"""
        try:
            # 获取历史信号
            history = self.signal_history[signal_type]
            if len(history) < 10:  # 需要足够的历史数据
                return

            # 计算历史成功率
            recent_signals = history[-50:]  # 最近50个信号
            success_count = 0
            for s in recent_signals:
                if await self._is_signal_successful(s):
                    success_count += 1
            success_rate = success_count / len(recent_signals)

            # 根据成功率调整阈值
            current_threshold = self.threshold_config.signal_threshold
            adaptation_rate = self.threshold_config.adaptation_rate

            if success_rate > 0.8:  # 成功率高，可以降低阈值
                new_threshold = current_threshold * (1 - adaptation_rate)
            elif success_rate < 0.6:  # 成功率低，提高阈值
                new_threshold = current_threshold * (1 + adaptation_rate)
            else:
                new_threshold = current_threshold

            # 应用阈值限制
            new_threshold = max(
                self.threshold_config.min_threshold,
                min(self.threshold_config.max_threshold, new_threshold),
            )

            self.threshold_config.signal_threshold = new_threshold

            if self.logger:
                await self.logger.debug(
                    f"Updated threshold for {signal_type.value}: "
                    f"{current_threshold:.3f} -> {new_threshold:.3f} "
                    f"(success_rate: {success_rate:.3f})"
                )

        except Exception as e:
            if self.logger:
                await self.logger.error(
                    f"Error updating threshold for {signal_type.value}: {e}"
                )

    async def _is_signal_successful(self, signal: UnifiedSignal) -> bool:
        """判断信号是否成功，基于实际市场表现分析"""
        try:
            # 多维度评估信号成功率
            signal_time = signal.timestamp
            current_time = datetime.now(UTC)
            time_elapsed = (current_time - signal_time).total_seconds()

            # 根据信号类型设定不同的评估时间窗口
            evaluation_window = {
                SignalType.ENTRY: 3600,  # 1小时
                SignalType.EXIT: 1800,  # 30分钟
                SignalType.RISK: 900,  # 15分钟
                SignalType.VOLATILITY: 7200,  # 2小时
            }.get(signal.signal_type, 3600)

            # 信号太新，无法评估
            if time_elapsed < evaluation_window:
                return False

            # 基础条件：强度和置信度门槛
            base_threshold = signal.strength > 0.6 and signal.confidence > 0.7
            if not base_threshold:
                return False

            # 市场一致性检查：信号方向与实际价格变动的一致性
            market_consistency = self._check_market_consistency(signal, time_elapsed)

            # 波动率预测准确性（针对波动率信号）
            vol_accuracy = await self._check_volatility_accuracy(signal, time_elapsed)

            # 综合评分
            success_score = (
                signal.strength * 0.3
                + signal.confidence * 0.2
                + market_consistency * 0.4
                + vol_accuracy * 0.1
            )

            # 动态成功阈值（根据市场条件调整）
            market_volatility = await self._get_current_market_volatility()
            if market_volatility is None:
                return False
            success_threshold = 0.75 if market_volatility < 0.6 else 0.70

            return success_score >= success_threshold

        except Exception as e:
            if self.logger:
                import logging

                logging.warning(f"Error evaluating signal success: {e}")
            # 严格模式：失败即失败
            return False

    def _check_market_consistency(
        self, signal: UnifiedSignal, time_elapsed: float
    ) -> float:
        """检查信号与市场实际走势的一致性"""
        try:
            if not hasattr(self, "market_data_cache"):
                return 0.5  # 中性分数

            # 获取信号发出时和现在的价格数据
            signal_time_price = self._get_historical_price(signal.timestamp)
            current_price = self._get_current_price()

            if not signal_time_price or not current_price:
                return 0.5

            price_change = (current_price - signal_time_price) / signal_time_price

            # 判断方向一致性
            if signal.signal_type == SignalType.ENTRY:
                expected_direction = (
                    1 if "bullish" in signal.metadata.get("direction", "") else -1
                )
                actual_direction = 1 if price_change > 0 else -1

                direction_consistency = (
                    1.0 if expected_direction == actual_direction else 0.2
                )

                # 幅度一致性
                expected_magnitude = signal.metadata.get("expected_move", 0.02)
                magnitude_ratio = min(abs(price_change) / expected_magnitude, 2.0)
                magnitude_score = (
                    magnitude_ratio if magnitude_ratio <= 1.0 else 2.0 - magnitude_ratio
                )

                return direction_consistency * 0.7 + magnitude_score * 0.3

            return 0.6  # 其他信号类型的中性分数

        except Exception:
            return 0.5

    async def _check_volatility_accuracy(
        self, signal: UnifiedSignal, time_elapsed: float
    ) -> float:
        """检查波动率预测的准确性"""
        try:
            if signal.signal_type != SignalType.VOLATILITY:
                return 0.5  # 非波动率信号返回中性分数

            # 获取真实的预测波动率或当前市场波动率
            predicted_vol = signal.metadata.get("predicted_volatility")
            if not predicted_vol:
                predicted_vol = await self._get_real_market_volatility()
            realized_vol = self._calculate_realized_volatility(
                signal.timestamp, time_elapsed
            )

            if realized_vol is None:
                return 0.5

            # 计算预测准确性
            vol_error = abs(predicted_vol - realized_vol) / predicted_vol
            accuracy_score = max(0.0, 1.0 - vol_error * 2.0)  # 50%误差对应0分

            return accuracy_score

        except Exception:
            return 0.5

    async def _get_current_market_volatility(self) -> float | None:
        """获取当前市场波动率"""
        try:
            if hasattr(self, "market_data_cache") and self.market_data_cache:
                # 计算最近24小时的实现波动率
                price_data = self.market_data_cache.get("price_history", [])
                if len(price_data) >= 24:
                    import numpy as np

                    prices = np.array([float(p) for p in price_data[-24:]])
                    returns = np.diff(np.log(prices))
                    return float(np.std(returns) * np.sqrt(24))  # 日化波动率

            # 严格模式：无法计算则返回None
            return None
        except Exception:
            return None

    async def _run_ml_optimization(self, market_data: dict[str, Any]):
        """运行机器学习优化（占位符）"""
        # 机器学习模型优化将在后续版本实现
        # 包括特征提取、模型训练、预测等
        pass

    def _extract_price_features(self, market_data: dict[str, Any]) -> dict[str, float]:
        """提取价格相关特征"""
        features = {}

        try:
            # Binance价格特征
            if "binance" in market_data:
                binance_data = market_data["binance"]
                features["btc_price"] = float(binance_data.get("price", 0))
                features["btc_volume"] = float(binance_data.get("volume", 0))

            # Deribit价格特征
            if "deribit" in market_data:
                deribit_data = market_data["deribit"]
                features["option_mark_price"] = float(deribit_data.get("mark_price", 0))
                features["option_volume"] = float(deribit_data.get("volume", 0))

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting price features: {e}")
                )

        return features

    def _extract_volume_features(self, market_data: dict[str, Any]) -> dict[str, float]:
        """提取成交量相关特征"""
        features = {}

        try:
            # 成交量比率、流入流出等特征
            if "binance" in market_data and "deribit" in market_data:
                binance_vol = float(market_data["binance"].get("volume", 0))
                deribit_vol = float(market_data["deribit"].get("volume", 0))

                if binance_vol > 0 and deribit_vol > 0:
                    features["volume_ratio"] = deribit_vol / binance_vol
                    features["total_volume"] = binance_vol + deribit_vol

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting volume features: {e}")
                )

        return features

    def _extract_volatility_features(
        self, market_data: dict[str, Any]
    ) -> dict[str, float]:
        """提取波动率相关特征"""
        features = {}

        try:
            # 隐含波动率特征
            if "deribit" in market_data:
                deribit_data = market_data["deribit"]
                features["implied_volatility"] = float(deribit_data.get("mark_iv", 0))

            # Greeks相关特征
            if "greeks" in market_data:
                greeks = market_data["greeks"]
                features["gamma"] = float(greeks.get("gamma", 0))
                features["vega"] = float(greeks.get("vega", 0))
                features["theta"] = float(greeks.get("theta", 0))

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting volatility features: {e}")
                )

        return features

    def _extract_sentiment_features(
        self, market_data: dict[str, Any]
    ) -> dict[str, float]:
        """提取市场情绪相关特征"""
        features = {}

        try:
            # 持仓量变化、资金费率等情绪指标
            if "deribit" in market_data:
                deribit_data = market_data["deribit"]
                features["open_interest"] = float(deribit_data.get("open_interest", 0))

            # 可以添加更多情绪指标

        except Exception as e:
            if self.logger:
                asyncio.create_task(
                    self.logger.error(f"Error extracting sentiment features: {e}")
                )

        return features

    # 公共接口方法

    async def get_signal_history(
        self, signal_type: SignalType, limit: int = 100
    ) -> list[UnifiedSignal]:
        """获取信号历史"""
        history = self.signal_history.get(signal_type, [])
        return history[-limit:] if limit > 0 else history

    async def get_signal_metrics(
        self, signal_type: SignalType | None = None
    ) -> dict[str, Any]:
        """获取信号统计指标"""
        if signal_type:
            metrics = self.signal_metrics.get(signal_type)
            if metrics:
                return {
                    "total_signals": metrics.total_signals,
                    "successful_signals": metrics.successful_signals,
                    "success_rate": metrics.success_rate,
                    "avg_strength": metrics.avg_strength,
                    "avg_confidence": metrics.avg_confidence,
                    "last_signal_time": metrics.last_signal_time.isoformat()
                    if metrics.last_signal_time
                    else None,
                }
        else:
            return {
                signal_type.value: {
                    "total_signals": metrics.total_signals,
                    "successful_signals": metrics.successful_signals,
                    "success_rate": metrics.success_rate,
                    "avg_strength": metrics.avg_strength,
                    "avg_confidence": metrics.avg_confidence,
                    "last_signal_time": metrics.last_signal_time.isoformat()
                    if metrics.last_signal_time
                    else None,
                }
                for signal_type, metrics in self.signal_metrics.items()
            }

    async def get_current_thresholds(self) -> dict[str, float]:
        """获取当前阈值配置"""
        return {
            "signal_threshold": self.threshold_config.signal_threshold,
            "confidence_threshold": self.threshold_config.confidence_threshold,
            "min_threshold": self.threshold_config.min_threshold,
            "max_threshold": self.threshold_config.max_threshold,
        }

    async def get_causal_metrics(self) -> dict[str, Any]:
        """获取最近一次滞后因果训练的指标和参数"""
        return dict(self._causal_metrics) if self._causal_metrics else {}

    async def predict_next_return_sign(self) -> str | None:
        """基于最新滞后因果模型预测下一步收益方向"""
        try:
            if self._lag_trainer is None:
                return None
            pred = self._lag_trainer.predict_next()
            if pred is None:
                return None
            return "bullish" if pred > 0 else ("bearish" if pred < 0 else "neutral")
        except Exception:
            return None

    async def update_threshold_config(self, **kwargs):
        """更新阈值配置"""
        for key, value in kwargs.items():
            if hasattr(self.threshold_config, key):
                setattr(self.threshold_config, key, value)

    async def register_analyzer(self, analyzer_type: str, analyzer):
        """注册信号分析器"""
        if analyzer_type == "structure":
            self.structure_analyzer = analyzer
        elif analyzer_type == "volatility":
            self.volatility_analyzer = analyzer
        elif analyzer_type == "gamma":
            self.gamma_analyzer = analyzer
        else:
            raise ValueError(f"Unknown analyzer type: {analyzer_type}")

        if self.logger:
            await self.logger.info(f"Registered {analyzer_type} analyzer")

    async def force_analysis(self) -> dict[str, Any]:
        """强制执行一次分析"""
        try:
            await self._run_analysis_cycle()
            return {
                "status": "success",
                "timestamp": datetime.now(UTC).isoformat(),
                "message": "Analysis completed successfully",
            }
        except Exception as e:
            return {
                "status": "error",
                "timestamp": datetime.now(UTC).isoformat(),
                "message": f"Analysis failed: {str(e)}",
            }

    async def _get_real_market_volatility(self) -> float:
        """获取真实的市场波动率"""
        try:
            # 方法1: 从期权隐含波动率数据获取
            iv_data = await self._get_implied_volatility_from_options()
            if iv_data and iv_data > 0:
                return iv_data

            # 方法2: 从历史价格计算实际波动率
            realized_vol = await self._calculate_realized_vol_from_price_history()
            if realized_vol and realized_vol > 0:
                return realized_vol

            # 方法3: 从波动率指数（如果有的话）
            vol_index = await self._get_volatility_index()
            if vol_index and vol_index > 0:
                return vol_index / 100  # 通常指数以百分比形式表示

            # 方法4: 基于当前市场状况的动态计算
            market_vol = await self._estimate_volatility_from_market_regime()
            if market_vol:
                return market_vol

            # 最后回退：使用保守估计
            return await self._get_default_volatility()

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"获取真实市场波动率失败: {e}")
            return await self._get_default_volatility()

    async def _get_implied_volatility_from_options(self) -> float | None:
        """从期权市场获取隐含波动率"""
        try:
            if not hasattr(self, "timescale_manager") or not self.timescale_manager:
                return None

            # 获取ATM期权的隐含波动率
            query = """
            SELECT AVG(implied_volatility) as avg_iv
            FROM option_data
            WHERE time >= NOW() - INTERVAL '1 hour'
            AND ABS(strike_price - underlying_price) / underlying_price < 0.02  -- ATM范围内
            AND implied_volatility > 0
            AND implied_volatility < 5  -- 过滤异常值
            """

            async with self.timescale_manager.get_connection(read_only=True) as conn:
                result = await conn.fetchrow(query)
                if result and result["avg_iv"]:
                    iv = float(result["avg_iv"])
                    if 0.05 < iv < 2.0:  # 合理范围内
                        return iv

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从期权获取隐含波动率失败: {e}")

        return None

    async def _calculate_realized_vol_from_price_history(self) -> float | None:
        """从历史价格数据计算实际波动率"""
        try:
            if not hasattr(self, "timescale_manager") or not self.timescale_manager:
                return None

            # 获取过去24小时的价格数据（每小时一个点）
            query = """
            WITH hourly_prices AS (
                SELECT
                    date_trunc('hour', time) as hour,
                    AVG(price) as price
                FROM market_data
                WHERE symbol = 'BTCUSDT'
                AND time >= NOW() - INTERVAL '24 hours'
                GROUP BY date_trunc('hour', time)
                ORDER BY hour
            )
            SELECT array_agg(price ORDER BY hour) as prices
            FROM hourly_prices
            """

            async with self.timescale_manager.get_connection(read_only=True) as conn:
                result = await conn.fetchrow(query)
                if result and result["prices"] and len(result["prices"]) > 10:
                    prices = [float(p) for p in result["prices"]]

                    # 计算对数收益率
                    import math

                    returns = [
                        math.log(prices[i] / prices[i - 1])
                        for i in range(1, len(prices))
                    ]

                    # 计算年化波动率
                    import numpy as np

                    std_dev = np.std(returns)
                    annualized_vol = std_dev * math.sqrt(365 * 24)  # 年化

                    if 0.01 < annualized_vol < 5.0:  # 合理范围
                        return float(annualized_vol)

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从历史价格计算波动率失败: {e}")

        return None

    async def _get_volatility_index(self) -> float | None:
        """获取波动率指数数据"""
        try:
            # 这里可以集成外部波动率指数API，比如：
            # - CBOE的VIX (如果有的话)
            # - 加密货币波动率指数
            # - 其他第三方波动率数据源

            # 当前返回None，表示该功能暂未实现
            return None

        except Exception:
            return None

    async def _estimate_volatility_from_market_regime(self) -> float | None:
        """基于市场状况动态估计波动率"""
        try:
            if not hasattr(self, "timescale_manager") or not self.timescale_manager:
                return None

            # 获取最近的市场状况指标
            query = """
            SELECT
                AVG(CASE WHEN price IS NOT NULL THEN ABS((price - LAG(price) OVER (ORDER BY time)) / LAG(price) OVER (ORDER BY time)) END) as avg_price_change,
                COUNT(*) as data_points
            FROM market_data
            WHERE symbol = 'BTCUSDT'
            AND time >= NOW() - INTERVAL '6 hours'
            """

            async with self.timescale_manager.get_connection(read_only=True) as conn:
                result = await conn.fetchrow(query)
                if result and result["avg_price_change"] and result["data_points"] > 50:
                    avg_change = float(result["avg_price_change"])

                    # 基于平均价格变动估算波动率
                    # 这是一个简化的估计方法
                    import math

                    estimated_vol = avg_change * math.sqrt(365 * 24 * 6)  # 粗略年化

                    # 应用合理的波动率范围
                    estimated_vol = max(0.1, min(estimated_vol, 3.0))
                    return estimated_vol

        except Exception as e:
            if self.logger:
                await self.logger.debug(f"从市场状况估计波动率失败: {e}")

        return None

    async def _get_default_volatility(self) -> float | None:
        """严格模式：不提供默认波动率，返回None"""
        return None
