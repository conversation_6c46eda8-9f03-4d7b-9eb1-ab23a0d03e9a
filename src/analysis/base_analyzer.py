"""
分析器基类
提供统一的分析器框架，减少重复代码
"""

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.config_manager import Config<PERSON>anager
from src.core.event_bus import EventBus
from src.data.cache_manager import CacheManager


@dataclass
class AnalyzerMetrics:
    """分析器通用指标基类"""

    # 分析器特有指标
    total_signals: int = 0
    successful_signals: int = 0
    false_positives: int = 0
    avg_strength: float = 0.0
    avg_confidence: float = 0.0
    last_signal_time: datetime | None = None

    # BaseComponent期望的指标
    error_count: int = 0
    warning_count: int = 0
    restart_count: int = 0
    start_time: datetime | None = None
    last_health_check: datetime | None = None
    uptime_seconds: float = 0.0
    custom_metrics: dict[str, Any] = None

    def __post_init__(self):
        if self.custom_metrics is None:
            self.custom_metrics = {}

    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_signals == 0:
            return 0.0
        return self.successful_signals / self.total_signals

    @property
    def false_positive_rate(self) -> float:
        """误报率"""
        if self.total_signals == 0:
            return 0.0
        return self.false_positives / self.total_signals


class BaseAnalyzer(BaseComponent, ABC):
    """
    分析器基类

    提供统一的初始化、配置管理、依赖注入和生命周期管理
    """

    def __init__(self, component_name: str, config_manager: ConfigManager):
        super().__init__(component_name)
        self.config_manager = config_manager

        # 核心组件依赖 - 统一的依赖注入模式
        self.event_bus: EventBus | None = None
        self.cache_manager: CacheManager | None = None
        self.timescale_manager = None  # 用于访问历史数据

        # 分析器配置
        self.config = self._load_analyzer_config()

        # 通用指标
        self.metrics = AnalyzerMetrics()

        # 状态管理
        self._analysis_tasks: list[asyncio.Task] = []
        self._is_analyzing = False

    def _load_analyzer_config(self) -> dict[str, Any]:
        """加载分析器配置，提供统一的默认配置框架"""
        analyzer_name = self.component_name.lower().replace("analyzer", "")

        # 基础默认配置（所有分析器共用）
        default_config = {
            # 基础分析参数
            "enabled": True,
            "analysis_interval": 60,  # 分析间隔（秒）
            "lookback_window": 20,  # 回看窗口大小
            "min_data_points": 5,  # 最小数据点数量
            # 信号生成参数
            "signal_cooldown_minutes": 30,  # 信号冷却时间（分钟）
            "confidence_threshold": 0.6,  # 最小置信度阈值
            "strength_threshold": 0.5,  # 最小信号强度阈值
            # 数据质量参数
            "min_volume_threshold": 1000,  # 最小成交量阈值
            "max_latency_ms": 200,  # 最大数据延迟（毫秒）
            "data_quality_threshold": "medium",  # 最低数据质量要求
            # 性能参数
            "max_concurrent_tasks": 10,  # 最大并发任务数
            "cache_ttl": 300,  # 缓存过期时间（秒）
            "enable_caching": True,  # 是否启用缓存
            # 错误处理参数
            "max_retries": 3,  # 最大重试次数
            "retry_delay": 1.0,  # 重试延迟（秒）
            "error_threshold": 5,  # 错误阈值
        }

        # 合并默认配置和特定配置
        specific_config = self.config_manager.get(f"analysis.{analyzer_name}", {})
        default_config.update(specific_config)

        # 允许子类定制默认配置
        custom_defaults = self._get_analyzer_defaults()
        if custom_defaults:
            default_config.update(custom_defaults)

        return default_config

    def _get_analyzer_defaults(self) -> dict[str, Any]:
        """子类可重写此方法提供特定的默认配置"""
        return {}

    def _validate_config(self) -> bool:
        """验证配置的有效性"""
        try:
            # 验证基础配置
            required_keys = ["enabled", "analysis_interval", "lookback_window"]
            for key in required_keys:
                if key not in self.config:
                    if self.logger:
                        asyncio.create_task(
                            self.logger.error(f"Missing required config key: {key}")
                        )
                    return False

            # 验证数值范围
            if self.config.get("analysis_interval", 0) <= 0:
                if self.logger:
                    asyncio.create_task(
                        self.logger.error("analysis_interval must be positive")
                    )
                return False

            if self.config.get("lookback_window", 0) <= 0:
                if self.logger:
                    asyncio.create_task(
                        self.logger.error("lookback_window must be positive")
                    )
                return False

            # 验证阈值范围
            confidence_threshold = self.config.get("confidence_threshold", 0.6)
            if not (0.0 <= confidence_threshold <= 1.0):
                if self.logger:
                    asyncio.create_task(
                        self.logger.error(
                            "confidence_threshold must be between 0.0 and 1.0"
                        )
                    )
                return False

            return True

        except Exception as e:
            if self.logger:
                asyncio.create_task(self.logger.error(f"Config validation error: {e}"))
            return False

    def get_config_value(self, key: str, default: Any = None) -> Any:
        """安全获取配置值"""
        return self.config.get(key, default)

    def is_enabled(self) -> bool:
        """检查分析器是否启用"""
        return self.config.get("enabled", True)

    def get_cache_key(self, *args) -> str:
        """生成标准化的缓存键"""
        base_key = f"{self.component_name.lower()}"
        if args:
            arg_str = "_".join(str(arg) for arg in args)
            return f"{base_key}:{arg_str}"
        return base_key

    async def _initialize_impl(self) -> bool:
        """BaseComponent抽象方法实现"""
        try:
            # 验证配置
            if self.logger:
                await self.logger.debug(
                    f"BaseAnalyzer: {self.component_name} 开始验证配置"
                )
            config_valid = self._validate_config()
            if self.logger:
                await self.logger.debug(
                    f"BaseAnalyzer: {self.component_name} 配置验证结果: {config_valid}"
                )
            if not config_valid:
                if self.logger:
                    await self.logger.error(f"{self.component_name}: 配置验证失败")
                else:
                    print(f"❌ BaseAnalyzer: {self.component_name}: 配置验证失败")
                return False

            # 验证必需的依赖
            if self.logger:
                await self.logger.debug(
                    f"BaseAnalyzer: {self.component_name} 开始验证依赖"
                )
            deps_valid = await self._validate_dependencies()
            if self.logger:
                await self.logger.debug(
                    f"BaseAnalyzer: {self.component_name} 依赖验证结果: {deps_valid}"
                )
            if not deps_valid:
                if self.logger:
                    await self.logger.error(f"{self.component_name}: 依赖验证失败")
                else:
                    print(f"❌ BaseAnalyzer: {self.component_name}: 依赖验证失败")
                return False

            # 执行子类特定的初始化
            if self.logger:
                await self.logger.debug(
                    f"BaseAnalyzer: 调用 {self.component_name}._initialize_analyzer()"
                )
            analyzer_result = await self._initialize_analyzer()
            if self.logger:
                await self.logger.debug(
                    f"BaseAnalyzer: {self.component_name}._initialize_analyzer() 返回: {analyzer_result}"
                )
            if not analyzer_result:
                if self.logger:
                    await self.logger.error(f"{self.component_name}: 分析器初始化失败")
                else:
                    print(f"❌ BaseAnalyzer: {self.component_name}: 分析器初始化失败")
                return False

            if self.logger:
                await self.logger.info(f"✅ {self.component_name} 初始化成功")
            return True

        except Exception as e:
            await self.logger.error(f"{self.component_name} 初始化失败: {e}")
            return False

    async def _start_impl(self) -> bool:
        """BaseComponent抽象方法实现"""
        try:
            # 启动分析任务
            self._is_analyzing = True

            # 启动子类特定的分析任务
            analysis_task = asyncio.create_task(self._run_analysis_loop())
            self._analysis_tasks.append(analysis_task)

            if self.logger:
                await self.logger.info(f"{self.component_name} 已启动")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"{self.component_name} 启动失败: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """BaseComponent抽象方法实现"""
        try:
            self._is_analyzing = False

            # 取消所有分析任务
            for task in self._analysis_tasks:
                if not task.done():
                    task.cancel()

            # 等待任务完成
            if self._analysis_tasks:
                await asyncio.gather(*self._analysis_tasks, return_exceptions=True)

            self._analysis_tasks.clear()

            if self.logger:
                await self.logger.info(f"⏹️ {self.component_name} 已停止")
            else:
                print(f"⏹️ BaseAnalyzer: {self.component_name} 已停止")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"{self.component_name} 停止失败: {e}")
            else:
                print(f"❌ BaseAnalyzer: {self.component_name} 停止失败: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """BaseComponent抽象方法实现"""
        try:
            issues = []

            # 检查基本状态 - 改为更宽松的检查
            if not self._is_analyzing:
                # 如果组件已初始化但未启动分析，标记为DEGRADED而不是UNHEALTHY
                if self._state == ComponentState.INITIALIZED:
                    issues.append("分析器已初始化但未启动")
                else:
                    issues.append("分析器未在运行")

            # 检查依赖是否健康 - 改为警告而不是错误
            if not await self._check_dependencies_health():
                issues.append("部分依赖不健康")

            # 执行子类特定的健康检查
            analyzer_health = await self._check_analyzer_health()
            if not analyzer_health:
                issues.append("分析器特定检查失败")

            # 根据问题数量确定健康状态
            if not issues:
                return HealthCheckResult(
                    status=HealthStatus.HEALTHY,
                    message=f"{self.component_name} 运行正常"
                )
            elif len(issues) == 1 and "已初始化但未启动" in issues[0]:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"{self.component_name} 已初始化但未完全启动",
                    details={"issues": issues}
                )
            elif len(issues) <= 2:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED,
                    message=f"{self.component_name} 部分功能异常: {', '.join(issues)}",
                    details={"issues": issues}
                )
            else:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message=f"{self.component_name} 多项功能异常: {', '.join(issues)}",
                    details={"issues": issues}
                )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"{self.component_name} 健康检查异常: {e}",
            )

    # 抽象方法，子类必须实现
    @abstractmethod
    async def _initialize_analyzer(self) -> bool:
        """子类特定的初始化逻辑"""
        pass

    @abstractmethod
    async def _run_analysis_loop(self):
        """主要的分析循环"""
        pass

    # 可选覆盖的方法
    async def _validate_dependencies(self) -> bool:
        """验证依赖，子类可重写"""
        # 只验证必需的依赖，其他依赖可以异步注入
        if self.config_manager is None:
            if self.logger:
                await self.logger.error(
                    f"{self.component_name}: ConfigManager 依赖缺失"
                )
            return False

        # 尝试从依赖注入器获取 EventBus
        if self.event_bus is None:
            if hasattr(self, "dependency_injector") and self.dependency_injector:
                try:
                    from src.core.event_bus import EventBus

                    self.event_bus = await self.dependency_injector.resolve(EventBus)
                    if self.logger:
                        await self.logger.info(
                            f"{self.component_name}: EventBus 已从依赖注入器获取"
                        )
                except Exception:
                    if self.logger:
                        await self.logger.debug(
                            f"{self.component_name}: EventBus 未从依赖注入器获取，将在运行时获取"
                        )
            else:
                if self.logger:
                    await self.logger.debug(
                        f"{self.component_name}: EventBus 未注入，将在运行时获取"
                    )

        # 尝试从依赖注入器获取 CacheManager
        if self.cache_manager is None:
            if hasattr(self, "dependency_injector") and self.dependency_injector:
                try:
                    from src.data.cache_manager import CacheManager

                    self.cache_manager = await self.dependency_injector.resolve(
                        CacheManager
                    )
                    if self.logger:
                        await self.logger.info(
                            f"{self.component_name}: CacheManager 已从依赖注入器获取"
                        )
                except Exception:
                    if self.logger:
                        await self.logger.warning(
                            f"{self.component_name}: CacheManager 未注入，将在运行时获取"
                        )
            else:
                if self.logger:
                    await self.logger.warning(
                        f"{self.component_name}: CacheManager 未注入，将在运行时获取"
                    )

        return True

    async def _check_dependencies_health(self) -> bool:
        """检查依赖健康状态"""
        try:
            if self.cache_manager and hasattr(self.cache_manager, "health_check"):
                cache_health = await self.cache_manager.health_check()
                if cache_health.status != HealthStatus.HEALTHY:
                    return False
            return True
        except Exception:
            return False

    async def _check_analyzer_health(self) -> bool:
        """分析器特定的健康检查，子类可重写"""
        return True

    def get_metrics(self) -> dict[str, Any]:
        """获取分析器指标"""
        return {
            "total_signals": self.metrics.total_signals,
            "successful_signals": self.metrics.successful_signals,
            "false_positives": self.metrics.false_positives,
            "success_rate": self.metrics.success_rate,
            "false_positive_rate": self.metrics.false_positive_rate,
            "avg_strength": self.metrics.avg_strength,
            "avg_confidence": self.metrics.avg_confidence,
            "last_signal_time": self.metrics.last_signal_time,
            "is_analyzing": self._is_analyzing,
        }
