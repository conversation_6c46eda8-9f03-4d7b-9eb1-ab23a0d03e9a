from __future__ import annotations

from dataclasses import dataclass
from datetime import UTC, datetime
from typing import Any

import numpy as np

try:
    from numba import njit

    def _jit(fn):
        return njit(cache=True, nogil=True, fastmath=True)

    NUMBA_AVAILABLE = True
except Exception:  # pragma: no cover

    def _jit(fn):
        return fn

    NUMBA_AVAILABLE = False


@dataclass
class SurfaceConfig:
    expiry_days: list[int] | tuple[int, ...] = (7, 30, 90)
    use_delta_buckets: bool = True
    # delta breakpoints for call/put in absolute value order (low->high)
    delta_breaks: list[float] | tuple[float, ...] = (0.1, 0.25, 0.4)
    # optional ks bands if delta missing: ATM band width
    ks_atm_band: float = 0.03
    weight: str = "oi"  # oi | volume | count


class OptionSurfaceAggregator:
    def __init__(self, config: SurfaceConfig | None = None):
        self.cfg = config or SurfaceConfig()
        eb = [self.cfg.expiry_days[0], self.cfg.expiry_days[1], self.cfg.expiry_days[2]]
        self.expiry_edges = np.array([eb[0], eb[1], eb[2]], dtype=np.float64)
        # bucket names
        self.expiry_names = np.array(["near", "mid", "far", "ultra"], dtype=object)
        self.moneyness_names = np.array(
            [
                "call_10d",
                "call_25d",
                "call_atm",
                "put_atm",
                "put_25d",
                "put_10d",
            ],
            dtype=object,
        )
        # mapping from (e_idx, m_idx) -> linear bucket index
        self.E = len(self.expiry_names)
        self.M = len(self.moneyness_names)
        self.N = self.E * self.M

        # aggregates arrays
        self.w = np.zeros(self.N, dtype=np.float64)
        self.ivw = np.zeros(self.N, dtype=np.float64)
        self.oi = np.zeros(self.N, dtype=np.float64)
        self.d_exp = np.zeros(self.N, dtype=np.float64)
        self.g_exp = np.zeros(self.N, dtype=np.float64)
        self.v_exp = np.zeros(self.N, dtype=np.float64)
        self.mw = np.zeros(self.N, dtype=np.float64)

        # previous snapshot for deltas
        self._prev = None

        # instrument state: inst -> dict{idx, iv, delta, gamma, vega, mark, oi, spot}
        self._inst: dict[str, dict[str, float | int]] = {}

    def _expiry_bucket(self, expiry: datetime, now: datetime) -> int:
        days = (expiry - now).total_seconds() / 86400.0
        if days <= self.expiry_edges[0]:
            return 0
        if days <= self.expiry_edges[1]:
            return 1
        if days <= self.expiry_edges[2]:
            return 2
        return 3

    def _moneyness_bucket(
        self,
        opt_type: str,
        strike: float | None,
        spot: float | None,
        delta: float | None,
    ) -> int:
        if delta is not None:
            d = float(delta)
            if opt_type.lower().startswith("c"):
                if d >= self.cfg.delta_breaks[2]:
                    return 2  # call_atm
                elif d >= self.cfg.delta_breaks[1]:
                    return 1  # call_25d
                else:
                    return 0  # call_10d
            else:
                if d <= -self.cfg.delta_breaks[2]:
                    return 3  # put_atm
                elif d <= -self.cfg.delta_breaks[1]:
                    return 4  # put_25d
                else:
                    return 5  # put_10d
        # fallback K/S
        if strike and spot and spot > 0:
            ks = float(strike) / float(spot)
            if opt_type.lower().startswith("c"):
                if abs(ks - 1.0) <= self.cfg.ks_atm_band:
                    return 2
                elif ks < 1.0 - self.cfg.ks_atm_band:
                    return 1
                else:
                    return 0
            else:
                if abs(ks - 1.0) <= self.cfg.ks_atm_band:
                    return 3
                elif ks > 1.0 + self.cfg.ks_atm_band:
                    return 4
                else:
                    return 5
        return 2 if opt_type.lower().startswith("c") else 3

    def _lin(self, e_idx: int, m_idx: int) -> int:
        return e_idx * self.M + m_idx

    def update_option(
        self, option_event: dict[str, Any], event_ts: datetime, spot_price: float | None
    ) -> None:
        inst = option_event.get("instrument_name") or option_event.get("symbol")
        if not inst:
            return
        opt_type = option_event.get("option_type") or (
            "call"
            if str(inst).endswith("-C")
            else ("put" if str(inst).endswith("-P") else "?")
        )
        strike = option_event.get("strike")
        # expiry parse (best-effort)
        expiry = event_ts
        exp_s = option_event.get("expiry")
        if isinstance(exp_s, str):
            from datetime import datetime as _dt

            for fmt in ("%Y-%m-%dT%H:%M:%S", "%Y-%m-%d %H:%M:%S", "%Y-%m-%d"):
                try:
                    expiry = _dt.strptime(exp_s, fmt).replace(tzinfo=UTC)
                    break
                except Exception:
                    continue

        iv = _to_float(option_event.get("iv") or option_event.get("mark_iv"))
        delta = _to_float(
            option_event.get("delta") or option_event.get("greeks", {}).get("delta")
        )
        gamma = _to_float(
            option_event.get("gamma") or option_event.get("greeks", {}).get("gamma")
        )
        vega = _to_float(
            option_event.get("vega") or option_event.get("greeks", {}).get("vega")
        )
        mark = _to_float(
            option_event.get("mark_price") or option_event.get("last_price")
        )
        if self.cfg.weight == "oi":
            w = _to_float(option_event.get("open_interest") or 0.0)
        elif self.cfg.weight == "volume":
            w = _to_float(option_event.get("volume") or 0.0)
        else:
            w = 1.0

        now = event_ts
        e_idx = self._expiry_bucket(expiry, now)
        m_idx = self._moneyness_bucket(opt_type, strike, spot_price, delta)
        b_idx = self._lin(e_idx, m_idx)

        old = self._inst.get(inst)
        if old is not None and old.get("b_idx") != b_idx:
            self._apply_contrib(
                int(old["b_idx"]),
                -1.0,
                float(old["w"]),
                float(old["iv"]),
                float(old["delta"]),
                float(old["gamma"]),
                float(old["vega"]),
                float(old["mark"]),
                float(old["spot"]),
            )

        st = {
            "b_idx": b_idx,
            "w": w,
            "iv": iv,
            "delta": delta,
            "gamma": gamma,
            "vega": vega,
            "mark": mark,
            "spot": _to_float(spot_price) if spot_price is not None else 0.0,
        }
        self._inst[inst] = st
        self._apply_contrib(b_idx, 1.0, w, iv, delta, gamma, vega, mark, st["spot"])

    @_jit
    def _apply_contrib(
        self,
        b_idx: int,
        sgn: float,
        w: float,
        iv: float,
        delta: float,
        gamma: float,
        vega: float,
        mark: float,
        spot: float,
    ):
        self.w[b_idx] += sgn * w
        self.ivw[b_idx] += sgn * w * iv
        self.oi[b_idx] += sgn * w
        self.d_exp[b_idx] += sgn * delta * w
        self.g_exp[b_idx] += sgn * gamma * (spot**2) * w
        self.v_exp[b_idx] += sgn * vega * w
        self.mw[b_idx] += sgn * w * mark

    def snapshot_features(self, spot_price: float) -> dict[str, dict[str, float]]:
        iv_vwap = np.divide(self.ivw, np.maximum(self.w, 1e-12))
        mark_vwap = np.divide(self.mw, np.maximum(self.w, 1e-12))
        cur = np.stack(
            [iv_vwap, self.oi, self.d_exp, self.g_exp, self.v_exp, mark_vwap], axis=1
        )
        d = np.zeros_like(cur) if self._prev is None else cur - self._prev
        self._prev = cur.copy()

        out: dict[str, dict[str, float]] = {}
        for e in range(self.E):
            for m in range(self.M):
                idx = self._lin(e, m)
                key = f"{self.expiry_names[e]}_{self.moneyness_names[m]}"
                out[key] = {
                    "iv_vwap": float(cur[idx, 0]),
                    "oi_sum": float(cur[idx, 1]),
                    "delta_exposure": float(cur[idx, 2]),
                    "gamma_exposure": float(cur[idx, 3]),
                    "vega_exposure": float(cur[idx, 4]),
                    "mark_vwap": float(cur[idx, 5]),
                    "d_iv": float(d[idx, 0]),
                    "d_oi": float(d[idx, 1]),
                    "d_gex": float(d[idx, 3]),
                    "d_vega": float(d[idx, 4]),
                }
        return out


def _to_float(x) -> float:
    try:
        if x is None:
            return 0.0
        return float(x)
    except Exception:
        return 0.0
