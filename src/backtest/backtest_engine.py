"""
完整回测引擎

提供历史数据回放、模拟交易执行、策略运行和性能分析功能
"""

import json
import logging
from collections import defaultdict
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from decimal import Decimal
from pathlib import Path
from typing import Any

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

from src.core.base_component import BaseComponent, HealthCheckResult
from src.core.event_bus import EventBus


@dataclass
class BacktestConfig:
    """回测配置"""

    start_date: datetime
    end_date: datetime
    initial_capital: Decimal = Decimal("100000")

    # 交易成本
    commission_rate: Decimal = Decimal("0.002")  # 0.2%手续费（适合加密货币期权）
    slippage_rate: Decimal = Decimal("0.001")  # 0.1%滑点（适合加密货币市场）

    # 数据配置
    data_frequency: str = "1m"  # 1m, 5m, 1h, 1d
    symbols: list[str] = field(default_factory=list)

    # 策略配置
    strategy_params: dict[str, Any] = field(default_factory=dict)

    # 风险配置
    max_position_size: Decimal = Decimal("10000")
    max_daily_loss: Decimal = Decimal("5000")

    # 输出配置
    save_trades: bool = True
    save_positions: bool = True
    save_metrics: bool = True
    generate_plots: bool = True


@dataclass
class Trade:
    """交易记录"""

    timestamp: datetime
    symbol: str
    side: str  # buy/sell
    quantity: Decimal
    price: Decimal
    commission: Decimal
    trade_id: str
    strategy_id: str = ""
    pnl: Decimal = Decimal("0")


@dataclass
class Position:
    """仓位记录"""

    timestamp: datetime
    symbol: str
    quantity: Decimal
    avg_price: Decimal
    market_value: Decimal
    unrealized_pnl: Decimal
    strategy_id: str = ""


@dataclass
class PerformanceMetrics:
    """性能指标"""

    # 收益指标
    total_return: float = 0.0
    annualized_return: float = 0.0
    cumulative_return: float = 0.0

    # 风险指标
    volatility: float = 0.0
    max_drawdown: float = 0.0
    max_drawdown_duration: int = 0
    var_95: float = 0.0
    var_99: float = 0.0

    # 风险调整收益
    sharpe_ratio: float = 0.0
    sortino_ratio: float = 0.0
    calmar_ratio: float = 0.0

    # 交易统计
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    avg_win: float = 0.0
    avg_loss: float = 0.0

    # 其他指标
    beta: float = 0.0
    alpha: float = 0.0
    information_ratio: float = 0.0


class MockExchange:
    """模拟交易所"""

    def __init__(self, config: BacktestConfig):
        self.config = config
        self.cash = config.initial_capital
        self.positions: dict[str, Decimal] = defaultdict(lambda: Decimal("0"))
        self.avg_costs: dict[str, Decimal] = defaultdict(lambda: Decimal("0"))
        self.trades: list[Trade] = []
        self.current_prices: dict[str, Decimal] = {}
        self.trade_counter = 0

        self.logger = logging.getLogger(__name__)

    async def submit_order(
        self,
        symbol: str,
        side: str,
        quantity: Decimal,
        order_type: str = "market",
        price: Decimal | None = None,
    ) -> Trade:
        """提交订单"""
        try:
            # 获取执行价格
            if order_type == "market":
                execution_price = self.current_prices.get(symbol, Decimal("0"))
                if execution_price <= 0:
                    raise ValueError(f"No market price available for {symbol}")
            else:
                execution_price = price or self.current_prices.get(symbol, Decimal("0"))

            # 应用滑点
            slippage = execution_price * self.config.slippage_rate
            if side == "buy":
                execution_price += slippage
            else:
                execution_price -= slippage

            # 计算手续费
            commission = execution_price * quantity * self.config.commission_rate

            # 检查资金充足性
            if side == "buy":
                required_cash = execution_price * quantity + commission
                if required_cash > self.cash:
                    raise ValueError(
                        f"Insufficient cash: required {required_cash}, available {self.cash}"
                    )
                self.cash -= required_cash
            else:
                # 检查仓位充足性
                if quantity > self.positions[symbol]:
                    raise ValueError(
                        f"Insufficient position: required {quantity}, available {self.positions[symbol]}"
                    )
                self.cash += execution_price * quantity - commission

            # 计算成交后的仓位与平均成本，并计算已实现PnL（严格按成交）
            realized_pnl = Decimal("0")
            prev_qty = self.positions[symbol]
            prev_avg = self.avg_costs[symbol]

            if side == "buy":
                new_qty = prev_qty + quantity
                # 加权平均成本
                if new_qty > 0:
                    self.avg_costs[symbol] = (
                        prev_avg * prev_qty + execution_price * quantity
                    ) / new_qty
                self.positions[symbol] = new_qty
                # 开仓/加仓不产生已实现PnL（手续费已反映在现金中）
                realized_pnl = Decimal("0")
            else:
                # 卖出平仓，计算已实现PnL
                if quantity > prev_qty:
                    raise ValueError(
                        f"Insufficient position: required {quantity}, available {prev_qty}"
                    )
                realized_pnl = (execution_price - prev_avg) * quantity - commission
                self.positions[symbol] = prev_qty - quantity
                if self.positions[symbol] == 0:
                    self.avg_costs[symbol] = Decimal("0")

            # 创建交易记录（包含严格计算的PnL）
            self.trade_counter += 1
            trade = Trade(
                timestamp=datetime.now(UTC),
                symbol=symbol,
                side=side,
                quantity=quantity,
                price=execution_price,
                commission=commission,
                trade_id=f"trade_{self.trade_counter}",
                pnl=realized_pnl,
            )

            self.trades.append(trade)
            return trade

        except Exception as e:
            self.logger.error(f"Order submission failed: {e}")
            raise

    def update_prices(self, prices: dict[str, Decimal]):
        """更新价格"""
        self.current_prices.update(prices)

    def get_portfolio_value(self) -> Decimal:
        """获取组合价值"""
        portfolio_value = self.cash

        for symbol, quantity in self.positions.items():
            if quantity > 0:
                price = self.current_prices.get(symbol, Decimal("0"))
                portfolio_value += quantity * price

        return portfolio_value

    def get_positions(self) -> dict[str, Decimal]:
        """获取仓位"""
        return dict(self.positions)

    def get_cash(self) -> Decimal:
        """获取现金"""
        return self.cash

    def get_avg_cost(self, symbol: str) -> Decimal:
        """获取平均持仓成本"""
        return self.avg_costs.get(symbol, Decimal("0"))


class StrategyRunner:
    """策略运行器"""

    def __init__(self, strategy_class, strategy_params: dict[str, Any]):
        self.strategy_class = strategy_class
        self.strategy_params = strategy_params
        self.strategy_instance = None

        self.logger = logging.getLogger(__name__)

    async def initialize(self, exchange: MockExchange, event_bus: EventBus):
        """初始化策略"""
        try:
            self.strategy_instance = self.strategy_class(self.strategy_params)

            # 设置策略依赖
            if hasattr(self.strategy_instance, "set_exchange"):
                self.strategy_instance.set_exchange(exchange)

            if hasattr(self.strategy_instance, "set_event_bus"):
                self.strategy_instance.set_event_bus(event_bus)

            # 初始化策略
            if hasattr(self.strategy_instance, "initialize"):
                await self.strategy_instance.initialize()

            self.logger.info("Strategy initialized successfully")

        except Exception as e:
            self.logger.error(f"Strategy initialization failed: {e}")
            raise

    async def on_market_data(self, data: dict[str, Any]):
        """处理市场数据"""
        try:
            if self.strategy_instance and hasattr(
                self.strategy_instance, "on_market_data"
            ):
                await self.strategy_instance.on_market_data(data)
        except Exception as e:
            self.logger.error(f"Strategy market data handling failed: {e}")

    async def on_trade_execution(self, trade: Trade):
        """处理交易执行"""
        try:
            if self.strategy_instance and hasattr(
                self.strategy_instance, "on_trade_execution"
            ):
                await self.strategy_instance.on_trade_execution(trade)
        except Exception as e:
            self.logger.error(f"Strategy trade execution handling failed: {e}")


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def calculate_metrics(
        self,
        portfolio_values: list[Decimal],
        trades: list[Trade],
        benchmark_returns: list[float] | None = None,
    ) -> PerformanceMetrics:
        """计算性能指标"""
        try:
            _ = benchmark_returns  # reserved for future comparison metrics
            if len(portfolio_values) < 2:
                return PerformanceMetrics()

            # 转换为numpy数组
            values = np.array([float(v) for v in portfolio_values])
            returns = np.diff(values) / values[:-1]

            # 基础收益指标
            total_return = (values[-1] - values[0]) / values[0]
            cumulative_return = values[-1] / values[0] - 1

            # 年化收益率
            trading_days = len(values)
            years = trading_days / 252  # 假设252个交易日
            annualized_return = (
                (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
            )

            # 波动率
            volatility = np.std(returns) * np.sqrt(252)

            # 最大回撤
            peak = np.maximum.accumulate(values)
            drawdown = (values - peak) / peak
            max_drawdown = np.min(drawdown)

            # 最大回撤持续期
            max_dd_duration = self._calculate_max_drawdown_duration(drawdown)

            # VaR计算
            var_95 = np.percentile(returns, 5) if len(returns) > 0 else 0
            var_99 = np.percentile(returns, 1) if len(returns) > 0 else 0

            # 风险调整收益
            risk_free_rate = 0.02  # 2%无风险利率
            excess_returns = returns - risk_free_rate / 252

            sharpe_ratio = (
                np.mean(excess_returns) / np.std(returns) * np.sqrt(252)
                if np.std(returns) > 0
                else 0
            )

            # Sortino比率
            downside_returns = returns[returns < 0]
            downside_std = np.std(downside_returns) if len(downside_returns) > 0 else 0
            sortino_ratio = (
                np.mean(excess_returns) / downside_std * np.sqrt(252)
                if downside_std > 0
                else 0
            )

            # Calmar比率
            calmar_ratio = (
                annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
            )

            # 交易统计
            trade_metrics = self._calculate_trade_metrics(trades)

            return PerformanceMetrics(
                total_return=total_return,
                annualized_return=annualized_return,
                cumulative_return=cumulative_return,
                volatility=volatility,
                max_drawdown=max_drawdown,
                max_drawdown_duration=max_dd_duration,
                var_95=var_95,
                var_99=var_99,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                calmar_ratio=calmar_ratio,
                **trade_metrics,
            )

        except Exception as e:
            self.logger.error(f"Performance calculation failed: {e}")
            return PerformanceMetrics()

    def _calculate_max_drawdown_duration(self, drawdown: np.ndarray) -> int:
        """计算最大回撤持续期"""
        in_drawdown = drawdown < 0
        durations = []
        current_duration = 0

        for is_dd in in_drawdown:
            if is_dd:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                current_duration = 0

        if current_duration > 0:
            durations.append(current_duration)

        return max(durations) if durations else 0

    def _calculate_trade_metrics(self, trades: list[Trade]) -> dict[str, Any]:
        """计算交易指标"""
        if not trades:
            return {
                "total_trades": 0,
                "winning_trades": 0,
                "losing_trades": 0,
                "win_rate": 0.0,
                "profit_factor": 0.0,
                "avg_win": 0.0,
                "avg_loss": 0.0,
            }

        # 计算每笔交易的PnL（严格基于成交记录中的pnl）
        winning_trades = 0
        losing_trades = 0
        total_profit = 0.0
        total_loss = 0.0

        for trade in trades:
            pnl = float(trade.pnl)
            if pnl > 0:
                winning_trades += 1
                total_profit += pnl
            elif pnl < 0:
                losing_trades += 1
                total_loss += abs(pnl)

        total_trades = len(trades)
        win_rate = winning_trades / total_trades if total_trades > 0 else 0
        profit_factor = total_profit / total_loss if total_loss > 0 else 0
        avg_win = total_profit / winning_trades if winning_trades > 0 else 0
        avg_loss = total_loss / losing_trades if losing_trades > 0 else 0

        return {
            "total_trades": total_trades,
            "winning_trades": winning_trades,
            "losing_trades": losing_trades,
            "win_rate": win_rate,
            "profit_factor": profit_factor,
            "avg_win": avg_win,
            "avg_loss": avg_loss,
        }


class ReportGenerator:
    """回测报告生成器"""

    def __init__(self, output_dir: str = "backtest_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        self.logger = logging.getLogger(__name__)

    def generate_report(
        self,
        config: BacktestConfig,
        metrics: PerformanceMetrics,
        portfolio_values: list[Decimal],
        trades: list[Trade],
        positions: list[Position],
    ) -> dict[str, Any]:
        """生成完整回测报告"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # 生成JSON报告
            report_data = {
                "config": self._config_to_dict(config),
                "metrics": self._metrics_to_dict(metrics),
                "summary": self._generate_summary(metrics),
                "portfolio_values": [float(v) for v in portfolio_values],
                "trades": [self._trade_to_dict(t) for t in trades]
                if config.save_trades
                else [],
                "positions": [self._position_to_dict(p) for p in positions]
                if config.save_positions
                else [],
                "timestamp": timestamp,
            }

            # 保存JSON报告
            json_file = self.output_dir / f"backtest_report_{timestamp}.json"
            with open(json_file, "w") as f:
                json.dump(report_data, f, indent=2, default=str)

            # 生成图表
            if config.generate_plots:
                self._generate_plots(portfolio_values, trades, timestamp)

            self.logger.info(f"Backtest report generated: {json_file}")
            return report_data

        except Exception as e:
            self.logger.error(f"Report generation failed: {e}")
            return {}

    def _config_to_dict(self, config: BacktestConfig) -> dict[str, Any]:
        """配置转字典"""
        return {
            "start_date": config.start_date.isoformat(),
            "end_date": config.end_date.isoformat(),
            "initial_capital": float(config.initial_capital),
            "commission_rate": float(config.commission_rate),
            "slippage_rate": float(config.slippage_rate),
            "data_frequency": config.data_frequency,
            "symbols": config.symbols,
            "strategy_params": config.strategy_params,
        }

    def _metrics_to_dict(self, metrics: PerformanceMetrics) -> dict[str, Any]:
        """指标转字典"""
        return {
            "total_return": metrics.total_return,
            "annualized_return": metrics.annualized_return,
            "volatility": metrics.volatility,
            "max_drawdown": metrics.max_drawdown,
            "sharpe_ratio": metrics.sharpe_ratio,
            "sortino_ratio": metrics.sortino_ratio,
            "calmar_ratio": metrics.calmar_ratio,
            "total_trades": metrics.total_trades,
            "win_rate": metrics.win_rate,
            "profit_factor": metrics.profit_factor,
        }

    def _generate_summary(self, metrics: PerformanceMetrics) -> dict[str, str]:
        """生成摘要"""
        return {
            "performance": f"Total Return: {metrics.total_return:.2%}, Sharpe: {metrics.sharpe_ratio:.2f}",
            "risk": f"Max Drawdown: {metrics.max_drawdown:.2%}, Volatility: {metrics.volatility:.2%}",
            "trading": f"Total Trades: {metrics.total_trades}, Win Rate: {metrics.win_rate:.2%}",
        }

    def _trade_to_dict(self, trade: Trade) -> dict[str, Any]:
        """交易转字典"""
        return {
            "timestamp": trade.timestamp.isoformat(),
            "symbol": trade.symbol,
            "side": trade.side,
            "quantity": float(trade.quantity),
            "price": float(trade.price),
            "commission": float(trade.commission),
            "trade_id": trade.trade_id,
        }

    def _position_to_dict(self, position: Position) -> dict[str, Any]:
        """仓位转字典"""
        return {
            "timestamp": position.timestamp.isoformat(),
            "symbol": position.symbol,
            "quantity": float(position.quantity),
            "avg_price": float(position.avg_price),
            "market_value": float(position.market_value),
            "unrealized_pnl": float(position.unrealized_pnl),
        }

    def _generate_plots(
        self, portfolio_values: list[Decimal], trades: list[Trade], timestamp: str
    ):
        """生成图表"""
        try:
            # 设置图表样式
            plt.style.use("seaborn-v0_8")
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 组合价值曲线
            values = [float(v) for v in portfolio_values]
            axes[0, 0].plot(values)
            axes[0, 0].set_title("Portfolio Value")
            axes[0, 0].set_ylabel("Value")

            # 收益率分布
            if len(values) > 1:
                returns = np.diff(values) / values[:-1]
                axes[0, 1].hist(returns, bins=50, alpha=0.7)
                axes[0, 1].set_title("Returns Distribution")
                axes[0, 1].set_xlabel("Returns")

            # 回撤曲线
            if len(values) > 1:
                peak = np.maximum.accumulate(values)
                drawdown = (values - peak) / peak
                axes[1, 0].fill_between(
                    range(len(drawdown)), drawdown, 0, alpha=0.3, color="red"
                )
                axes[1, 0].set_title("Drawdown")
                axes[1, 0].set_ylabel("Drawdown %")

            # 交易分析
            if trades:
                trade_pnl = [float(t.pnl) for t in trades if t.pnl != 0]
                if trade_pnl:
                    axes[1, 1].bar(
                        range(len(trade_pnl)),
                        trade_pnl,
                        color=["green" if pnl > 0 else "red" for pnl in trade_pnl],
                    )
                    axes[1, 1].set_title("Trade PnL")
                    axes[1, 1].set_ylabel("PnL")

            plt.tight_layout()

            # 保存图表
            plot_file = self.output_dir / f"backtest_plots_{timestamp}.png"
            plt.savefig(plot_file, dpi=300, bbox_inches="tight")
            plt.close()

            self.logger.info(f"Plots saved: {plot_file}")

        except Exception as e:
            self.logger.error(f"Plot generation failed: {e}")


class BacktestEngine(BaseComponent):
    """回测引擎"""

    def __init__(self, config: BacktestConfig):
        super().__init__("BacktestEngine", config.__dict__)
        self.config = config

        # 核心组件
        self.mock_exchange = MockExchange(config)
        self.strategy_runner: StrategyRunner | None = None
        self.performance_analyzer = PerformanceAnalyzer()
        self.report_generator = ReportGenerator()

        # 数据存储
        self.market_data: dict[str, pd.DataFrame] = {}
        self.portfolio_values: list[Decimal] = []
        self.positions_history: list[Position] = []

        self.logger = logging.getLogger(__name__)

    async def _initialize_impl(self) -> bool:
        """具体初始化实现"""
        try:
            self.logger.info("BacktestEngine initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"BacktestEngine initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """具体启动实现"""
        try:
            self.logger.info("BacktestEngine started")
            return True
        except Exception as e:
            self.logger.error(f"BacktestEngine start failed: {e}")
            return False

    async def _stop_impl(self):
        """具体停止实现"""
        self.logger.info("BacktestEngine stopped")

    async def _health_check_impl(self) -> HealthCheckResult:
        """具体健康检查实现"""
        try:
            from src.core.base_component import HealthStatus

            # 检查基本状态
            if not self.is_initialized:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY, message="Component not initialized"
                )

            # 检查数据状态
            if not self.market_data:
                return HealthCheckResult(
                    status=HealthStatus.DEGRADED, message="No market data loaded"
                )

            return HealthCheckResult(
                status=HealthStatus.HEALTHY, message="BacktestEngine operating normally"
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {e}"
            )

    async def load_data(self, data_loader: Callable) -> bool:
        """加载历史数据"""
        try:
            self.market_data = await data_loader(
                self.config.symbols,
                self.config.start_date,
                self.config.end_date,
                self.config.data_frequency,
            )

            if not self.market_data:
                raise ValueError("No market data loaded")

            self.logger.info(f"Loaded data for {len(self.market_data)} symbols")
            return True

        except Exception as e:
            self.logger.error(f"Data loading failed: {e}")
            return False

    async def run_backtest(
        self, strategy_class, strategy_params: dict[str, Any]
    ) -> dict[str, Any]:
        """运行回测"""
        try:
            self.logger.info("Starting backtest...")

            # 初始化策略运行器
            self.strategy_runner = StrategyRunner(strategy_class, strategy_params)
            await self.strategy_runner.initialize(self.mock_exchange, self.event_bus)

            # 执行回测模拟
            await self._run_simulation()

            # 计算性能指标
            metrics = self.performance_analyzer.calculate_metrics(
                self.portfolio_values, self.mock_exchange.trades
            )

            # 生成报告
            report = self.report_generator.generate_report(
                self.config,
                metrics,
                self.portfolio_values,
                self.mock_exchange.trades,
                self.positions_history,
            )

            self.logger.info(
                f"Backtest completed. Total return: {metrics.total_return:.2%}"
            )
            return report

        except Exception as e:
            self.logger.error(f"Backtest failed: {e}")
            raise

    async def _run_simulation(self):
        """运行模拟"""
        try:
            # 获取所有时间戳
            first_symbol = list(self.market_data.keys())[0]
            timestamps = self.market_data[first_symbol].index

            for timestamp in timestamps:
                # 更新市场价格
                current_prices = {}
                for symbol, data in self.market_data.items():
                    if timestamp in data.index:
                        current_prices[symbol] = Decimal(
                            str(data.loc[timestamp, "close"])
                        )

                self.mock_exchange.update_prices(current_prices)

                # 发送市场数据给策略
                if self.strategy_runner:
                    market_data = {"timestamp": timestamp, "prices": current_prices}
                    await self.strategy_runner.on_market_data(market_data)

                # 记录组合价值
                portfolio_value = self.mock_exchange.get_portfolio_value()
                self.portfolio_values.append(portfolio_value)

                # 记录仓位
                positions = self.mock_exchange.get_positions()
                for symbol, quantity in positions.items():
                    if quantity > 0:
                        price = current_prices.get(symbol, Decimal("0"))
                        avg_cost = self.mock_exchange.get_avg_cost(symbol)
                        position = Position(
                            timestamp=timestamp,
                            symbol=symbol,
                            quantity=quantity,
                            avg_price=avg_cost,
                            market_value=quantity * price,
                            unrealized_pnl=(price - avg_cost) * quantity,
                        )
                        self.positions_history.append(position)

            self.logger.info(
                f"Simulation completed with {len(self.portfolio_values)} data points"
            )

        except Exception as e:
            self.logger.error(f"Simulation failed: {e}")
            raise
