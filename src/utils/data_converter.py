"""
数据转换器 - 统一不同数据源的字段映射

解决DeribitClient.MarketData、BinanceClient.MarketData和BaseOptionData之间的字段不一致问题
"""

from dataclasses import asdict
from datetime import datetime
from decimal import Decimal

# 延迟导入以避免循环导入
from typing import TYPE_CHECKING, Any, TypeVar

from src.analysis.common_types import BaseOptionData, OptionType, UnifiedMarketData

if TYPE_CHECKING:
    # Use real types for type checkers
    from src.gateways.binance_client import BinanceMarketData as _BinanceMarketData
    from src.gateways.deribit_client import MarketData as _DeribitMarketData

    BinanceMarketData = _BinanceMarketData
    DeribitMarketData = _DeribitMarketData
else:
    # At runtime, avoid importing heavy modules; keep as Any to prevent lint errors
    BinanceMarketData = Any  # type: ignore
    DeribitMarketData = Any  # type: ignore


def safe_float_conversion(value: Any, default: float = 0.0) -> float:
    """安全的浮点数转换，处理None和空字符串"""
    if value is None or value == "" or value == "None":
        return default
    try:
        return float(value)
    except (ValueError, TypeError):
        return default


def safe_int_conversion(value: Any, default: int = 0) -> int:
    """安全的整数转换，处理None和空字符串"""
    if value is None or value == "" or value == "None":
        return default
    try:
        return int(float(value))
    except (ValueError, TypeError):
        return default


T = TypeVar("T")


class DataConverter:
    """统一数据转换器"""

    @staticmethod
    def deribit_to_unified(deribit_data: "DeribitMarketData") -> UnifiedMarketData:
        """将DeribitClient.MarketData转换为UnifiedMarketData"""
        return UnifiedMarketData(
            symbol=deribit_data.instrument_name,
            exchange="deribit",
            timestamp=deribit_data.timestamp,
            price=Decimal(str(deribit_data.mark_price)),
            bid=Decimal(str(deribit_data.bid_price)),
            ask=Decimal(str(deribit_data.ask_price)),
            volume=0,  # DeribitClient不提供当前成交量
            volume_24h=safe_int_conversion(deribit_data.volume_24h),
            strike=Decimal(str(deribit_data.strike)),
            expiry=datetime.fromisoformat(deribit_data.expiry)
            if isinstance(deribit_data.expiry, str)
            else deribit_data.expiry,
            option_type=OptionType.CALL
            if deribit_data.option_type.upper() == "C"
            else OptionType.PUT,
            open_interest=safe_int_conversion(deribit_data.open_interest),
        )

    @staticmethod
    def binance_to_unified(binance_data: "BinanceMarketData") -> UnifiedMarketData:
        """将BinanceMarketData转换为UnifiedMarketData"""
        return UnifiedMarketData(
            symbol=binance_data.symbol,
            exchange="binance",
            timestamp=binance_data.timestamp,
            price=binance_data.price,
            bid=binance_data.price * Decimal("0.999"),  # 估算买价
            ask=binance_data.price * Decimal("1.001"),  # 估算卖价
            volume=safe_int_conversion(binance_data.volume),
            volume_24h=safe_int_conversion(
                binance_data.volume
            ),  # Binance的volume通常是24h数据
            open_interest=0,  # Binance现货没有持仓量
        )

    @staticmethod
    def deribit_to_base_option(deribit_data: "DeribitMarketData") -> BaseOptionData:
        """将DeribitClient.MarketData转换为BaseOptionData"""
        return BaseOptionData(
            symbol=deribit_data.instrument_name,
            strike=Decimal(str(deribit_data.strike)),
            expiry=datetime.fromisoformat(deribit_data.expiry)
            if isinstance(deribit_data.expiry, str)
            else deribit_data.expiry,
            option_type=OptionType.CALL
            if deribit_data.option_type.upper() == "C"
            else OptionType.PUT,
            mark_price=Decimal(str(deribit_data.mark_price)),
            bid=Decimal(str(deribit_data.bid_price)),
            ask=Decimal(str(deribit_data.ask_price)),
            volume=0,  # DeribitClient不提供当前成交量
            volume_24h=safe_int_conversion(deribit_data.volume_24h),
            open_interest=safe_int_conversion(deribit_data.open_interest),
            timestamp=deribit_data.timestamp,
        )

    @staticmethod
    def binance_to_base_option(
        binance_data: "BinanceMarketData",
        option_type: OptionType,
        strike: float,
        expiry: datetime,
    ) -> BaseOptionData:
        """将BinanceMarketData转换为BaseOptionData（补充期权相关字段）"""
        return BaseOptionData(
            symbol=binance_data.symbol,
            strike=Decimal(str(strike)),
            expiry=expiry,
            option_type=option_type,
            mark_price=binance_data.price,
            bid=binance_data.price * Decimal("0.999"),  # 估算买价
            ask=binance_data.price * Decimal("1.001"),  # 估算卖价
            volume=safe_int_conversion(binance_data.volume),
            volume_24h=safe_int_conversion(
                binance_data.volume
            ),  # Binance的volume通常是24h数据
            open_interest=0,  # Binance现货没有持仓量
            timestamp=binance_data.timestamp,
        )

    @staticmethod
    def dict_to_base_option(data_dict: dict[str, Any]) -> BaseOptionData:
        """将字典数据转换为BaseOptionData，处理字段映射不一致"""
        # 字段映射规则
        field_mappings = {
            # volume字段统一
            "volume": data_dict.get("volume", data_dict.get("volume_24h", 0)),
            "volume_24h": data_dict.get("volume_24h", data_dict.get("volume", 0)),
            # 价格字段映射
            "mark_price": data_dict.get(
                "mark_price", data_dict.get("price", data_dict.get("mark", 0))
            ),
            "bid": data_dict.get(
                "bid", data_dict.get("bid_price", data_dict.get("best_bid_price", 0))
            ),
            "ask": data_dict.get(
                "ask", data_dict.get("ask_price", data_dict.get("best_ask_price", 0))
            ),
            # 期权特定字段
            "strike": data_dict.get("strike", data_dict.get("strike_price", 0)),
            "option_type": data_dict.get("option_type", data_dict.get("type", "CALL")),
            "open_interest": data_dict.get("open_interest", data_dict.get("oi", 0)),
            # 基础字段
            "symbol": data_dict.get("symbol", data_dict.get("instrument_name", "")),
            "expiry": data_dict.get(
                "expiry", data_dict.get("expiration_timestamp", datetime.now())
            ),
            "timestamp": data_dict.get("timestamp", datetime.now()),
        }

        return BaseOptionData(
            symbol=str(field_mappings["symbol"]),
            strike=Decimal(str(field_mappings["strike"])),
            expiry=field_mappings["expiry"]
            if isinstance(field_mappings["expiry"], datetime)
            else datetime.fromisoformat(str(field_mappings["expiry"])),
            option_type=OptionType.CALL
            if str(field_mappings["option_type"]).upper() in ("C", "CALL")
            else OptionType.PUT,
            mark_price=Decimal(str(field_mappings["mark_price"])),
            bid=Decimal(str(field_mappings["bid"])),
            ask=Decimal(str(field_mappings["ask"])),
            volume=safe_int_conversion(field_mappings["volume"]),
            volume_24h=safe_int_conversion(field_mappings["volume_24h"]),
            open_interest=safe_int_conversion(field_mappings["open_interest"]),
            timestamp=field_mappings["timestamp"],
        )

    @staticmethod
    def normalize_volume_field(data: dict[str, Any]) -> dict[str, Any]:
        """标准化volume字段，确保同时包含volume和volume_24h"""
        normalized = data.copy()

        if "volume_24h" in data and "volume" not in data:
            normalized["volume"] = data["volume_24h"]
        elif "volume" in data and "volume_24h" not in data:
            normalized["volume_24h"] = data["volume"]

        return normalized

    @classmethod
    def convert_to_unified_format(
        cls,
        data: "DeribitMarketData | BinanceMarketData | dict[str, Any]",
        target_type: type[T] = None,
    ) -> BaseOptionData | dict[str, Any]:
        """转换为统一格式"""
        # 运行时导入以避免循环导入 - 已移除未使用的导入

        if hasattr(data, "__class__") and "DeribitMarketData" in str(type(data)):
            return cls.deribit_to_base_option(data)
        elif hasattr(data, "__class__") and "BinanceMarketData" in str(type(data)):
            # 需要额外的期权信息，返回字典格式
            result = asdict(data)
            return cls.normalize_volume_field(result)
        elif isinstance(data, dict):
            if target_type == BaseOptionData:
                return cls.dict_to_base_option(data)
            else:
                return cls.normalize_volume_field(data)
        else:
            raise ValueError(f"Unsupported data type: {type(data)}")


# 便捷函数
def normalize_market_data(
    data: "DeribitMarketData | BinanceMarketData | dict[str, Any]",
) -> BaseOptionData:
    """标准化市场数据为BaseOptionData格式"""
    return DataConverter.convert_to_unified_format(data, BaseOptionData)


def ensure_volume_fields(data: dict[str, Any]) -> dict[str, Any]:
    """确保数据包含完整的volume字段"""
    return DataConverter.normalize_volume_field(data)
