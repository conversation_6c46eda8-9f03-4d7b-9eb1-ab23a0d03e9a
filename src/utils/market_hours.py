from __future__ import annotations

import asyncio
from collections.abc import Callable
from datetime import UTC, datetime, time, timedelta
from enum import Enum
from typing import Any

"""
美股市场时段管理与 queue_until_open 逻辑
- 判断当前是否在交易时段
- 支持盘前/盘后策略
- 实现闭市排队与开盘重定价
"""


class MarketHoursPolicy(str, Enum):
    OPEN_ONLY = "open_only"
    PREPOST_ALLOWED = "prepost_allowed"
    QUEUE_UNTIL_OPEN = "queue_until_open"


class MarketSession(str, Enum):
    CLOSED = "closed"
    PREMARKET = "premarket"
    REGULAR = "regular"
    AFTERHOURS = "afterhours"


class MarketHoursManager:
    """美股市场时段管理器"""

    def __init__(self):
        # 美股常规交易时间 (ET)
        self.regular_open = time(9, 30)  # 9:30 AM ET
        self.regular_close = time(16, 0)  # 4:00 PM ET

        # 盘前盘后时间
        self.premarket_open = time(4, 0)  # 4:00 AM ET
        self.afterhours_close = time(20, 0)  # 8:00 PM ET

        # 排队订单存储
        self._queued_orders: list = []
        self._queue_handlers: dict[str, Callable] = {}

    def get_current_session(self, dt: datetime | None = None) -> MarketSession:
        """获取当前市场时段"""
        if dt is None:
            dt = datetime.now(UTC)

        # 转换为 ET (简化处理，不考虑夏令时)
        et_time = (dt - timedelta(hours=5)).time()
        weekday = dt.weekday()

        # 周末闭市
        if weekday >= 5:  # Saturday = 5, Sunday = 6
            return MarketSession.CLOSED

        # 判断时段
        if self.premarket_open <= et_time < self.regular_open:
            return MarketSession.PREMARKET
        elif self.regular_open <= et_time < self.regular_close:
            return MarketSession.REGULAR
        elif self.regular_close <= et_time < self.afterhours_close:
            return MarketSession.AFTERHOURS
        else:
            return MarketSession.CLOSED

    def is_trading_allowed(
        self, policy: MarketHoursPolicy, dt: datetime | None = None
    ) -> bool:
        """根据策略判断是否允许交易"""
        session = self.get_current_session(dt)

        if policy == MarketHoursPolicy.OPEN_ONLY:
            return session == MarketSession.REGULAR
        elif policy == MarketHoursPolicy.PREPOST_ALLOWED:
            return session in (
                MarketSession.PREMARKET,
                MarketSession.REGULAR,
                MarketSession.AFTERHOURS,
            )
        elif policy == MarketHoursPolicy.QUEUE_UNTIL_OPEN:
            # 闭市时排队，其他时段允许
            return session != MarketSession.CLOSED

        return False

    def queue_order(self, order_data: dict[str, Any], handler: Callable):
        """将订单加入开盘排队"""
        self._queued_orders.append(
            {
                "order_data": order_data,
                "handler": handler,
                "queued_at": datetime.now(UTC),
            }
        )

    def get_next_open_time(self, dt: datetime | None = None) -> datetime:
        """获取下一个开盘时间"""
        if dt is None:
            dt = datetime.now(UTC)

        # 简化实现：假设下一个工作日 9:30 AM ET
        et_dt = dt - timedelta(hours=5)

        # 如果是周末，跳到周一
        if et_dt.weekday() >= 5:
            days_to_monday = 7 - et_dt.weekday()
            et_dt += timedelta(days=days_to_monday)

        # 如果已过当日开盘时间，跳到下一个工作日
        if et_dt.time() >= self.regular_open:
            et_dt += timedelta(days=1)
            if et_dt.weekday() >= 5:
                days_to_monday = 7 - et_dt.weekday()
                et_dt += timedelta(days=days_to_monday)

        # 设置为开盘时间
        next_open = et_dt.replace(
            hour=self.regular_open.hour,
            minute=self.regular_open.minute,
            second=0,
            microsecond=0,
        )

        # 转回 UTC
        return next_open + timedelta(hours=5)

    async def process_queued_orders(self):
        """处理排队的订单（开盘时调用）"""
        if not self._queued_orders:
            return

        # 处理所有排队订单
        for queued in self._queued_orders.copy():
            try:
                handler = queued["handler"]
                order_data = queued["order_data"]

                # 调用处理器（应包含重新定价逻辑）
                await handler(order_data)

                # 从队列移除
                self._queued_orders.remove(queued)

            except Exception as e:
                try:
                    import logging
                    logging.getLogger(__name__).warning(
                        f"Failed to process queued order: {e}"
                    )
                except Exception:
                    pass

    async def start_market_monitor(self):
        """启动市场时段监控（检测开盘并处理排队订单）"""
        while True:
            try:
                session = self.get_current_session()

                # 检测到开盘时处理排队订单
                if session == MarketSession.REGULAR and self._queued_orders:
                    await self.process_queued_orders()

                # 每分钟检查一次
                await asyncio.sleep(60)

            except Exception as e:
                try:
                    import logging
                    logging.getLogger(__name__).warning(f"Market monitor error: {e}")
                except Exception:
                    pass
                await asyncio.sleep(60)
