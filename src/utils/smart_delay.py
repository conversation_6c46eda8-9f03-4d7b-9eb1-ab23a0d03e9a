#!/usr/bin/env python3
"""
智能延迟策略 - SmartDelay

实现自适应的延迟管理，根据系统状态和业务需求动态调整延迟时间：
- 基于系统负载的自适应延迟
- 错误驱动的指数退避
- 业务优先级的延迟调度
- 市场状态感知的延迟优化
- 延迟性能监控和分析

设计要点：
- 替代固定的asyncio.sleep调用
- 支持多种延迟策略和组合
- 提供延迟性能指标收集
- 集成系统监控和自愈机制
- 支持延迟预测和优化
"""

import asyncio
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import UTC, datetime
from enum import Enum
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus


class DelayReason(Enum):
    """延迟原因枚举"""

    RATE_LIMITING = "rate_limiting"  # 速率限制
    ERROR_RECOVERY = "error_recovery"  # 错误恢复
    SYSTEM_LOAD = "system_load"  # 系统负载
    BUSINESS_LOGIC = "business_logic"  # 业务逻辑
    MARKET_CONDITION = "market_condition"  # 市场条件
    BACKPRESSURE = "backpressure"  # 背压控制
    THROTTLING = "throttling"  # 节流控制


class DelayPriority(Enum):
    """延迟优先级"""

    CRITICAL = 0  # 关键操作，最短延迟
    HIGH = 1  # 高优先级
    NORMAL = 2  # 正常优先级
    LOW = 3  # 低优先级
    BACKGROUND = 4  # 后台任务，可长延迟


@dataclass
class DelayRequest:
    """延迟请求"""

    base_delay: float  # 基础延迟时间
    reason: DelayReason  # 延迟原因
    priority: DelayPriority = DelayPriority.NORMAL  # 优先级
    context: dict[str, Any] = field(default_factory=dict)  # 上下文信息
    max_delay: float | None = None  # 最大延迟时间
    jitter: bool = True  # 是否添加抖动
    backoff_factor: float = 2.0  # 退避因子
    retry_count: int = 0  # 重试次数
    component_name: str = ""  # 组件名称
    operation_name: str = ""  # 操作名称


@dataclass
class DelayResult:
    """延迟结果"""

    actual_delay: float  # 实际延迟时间
    reason: DelayReason  # 延迟原因
    strategy_used: str  # 使用的策略
    adjustments: dict[str, float] = field(default_factory=dict)  # 调整因子
    start_time: datetime = field(default_factory=lambda: datetime.now(UTC))
    end_time: datetime | None = None

    @property
    def execution_time(self) -> float:
        """实际执行时间"""
        if self.end_time:
            return (self.end_time - self.start_time).total_seconds()
        return 0.0


@dataclass
class DelayMetrics:
    """延迟指标"""

    total_delays: int = 0  # 总延迟次数
    total_delay_time: float = 0.0  # 总延迟时间
    avg_delay_time: float = 0.0  # 平均延迟时间
    delay_by_reason: dict[DelayReason, int] = field(default_factory=dict)
    delay_by_priority: dict[DelayPriority, int] = field(default_factory=dict)
    delay_by_component: dict[str, int] = field(default_factory=dict)
    successful_delays: int = 0  # 成功延迟次数
    interrupted_delays: int = 0  # 中断延迟次数
    delay_accuracy: float = 1.0  # 延迟精度
    last_delay_time: datetime | None = None  # 最后延迟时间


class DelayStrategy(ABC):
    """延迟策略基类"""

    @abstractmethod
    async def calculate_delay(self, request: DelayRequest) -> float:
        """计算延迟时间"""
        pass

    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass


class FixedDelayStrategy(DelayStrategy):
    """固定延迟策略"""

    def get_strategy_name(self) -> str:
        return "fixed"

    async def calculate_delay(self, request: DelayRequest) -> float:
        return request.base_delay


class ExponentialBackoffStrategy(DelayStrategy):
    """指数退避策略"""

    def __init__(self, max_delay: float = 300.0):
        self.max_delay = max_delay

    def get_strategy_name(self) -> str:
        return "exponential_backoff"

    async def calculate_delay(self, request: DelayRequest) -> float:
        delay = request.base_delay * (request.backoff_factor**request.retry_count)
        max_allowed = request.max_delay or self.max_delay
        return min(delay, max_allowed)


class AdaptiveDelayStrategy(DelayStrategy):
    """自适应延迟策略"""

    def __init__(self):
        self.system_load_factor = 1.0
        self.error_rate_factor = 1.0
        self.market_volatility_factor = 1.0

    def get_strategy_name(self) -> str:
        return "adaptive"

    async def calculate_delay(self, request: DelayRequest) -> float:
        base_delay = request.base_delay

        # 根据优先级调整
        priority_factors = {
            DelayPriority.CRITICAL: 0.5,
            DelayPriority.HIGH: 0.7,
            DelayPriority.NORMAL: 1.0,
            DelayPriority.LOW: 1.5,
            DelayPriority.BACKGROUND: 3.0,
        }

        delay = base_delay * priority_factors.get(request.priority, 1.0)

        # 根据系统状态调整
        delay *= self.system_load_factor
        delay *= self.error_rate_factor

        # 根据市场条件调整
        if request.reason == DelayReason.MARKET_CONDITION:
            delay *= self.market_volatility_factor

        # 应用最大限制
        if request.max_delay:
            delay = min(delay, request.max_delay)

        return delay

    def update_system_metrics(
        self, cpu_usage: float, memory_usage: float, error_rate: float
    ):
        """更新系统指标"""
        # 系统负载因子 (CPU和内存使用率影响)
        load_avg = (cpu_usage + memory_usage) / 2
        if load_avg > 0.8:
            self.system_load_factor = 2.0
        elif load_avg > 0.6:
            self.system_load_factor = 1.5
        else:
            self.system_load_factor = 1.0

        # 错误率因子
        if error_rate > 0.1:  # 10%以上错误率
            self.error_rate_factor = 3.0
        elif error_rate > 0.05:  # 5%以上错误率
            self.error_rate_factor = 2.0
        else:
            self.error_rate_factor = 1.0

    def update_market_metrics(self, volatility: float, spread: float):
        """更新市场指标"""
        # 波动率越高，延迟适当增加以避免误操作（调整为加密货币阈值）
        if volatility > 1.5:  # 150%+ 超高波动率
            self.market_volatility_factor = 1.5
        elif volatility > 0.8:  # 80%+ 高波动率
            self.market_volatility_factor = 1.2
        else:
            self.market_volatility_factor = 1.0


class JitterStrategy(DelayStrategy):
    """抖动延迟策略"""

    def __init__(self, jitter_factor: float = 0.1):
        self.jitter_factor = jitter_factor

    def get_strategy_name(self) -> str:
        return "jitter"

    async def calculate_delay(self, request: DelayRequest) -> float:
        import random

        base_delay = request.base_delay

        if request.jitter:
            # 添加随机抖动，避免雷群效应
            jitter = random.uniform(-self.jitter_factor, self.jitter_factor)
            delay = base_delay * (1 + jitter)
        else:
            delay = base_delay

        return max(0.0, delay)


class CompositeDelayStrategy(DelayStrategy):
    """组合延迟策略"""

    def __init__(self, strategies: list[DelayStrategy]):
        self.strategies = strategies

    def get_strategy_name(self) -> str:
        return "composite"

    async def calculate_delay(self, request: DelayRequest) -> float:
        # 应用所有策略并取结果的加权平均
        delays = []
        for strategy in self.strategies:
            delay = await strategy.calculate_delay(request)
            delays.append(delay)

        # 简单平均（可以改为加权平均）
        return sum(delays) / len(delays) if delays else request.base_delay


class SmartDelayManager(BaseComponent):
    """
    智能延迟管理器

    提供自适应的延迟管理，根据系统状态和业务需求动态调整延迟时间
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("SmartDelayManager", config)

        # 延迟策略
        self.strategies: dict[str, DelayStrategy] = {}
        self.default_strategy = "adaptive"

        # 指标收集
        self.delay_metrics = DelayMetrics()
        self.delay_history: list[DelayResult] = []
        self.max_history_size = self.config.get("max_history_size", 1000)

        # 系统监控
        self.system_monitor_enabled = self.config.get("system_monitor_enabled", True)
        self.market_monitor_enabled = self.config.get("market_monitor_enabled", True)

        # 配置
        self.enable_jitter = self.config.get("enable_jitter", True)
        self.enable_adaptive = self.config.get("enable_adaptive", True)
        self.max_concurrent_delays = self.config.get("max_concurrent_delays", 100)

        # 当前活跃的延迟
        self.active_delays: dict[str, asyncio.Task] = {}

        # 初始化策略
        self._initialize_strategies()

    def _initialize_strategies(self):
        """初始化延迟策略"""
        # 基础策略
        self.strategies["fixed"] = FixedDelayStrategy()
        self.strategies["exponential"] = ExponentialBackoffStrategy()
        self.strategies["jitter"] = JitterStrategy()

        if self.enable_adaptive:
            self.strategies["adaptive"] = AdaptiveDelayStrategy()

        # 组合策略
        if self.enable_adaptive and self.enable_jitter:
            composite_strategies = [
                self.strategies["adaptive"],
                self.strategies["jitter"],
            ]
            self.strategies["smart"] = CompositeDelayStrategy(composite_strategies)
            self.default_strategy = "smart"

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            await self.log_initialization(True, "Initializing SmartDelayManager")

            # 清理状态
            self.delay_history.clear()
            self.active_delays.clear()

            # 重置延迟指标
            self.delay_metrics = DelayMetrics()

            if self.logger:
                await self.logger.info("SmartDelayManager initialized successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"SmartDelayManager initialization failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            if self.logger:
                await self.logger.info("Starting SmartDelayManager")

            # 启动系统监控任务
            if self.system_monitor_enabled:
                asyncio.create_task(self._system_monitor_loop())

            if self.logger:
                await self.logger.info("SmartDelayManager started successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"SmartDelayManager start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            if self.logger:
                await self.logger.info("Stopping SmartDelayManager")

            # 取消所有活跃的延迟
            for task in self.active_delays.values():
                if not task.done():
                    task.cancel()

            # 等待所有任务完成
            if self.active_delays:
                await asyncio.gather(
                    *self.active_delays.values(), return_exceptions=True
                )

            self.active_delays.clear()

            if self.logger:
                await self.logger.info("SmartDelayManager stopped successfully")
            return True

        except Exception as e:
            if self.logger:
                await self.logger.error(f"SmartDelayManager stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            issues = []

            # 检查活跃延迟数量
            active_count = len(self.active_delays)
            if active_count > self.max_concurrent_delays * 0.9:
                issues.append(f"High concurrent delays: {active_count}")

            # 检查延迟精度
            if self.delay_metrics.delay_accuracy < 0.8:
                issues.append(
                    f"Low delay accuracy: {self.delay_metrics.delay_accuracy:.2f}"
                )

            # 检查中断率
            if self.delay_metrics.total_delays > 0:
                interrupt_rate = (
                    self.delay_metrics.interrupted_delays
                    / self.delay_metrics.total_delays
                )
                if interrupt_rate > 0.1:
                    issues.append(f"High interrupt rate: {interrupt_rate:.2f}")

            # 确定健康状态
            if not issues:
                status = HealthStatus.HEALTHY
                message = "SmartDelayManager is healthy"
            elif len(issues) <= 2:
                status = HealthStatus.DEGRADED
                message = f"SmartDelayManager has minor issues: {', '.join(issues)}"
            else:
                status = HealthStatus.UNHEALTHY
                message = f"SmartDelayManager has major issues: {', '.join(issues)}"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "active_delays": active_count,
                    "total_delays": self.delay_metrics.total_delays,
                    "avg_delay_time": self.delay_metrics.avg_delay_time,
                    "delay_accuracy": self.delay_metrics.delay_accuracy,
                    "interrupt_rate": self.delay_metrics.interrupted_delays
                    / max(1, self.delay_metrics.total_delays),
                    "issues": issues,
                },
                timestamp=datetime.now(UTC),
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                timestamp=datetime.now(UTC),
            )

    async def smart_sleep(
        self,
        delay: float,
        reason: DelayReason = DelayReason.BUSINESS_LOGIC,
        priority: DelayPriority = DelayPriority.NORMAL,
        strategy: str | None = None,
        **kwargs,
    ) -> DelayResult:
        """
        智能延迟

        Args:
            delay: 基础延迟时间
            reason: 延迟原因
            priority: 优先级
            strategy: 指定策略名称
            **kwargs: 其他参数

        Returns:
            DelayResult: 延迟结果
        """
        # 创建延迟请求
        request = DelayRequest(
            base_delay=delay, reason=reason, priority=priority, **kwargs
        )

        # 选择策略
        strategy_name = strategy or self.default_strategy
        selected_strategy = self.strategies.get(strategy_name, self.strategies["fixed"])

        # 计算实际延迟时间
        actual_delay = await selected_strategy.calculate_delay(request)

        # 创建延迟结果
        result = DelayResult(
            actual_delay=actual_delay,
            reason=reason,
            strategy_used=selected_strategy.get_strategy_name(),
        )

        # 执行延迟
        delay_id = f"{id(result)}_{time.time()}"

        try:
            # 创建延迟任务
            delay_task = asyncio.create_task(asyncio.sleep(actual_delay))
            self.active_delays[delay_id] = delay_task

            # 执行延迟
            start_time = time.time()
            await delay_task
            end_time = time.time()

            # 更新结果
            result.end_time = datetime.now(UTC)

            # 计算延迟精度
            expected_delay = actual_delay
            actual_execution = end_time - start_time
            accuracy = min(1.0, expected_delay / max(actual_execution, 0.001))

            # 更新指标
            self._update_metrics(result, accuracy, interrupted=False)

            if self.logger:
                await self.logger.debug(
                    f"Smart sleep completed: {actual_delay:.3f}s "
                    f"(expected: {delay:.3f}s, strategy: {strategy_name})"
                )

        except asyncio.CancelledError:
            # 延迟被中断
            result.end_time = datetime.now(UTC)
            self._update_metrics(result, 0.0, interrupted=True)

            if self.logger:
                await self.logger.debug(f"Smart sleep interrupted: {delay_id}")

            raise

        except Exception as e:
            # 延迟执行出错
            result.end_time = datetime.now(UTC)
            self._update_metrics(result, 0.0, interrupted=True)

            if self.logger:
                await self.logger.error(f"Smart sleep error: {e}")

            raise

        finally:
            # 清理活跃延迟
            self.active_delays.pop(delay_id, None)

        return result

    def _update_metrics(self, result: DelayResult, accuracy: float, interrupted: bool):
        """更新指标"""
        self.delay_metrics.total_delays += 1
        self.delay_metrics.total_delay_time += result.actual_delay
        self.delay_metrics.avg_delay_time = (
            self.delay_metrics.total_delay_time / self.delay_metrics.total_delays
        )

        # 按原因统计
        if result.reason not in self.delay_metrics.delay_by_reason:
            self.delay_metrics.delay_by_reason[result.reason] = 0
        self.delay_metrics.delay_by_reason[result.reason] += 1

        # 更新精度和中断统计
        if interrupted:
            self.delay_metrics.interrupted_delays += 1
        else:
            self.delay_metrics.successful_delays += 1

            # 更新延迟精度（加权平均）
            total_successful = self.delay_metrics.successful_delays
            current_weight = 1.0 / total_successful
            prev_weight = 1.0 - current_weight
            self.delay_metrics.delay_accuracy = (
                self.delay_metrics.delay_accuracy * prev_weight
                + accuracy * current_weight
            )

        self.delay_metrics.last_delay_time = datetime.now(UTC)

        # 添加到历史记录
        self.delay_history.append(result)
        if len(self.delay_history) > self.max_history_size:
            self.delay_history.pop(0)

    async def _system_monitor_loop(self):
        """系统监控循环"""
        try:
            while self.is_running:
                try:
                    # 获取系统指标（简化版本）
                    import psutil

                    cpu_usage = psutil.cpu_percent(interval=1) / 100.0
                    memory_info = psutil.virtual_memory()
                    memory_usage = memory_info.percent / 100.0

                    # 计算错误率（基于最近的延迟历史）
                    error_rate = self._calculate_error_rate()

                    # 更新自适应策略
                    if "adaptive" in self.strategies:
                        adaptive_strategy = self.strategies["adaptive"]
                        if isinstance(adaptive_strategy, AdaptiveDelayStrategy):
                            adaptive_strategy.update_system_metrics(
                                cpu_usage, memory_usage, error_rate
                            )

                    await asyncio.sleep(60)  # 每分钟监控一次

                except Exception as e:
                    if self.logger:
                        await self.logger.warning(f"System monitor error: {e}")
                    await asyncio.sleep(60)

        except ImportError:
            # psutil不可用，禁用系统监控
            if self.logger:
                await self.logger.warning(
                    "psutil not available, system monitoring disabled"
                )

    def _calculate_error_rate(self) -> float:
        """计算错误率"""
        if not self.delay_history:
            return 0.0

        # 计算最近100次延迟的中断率作为错误率的近似
        recent_delays = self.delay_history[-100:]
        interrupted_count = sum(
            1 for delay in recent_delays if delay.execution_time == 0.0
        )

        return interrupted_count / len(recent_delays)

    async def get_metrics(self) -> dict[str, Any]:
        """获取延迟指标"""
        base_metrics = await super().get_metrics()

        delay_metrics = {
            "total_delays": self.delay_metrics.total_delays,
            "total_delay_time": self.delay_metrics.total_delay_time,
            "avg_delay_time": self.delay_metrics.avg_delay_time,
            "successful_delays": self.delay_metrics.successful_delays,
            "interrupted_delays": self.delay_metrics.interrupted_delays,
            "delay_accuracy": self.delay_metrics.delay_accuracy,
            "active_delays": len(self.active_delays),
            "delay_by_reason": {
                k.value: v for k, v in self.delay_metrics.delay_by_reason.items()
            },
            "last_delay_time": self.delay_metrics.last_delay_time.isoformat()
            if self.delay_metrics.last_delay_time
            else None,
            "available_strategies": list(self.strategies.keys()),
            "default_strategy": self.default_strategy,
        }

        base_metrics.update(delay_metrics)
        return base_metrics

    def cancel_all_delays(self):
        """取消所有延迟"""
        cancelled_count = 0
        for task in self.active_delays.values():
            if not task.done():
                task.cancel()
                cancelled_count += 1

        return cancelled_count


# 全局智能延迟管理器实例
_global_delay_manager: SmartDelayManager | None = None


def get_delay_manager() -> SmartDelayManager | None:
    """获取全局延迟管理器"""
    return _global_delay_manager


def set_delay_manager(manager: SmartDelayManager):
    """设置全局延迟管理器"""
    global _global_delay_manager
    _global_delay_manager = manager


async def smart_sleep(
    delay: float,
    reason: DelayReason = DelayReason.BUSINESS_LOGIC,
    priority: DelayPriority = DelayPriority.NORMAL,
    **kwargs,
) -> DelayResult:
    """
    全局智能延迟函数

    这是替代 asyncio.sleep 的智能版本
    """
    manager = get_delay_manager()
    if manager and manager.is_running:
        return await manager.smart_sleep(delay, reason, priority, **kwargs)
    else:
        # 降级到普通延迟
        await asyncio.sleep(delay)
        return DelayResult(actual_delay=delay, reason=reason, strategy_used="fallback")
