"""
期权日历工具

提供真实的期权到期日计算，支持美股期权的标准到期规则
"""

from datetime import date, timedelta


class OptionCalendar:
    """期权日历管理器"""

    # 美国联邦假日（影响期权到期的主要假日）
    FEDERAL_HOLIDAYS = {
        # 固定日期假日
        "new_year": (1, 1),
        "independence": (7, 4),
        "christmas": (12, 25),
    }

    def __init__(self):
        self._holiday_cache = {}

    def get_third_friday(self, year: int, month: int) -> date:
        """获取指定年月的第三个周五"""
        # 找到月份的第一天
        first_day = date(year, month, 1)

        # 找到第一个周五
        days_until_friday = (4 - first_day.weekday()) % 7
        first_friday = first_day + timedelta(days=days_until_friday)

        # 第三个周五
        third_friday = first_friday + timedelta(days=14)

        return third_friday

    def is_business_day(self, check_date: date) -> bool:
        """检查是否为工作日（排除周末和主要假日）"""
        # 周末
        if check_date.weekday() >= 5:  # Saturday = 5, Sunday = 6
            return False

        # 检查联邦假日
        return not self._is_federal_holiday(check_date)

    def _is_federal_holiday(self, check_date: date) -> bool:
        """检查是否为联邦假日"""
        year = check_date.year
        month = check_date.month
        day = check_date.day

        # 固定日期假日
        for _holiday_name, (
            holiday_month,
            holiday_day,
        ) in self.FEDERAL_HOLIDAYS.items():
            if month == holiday_month and day == holiday_day:
                return True

        # 浮动假日
        # Martin Luther King Jr. Day - 1月第三个周一
        if month == 1:
            mlk_day = self._get_nth_weekday(year, 1, 0, 3)  # 第三个周一
            if check_date == mlk_day:
                return True

        # Presidents Day - 2月第三个周一
        if month == 2:
            presidents_day = self._get_nth_weekday(year, 2, 0, 3)
            if check_date == presidents_day:
                return True

        # Memorial Day - 5月最后一个周一
        if month == 5:
            memorial_day = self._get_last_weekday(year, 5, 0)
            if check_date == memorial_day:
                return True

        # Labor Day - 9月第一个周一
        if month == 9:
            labor_day = self._get_nth_weekday(year, 9, 0, 1)
            if check_date == labor_day:
                return True

        # Thanksgiving - 11月第四个周四
        if month == 11:
            thanksgiving = self._get_nth_weekday(year, 11, 3, 4)
            if check_date == thanksgiving:
                return True

        return False

    def _get_nth_weekday(self, year: int, month: int, weekday: int, n: int) -> date:
        """获取指定月份的第n个指定星期几"""
        first_day = date(year, month, 1)
        days_until_weekday = (weekday - first_day.weekday()) % 7
        first_occurrence = first_day + timedelta(days=days_until_weekday)
        return first_occurrence + timedelta(days=7 * (n - 1))

    def _get_last_weekday(self, year: int, month: int, weekday: int) -> date:
        """获取指定月份的最后一个指定星期几"""
        # 下个月的第一天
        next_month = date(year + 1, 1, 1) if month == 12 else date(year, month + 1, 1)

        # 往前找到最后一个指定星期几
        last_day = next_month - timedelta(days=1)
        days_back = (last_day.weekday() - weekday) % 7
        return last_day - timedelta(days=days_back)

    def get_standard_expiry(self, year: int, month: int) -> date:
        """获取标准期权到期日（第三个周五）"""
        third_friday = self.get_third_friday(year, month)

        # 如果第三个周五是假日，向前调整到最近的工作日
        expiry_date = third_friday
        while not self.is_business_day(expiry_date):
            expiry_date = expiry_date - timedelta(days=1)

        return expiry_date

    def get_next_expiry(self, from_date: date | None = None, min_days: int = 7) -> date:
        """获取下一个期权到期日"""
        if from_date is None:
            from_date = date.today()

        # 从当前月份开始查找
        current_year = from_date.year
        current_month = from_date.month

        for i in range(12):  # 最多查找12个月
            check_year = current_year
            check_month = current_month + i

            # 处理跨年
            if check_month > 12:
                check_year += (check_month - 1) // 12
                check_month = ((check_month - 1) % 12) + 1

            expiry = self.get_standard_expiry(check_year, check_month)

            # 检查是否满足最小天数要求
            if expiry > from_date and (expiry - from_date).days >= min_days:
                return expiry

        # 如果找不到，返回一年后的到期日
        return self.get_standard_expiry(current_year + 1, current_month)

    def get_expiry_list(
        self, from_date: date | None = None, months: int = 6
    ) -> list[date]:
        """获取未来几个月的期权到期日列表"""
        if from_date is None:
            from_date = date.today()

        expiry_list = []
        current_year = from_date.year
        current_month = from_date.month

        for i in range(months):
            check_year = current_year
            check_month = current_month + i

            # 处理跨年
            if check_month > 12:
                check_year += (check_month - 1) // 12
                check_month = ((check_month - 1) % 12) + 1

            expiry = self.get_standard_expiry(check_year, check_month)

            # 只包含未来的到期日
            if expiry > from_date:
                expiry_list.append(expiry)

        return expiry_list

    def is_expiry_date(self, check_date: date) -> bool:
        """检查指定日期是否为期权到期日"""
        # 检查是否为第三个周五（或调整后的工作日）
        year = check_date.year
        month = check_date.month

        standard_expiry = self.get_standard_expiry(year, month)
        return check_date == standard_expiry

    def days_to_expiry(self, expiry_date: date, from_date: date | None = None) -> int:
        """计算到期权到期的天数"""
        if from_date is None:
            from_date = date.today()

        return (expiry_date - from_date).days

    def get_expiry_for_tenor(
        self, tenor_days: int, from_date: date | None = None
    ) -> date:
        """根据期限天数获取最接近的期权到期日"""
        if from_date is None:
            from_date = date.today()

        target_date = from_date + timedelta(days=tenor_days)

        # 获取未来6个月的到期日
        expiry_list = self.get_expiry_list(from_date, 6)

        if not expiry_list:
            # 如果没有找到，返回标准计算
            return self.get_standard_expiry(target_date.year, target_date.month)

        # 找到最接近目标日期的到期日
        closest_expiry = min(expiry_list, key=lambda x: abs((x - target_date).days))

        return closest_expiry


# 全局实例
option_calendar = OptionCalendar()
