#!/usr/bin/env python3
"""
异步日志系统 - AsyncLogger

提供高性能的异步日志记录功能，支持：
- 结构化日志输出（JSON格式）
- 异步写入，不阻塞主线程
- 日志轮转和压缩
- 链路追踪和上下文传递
- 远程传输（Elasticsearch集成）
- 交易决策和风险事件记录
"""

import asyncio
import json
import sys
import traceback
import uuid
from collections.abc import Callable
from contextvars import ContextVar
from dataclasses import asdict, dataclass, field
from datetime import UTC, datetime
from enum import Enum
from pathlib import Path
from typing import Any

from loguru import logger
import os as _os

# 导入BaseComponent - 使用相对导入优先
try:
    from ..core.base_component import (
        BaseComponent,
        ComponentState,
        HealthCheckResult,
        HealthStatus,
    )
except ImportError:
    # 如果相对导入失败，尝试绝对导入（避免动态修改sys.path）
    try:
        from src.core.base_component import (
            BaseComponent,
            ComponentState,
            HealthCheckResult,
            HealthStatus,
        )
    except ImportError:
        # 如果都失败，定义简单的基类
        import asyncio
        from enum import Enum

        class ComponentState(Enum):
            UNINITIALIZED = "uninitialized"
            INITIALIZING = "initializing"
            INITIALIZED = "initialized"
            STARTING = "starting"
            RUNNING = "running"
            STOPPING = "stopping"
            STOPPED = "stopped"
            ERROR = "error"
            FAILED = "failed"

        class BaseComponent:
            def __init__(self, component_name: str = "AsyncLogger"):
                self.component_name = component_name
                self._state = ComponentState.UNINITIALIZED
                self._state_lock = asyncio.Lock()

            @property
            def state(self) -> ComponentState:
                return self._state

            async def initialize(self) -> bool:
                async with self._state_lock:
                    if self._state in (
                        ComponentState.INITIALIZED,
                        ComponentState.RUNNING,
                    ):
                        return True
                    elif self._state == ComponentState.INITIALIZING:
                        return False

                    self._state = ComponentState.INITIALIZING
                    try:
                        result = await self._initialize_impl()
                        if result:
                            self._state = ComponentState.INITIALIZED
                        else:
                            self._state = ComponentState.ERROR
                        return result
                    except Exception:
                        self._state = ComponentState.FAILED
                        return False

            async def _initialize_impl(self) -> bool:
                # AsyncLogger 的初始化逻辑
                return True

            async def start(self) -> bool:
                async with self._state_lock:
                    if self._state == ComponentState.RUNNING:
                        return True
                    elif self._state == ComponentState.STARTING:
                        return False

                    if (
                        self._state != ComponentState.INITIALIZED
                        and not await self.initialize()
                    ):
                        return False

                    self._state = ComponentState.STARTING
                    try:
                        result = await self._start_impl()
                        if result:
                            self._state = ComponentState.RUNNING
                        else:
                            self._state = ComponentState.ERROR
                        return result
                    except Exception:
                        self._state = ComponentState.FAILED
                        return False

            async def _start_impl(self) -> bool:
                # AsyncLogger 的启动逻辑
                return True

            async def stop(self) -> bool:
                async with self._state_lock:
                    if self._state == ComponentState.STOPPED:
                        return True
                    elif self._state == ComponentState.STOPPING:
                        return False

                    self._state = ComponentState.STOPPING
                    try:
                        result = await self._stop_impl()
                        if result:
                            self._state = ComponentState.STOPPED
                        else:
                            self._state = ComponentState.ERROR
                        return result
                    except Exception:
                        self._state = ComponentState.FAILED
                        return False

            async def _stop_impl(self) -> bool:
                return True

        class HealthStatus:
            HEALTHY = "healthy"
            UNHEALTHY = "unhealthy"

        class HealthCheckResult:
            def __init__(self, status, message, details=None):
                self.status = status
                self.message = message
                self.details = details or {}


import contextlib
import time

import aiohttp


class LogLevel(Enum):
    """日志级别枚举"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class LogCategory(Enum):
    """日志分类枚举"""

    SYSTEM = "system"
    TRADING = "trading"
    STRATEGY = "strategy"
    RISK = "risk"
    DATA = "data"
    EXECUTION = "execution"
    CONNECTIVITY = "connectivity"
    PERFORMANCE = "performance"


@dataclass
class LogContext:
    """日志上下文"""

    trace_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    span_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str | None = None
    session_id: str | None = None
    strategy_id: str | None = None
    order_id: str | None = None
    request_id: str | None = None

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {k: v for k, v in asdict(self).items() if v is not None}


@dataclass
class StructuredLogEntry:
    """结构化日志条目"""

    timestamp: datetime
    level: LogLevel
    category: LogCategory
    message: str
    context: LogContext
    data: dict[str, Any] = field(default_factory=dict)
    module: str | None = None
    function: str | None = None
    line: int | None = None
    exception: str | None = None

    def to_json(self) -> str:
        """转换为JSON字符串"""
        entry_dict = {
            "timestamp": self.timestamp.isoformat(),
            "level": self.level.value,
            "category": self.category.value,
            "message": self.message,
            "context": self.context.to_dict() if self.context else {},
            "data": self.data,
        }

        if self.module:
            entry_dict["module"] = self.module
        if self.function:
            entry_dict["function"] = self.function
        if self.line:
            entry_dict["line"] = self.line
        if self.exception:
            entry_dict["exception"] = self.exception

        return json.dumps(entry_dict, ensure_ascii=False, default=str)


# 全局上下文变量
log_context: ContextVar[LogContext] = ContextVar("log_context", default=None)


class ElasticsearchHandler:
    """Elasticsearch日志处理器"""

    def __init__(
        self,
        elasticsearch_url: str,
        index_prefix: str = "btc-option-grid",
        batch_size: int = 100,
        flush_interval: int = 30,
    ):
        self.elasticsearch_url = elasticsearch_url
        self.index_prefix = index_prefix
        self.batch_size = batch_size
        self.flush_interval = flush_interval
        self.log_buffer: list[StructuredLogEntry] = []
        self.last_flush = time.time()
        self._session: aiohttp.ClientSession | None = None

    async def initialize(self):
        """初始化Elasticsearch连接"""
        self._session = aiohttp.ClientSession()

    async def handle_log(self, entry: StructuredLogEntry):
        """处理日志条目"""
        self.log_buffer.append(entry)

        # 检查是否需要刷新
        if (
            len(self.log_buffer) >= self.batch_size
            or time.time() - self.last_flush >= self.flush_interval
        ):
            await self.flush()

    async def flush(self):
        """刷新日志到Elasticsearch"""
        if not self.log_buffer or not self._session:
            return

        try:
            # 构建批量索引请求
            bulk_data = []
            today = datetime.now(UTC).strftime("%Y.%m.%d")
            index_name = f"{self.index_prefix}-{today}"

            for entry in self.log_buffer:
                # 索引元数据
                bulk_data.append(json.dumps({"index": {"_index": index_name}}))
                # 文档数据
                bulk_data.append(entry.to_json())

            bulk_body = "\n".join(bulk_data) + "\n"

            # 发送到Elasticsearch
            async with self._session.post(
                f"{self.elasticsearch_url}/_bulk",
                data=bulk_body,
                headers={"Content-Type": "application/x-ndjson"},
            ) as response:
                if response.status != 200:
                    logger.error(
                        f"Failed to send logs to Elasticsearch: {response.status}"
                    )

            self.log_buffer.clear()
            self.last_flush = time.time()

        except Exception as e:
            logger.error(f"Error flushing logs to Elasticsearch: {e}")

    async def close(self):
        """关闭连接"""
        await self.flush()
        if self._session:
            await self._session.close()


class AsyncLogger(BaseComponent):
    """
    异步日志记录器

    基于loguru实现的高性能异步日志系统，支持结构化日志、链路追踪、
    远程传输等企业级功能
    """

    def __init__(
        self,
        level: str = "INFO",
        log_file: str | None = None,
        json_format: bool = True,
        enable_elasticsearch: bool = False,
        elasticsearch_url: str | None = None,
        max_file_size: str = "100 MB",
        retention_days: int = 30,
        compression: bool = True,
    ):
        super().__init__(component_name="AsyncLogger")
        self.level = level
        # 开发开关：debug 跟踪（env: DEBUG_TRACING=true 时开启）
        try:
            self.debug_tracing: bool = (
                str(_os.getenv("DEBUG_TRACING", "false")).lower() == "true"
            )
        except Exception:
            self.debug_tracing = False
        # 日志目录优先级：显式 log_file -> 环境变量 LOG_DIR -> 项目根 logs
        self.log_file = None
        try:
            import os as _os

            if log_file:
                # 调用方显式传入
                self.log_file = log_file
            else:
                log_dir_env = _os.getenv("LOG_DIR")
                if log_dir_env:
                    base_dir = Path(log_dir_env)
                else:
                    # 以项目根为基准（src/utils/async_logger.py -> src -> project_root）
                    base_dir = Path(__file__).resolve().parents[2] / "logs"
                base_dir.mkdir(parents=True, exist_ok=True)
                self.log_file = str(base_dir / "app.log")
        except Exception:
            # 退回仅控制台输出
            self.log_file = None
        self.json_format = json_format
        self.enable_elasticsearch = enable_elasticsearch
        self.elasticsearch_url = elasticsearch_url
        self.max_file_size = max_file_size
        self.retention_days = retention_days
        self.compression = compression

        self._configured = False
        self._elasticsearch_handler: ElasticsearchHandler | None = None
        self._log_hooks: list[Callable] = []

        # Structured data normalization config
        self._FIELD_ALIASES: dict[str, str] = {
            # timestamps
            "ts": "timestamp",
            "time": "timestamp",
            "event_time": "timestamp",
            # symbols
            "sym": "symbol",
            "instrument": "symbol",
            "instrument_name": "symbol",
            # quantities
            "qty": "quantity",
            # reason/description synonyms
            "description": "reason",
            "msg": "reason",
            # 'message' is reserved at top-level; map only if reason missing (handled in code)
        }
        self._PRIORITY_ORDER: list[str] = [
            "timestamp",
            "symbol",
            "reason",
            "action",
            "side",
            "severity",
            "confidence",
            "quantity",
            "size",
            "price",
            "strike",
            "option_type",
            "days_to_expiry",
            "account_id",
            "strategy_id",
            "order_id",
            "event_type",
        ]

    @staticmethod
    def _to_iso_timestamp(val: Any) -> str | Any:
        try:
            if isinstance(val, datetime):
                return val.astimezone(UTC).isoformat()
            if isinstance(val, (int | float)):
                # epoch seconds or ms
                sec = val / 1000.0 if val > 1e12 else float(val)
                return datetime.fromtimestamp(sec, UTC).isoformat()
            if isinstance(val, str):
                return val
        except Exception:
            return val
        return val

    def _normalize_data(self, data: dict[str, Any] | None) -> dict[str, Any]:
        if not isinstance(data, dict) or not data:
            return {}

        normalized: dict[str, Any] = dict(data)
        # Apply alias mapping: backfill canonical when missing
        for src, dst in self._FIELD_ALIASES.items():
            if src in data and dst not in normalized:
                normalized[dst] = data[src]
        # Only map 'message' to 'reason' when explicit reason missing
        if "reason" not in normalized and "message" in data:
            normalized["reason"] = data["message"]

        # Normalize timestamp if present
        if "timestamp" in normalized:
            normalized["timestamp"] = self._to_iso_timestamp(normalized["timestamp"])

        # Build ordered dict with priority keys first
        ordered: dict[str, Any] = {}
        for key in self._PRIORITY_ORDER:
            if key in normalized:
                ordered[key] = normalized[key]
        # Append remaining keys in alphabetical order for stability
        for key in sorted(k for k in normalized if k not in ordered):
            ordered[key] = normalized[key]

        return ordered

    def set_logger(self, logger: "AsyncLogger"):
        """设置日志记录器 - AsyncLogger忽略此调用，因为它本身就是日志器"""
        # AsyncLogger不需要外部日志器，忽略此调用
        pass

    def set_dependency_injector(self, dependency_injector):
        """设置依赖注入器 - AsyncLogger忽略此调用"""
        # AsyncLogger不需要依赖注入器，忽略此调用
        pass

    async def initialize(self) -> bool:
        """初始化日志系统"""
        # 先调用父类的initialize，它会调用_initialize_impl
        return await super().initialize()

    async def _configure_loguru(self):
        """配置loguru日志器"""
        # 移除默认处理器
        logger.remove()

        # 控制台处理器
        logger.add(
            sys.stdout,
            level=self.level,
            format="{message}",
            colorize=False,
            enqueue=True,
        )

        # 文件处理器（写入 self.log_file 若可用）
        if self.log_file:
            log_path = Path(self.log_file)
            with contextlib.suppress(Exception):
                log_path.parent.mkdir(parents=True, exist_ok=True)

            logger.add(
                self.log_file,
                level=self.level,
                format="{message}",
                rotation=self.max_file_size,
                retention=f"{self.retention_days} days",
                compression="gz" if self.compression else None,
                enqueue=True,
                serialize=self.json_format,
            )

    def _json_formatter(self, record):
        """JSON格式化器"""
        try:
            # 获取当前上下文
            context = log_context.get() or LogContext()

            # 构建结构化日志条目
            entry = StructuredLogEntry(
                timestamp=datetime.now(UTC),
                level=LogLevel(record["level"].name),
                category=LogCategory(record["extra"].get("category", "system")),
                message=record["message"],
                context=context,
                data=record["extra"].get("data", {}),
                module=record["name"],
                function=record["function"],
                line=record["line"],
                exception=str(record["exception"]) if record["exception"] else None,
            )

            return entry.to_json() + "\n"
        except Exception:
            # 如果格式化失败，返回基本格式
            return f'{{"timestamp": "{datetime.now(UTC).isoformat()}", "level": "{record["level"].name}", "message": "{record["message"]}"}}\n'

    async def _setup_elasticsearch(self):
        """设置Elasticsearch处理器"""
        self._elasticsearch_handler = ElasticsearchHandler(self.elasticsearch_url)
        await self._elasticsearch_handler.initialize()

    def set_context(self, **kwargs) -> LogContext:
        """设置日志上下文"""
        current_context = log_context.get() or LogContext()

        # 更新上下文
        for key, value in kwargs.items():
            if hasattr(current_context, key):
                setattr(current_context, key, value)

        log_context.set(current_context)
        return current_context

    def get_context(self) -> LogContext:
        """获取当前日志上下文"""
        return log_context.get() or LogContext()

    def clear_context(self):
        """清除日志上下文"""
        log_context.set(LogContext())

    async def debug(
        self,
        message: str,
        category: LogCategory = LogCategory.SYSTEM,
        data: dict[str, Any] | None = None,
        **kwargs,
    ):
        """记录调试日志"""
        await self._log(LogLevel.DEBUG, message, category, data, **kwargs)

    async def info(
        self,
        message: str,
        category: LogCategory = LogCategory.SYSTEM,
        data: dict[str, Any] | None = None,
        **kwargs,
    ):
        """记录信息日志"""
        await self._log(LogLevel.INFO, message, category, data, **kwargs)

    async def warning(
        self,
        message: str,
        category: LogCategory = LogCategory.SYSTEM,
        data: dict[str, Any] | None = None,
        **kwargs,
    ):
        """记录警告日志"""
        await self._log(LogLevel.WARNING, message, category, data, **kwargs)

    async def error(
        self,
        message: str,
        category: LogCategory = LogCategory.SYSTEM,
        data: dict[str, Any] | None = None,
        exception: Exception | None = None,
        **kwargs,
    ):
        """记录错误日志"""
        if exception:
            kwargs["exception"] = traceback.format_exc()
        await self._log(LogLevel.ERROR, message, category, data, **kwargs)

    async def critical(
        self,
        message: str,
        category: LogCategory = LogCategory.SYSTEM,
        data: dict[str, Any] | None = None,
        exception: Exception | None = None,
        **kwargs,
    ):
        """记录严重错误日志"""
        if exception:
            kwargs["exception"] = traceback.format_exc()
        await self._log(LogLevel.CRITICAL, message, category, data, **kwargs)

    async def _log(
        self,
        level: LogLevel,
        message: str,
        category: LogCategory,
        data: dict[str, Any] | None,
        **kwargs,
    ):
        """内部日志记录方法"""
        # 全局开关：当未开启 debug_tracing 时，忽略 debug 级日志
        if level == LogLevel.DEBUG and not getattr(self, "debug_tracing", False):
            return
        # 规范化结构化数据，提高键名与顺序一致性
        norm_data = self._normalize_data(data)

        # 统一结构化为JSON字符串，再输出，避免格式化键冲突
        context = log_context.get() or LogContext()
        entry = StructuredLogEntry(
            timestamp=datetime.now(UTC),
            level=level,
            category=category,
            message=message,
            context=context,
            data=norm_data,
        )
        text = entry.to_json()
        logger.log(level.value, text)

        # 如果启用了Elasticsearch，也发送到那里
        if self._elasticsearch_handler:
            await self._elasticsearch_handler.handle_log(entry)

        # 执行日志钩子
        for hook in self._log_hooks:
            try:
                if asyncio.iscoroutinefunction(hook):
                    await hook(level, category, message, norm_data)
                else:
                    hook(level, category, message, norm_data)
            except Exception as e:
                logger.error(f"Log hook failed: {e}")

    async def log_trading_decision(
        self,
        decision_type: str,
        symbol: str,
        action: str,
        quantity: float,
        price: float,
        strategy_id: str,
        reason: str,
        confidence: float = 0.0,
        **kwargs,
    ):
        """记录交易决策日志"""
        data = {
            "decision_type": decision_type,
            "symbol": symbol,
            "action": action,
            "quantity": quantity,
            "price": price,
            "strategy_id": strategy_id,
            "reason": reason,
            "confidence": confidence,
            **kwargs,
        }

        await self.info(
            f"Trading decision: {action} {quantity} {symbol} at {price}",
            category=LogCategory.TRADING,
            data=data,
        )

    async def log_risk_event(
        self,
        risk_type: str,
        severity: str,
        description: str,
        current_value: float,
        threshold: float,
        action_taken: str,
        **kwargs,
    ):
        """记录风险事件日志"""
        data = {
            "risk_type": risk_type,
            "severity": severity,
            "description": description,
            "current_value": current_value,
            "threshold": threshold,
            "action_taken": action_taken,
            **kwargs,
        }

        level = LogLevel.WARNING if severity == "medium" else LogLevel.ERROR
        await self._log(
            level, f"Risk event: {risk_type} - {description}", LogCategory.RISK, data
        )

    async def log_strategy_performance(
        self,
        strategy_id: str,
        pnl: float,
        win_rate: float,
        sharpe_ratio: float,
        max_drawdown: float,
        trades_count: int,
        **kwargs,
    ):
        """记录策略性能日志"""
        data = {
            "strategy_id": strategy_id,
            "pnl": pnl,
            "win_rate": win_rate,
            "sharpe_ratio": sharpe_ratio,
            "max_drawdown": max_drawdown,
            "trades_count": trades_count,
            **kwargs,
        }

        await self.info(
            f"Strategy performance: {strategy_id} PnL={pnl:.2f}",
            category=LogCategory.STRATEGY,
            data=data,
        )

    async def log_execution_latency(
        self, operation: str, latency_ms: float, success: bool, **kwargs
    ):
        """记录执行延迟日志"""
        data = {
            "operation": operation,
            "latency_ms": latency_ms,
            "success": success,
            **kwargs,
        }

        level = LogLevel.INFO if success else LogLevel.WARNING
        await self._log(
            level,
            f"Execution: {operation} took {latency_ms:.2f}ms",
            LogCategory.PERFORMANCE,
            data,
        )

    def add_log_hook(self, hook: Callable):
        """添加日志钩子"""
        self._log_hooks.append(hook)

    def remove_log_hook(self, hook: Callable):
        """移除日志钩子"""
        if hook in self._log_hooks:
            self._log_hooks.remove(hook)

    async def cleanup(self) -> bool:
        """清理资源"""
        try:
            if self._elasticsearch_handler:
                await self._elasticsearch_handler.close()

            # 清理loguru处理器
            logger.remove()

            self._configured = False
            return True
        except Exception as e:
            logger.error(f"Failed to cleanup AsyncLogger: {e}")
            return False

    # BaseComponent抽象方法实现
    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        if not self._configured:
            await self._configure_loguru()

            if self.enable_elasticsearch and self.elasticsearch_url:
                await self._setup_elasticsearch()

            self._configured = True
            await self.info(
                "AsyncLogger initialized successfully", category=LogCategory.SYSTEM
            )
        return True

    async def _start_impl(self) -> bool:
        """启动实现"""
        # AsyncLogger在初始化后就可以使用，不需要额外的启动步骤
        await self.info("AsyncLogger started successfully", category=LogCategory.SYSTEM)
        return True

    async def _stop_impl(self) -> bool:
        """停止实现"""
        return await self.cleanup()

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            is_healthy = self._configured

            if is_healthy:
                status = HealthStatus.HEALTHY
                message = "AsyncLogger is operational"
            else:
                status = HealthStatus.UNHEALTHY
                message = "AsyncLogger is not configured"

            details = {
                "configured": self._configured,
                "elasticsearch_enabled": self.enable_elasticsearch,
                "elasticsearch_connected": self._elasticsearch_handler is not None,
                "log_hooks_count": len(self._log_hooks),
                "level": self.level,
            }

            return HealthCheckResult(status=status, message=message, details=details)

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                details={"error": str(e)},
            )


# 全局异步日志器实例
async_logger = AsyncLogger()


async def get_logger() -> AsyncLogger:
    """获取异步日志器实例"""
    if not async_logger._configured:
        await async_logger.initialize()
    return async_logger


def with_log_context(**kwargs):
    """日志上下文装饰器"""

    def decorator(func):
        async def async_wrapper(*args, **func_kwargs):
            # 保存当前上下文
            old_context = log_context.get() or LogContext()

            # 创建新上下文，合并现有上下文和新参数
            old_dict = asdict(old_context)
            new_dict = {**old_dict, **kwargs}
            new_context = LogContext(**new_dict)
            log_context.set(new_context)

            try:
                return await func(*args, **func_kwargs)
            finally:
                # 恢复原始上下文
                log_context.set(old_context)

        def sync_wrapper(*args, **func_kwargs):
            # 保存当前上下文
            old_context = log_context.get() or LogContext()

            # 创建新上下文，合并现有上下文和新参数
            old_dict = asdict(old_context)
            new_dict = {**old_dict, **kwargs}
            new_context = LogContext(**new_dict)
            log_context.set(new_context)

            try:
                return func(*args, **func_kwargs)
            finally:
                # 恢复原始上下文
                log_context.set(old_context)

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator
