#!/usr/bin/env python3
"""
加密工具类 - CryptoHelper

提供API密钥的安全加密和解密功能，解决明文存储安全漏洞
"""

import base64
import os

from cryptography.fernet import Fernet


class CryptoHelper:
    """
    加密工具类 - 处理敏感数据的加密和解密

    使用Fernet对称加密确保API密钥安全存储
    """

    def __init__(self, encryption_key: str | None = None):
        """
        初始化加密工具

        Args:
            encryption_key: 可选的加密密钥，如果不提供则从环境变量读取
        """
        if encryption_key:
            self.key = encryption_key.encode()
        else:
            # 从环境变量获取加密密钥，如果不存在则生成新的
            key_str = os.getenv("ENCRYPTION_KEY")
            if not key_str:
                # 生成新的加密密钥
                self.key = Fernet.generate_key()
                try:
                    import logging
                    _log = logging.getLogger(__name__)
                    _log.info(
                        "Generated new encryption key; set ENCRYPTION_KEY in environment."
                    )
                except Exception:
                    pass
            else:
                self.key = key_str.encode()

        self.fernet = Fernet(self.key)

    def encrypt(self, plaintext: str) -> str:
        """
        加密明文字符串

        Args:
            plaintext: 需要加密的明文

        Returns:
            加密后的base64编码字符串
        """
        if not plaintext:
            return ""

        encrypted_bytes = self.fernet.encrypt(plaintext.encode())
        return base64.b64encode(encrypted_bytes).decode()

    def decrypt(self, encrypted_text: str) -> str:
        """
        解密密文字符串

        Args:
            encrypted_text: 加密的base64编码字符串

        Returns:
            解密后的明文字符串
        """
        if not encrypted_text:
            return ""

        try:
            encrypted_bytes = base64.b64decode(encrypted_text.encode())
            decrypted_bytes = self.fernet.decrypt(encrypted_bytes)
            return decrypted_bytes.decode()
        except Exception:
            # 如果解密失败，可能是明文数据，直接返回
            # 这允许逐步迁移到加密存储
            return encrypted_text

    def is_encrypted(self, text: str) -> bool:
        """
        检查文本是否已加密

        Args:
            text: 要检查的文本

        Returns:
            True if encrypted, False otherwise
        """
        if not text:
            return False

        try:
            encrypted_bytes = base64.b64decode(text.encode())
            self.fernet.decrypt(encrypted_bytes)
            return True
        except Exception:
            return False

    @staticmethod
    def generate_key() -> str:
        """
        生成新的加密密钥

        Returns:
            base64编码的新密钥
        """
        return Fernet.generate_key().decode()

    def encrypt_api_credentials(self, api_key: str, api_secret: str) -> tuple[str, str]:
        """
        加密API凭据

        Args:
            api_key: API密钥
            api_secret: API密钥

        Returns:
            (encrypted_api_key, encrypted_api_secret)
        """
        encrypted_key = self.encrypt(api_key) if api_key else ""
        encrypted_secret = self.encrypt(api_secret) if api_secret else ""

        return encrypted_key, encrypted_secret

    def decrypt_api_credentials(
        self, encrypted_api_key: str, encrypted_api_secret: str
    ) -> tuple[str, str]:
        """
        解密API凭据

        Args:
            encrypted_api_key: 加密的API密钥
            encrypted_api_secret: 加密的API密钥

        Returns:
            (api_key, api_secret)
        """
        api_key = self.decrypt(encrypted_api_key) if encrypted_api_key else ""
        api_secret = self.decrypt(encrypted_api_secret) if encrypted_api_secret else ""

        return api_key, api_secret
