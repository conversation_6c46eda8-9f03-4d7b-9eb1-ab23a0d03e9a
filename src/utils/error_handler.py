"""
统一错误处理工具 - Enhanced Version
提供更精确的异常处理，避免过度使用 except Exception

新增功能：
- 智能异常分类和转换
- 批量异常处理装饰器
- 错误统计和监控
- 上下文感知的错误处理
- 链式异常处理支持
"""

import asyncio
import logging
from collections import defaultdict
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime
from functools import wraps
from typing import Any


class ErrorCategory:
    """错误分类"""

    NETWORK = "network"
    API = "api"
    DATA = "data"
    CONFIG = "config"
    TRADING = "trading"
    SYSTEM = "system"
    VALIDATION = "validation"
    BUSINESS = "business"
    SECURITY = "security"


class TradingSystemError(Exception):
    """交易系统基础异常"""

    def __init__(
        self,
        message: str,
        category: str = ErrorCategory.SYSTEM,
        context: dict[str, Any] = None,
    ):
        super().__init__(message)
        self.category = category
        self.context = context or {}


class NetworkError(TradingSystemError):
    """网络相关异常"""

    def __init__(self, message: str, context: dict[str, Any] = None):
        super().__init__(message, ErrorCategory.NETWORK, context)


class APIError(TradingSystemError):
    """API相关异常"""

    def __init__(
        self,
        message: str,
        api_name: str = "",
        status_code: int = None,
        context: dict[str, Any] = None,
    ):
        context = context or {}
        context.update({"api_name": api_name, "status_code": status_code})
        super().__init__(message, ErrorCategory.API, context)


class DataError(TradingSystemError):
    """数据相关异常"""

    def __init__(
        self, message: str, data_source: str = "", context: dict[str, Any] = None
    ):
        context = context or {}
        context.update({"data_source": data_source})
        super().__init__(message, ErrorCategory.DATA, context)


class ConfigError(TradingSystemError):
    """配置相关异常"""

    def __init__(
        self, message: str, config_key: str = "", context: dict[str, Any] = None
    ):
        context = context or {}
        context.update({"config_key": config_key})
        super().__init__(message, ErrorCategory.CONFIG, context)


class TradingError(TradingSystemError):
    """交易相关异常"""

    def __init__(self, message: str, symbol: str = "", context: dict[str, Any] = None):
        context = context or {}
        context.update({"symbol": symbol})
        super().__init__(message, ErrorCategory.TRADING, context)


class ValidationError(TradingSystemError):
    """验证相关异常"""

    def __init__(
        self,
        message: str,
        field: str = "",
        value: Any = None,
        context: dict[str, Any] = None,
    ):
        context = context or {}
        context.update(
            {"field": field, "value": str(value) if value is not None else None}
        )
        super().__init__(message, ErrorCategory.VALIDATION, context)


class BusinessError(TradingSystemError):
    """业务逻辑异常"""

    def __init__(
        self, message: str, business_rule: str = "", context: dict[str, Any] = None
    ):
        context = context or {}
        context.update({"business_rule": business_rule})
        super().__init__(message, ErrorCategory.BUSINESS, context)


class SecurityError(TradingSystemError):
    """安全相关异常"""

    def __init__(
        self, message: str, security_domain: str = "", context: dict[str, Any] = None
    ):
        context = context or {}
        context.update({"security_domain": security_domain})
        super().__init__(message, ErrorCategory.SECURITY, context)


@dataclass
class ErrorStats:
    """错误统计信息"""

    total_errors: int = 0
    errors_by_category: dict[str, int] = field(default_factory=lambda: defaultdict(int))
    errors_by_component: dict[str, int] = field(
        default_factory=lambda: defaultdict(int)
    )
    last_error_time: datetime | None = None
    error_rate_per_minute: float = 0.0

    def add_error(self, category: str, component: str = "unknown"):
        """记录错误"""
        self.total_errors += 1
        self.errors_by_category[category] += 1
        self.errors_by_component[component] += 1
        self.last_error_time = datetime.now(UTC)

    def get_error_rate(self, time_window_minutes: int = 10) -> float:
        """获取错误率"""
        if not self.last_error_time:
            return 0.0

        now = datetime.now(UTC)
        time_diff = (now - self.last_error_time).total_seconds() / 60

        if time_diff > time_window_minutes:
            return 0.0

        return self.total_errors / max(time_diff, 1)

    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {
            "total_errors": self.total_errors,
            "errors_by_category": dict(self.errors_by_category),
            "errors_by_component": dict(self.errors_by_component),
            "last_error_time": self.last_error_time.isoformat()
            if self.last_error_time
            else None,
            "error_rate_per_minute": self.get_error_rate(),
        }


class ErrorHandler:
    """错误处理器"""

    # 错误统计实例
    _stats = ErrorStats()

    # 常见异常映射到我们的错误类型
    EXCEPTION_MAPPING = {
        ConnectionError: NetworkError,
        asyncio.TimeoutError: NetworkError,
        TimeoutError: NetworkError,
        ValueError: ValidationError,
        KeyError: DataError,
        TypeError: DataError,
        PermissionError: SecurityError,
        FileNotFoundError: ConfigError,
        AttributeError: DataError,
        IndexError: DataError,
        ZeroDivisionError: DataError,
    }

    # 需要重试的异常类型
    RETRYABLE_EXCEPTIONS = {
        NetworkError,
        APIError,
        asyncio.TimeoutError,
        ConnectionError,
    }

    # 严重错误类型（需要立即关注）
    CRITICAL_EXCEPTIONS = {
        SecurityError,
        TradingError,
    }

    @staticmethod
    def categorize_exception(
        exc: Exception, component: str = "unknown"
    ) -> TradingSystemError:
        """将标准异常转换为我们的分类异常"""
        exc_type = type(exc)

        # 如果已经是我们的异常类型，直接返回
        if isinstance(exc, TradingSystemError):
            ErrorHandler._stats.add_error(exc.category, component)
            return exc

        if exc_type in ErrorHandler.EXCEPTION_MAPPING:
            mapped_class = ErrorHandler.EXCEPTION_MAPPING[exc_type]
            categorized_error = mapped_class(
                str(exc),
                context={
                    "original_exception": exc_type.__name__,
                    "component": component,
                    "timestamp": datetime.now(UTC).isoformat(),
                },
            )
        else:
            # 默认返回系统错误
            categorized_error = TradingSystemError(
                str(exc),
                context={
                    "original_exception": exc_type.__name__,
                    "component": component,
                    "timestamp": datetime.now(UTC).isoformat(),
                },
            )

        # 记录错误统计
        ErrorHandler._stats.add_error(categorized_error.category, component)
        return categorized_error

    @staticmethod
    def is_retryable(exc: Exception) -> bool:
        """判断异常是否可重试"""
        return any(
            isinstance(exc, exc_type) for exc_type in ErrorHandler.RETRYABLE_EXCEPTIONS
        )

    @staticmethod
    def is_critical(exc: Exception) -> bool:
        """判断异常是否严重"""
        return any(
            isinstance(exc, exc_type) for exc_type in ErrorHandler.CRITICAL_EXCEPTIONS
        )

    @staticmethod
    def get_error_stats() -> dict[str, Any]:
        """获取错误统计信息"""
        return ErrorHandler._stats.to_dict()

    @staticmethod
    def reset_stats():
        """重置错误统计"""
        ErrorHandler._stats = ErrorStats()

    @staticmethod
    async def handle_with_retry(
        func: Callable,
        max_retries: int = 3,
        delay: float = 1.0,
        backoff_factor: float = 2.0,
        logger: logging.Logger | None = None,
        *args,
        **kwargs,
    ) -> Any:
        """
        带重试的异常处理

        Args:
            func: 要执行的函数
            max_retries: 最大重试次数
            delay: 初始延迟时间
            backoff_factor: 退避系数
            logger: 日志记录器

        Returns:
            函数执行结果
        """
        last_exception = None
        current_delay = delay

        for attempt in range(max_retries + 1):
            try:
                if asyncio.iscoroutinefunction(func):
                    return await func(*args, **kwargs)
                else:
                    return func(*args, **kwargs)

            except Exception as e:
                last_exception = e
                categorized_error = ErrorHandler.categorize_exception(e, func.__name__)

                if attempt == max_retries or not ErrorHandler.is_retryable(e):
                    # 检查是否是严重错误
                    if ErrorHandler.is_critical(categorized_error):
                        if logger:
                            logger.critical(
                                f"CRITICAL ERROR in {func.__name__}: {categorized_error.category} - {str(e)}"
                            )
                        raise categorized_error from e
                    if logger:
                        logger.error(
                            f"Function {func.__name__} failed after {attempt + 1} attempts: "
                            f"{categorized_error.category} - {str(e)}"
                        )
                    raise categorized_error from e

                if logger:
                    logger.warning(
                        f"Function {func.__name__} attempt {attempt + 1} failed: {str(e)}. "
                        f"Retrying in {current_delay}s..."
                    )

                await asyncio.sleep(current_delay)
                current_delay *= backoff_factor

        # 不应该到达这里，但为了安全起见
        raise ErrorHandler.categorize_exception(last_exception, func.__name__)


def safe_async(
    exceptions_to_catch: type[Exception] | tuple = None,
    default_return: Any = None,
    log_errors: bool = True,
    category: str = ErrorCategory.SYSTEM,
):
    """
    安全的异步函数装饰器，提供精确的异常处理

    Args:
        exceptions_to_catch: 要捕获的异常类型
        default_return: 发生异常时的默认返回值
        log_errors: 是否记录错误
        category: 错误分类
    """
    if exceptions_to_catch is None:
        exceptions_to_catch = (NetworkError, APIError, DataError)

    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except exceptions_to_catch as e:
                if log_errors and args and hasattr(args[0], "logger"):
                    logger = args[0].logger
                    await logger.warning(
                        f"{func.__name__} 发生 {category} 错误: {str(e)}"
                    )
                return default_return
            except Exception as e:
                # 转换为分类异常
                component = (
                    getattr(args[0], "component_name", "unknown") if args else "unknown"
                )
                categorized_error = ErrorHandler.categorize_exception(e, component)

                if (
                    log_errors
                    and args
                    and hasattr(args[0], "logger")
                    and args[0].logger
                ):
                    logger = args[0].logger
                    if ErrorHandler.is_critical(categorized_error):
                        await logger.critical(
                            f"{func.__name__} 发生严重错误: {categorized_error.category} - {str(e)}"
                        )
                    else:
                        await logger.error(
                            f"{func.__name__} 发生未预期错误: {categorized_error.category} - {str(e)}"
                        )

                # 如果是严重错误，重新抛出
                if categorized_error.category in [
                    ErrorCategory.SYSTEM,
                    ErrorCategory.SECURITY,
                ] or ErrorHandler.is_critical(categorized_error):
                    raise categorized_error from e

                return default_return

        return wrapper

    return decorator


def safe_sync(
    exceptions_to_catch: type[Exception] | tuple = None,
    default_return: Any = None,
    log_errors: bool = True,
):
    """
    安全的同步函数装饰器
    """
    if exceptions_to_catch is None:
        exceptions_to_catch = (ValueError, KeyError, TypeError)

    def decorator(func: Callable):
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exceptions_to_catch as e:
                if log_errors:
                    print(f"{func.__name__} 发生错误: {str(e)}")
                return default_return
            except Exception as e:
                categorized_error = ErrorHandler.categorize_exception(e, func.__name__)
                if log_errors:
                    if ErrorHandler.is_critical(categorized_error):
                        print(
                            f"CRITICAL: {func.__name__} 发生严重错误: {categorized_error.category} - {str(e)}"
                        )
                    else:
                        print(
                            f"{func.__name__} 发生严重错误: {categorized_error.category} - {str(e)}"
                        )
                raise categorized_error from e

        return wrapper

    return decorator


def safe_operation(
    fallback_value: Any = None, log_errors: bool = True, component: str = "unknown"
):
    """
    简化的安全操作装饰器

    Args:
        fallback_value: 回退值
        log_errors: 是否记录错误
        component: 组件名称
    """

    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                categorized_error = ErrorHandler.categorize_exception(e, component)

                if (
                    log_errors
                    and args
                    and hasattr(args[0], "logger")
                    and args[0].logger
                ):
                    if ErrorHandler.is_critical(categorized_error):
                        await args[0].logger.critical(
                            f"CRITICAL: {func.__name__} failed: {categorized_error.category} - {str(e)}"
                        )
                        raise categorized_error from e
                    else:
                        await args[0].logger.error(
                            f"{func.__name__} failed: {categorized_error.category} - {str(e)}"
                        )

                # 严重错误立即抛出
                if ErrorHandler.is_critical(categorized_error):
                    raise categorized_error from e

                # 返回回退值
                return fallback_value

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                categorized_error = ErrorHandler.categorize_exception(e, component)

                if log_errors:
                    if ErrorHandler.is_critical(categorized_error):
                        print(
                            f"CRITICAL: {func.__name__} failed: {categorized_error.category} - {str(e)}"
                        )
                        raise categorized_error from e
                    else:
                        print(
                            f"{func.__name__} failed: {categorized_error.category} - {str(e)}"
                        )

                if ErrorHandler.is_critical(categorized_error):
                    raise categorized_error from e

                return fallback_value

        # 根据函数类型返回对应的包装器
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator


def error_boundary(
    exceptions_to_handle: type[Exception] | tuple = None,
    log_level: str = "error",
    reraise: bool = False,
    component: str = "unknown",
):
    """
    错误边界装饰器，提供细粒度的异常处理控制

    Args:
        exceptions_to_handle: 要处理的异常类型
        log_level: 日志级别 (debug, info, warning, error, critical)
        reraise: 是否重新抛出异常
        component: 组件名称
    """
    if exceptions_to_handle is None:
        exceptions_to_handle = Exception

    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except exceptions_to_handle as e:
                categorized_error = ErrorHandler.categorize_exception(e, component)

                # 记录日志
                if args and hasattr(args[0], "logger") and args[0].logger:
                    logger = args[0].logger
                    log_method = getattr(logger, log_level, logger.error)
                    await log_method(
                        f"Error boundary caught {categorized_error.category} in {func.__name__}: {str(e)}"
                    )

                if reraise:
                    raise categorized_error from e

                return None

        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except exceptions_to_handle as e:
                categorized_error = ErrorHandler.categorize_exception(e, component)

                # 记录日志
                print(
                    f"Error boundary caught {categorized_error.category} in {func.__name__}: {str(e)}"
                )

                if reraise:
                    raise categorized_error from e

                return None

        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper

    return decorator
