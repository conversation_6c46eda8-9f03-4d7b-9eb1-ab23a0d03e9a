#!/usr/bin/env python3
"""
BTC期权网格交易机器人 - 统一主程序

使用依赖注入容器的统一架构，替代手动依赖管理的复杂性。
基于ApplicationContainer自动组件发现和生命周期管理。
"""

import asyncio
import contextlib
import os
import signal
import sys
from pathlib import Path

# 确保可以导入src模块 - 使用更安全的方法
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

from dotenv import load_dotenv  # noqa: E402

from src.core.application_container import ApplicationContainer  # noqa: E402
from src.core.base_component import HealthStatus  # noqa: E402
from src.utils.async_logger import AsyncLogger  # noqa: E402

# 提前加载环境变量
load_dotenv()


class UnifiedTradingSystem:
    """统一的交易系统主控制器

    使用ApplicationContainer管理所有组件的生命周期和依赖关系
    """

    def __init__(self):
        self.container: ApplicationContainer | None = None
        self.logger: AsyncLogger | None = None
        self._shutdown_event = asyncio.Event()
        self._health_check_task: asyncio.Task | None = None

        # 注册信号处理器
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """设置信号处理器"""

        def signal_handler(signum, _frame):
            try:
                if self.logger:
                    asyncio.create_task(
                        self.logger.info(f"接收到信号 {signum}，开始优雅关闭…")
                    )
            except Exception:
                pass
            # 设置关闭事件，而不是直接创建任务
            self._shutdown_event.set()

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def _check_infrastructure(self) -> bool:
        """检查基础设施可用性"""
        # 检查基础设施可用性
        if self.logger:
            await self.logger.info("🔍 检查基础设施可用性…")

        # 检查数据库连接
        import socket

        from src.core.config_manager import ConfigManager

        try:
            # 加载配置检查数据库
            config_manager = ConfigManager()
            await config_manager.initialize()
            db_config = config_manager.get("database", {})

            host = db_config.get("host", "localhost")
            port = int(db_config.get("port", 5432))

            if self.logger:
                await self.logger.info(f"检查数据库连接 {host}:{port}…")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            try:
                result = sock.connect_ex((host, port))
                if result != 0:
                    if self.logger:
                        await self.logger.error(
                            f"数据库连接失败: 无法连接到 {host}:{port}"
                        )
                        await self.logger.info("请确保PostgreSQL/TimescaleDB服务正在运行")
                    return False
                if self.logger:
                    await self.logger.info(f"✅ 数据库连接正常: {host}:{port}")
            finally:
                sock.close()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"基础设施检查失败: {e}")
            return False

        return True

    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            # 先检查基础设施
            if not await self._check_infrastructure():
                if self.logger:
                    await self.logger.error("基础设施检查未通过，系统无法启动")
                    await self.logger.info(
                        "交易系统需要数据库连接来确保数据完整性和风险控制"
                    )
                return False

            if self.logger:
                await self.logger.info("🔧 创建应用容器…")
            # 创建应用容器
            self.container = ApplicationContainer()

            if self.logger:
                await self.logger.info("开始初始化容器…")
            # 初始化容器
            success = await self.container.initialize()
            if not success:
                if self.logger:
                    await self.logger.error("容器初始化失败")
                return False

            # 获取日志器（DI）
            # 尝试通过依赖注入系统获取AsyncLogger，因为组件实例在初始化阶段还未创建
            try:
                from src.utils.async_logger import AsyncLogger

                self.logger = await self.container.dependency_injector.resolve(
                    AsyncLogger
                )
                pass
            except Exception as e:
                if self.logger:
                    await self.logger.warning(f"无法获取AsyncLogger: {e}")
                self.logger = None

            # 重要：将日志器设置回ApplicationContainer，这样才能看到详细的启动日志
            if self.logger:
                self.container.set_logger(self.logger)
                await self.logger.info("启动BTC期权网格交易系统...")
                await self.logger.info("系统初始化完成，开始启动组件...")
                await self.logger.info("✅ 系统初始化成功，日志器已配置")
            else:
                pass
            if self.logger:
                await self.logger.info("✅ 系统初始化成功")
            return True

        except Exception as e:
            import traceback
            if self.logger:
                await self.logger.error(f"系统初始化异常: {e}")
                await self.logger.debug(f"错误详情: {traceback.format_exc()}")
            if self.logger:
                await self.logger.error(f"系统初始化失败: {e}")
            return False

    async def start(self) -> bool:
        """启动系统"""
        try:
            if self.logger:
                await self.logger.info("开始启动系统…")
            if not self.container:
                if self.logger:
                    await self.logger.error("容器未初始化")
                if self.logger:
                    await self.logger.error("容器未初始化")
                return False

            if self.logger:
                await self.logger.info("⚡ 启动容器（会自动启动所有组件）…")
            # 启动容器（会自动启动所有组件）
            try:
                success = await asyncio.wait_for(self.container.start(), timeout=120.0)
                pass
            except TimeoutError:
                if self.logger:
                    await self.logger.warning("容器启动超时（120秒）")
                success = False
            if not success:
                if self.logger:
                    await self.logger.error("容器启动失败")
                if self.logger:
                    await self.logger.error("容器启动失败")
                return False
            else:
                if self.logger:
                    await self.logger.debug("容器启动成功")

            if self.logger:
                await self.logger.info("🏥 启动健康检查…")
            # 启动健康检查
            self._health_check_task = asyncio.create_task(self._health_monitor())

            if self.logger:
                await self.logger.info("🎉 交易系统启动成功！")

            if self.logger:
                await self.logger.info("✅ 系统启动完成")
            return True

        except Exception as e:
            import traceback
            if self.logger:
                await self.logger.error(f"系统启动异常: {e}")
                await self.logger.debug(f"错误详情: {traceback.format_exc()}")
            if self.logger:
                await self.logger.error(f"系统启动失败: {e}")
            return False

    async def run(self):
        """运行系统主循环"""
        try:
            if self.logger:
                await self.logger.info("系统进入主循环，等待关闭信号...")

            # 等待关闭信号
            await self._shutdown_event.wait()

            # 接收到关闭信号后主动关闭系统
            if self.logger:
                await self.logger.info("🔄 接收到关闭信号，开始系统关闭流程…")
            await self.shutdown()

        except Exception as e:
            if self.logger:
                await self.logger.error(f"主循环异常: {e}")
            raise

    async def shutdown(self):
        """关闭系统"""
        try:
            if self.logger:
                await self.logger.info("开始系统关闭流程...")

            # 发送系统关闭事件（尽早发布，供通知组件感知）
            try:
                if self.container and self.container.event_bus:
                    from src.core.event_bus import SystemEvent

                    await self.container.event_bus.publish(
                        SystemEvent(
                            component="UnifiedTradingSystem",
                            action="shutdown",
                            status="stopping",
                            details={},
                        )
                    )
            except Exception:
                # 发布失败不影响后续关闭流程
                pass

            # 停止健康检查
            if self._health_check_task:
                self._health_check_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._health_check_task

            # 停止容器（会自动停止所有组件）
            if self.container:
                await self.container.stop()

            # 设置关闭事件
            self._shutdown_event.set()

            if self.logger:
                await self.logger.info("🔴 系统关闭完成")

        except Exception as e:
            if self.logger:
                await self.logger.error(f"关闭过程异常: {e}")

    async def _health_monitor(self):
        """健康监控循环"""
        try:
            while True:
                try:
                    await asyncio.sleep(60)  # 每分钟检查一次

                    if not self.container:
                        continue

                    # 获取系统健康状态
                    health_result = await self.container.health_check()

                    if health_result.status == HealthStatus.UNHEALTHY:
                        if self.logger:
                            await self.logger.error(
                                f"系统健康检查失败: {health_result.message}"
                            )
                    elif health_result.status == HealthStatus.DEGRADED and self.logger:
                        await self.logger.warning(
                            f"系统性能下降: {health_result.message}"
                        )

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    if self.logger:
                        await self.logger.error(f"健康检查异常: {e}")
                    # 继续运行，不因健康检查失败而停止系统

        except asyncio.CancelledError:
            pass

    async def get_system_status(self) -> dict:
        """获取系统状态"""
        if not self.container:
            return {"status": "not_initialized"}

        try:
            # 获取所有组件状态
            all_components = self.container.get_all_components()
            component_statuses = {}

            for component_name, component in all_components.items():
                try:
                    if hasattr(component, "state"):
                        component_statuses[component_name] = component.state.value
                    else:
                        component_statuses[component_name] = "unknown"
                except Exception:
                    component_statuses[component_name] = "error"

            # 获取健康状态
            health_result = await self.container.health_check()

            return {
                "status": "running",
                "health": health_result.status.value,
                "components": component_statuses,
                "registered_components": len(all_components),
                "health_message": health_result.message,
            }

        except Exception as e:
            return {"status": "error", "error": str(e)}


async def main():
    """主函数"""
    system = UnifiedTradingSystem()

    try:
        # 初始化系统
        if not await system.initialize():
            print("❌ 系统初始化失败")
            return 1

        # 启动系统
        if not await system.start():
            print("❌ 系统启动失败")
            await system.shutdown()
            return 1

        # 运行系统（包含内置关闭流程）
        await system.run()

        return 0

    except KeyboardInterrupt:
        # 键盘中断
        if system.logger:
            await system.logger.info("🔔 接收到KeyboardInterrupt")
        # run()方法已经处理了关闭，这里不需要重复调用
        return 0
    except Exception as e:
        if system.logger:
            await system.logger.error(f"系统运行异常: {e}")
        await system.shutdown()
        return 1


if __name__ == "__main__":
    # 检查Python版本

    # 检查必要的环境变量
    required_env_vars = []

    # IBKR配置检查
    ibkr_enabled = os.getenv("ENABLE_IBKR", "true").lower() == "true"

    if ibkr_enabled:
        # IBKR 提示
        pass

    # 检查Telegram配置（如果需要）
    if os.getenv("ENABLE_TELEGRAM", "false").lower() == "true":
        required_env_vars.extend(["TELEGRAM_BOT_TOKEN", "TELEGRAM_CHAT_ID"])

    # 检查Deribit配置（如果需要）——公共模式下不强制API Key
    if (
        os.getenv("ENABLE_DERIBIT", "true").lower() == "true"
        and os.getenv("DERIBIT_API_KEY")
        and os.getenv("DERIBIT_API_SECRET")
    ):
        required_env_vars.extend(["DERIBIT_API_KEY", "DERIBIT_API_SECRET"])

    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    if missing_vars:
        # 缺少环境变量：在生产中可使用logger输出
        pass
        sys.exit(1)

    try:
        # 运行主程序
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        sys.exit(0)
    except Exception as e:
        # 程序启动失败
        pass
        sys.exit(1)
