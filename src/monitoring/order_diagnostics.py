from __future__ import annotations

import asyncio
from collections import deque
from dataclasses import dataclass
from datetime import UTC, datetime
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import BaseEvent, EventBus


@dataclass
class RejectionSample:
    ts: datetime
    order_id: str
    symbol: str
    reason: str | None
    ibkr_bid: float | None
    ibkr_ask: float | None
    proxy: dict[str, Any] | None


class OrderDiagnosticsMonitor(BaseComponent):
    """订单诊断监控

    - 订阅 order_rejected / order_rejected_details
    - 维护计数与最近N条样本（默认100）
    - 便于调参与审计追踪
    """

    event_bus: EventBus | None = None

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("OrderDiagnosticsMonitor", config or {})
        self._count_rejected = 0
        self._count_details = 0
        self._samples: deque[RejectionSample] = deque(
            maxlen=int(self.config.get("max_samples", 100))
        )
        self._lock = asyncio.Lock()

    async def _initialize_impl(self) -> bool:
        try:
            # 从配置管理器读取采样上限（如存在）
            try:
                if self.dependency_injector:
                    from src.core.config_manager import ConfigManager

                    cfgm = await self.dependency_injector.resolve(ConfigManager)
                    mon_cfg = cfgm.get("monitoring.order_diagnostics") or {}
                    ms = int(mon_cfg.get("max_samples", 0))
                    if ms and ms > 0:
                        self._samples = deque(maxlen=ms)
            except Exception:
                pass
            if self.event_bus:
                from src.core.event_bus import EventType

                await self.event_bus.subscribe(
                    EventType.ORDER_REJECTED, self._on_rejected
                )
                await self.event_bus.subscribe(
                    EventType.ORDER_REJECTED_DETAILS, self._on_details
                )
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"OrderDiagnosticsMonitor init failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        return True

    async def _stop_impl(self) -> bool:
        return True

    async def _on_rejected(self, ev: BaseEvent):
        try:
            async with self._lock:
                self._count_rejected += 1
                d = ev.data or {}
                self._samples.append(
                    RejectionSample(
                        ts=datetime.now(UTC),
                        order_id=str(d.get("order_id")),
                        symbol=str(d.get("symbol")),
                        reason=str(d.get("reason"))
                        if d.get("reason") is not None
                        else None,
                        ibkr_bid=d.get("ibkr_bid"),
                        ibkr_ask=d.get("ibkr_ask"),
                        proxy=d.get("liquidity_proxy")
                        if isinstance(d.get("liquidity_proxy"), dict)
                        else None,
                    )
                )
        except Exception:
            pass

    async def _on_details(self, ev: BaseEvent):
        try:
            async with self._lock:
                self._count_details += 1
                d = ev.data or {}
                # 将详情补充到最近同ID样本（若存在）
                oid = str(d.get("order_id"))
                for s in reversed(self._samples):
                    if s.order_id == oid:
                        s.ibkr_bid = d.get("ibkr_bid")
                        s.ibkr_ask = d.get("ibkr_ask")
                        break
        except Exception:
            pass

    async def _health_check_impl(self) -> HealthCheckResult:
        try:
            details = {
                "rejected": self._count_rejected,
                "rejected_details": self._count_details,
                "samples": len(self._samples),
            }
            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="Order diagnostics running",
                details=details,
            )
        except Exception as e:
            return HealthCheckResult(status=HealthStatus.DEGRADED, message=str(e))

    # 可供外部查询最近样本
    def get_recent_samples(self) -> list[dict[str, Any]]:
        return [
            {
                "ts": s.ts.isoformat(),
                "order_id": s.order_id,
                "symbol": s.symbol,
                "reason": s.reason,
                "ibkr_bid": s.ibkr_bid,
                "ibkr_ask": s.ibkr_ask,
                "proxy": s.proxy,
            }
            for s in list(self._samples)
        ]
