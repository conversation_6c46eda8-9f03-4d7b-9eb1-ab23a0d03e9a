from __future__ import annotations

from prometheus_client import Counter

# Global Prometheus counters for strict-mode diagnostics
STRICT_MISSING_DATA = Counter(
    "strict_missing_data_total",
    "Count of missing/invalid inputs in strict mode",
    labelnames=("component", "key"),
)

STRICT_SKIPS = Counter(
    "strict_skips_total",
    "Count of operations skipped due to strict-mode constraints",
    labelnames=("component", "reason"),
)


def inc_missing(component: str, key: str) -> None:
    from contextlib import suppress

    with suppress(Exception):
        STRICT_MISSING_DATA.labels(component=component, key=key).inc()


def inc_skip(component: str, reason: str) -> None:
    from contextlib import suppress

    with suppress(Exception):
        STRICT_SKIPS.labels(component=component, reason=reason).inc()
