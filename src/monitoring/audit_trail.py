#!/usr/bin/env python3
"""
审计流水 - AuditTrail

P0 实现：
- 订阅策略决策、订单生命周期、仓位更新事件
- 将关键信息以 JSONL 方式落盘（按日分文件）
- 可通过配置开关启用/禁用，默认仅文件落地

配置（config.yaml）：

monitoring:
  audit_trail:
    enabled: true
    sinks: ["file"]           # 目前仅实现 file
    file:
      dir: logs/audit
      retention_days: 90

"""

import asyncio
import json
import os
from dataclasses import asdict, is_dataclass
from datetime import UTC, datetime, timedelta
from pathlib import Path
from typing import Any

from src.core.base_component import BaseComponent, HealthCheckResult, HealthStatus
from src.core.event_bus import (
    BaseEvent,
    EventBus,
    EventType,
    PositionUpdateEvent,
    TradingDecisionEvent,
)
try:
    from src.core.config_manager import ConfigManager
except Exception:  # 在测试环境下不强制依赖 yaml/pydantic
    ConfigManager = None  # type: ignore


class AuditTrail(BaseComponent):
    """审计流水记录器（文件 JSONL 落盘）。"""

    # 依赖注入
    event_bus: EventBus | None
    config_manager: Any | None

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("AuditTrail", config)
        self.event_bus = None
        self.config_manager = None

        # 运行配置（默认）
        self._enabled = True
        self._sinks: list[str] = ["file"]
        self._file_dir = Path("logs/audit")
        self._retention_days = 90

        # 写入锁，防止并发写交错
        self._locks: dict[str, asyncio.Lock] = {}

        # 后台任务
        self._cleanup_task: asyncio.Task | None = None

        # 指标
        self._counters: dict[str, int] = {
            "decisions": 0,
            "orders": 0,
            "fills": 0,
            "entries_exits": 0,
        }

    async def _initialize_impl(self) -> bool:
        try:
            # 读取配置
            await self._load_config()

            # 准备目录
            if "file" in self._sinks and self._enabled:
                self._file_dir.mkdir(parents=True, exist_ok=True)

            if self.logger:
                await self.logger.info(
                    f"AuditTrail initialized (enabled={self._enabled}, sinks={self._sinks})"
                )
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"AuditTrail init failed: {e}")
            return False

    async def _start_impl(self) -> bool:
        try:
            if not self._enabled:
                if self.logger:
                    await self.logger.info("AuditTrail disabled by config")
                return True

            # 解析依赖（兜底）
            if not self.event_bus and hasattr(self, "dependency_injector") and self.dependency_injector:
                try:
                    self.event_bus = await self.dependency_injector.resolve(EventBus)
                except Exception:
                    pass

            if not self.event_bus:
                if self.logger:
                    await self.logger.warning("EventBus not available; AuditTrail inactive")
                return True

            # 订阅事件
            await self._subscribe_events()

            # 启动清理任务
            if "file" in self._sinks:
                self._cleanup_task = asyncio.create_task(self._retention_cleanup_loop())

            if self.logger:
                await self.logger.info("AuditTrail started")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"AuditTrail start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        try:
            if self._cleanup_task and not self._cleanup_task.done():
                self._cleanup_task.cancel()
                with asyncio.CancelledError:
                    pass
            if self.logger:
                await self.logger.info("AuditTrail stopped")
            return True
        except Exception as e:
            if self.logger:
                await self.logger.error(f"AuditTrail stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        try:
            return HealthCheckResult(
                status=HealthStatus.HEALTHY,
                message="AuditTrail operational",
                details={
                    "enabled": self._enabled,
                    "sinks": self._sinks,
                    "counters": dict(self._counters),
                },
            )
        except Exception as e:
            return HealthCheckResult(status=HealthStatus.UNHEALTHY, message=str(e))

    async def _load_config(self) -> None:
        try:
            # 通过 ConfigManager 获取配置
            if not self.config_manager and hasattr(self, "dependency_injector") and self.dependency_injector:
                try:
                    self.config_manager = await self.dependency_injector.resolve(
                        ConfigManager
                    )
                except Exception:
                    pass

            cfg = {}
            try:
                if self.config_manager:
                    allcfg = self.config_manager.get_section("monitoring") or {}
                    cfg = (allcfg or {}).get("audit_trail", {})
            except Exception:
                cfg = {}

            self._enabled = bool(cfg.get("enabled", True))
            self._sinks = list(cfg.get("sinks", ["file"]))
            file_cfg = cfg.get("file", {}) or {}
            dir_path = file_cfg.get("dir", "logs/audit")
            self._file_dir = Path(dir_path)
            self._retention_days = int(file_cfg.get("retention_days", 90))
        except Exception:
            # 使用默认
            self._enabled = True
            self._sinks = ["file"]

    async def _subscribe_events(self) -> None:
        assert self.event_bus is not None

        # 决策事件（入/出场意图）
        await self.event_bus.subscribe(EventType.TRADING_DECISION, self._on_trading_decision)

        # 订单生命周期
        order_events = [
            EventType.ORDER_SUBMITTED,
            EventType.ORDER_EXECUTED,
            EventType.ORDER_FILLED,
            EventType.ORDER_REJECTED,
            EventType.ORDER_FAILED,
            EventType.ORDER_CANCELLED,
            EventType.ORDER_CANCEL,
            EventType.ORDER_FILL_FEED,  # 备用馈送
        ]
        for et in order_events:
            await self.event_bus.subscribe(et, self._on_order_event)

        # 仓位更新（事实的开/平仓）
        await self.event_bus.subscribe(EventType.POSITION_UPDATE, self._on_position_event)
        await self.event_bus.subscribe(EventType.POSITION_UPDATED, self._on_position_updated)

    # 事件处理
    async def _on_trading_decision(self, event: TradingDecisionEvent):
        try:
            record = {
                "time": self._iso(getattr(event, "timestamp", datetime.now(UTC))),
                "event_type": "TRADING_DECISION",
                "strategy_name": getattr(event, "strategy_name", None),
                "action": getattr(event, "action", None),
                "instruments": getattr(event, "instruments", None),
                "confidence": getattr(event, "confidence", None),
                "risk_params": getattr(event, "risk_params", None),
                "decision_data": getattr(event, "decision_data", None),
                "source": getattr(event, "source", None),
                "event_id": getattr(event, "event_id", None),
            }
            await self._write_jsonl("decisions", record)
            self._counters["decisions"] += 1
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Audit decision write failed: {e}")

    async def _on_order_event(self, event: BaseEvent):
        try:
            etype = getattr(event, "event_type", "order")
            data = getattr(event, "data", {}) or {}
            ts = getattr(event, "timestamp", datetime.now(UTC))
            base = {
                "time": self._iso(ts),
                "event_type": str(etype),
                "source": getattr(event, "source", None),
                "event_id": getattr(event, "event_id", None),
            }

            # 区分成交（fills）与其他订单事件（orders）
            target = "fills" if str(etype) in {EventType.ORDER_FILLED.value, EventType.ORDER_FILL_FEED.value} else "orders"
            # 合并字典（包含原始 data）
            record = {**base, **self._to_plain(data)}
            await self._write_jsonl(target, record)
            self._counters[target] += 1
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Audit order write failed: {e}")

    async def _on_position_event(self, event: PositionUpdateEvent):
        try:
            # 仅记录开/平仓事实
            change_type = getattr(event, "change_type", "")
            if change_type not in {"open", "close"}:
                return
            ts = getattr(event, "timestamp", datetime.now(UTC))
            record = {
                "time": self._iso(ts),
                "event_type": "POSITION_UPDATE",
                "type": "entry" if change_type == "open" else "exit",
                "symbol": getattr(event, "symbol", None),
                "position_change": self._to_plain(getattr(event, "position_change", {})),
                "current_position": self._to_plain(getattr(event, "current_position", {})),
                "pnl_data": self._to_plain(getattr(event, "pnl_data", {})),
                "source": getattr(event, "source", None),
                "event_id": getattr(event, "event_id", None),
            }
            await self._write_jsonl("entries_exits", record)
            self._counters["entries_exits"] += 1
        except Exception as e:
            if self.logger:
                await self.logger.warning(f"Audit position write failed: {e}")

    async def _on_position_updated(self, event: BaseEvent):
        try:
            # 增量更新（非必须）；记录到 orders 流或单独的 position_updates 也可
            # 这里暂不写，避免冗余；如有需要，可开启：
            # ts = getattr(event, "timestamp", datetime.now(UTC))
            # record = {"time": self._iso(ts), "event_type": "POSITION_UPDATED", **self._to_plain(getattr(event, "data", {}))}
            # await self._write_jsonl("entries_exits", record)
            return
        except Exception:
            return

    # 落地实现
    async def _write_jsonl(self, stream: str, record: dict[str, Any]) -> None:
        if "file" not in self._sinks:
            return
        day = datetime.now(UTC).strftime("%Y%m%d")
        fname = f"{stream}-{day}.jsonl"
        fpath = self._file_dir / fname
        lock = self._locks.setdefault(stream, asyncio.Lock())
        line = json.dumps(record, ensure_ascii=False, default=str) + "\n"

        async with lock:
            try:
                # 使用线程池写入，避免阻塞事件循环
                await asyncio.to_thread(self._append_text, fpath, line)
            except Exception as e:
                if self.logger:
                    await self.logger.warning(f"AuditTrail write failed: {e}")

    @staticmethod
    def _append_text(path: Path, text: str) -> None:
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "a", encoding="utf-8") as f:
            f.write(text)

    async def _retention_cleanup_loop(self):
        while True:
            try:
                await asyncio.sleep(3600)  # 每小时检查一次
                if self._retention_days <= 0:
                    continue
                cutoff = datetime.now(UTC) - timedelta(days=self._retention_days)
                for p in self._file_dir.glob("*.jsonl"):
                    try:
                        # 从文件名解析日期（最后 8 位）
                        stem = p.stem  # e.g., decisions-20250101
                        date_part = stem.split("-")[-1]
                        dt = datetime.strptime(date_part, "%Y%m%d").replace(tzinfo=UTC)
                        if dt < cutoff:
                            os.remove(p)
                    except Exception:
                        continue
            except asyncio.CancelledError:
                break
            except Exception:
                await asyncio.sleep(3600)

    # 工具
    @staticmethod
    def _iso(ts: datetime) -> str:
        try:
            return ts.astimezone(UTC).isoformat()
        except Exception:
            return datetime.now(UTC).isoformat()

    @staticmethod
    def _to_plain(obj: Any) -> Any:
        try:
            if obj is None:
                return None
            if is_dataclass(obj):
                return asdict(obj)
            if isinstance(obj, dict):
                out = {}
                for k, v in obj.items():
                    out[k] = AuditTrail._to_plain(v)
                return out
            if isinstance(obj, (list, tuple)):
                return [AuditTrail._to_plain(x) for x in obj]
            # numpy 等不可序列化类型转为基本类型
            try:
                import numpy as _np

                if isinstance(obj, (_np.floating, _np.integer)):
                    return float(obj)
            except Exception:
                pass
            return obj
        except Exception:
            return obj
