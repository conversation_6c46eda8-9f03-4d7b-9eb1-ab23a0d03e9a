#!/usr/bin/env python3
"""
性能监控系统 - PerformanceMonitor

实现全面的系统性能监控和分析，提供：
- 实时性能指标收集和分析
- 系统资源使用监控
- 交易性能指标追踪
- 自动性能优化建议
- 性能异常检测和告警

设计要点：
- 低开销的指标收集
- 多维度性能分析
- 自适应阈值监控
- 性能趋势分析和预测
- 集成告警和自愈机制
"""

import asyncio
import contextlib
import statistics
import time
from collections import defaultdict, deque
from collections.abc import Callable
from dataclasses import dataclass, field
from datetime import UTC, datetime, timedelta
from enum import Enum
from typing import Any

import psutil

from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)


class MetricType(Enum):
    """指标类型"""

    COUNTER = "counter"  # 计数器（累积值）
    GAUGE = "gauge"  # 仪表盘（瞬时值）
    HISTOGRAM = "histogram"  # 直方图（分布统计）
    TIMER = "timer"  # 计时器（执行时间）
    RATE = "rate"  # 速率（每秒事件数）


class PerformanceLevel(Enum):
    """性能等级"""

    EXCELLENT = "excellent"  # 优秀 (> 95th percentile)
    GOOD = "good"  # 良好 (80-95th percentile)
    AVERAGE = "average"  # 平均 (50-80th percentile)
    POOR = "poor"  # 较差 (20-50th percentile)
    CRITICAL = "critical"  # 严重 (< 20th percentile)


@dataclass
class MetricValue:
    """指标值"""

    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    tags: dict[str, str] = field(default_factory=dict)
    unit: str = ""


@dataclass
class PerformanceMetrics:
    """性能指标汇总"""

    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))

    # 系统指标
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    disk_io_read_mb: float = 0.0
    disk_io_write_mb: float = 0.0
    network_sent_mb: float = 0.0
    network_recv_mb: float = 0.0

    # 应用指标
    active_connections: int = 0
    total_requests: int = 0
    requests_per_second: float = 0.0
    avg_response_time_ms: float = 0.0
    error_rate_percent: float = 0.0

    # 交易指标
    orders_per_minute: float = 0.0
    positions_count: int = 0
    pnl_today: float = 0.0
    trade_success_rate: float = 0.0
    avg_trade_latency_ms: float = 0.0

    # 数据指标
    data_feed_latency_ms: float = 0.0
    data_points_per_second: float = 0.0
    cache_hit_rate: float = 0.0
    event_queue_size: int = 0

    # 质量评分
    overall_performance: PerformanceLevel = PerformanceLevel.AVERAGE


@dataclass
class PerformanceAlert:
    """性能告警"""

    metric_name: str
    current_value: float
    threshold: float
    severity: str
    message: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))
    resolved: bool = False


@dataclass
class PerformanceRecommendation:
    """性能优化建议"""

    category: str
    priority: str
    description: str
    impact_estimate: str
    implementation_effort: str
    timestamp: datetime = field(default_factory=lambda: datetime.now(UTC))


class PerformanceMonitor(BaseComponent):
    """
    性能监控系统

    提供全面的系统性能监控、分析和优化建议
    """

    def __init__(self, config: dict[str, Any] | None = None):
        super().__init__("PerformanceMonitor", config)

        # 配置
        self.collection_interval = self.config.get("collection_interval", 5.0)
        self.metric_retention_hours = self.config.get("metric_retention_hours", 24)
        self.alert_thresholds = self.config.get(
            "alert_thresholds",
            {
                "cpu_percent": 80.0,
                "memory_percent": 85.0,
                "error_rate_percent": 5.0,
                "avg_response_time_ms": 1000.0,
                "data_feed_latency_ms": 500.0,
            },
        )
        self.performance_benchmarks = self.config.get(
            "performance_benchmarks",
            {
                "excellent": {"cpu": 50, "memory": 60, "response_time": 100},
                "good": {"cpu": 70, "memory": 75, "response_time": 300},
                "average": {"cpu": 85, "memory": 85, "response_time": 500},
                "poor": {"cpu": 95, "memory": 95, "response_time": 1000},
            },
        )

        # 数据存储
        self.metrics_history: dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.current_metrics = PerformanceMetrics()
        self.active_alerts: dict[str, PerformanceAlert] = {}
        self.recommendations: list[PerformanceRecommendation] = []

        # 系统监控
        self.process = psutil.Process()
        self.last_net_io = None
        self.last_disk_io = None
        self.last_net_io_time = None
        self.last_disk_io_time = None

        # 任务管理
        self.collection_task: asyncio.Task | None = None
        self.analysis_task: asyncio.Task | None = None

        # 回调函数
        self.alert_callbacks: list[Callable[[PerformanceAlert], None]] = []
        self.recommendation_callbacks: list[
            Callable[[PerformanceRecommendation], None]
        ] = []

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            await self.log_initialization(True, "Initializing PerformanceMonitor")

            # 初始化系统监控基线
            await self._initialize_system_baseline()

            await self.log_initialization(
                True, "PerformanceMonitor initialized successfully"
            )
            return True

        except Exception as e:
            await self.log_initialization(
                False, f"PerformanceMonitor initialization failed: {e}"
            )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            await self.log_startup(True, "Starting PerformanceMonitor")

            # 启动指标收集任务
            self.collection_task = asyncio.create_task(self._metrics_collection_loop())

            # 启动性能分析任务
            self.analysis_task = asyncio.create_task(self._performance_analysis_loop())

            await self.log_startup(True, "PerformanceMonitor started successfully")
            return True

        except Exception as e:
            await self.log_startup(False, f"PerformanceMonitor start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            await self.log_shutdown(True, "Stopping PerformanceMonitor")

            # 停止任务
            if self.collection_task:
                self.collection_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self.collection_task

            if self.analysis_task:
                self.analysis_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self.analysis_task

            await self.log_shutdown(True, "PerformanceMonitor stopped successfully")
            return True

        except Exception as e:
            await self.log_shutdown(False, f"PerformanceMonitor stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            # 检查任务状态
            collection_healthy = (
                self.collection_task and not self.collection_task.done()
            )
            analysis_healthy = self.analysis_task and not self.analysis_task.done()

            # 检查系统资源
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent

            if cpu_percent > 95 or memory_percent > 95:
                status = HealthStatus.UNHEALTHY
                message = f"System resources critical: CPU {cpu_percent}%, Memory {memory_percent}%"
            elif not collection_healthy or not analysis_healthy:
                status = HealthStatus.DEGRADED
                message = "Performance monitoring tasks not running properly"
            elif cpu_percent > 80 or memory_percent > 80:
                status = HealthStatus.DEGRADED
                message = f"High system resource usage: CPU {cpu_percent}%, Memory {memory_percent}%"
            else:
                status = HealthStatus.HEALTHY
                message = "Performance monitoring operating normally"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "collection_task_running": collection_healthy,
                    "analysis_task_running": analysis_healthy,
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent,
                    "active_alerts": len(self.active_alerts),
                    "metrics_collected": len(self.metrics_history),
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def _initialize_system_baseline(self):
        """初始化系统基线"""
        try:
            # 收集初始网络和磁盘IO数据
            self.last_net_io = psutil.net_io_counters()
            self.last_disk_io = psutil.disk_io_counters()
            # 初始化时间戳
            current_time = time.time()
            self.last_net_io_time = current_time
            self.last_disk_io_time = current_time

            # 预热CPU和内存监控
            psutil.cpu_percent()
            psutil.virtual_memory()

            await self.safe_log_info("System baseline initialized")

        except Exception as e:
            await self.safe_log_error(f"Failed to initialize system baseline: {e}")

    async def _metrics_collection_loop(self):
        """指标收集循环"""
        try:
            while True:
                try:
                    # 收集系统指标
                    await self._collect_system_metrics()

                    # 收集应用指标
                    await self._collect_application_metrics()

                    # 检查告警
                    await self._check_alerts()

                    await asyncio.sleep(self.collection_interval)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    await self.safe_log_error(f"Metrics collection error: {e}")
                    await asyncio.sleep(1)

        except asyncio.CancelledError:
            pass
        finally:
            await self.safe_log_info("Metrics collection loop stopped")

    async def _performance_analysis_loop(self):
        """性能分析循环"""
        try:
            while True:
                try:
                    # 分析性能趋势
                    await self._analyze_performance_trends()

                    # 生成优化建议
                    await self._generate_recommendations()

                    # 清理过期数据
                    await self._cleanup_old_data()

                    # 每分钟分析一次
                    await asyncio.sleep(60)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    await self.safe_log_error(f"Performance analysis error: {e}")
                    await asyncio.sleep(10)

        except asyncio.CancelledError:
            pass
        finally:
            await self.safe_log_info("Performance analysis loop stopped")

    async def _collect_system_metrics(self):
        """收集系统指标"""
        try:
            # CPU和内存
            self.current_metrics.cpu_percent = psutil.cpu_percent()
            memory_info = psutil.virtual_memory()
            self.current_metrics.memory_percent = memory_info.percent
            self.current_metrics.memory_used_mb = memory_info.used / 1024 / 1024

            # 网络IO
            net_io = psutil.net_io_counters()
            current_time = time.time()
            if self.last_net_io and self.last_net_io_time:
                time_delta = current_time - self.last_net_io_time
                if time_delta > 0:
                    self.current_metrics.network_sent_mb = (
                        (net_io.bytes_sent - self.last_net_io.bytes_sent)
                        / 1024
                        / 1024
                        / time_delta
                    )
                    self.current_metrics.network_recv_mb = (
                        (net_io.bytes_recv - self.last_net_io.bytes_recv)
                        / 1024
                        / 1024
                        / time_delta
                    )

            self.last_net_io = net_io
            self.last_net_io_time = current_time

            # 磁盘IO
            disk_io = psutil.disk_io_counters()
            if disk_io and self.last_disk_io and self.last_disk_io_time:
                time_delta = current_time - self.last_disk_io_time
                if time_delta > 0:
                    self.current_metrics.disk_io_read_mb = (
                        (disk_io.read_bytes - self.last_disk_io.read_bytes)
                        / 1024
                        / 1024
                        / time_delta
                    )
                    self.current_metrics.disk_io_write_mb = (
                        (disk_io.write_bytes - self.last_disk_io.write_bytes)
                        / 1024
                        / 1024
                        / time_delta
                    )

            if disk_io:
                self.last_disk_io = disk_io
                self.last_disk_io_time = current_time

            # 记录历史数据
            await self._record_metric("cpu_percent", self.current_metrics.cpu_percent)
            await self._record_metric(
                "memory_percent", self.current_metrics.memory_percent
            )
            await self._record_metric(
                "memory_used_mb", self.current_metrics.memory_used_mb
            )

        except Exception as e:
            await self.safe_log_error(f"Failed to collect system metrics: {e}")

    async def _collect_application_metrics(self):
        """收集应用指标"""
        try:
            # 这里需要与其他组件集成来获取应用级指标
            # 暂时使用模拟数据，实际实现时需要注入依赖

            # 更新整体性能评级
            await self._calculate_overall_performance()

        except Exception as e:
            await self.safe_log_error(f"Failed to collect application metrics: {e}")

    async def _record_metric(
        self, name: str, value: float, tags: dict[str, str] | None = None
    ):
        """记录指标"""
        metric = MetricValue(
            name=name, value=value, metric_type=MetricType.GAUGE, tags=tags or {}
        )

        self.metrics_history[name].append(metric)

        # 记录性能指标
        await self.log_performance_metric(name, value, context=f"tags={tags}")

    async def _check_alerts(self):
        """检查告警"""
        try:
            # 检查各项阈值
            for metric_name, threshold in self.alert_thresholds.items():
                current_value = getattr(self.current_metrics, metric_name, 0)

                if current_value > threshold:
                    # 创建或更新告警
                    if metric_name not in self.active_alerts:
                        alert = PerformanceAlert(
                            metric_name=metric_name,
                            current_value=current_value,
                            threshold=threshold,
                            severity="high",
                            message=f"{metric_name} exceeded threshold: {current_value:.2f} > {threshold}",
                        )

                        self.active_alerts[metric_name] = alert
                        await self._trigger_alert(alert)

                elif metric_name in self.active_alerts:
                    # 解决告警
                    alert = self.active_alerts[metric_name]
                    alert.resolved = True
                    await self._resolve_alert(alert)
                    del self.active_alerts[metric_name]

        except Exception as e:
            await self.safe_log_error(f"Alert checking failed: {e}")

    async def _trigger_alert(self, alert: PerformanceAlert):
        """触发告警"""
        await self.safe_log_warning(f"Performance alert: {alert.message}")

        # 调用回调函数
        for callback in self.alert_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(alert)
                else:
                    callback(alert)
            except Exception as e:
                await self.safe_log_error(f"Alert callback error: {e}")

    async def _resolve_alert(self, alert: PerformanceAlert):
        """解决告警"""
        await self.safe_log_info(f"Performance alert resolved: {alert.metric_name}")

    async def _calculate_overall_performance(self):
        """计算整体性能评级"""
        try:
            benchmarks = self.performance_benchmarks

            # 获取关键指标
            cpu = self.current_metrics.cpu_percent
            memory = self.current_metrics.memory_percent
            response_time = self.current_metrics.avg_response_time_ms

            # 计算得分 (越低越好)
            score = 0
            if cpu <= benchmarks["excellent"]["cpu"]:
                score += 4
            elif cpu <= benchmarks["good"]["cpu"]:
                score += 3
            elif cpu <= benchmarks["average"]["cpu"]:
                score += 2
            elif cpu <= benchmarks["poor"]["cpu"]:
                score += 1
            else:
                score += 0

            if memory <= benchmarks["excellent"]["memory"]:
                score += 4
            elif memory <= benchmarks["good"]["memory"]:
                score += 3
            elif memory <= benchmarks["average"]["memory"]:
                score += 2
            elif memory <= benchmarks["poor"]["memory"]:
                score += 1
            else:
                score += 0

            if response_time <= benchmarks["excellent"]["response_time"]:
                score += 4
            elif response_time <= benchmarks["good"]["response_time"]:
                score += 3
            elif response_time <= benchmarks["average"]["response_time"]:
                score += 2
            elif response_time <= benchmarks["poor"]["response_time"]:
                score += 1
            else:
                score += 0

            # 根据综合得分确定等级
            avg_score = score / 3
            if avg_score >= 3.5:
                self.current_metrics.overall_performance = PerformanceLevel.EXCELLENT
            elif avg_score >= 2.5:
                self.current_metrics.overall_performance = PerformanceLevel.GOOD
            elif avg_score >= 1.5:
                self.current_metrics.overall_performance = PerformanceLevel.AVERAGE
            elif avg_score >= 0.5:
                self.current_metrics.overall_performance = PerformanceLevel.POOR
            else:
                self.current_metrics.overall_performance = PerformanceLevel.CRITICAL

        except Exception as e:
            await self.safe_log_error(f"Performance calculation failed: {e}")

    async def _analyze_performance_trends(self):
        """分析性能趋势"""
        try:
            # 分析CPU趋势
            if (
                "cpu_percent" in self.metrics_history
                and len(self.metrics_history["cpu_percent"]) > 10
            ):
                cpu_values = [
                    m.value for m in list(self.metrics_history["cpu_percent"])[-10:]
                ]
                cpu_trend = statistics.mean(cpu_values[-5:]) - statistics.mean(
                    cpu_values[:5]
                )

                if cpu_trend > 10:  # CPU使用率持续上升
                    await self.safe_log_warning("CPU usage showing upward trend")

        except Exception as e:
            await self.safe_log_error(f"Trend analysis failed: {e}")

    async def _generate_recommendations(self):
        """生成优化建议"""
        try:
            new_recommendations = []

            # CPU优化建议
            if self.current_metrics.cpu_percent > 80:
                new_recommendations.append(
                    PerformanceRecommendation(
                        category="system",
                        priority="high",
                        description="CPU usage is high. Consider optimizing compute-intensive operations or adding more CPU resources.",
                        impact_estimate="Moderate - May improve system responsiveness by 20-30%",
                        implementation_effort="Low to Medium - Depends on optimization approach",
                    )
                )

            # 内存优化建议
            if self.current_metrics.memory_percent > 85:
                new_recommendations.append(
                    PerformanceRecommendation(
                        category="system",
                        priority="high",
                        description="Memory usage is high. Consider implementing memory optimization or adding more RAM.",
                        impact_estimate="High - May improve system stability and performance by 30-50%",
                        implementation_effort="Low to High - Ranges from configuration changes to hardware upgrades",
                    )
                )

            # 添加新建议
            for recommendation in new_recommendations:
                self.recommendations.append(recommendation)
                await self._trigger_recommendation(recommendation)

        except Exception as e:
            await self.safe_log_error(f"Recommendation generation failed: {e}")

    async def _trigger_recommendation(self, recommendation: PerformanceRecommendation):
        """触发优化建议"""
        await self.safe_log_info(
            f"Performance recommendation ({recommendation.priority}): {recommendation.description}"
        )

        # 调用回调函数
        for callback in self.recommendation_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(recommendation)
                else:
                    callback(recommendation)
            except Exception as e:
                await self.safe_log_error(f"Recommendation callback error: {e}")

    async def _cleanup_old_data(self):
        """清理过期数据"""
        try:
            cutoff_time = datetime.now(UTC) - timedelta(
                hours=self.metric_retention_hours
            )

            for _metric_name, history in self.metrics_history.items():
                # 清理过期指标
                while history and history[0].timestamp < cutoff_time:
                    history.popleft()

            # 清理过期建议
            self.recommendations = [
                r for r in self.recommendations if r.timestamp >= cutoff_time
            ]

        except Exception as e:
            await self.safe_log_error(f"Data cleanup failed: {e}")

    # 公共接口方法

    def add_alert_callback(self, callback: Callable[[PerformanceAlert], None]):
        """添加告警回调"""
        self.alert_callbacks.append(callback)

    def add_recommendation_callback(
        self, callback: Callable[[PerformanceRecommendation], None]
    ):
        """添加建议回调"""
        self.recommendation_callbacks.append(callback)

    async def get_current_metrics(self) -> PerformanceMetrics:
        """获取当前指标"""
        return self.current_metrics

    async def get_metric_history(
        self, metric_name: str, hours: int = 1
    ) -> list[MetricValue]:
        """获取指标历史"""
        if metric_name not in self.metrics_history:
            return []

        cutoff_time = datetime.now(UTC) - timedelta(hours=hours)
        return [
            m for m in self.metrics_history[metric_name] if m.timestamp >= cutoff_time
        ]

    async def get_active_alerts(self) -> list[PerformanceAlert]:
        """获取活跃告警"""
        return list(self.active_alerts.values())

    async def get_recommendations(
        self, hours: int = 24
    ) -> list[PerformanceRecommendation]:
        """获取优化建议"""
        cutoff_time = datetime.now(UTC) - timedelta(hours=hours)
        return [r for r in self.recommendations if r.timestamp >= cutoff_time]

    async def record_custom_metric(
        self,
        name: str,
        value: float,
        metric_type: MetricType = MetricType.GAUGE,
        tags: dict[str, str] | None = None,
    ):
        """记录自定义指标"""
        _ = metric_type  # reserved for future use; referenced to avoid unused warnings
        # metric_type is reserved for future differentiation; currently ignored
        await self._record_metric(name, value, tags)

    async def get_system_summary(self) -> dict[str, Any]:
        """获取系统摘要"""
        return {
            "timestamp": datetime.now(UTC).isoformat(),
            "overall_performance": self.current_metrics.overall_performance.value,
            "system": {
                "cpu_percent": self.current_metrics.cpu_percent,
                "memory_percent": self.current_metrics.memory_percent,
                "memory_used_mb": self.current_metrics.memory_used_mb,
            },
            "alerts": {
                "active_count": len(self.active_alerts),
                "alerts": [
                    {
                        "metric": alert.metric_name,
                        "severity": alert.severity,
                        "message": alert.message,
                    }
                    for alert in self.active_alerts.values()
                ],
            },
            "recommendations": {
                "count": len(
                    [
                        r
                        for r in self.recommendations
                        if r.timestamp >= datetime.now(UTC) - timedelta(hours=24)
                    ]
                ),
                "high_priority": len(
                    [
                        r
                        for r in self.recommendations
                        if r.priority == "high"
                        and r.timestamp >= datetime.now(UTC) - timedelta(hours=24)
                    ]
                ),
            },
        }
