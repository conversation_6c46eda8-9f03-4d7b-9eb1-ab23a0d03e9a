#!/usr/bin/env python3
"""
系统指标集成 - SystemMetricsIntegration

将性能监控系统与现有交易系统组件深度集成，提供：
- 交易系统专用指标收集
- 组件间性能数据共享
- 自动化性能优化触发
- 系统健康状态协调

设计要点：
- 与现有组件无缝集成
- 低开销的数据收集
- 实时性能反馈
- 自动化优化决策
"""

import asyncio
import contextlib
from typing import TYPE_CHECKING, Any

from prometheus_client import start_http_server

from src.core.base_component import (
    BaseComponent,
    HealthCheckResult,
    HealthStatus,
)

from .performance_monitor import (
    PerformanceAlert,
    PerformanceMonitor,
    PerformanceRecommendation,
)

if TYPE_CHECKING:
    from ..core.event_bus import EventBus
    from ..data.data_engine import DataEngine
    from ..data.timescale_manager import TimescaleDBManager
    from ..execution.order_manager import OrderManager
    from ..strategy.strategy_coordinator import StrategyCoordinator


class SystemMetricsIntegration(BaseComponent):
    """
    系统指标集成器

    协调各系统组件的性能监控，提供统一的性能管理接口
    """

    def __init__(
        self,
        config: dict[str, Any] | None = None,
        performance_monitor: PerformanceMonitor | None = None,
    ):
        super().__init__("SystemMetricsIntegration", config)

        # 性能监控器（通过依赖注入提供）
        self.performance_monitor = performance_monitor

        # 系统组件引用（通过依赖注入设置）
        self.event_bus: EventBus | None = None
        self.data_engine: DataEngine | None = None
        self.timescale_manager: TimescaleDBManager | None = None
        self.strategy_coordinator: StrategyCoordinator | None = None
        self.order_manager: OrderManager | None = None

        # 配置
        self.integration_interval = self.config.get("integration_interval", 10.0)
        self.auto_optimization = self.config.get("auto_optimization", True)

        # 集成任务
        self.integration_task: asyncio.Task | None = None
        self.optimization_task: asyncio.Task | None = None
        self._prom_started: bool = False

    async def _initialize_impl(self) -> bool:
        """初始化实现"""
        try:
            await self.log_initialization(True, "Initializing SystemMetricsIntegration")

            # 使用注入的PerformanceMonitor或创建新的（依赖注入将提供单例）
            if not self.performance_monitor:
                monitor_config = self.config.get("performance_monitor", {})
                self.performance_monitor = PerformanceMonitor(monitor_config)

            # 设置告警和建议回调
            if self.performance_monitor:
                self.performance_monitor.add_alert_callback(
                    self._handle_performance_alert
                )
                self.performance_monitor.add_recommendation_callback(
                    self._handle_performance_recommendation
                )

            # 初始化性能监控器（如果尚未初始化）
            if (
                self.performance_monitor.state.value == "uninitialized"
                and not await self.performance_monitor.initialize()
            ):
                return False

            await self.log_initialization(
                True, "SystemMetricsIntegration initialized successfully"
            )
            return True

        except Exception as e:
            await self.log_initialization(
                False, f"SystemMetricsIntegration initialization failed: {e}"
            )
            return False

    async def _start_impl(self) -> bool:
        """启动实现"""
        try:
            await self.log_startup(True, "Starting SystemMetricsIntegration")

            # 启动性能监控器（如果尚未启动）
            if (
                self.performance_monitor.state.value not in ["running"]
                and not await self.performance_monitor.start()
            ):
                return False

            # 启动集成任务
            self.integration_task = asyncio.create_task(self._integration_loop())

            # 启动优化任务（如果启用）
            if self.auto_optimization:
                self.optimization_task = asyncio.create_task(self._optimization_loop())

            # 启动 Prometheus 导出（若未启动）
            try:
                port = int(self.config.get("prometheus_port") or 0)
            except Exception:
                port = 0
            if port and not self._prom_started:
                start_http_server(port)
                self._prom_started = True
                await self.safe_log_info(f"Prometheus exporter started on :{port}")

            await self.log_startup(
                True, "SystemMetricsIntegration started successfully"
            )
            return True

        except Exception as e:
            await self.log_startup(False, f"SystemMetricsIntegration start failed: {e}")
            return False

    async def _stop_impl(self) -> bool:
        """停止实现"""
        try:
            await self.log_shutdown(True, "Stopping SystemMetricsIntegration")

            # 停止任务
            if self.integration_task:
                self.integration_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self.integration_task

            if self.optimization_task:
                self.optimization_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self.optimization_task

            # 停止性能监控器
            if self.performance_monitor:
                await self.performance_monitor.stop()

            await self.log_shutdown(
                True, "SystemMetricsIntegration stopped successfully"
            )
            return True

        except Exception as e:
            await self.log_shutdown(False, f"SystemMetricsIntegration stop failed: {e}")
            return False

    async def _health_check_impl(self) -> HealthCheckResult:
        """健康检查实现"""
        try:
            if not self.performance_monitor:
                return HealthCheckResult(
                    status=HealthStatus.UNHEALTHY,
                    message="PerformanceMonitor not initialized",
                )

            # 检查性能监控器状态
            monitor_health = await self.performance_monitor.health_check()

            # 检查集成任务状态
            integration_healthy = (
                self.integration_task and not self.integration_task.done()
            )

            if monitor_health.status == HealthStatus.UNHEALTHY:
                status = HealthStatus.UNHEALTHY
                message = f"PerformanceMonitor unhealthy: {monitor_health.message}"
            elif not integration_healthy:
                status = HealthStatus.DEGRADED
                message = "Integration task not running"
            elif monitor_health.status == HealthStatus.DEGRADED:
                status = HealthStatus.DEGRADED
                message = f"PerformanceMonitor degraded: {monitor_health.message}"
            else:
                status = HealthStatus.HEALTHY
                message = "SystemMetricsIntegration operating normally"

            return HealthCheckResult(
                status=status,
                message=message,
                details={
                    "performance_monitor_status": monitor_health.status.value,
                    "integration_task_running": integration_healthy,
                    "auto_optimization_enabled": self.auto_optimization,
                    "optimization_task_running": self.optimization_task
                    and not self.optimization_task.done()
                    if self.optimization_task
                    else False,
                },
            )

        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY, message=f"Health check failed: {str(e)}"
            )

    async def _integration_loop(self):
        """系统集成循环"""
        try:
            while True:
                try:
                    # 收集各组件指标
                    await self._collect_component_metrics()

                    # 更新组件健康状态
                    await self._update_component_health()

                    await asyncio.sleep(self.integration_interval)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    await self.safe_log_error(f"Integration loop error: {e}")
                    await asyncio.sleep(1)

        except asyncio.CancelledError:
            pass
        finally:
            await self.safe_log_info("Integration loop stopped")

    async def _optimization_loop(self):
        """自动优化循环"""
        try:
            while True:
                try:
                    # 检查是否需要优化
                    await self._check_optimization_triggers()

                    # 执行自动优化
                    await self._execute_automatic_optimizations()

                    # 每5分钟检查一次优化机会
                    await asyncio.sleep(300)

                except asyncio.CancelledError:
                    break
                except Exception as e:
                    await self.safe_log_error(f"Optimization loop error: {e}")
                    await asyncio.sleep(30)

        except asyncio.CancelledError:
            pass
        finally:
            await self.safe_log_info("Optimization loop stopped")

    async def _collect_component_metrics(self):
        """收集组件指标"""
        try:
            # 事件总线指标
            if self.event_bus:
                try:
                    event_metrics = await self.event_bus.get_metrics()
                    await self.performance_monitor.record_custom_metric(
                        "events_processed_total",
                        event_metrics.get("total_processed", 0),
                    )
                    await self.performance_monitor.record_custom_metric(
                        "events_per_second", event_metrics.get("events_per_second", 0)
                    )
                    await self.performance_monitor.record_custom_metric(
                        "event_queue_size", event_metrics.get("queue_size", 0)
                    )
                except Exception as e:
                    await self.safe_log_warning(
                        f"Failed to collect EventBus metrics: {e}"
                    )

            # 数据引擎指标
            if self.data_engine:
                try:
                    # 假设data_engine有get_metrics方法
                    if hasattr(self.data_engine, "get_metrics"):
                        data_metrics = await self.data_engine.get_metrics()
                        await self.performance_monitor.record_custom_metric(
                            "data_feed_latency_ms",
                            data_metrics.get("avg_latency_ms", 0),
                        )
                        await self.performance_monitor.record_custom_metric(
                            "data_points_per_second", data_metrics.get("data_rate", 0)
                        )
                except Exception as e:
                    await self.safe_log_warning(
                        f"Failed to collect DataEngine metrics: {e}"
                    )

            # 策略协调器指标
            if self.strategy_coordinator:
                try:
                    if hasattr(self.strategy_coordinator, "get_metrics"):
                        strategy_metrics = await self.strategy_coordinator.get_metrics()
                        await self.performance_monitor.record_custom_metric(
                            "active_strategies", strategy_metrics.get("active_count", 0)
                        )
                        await self.performance_monitor.record_custom_metric(
                            "strategy_success_rate",
                            strategy_metrics.get("success_rate", 0),
                        )
                except Exception as e:
                    await self.safe_log_warning(
                        f"Failed to collect StrategyCoordinator metrics: {e}"
                    )

            # 订单管理器指标
            if self.order_manager:
                try:
                    if hasattr(self.order_manager, "get_metrics"):
                        order_metrics = await self.order_manager.get_metrics()
                        await self.performance_monitor.record_custom_metric(
                            "orders_per_minute",
                            order_metrics.get("orders_per_minute", 0),
                        )
                        await self.performance_monitor.record_custom_metric(
                            "avg_order_latency_ms",
                            order_metrics.get("avg_latency_ms", 0),
                        )
                        await self.performance_monitor.record_custom_metric(
                            "order_success_rate", order_metrics.get("success_rate", 0)
                        )
                except Exception as e:
                    await self.safe_log_warning(
                        f"Failed to collect OrderManager metrics: {e}"
                    )

            # 数据库（TimescaleDB）写入指标汇总
            if self.timescale_manager:
                try:
                    if hasattr(self.timescale_manager, "get_metrics"):
                        ts_metrics = await self.timescale_manager.get_metrics()
                        custom = ts_metrics.get("custom_metrics", {}) or {}

                        tables = [
                            "market_data",
                            "option_data",
                            "trades",
                            "position_snapshots",
                            "risk_metrics",
                            "signals",
                            "signals_shadow",
                            "kline_data",
                        ]

                        total_failures = 0
                        total_time = 0
                        total_count = 0

                        for tbl in tables:
                            cnt = custom.get(f"db_write_count_{tbl}", 0) or 0
                            t_sum = custom.get(f"db_write_time_ms_sum_{tbl}", 0) or 0
                            fails = custom.get(f"db_write_failures_{tbl}", 0) or 0

                            avg_ms = (t_sum / cnt) if cnt else 0
                            await self.performance_monitor.record_custom_metric(
                                f"db_write_avg_ms_{tbl}", avg_ms
                            )
                            await self.performance_monitor.record_custom_metric(
                                f"db_write_failures_{tbl}", fails
                            )
                            await self.performance_monitor.record_custom_metric(
                                f"db_write_count_{tbl}", cnt
                            )

                            total_failures += fails
                            total_time += t_sum
                            total_count += cnt

                        overall_avg = (total_time / total_count) if total_count else 0
                        await self.performance_monitor.record_custom_metric(
                            "db_write_failures_total", total_failures
                        )
                        await self.performance_monitor.record_custom_metric(
                            "db_write_avg_ms_all_tables", overall_avg
                        )
                except Exception as e:
                    await self.safe_log_warning(
                        f"Failed to collect TimescaleDB metrics: {e}"
                    )

        except Exception as e:
            await self.safe_log_error(f"Component metrics collection failed: {e}")

    async def _update_component_health(self):
        """更新组件健康状态"""
        try:
            component_health = {}

            # 检查各组件健康状态
            components = [
                ("EventBus", self.event_bus),
                ("DataEngine", self.data_engine),
                ("StrategyCoordinator", self.strategy_coordinator),
                ("OrderManager", self.order_manager),
            ]

            for name, component in components:
                if component and hasattr(component, "health_check"):
                    try:
                        health = await component.health_check()

                        # 确保health是HealthCheckResult对象
                        if hasattr(health, "status"):
                            component_health[name] = health.status.value
                            # 记录健康状态指标
                            health_score = (
                                1
                                if health.status == HealthStatus.HEALTHY
                                else (
                                    0.5 if health.status == HealthStatus.DEGRADED else 0
                                )
                            )
                        elif isinstance(health, dict):
                            # 处理dict类型的健康检查结果
                            status_str = health.get("status", "unknown")
                            component_health[name] = status_str
                            health_score = 1 if status_str == "healthy" else 0
                        else:
                            # 其他类型，标记为未知
                            component_health[name] = "unknown"
                            health_score = 0

                        await self.performance_monitor.record_custom_metric(
                            f"{name.lower()}_health_score",
                            health_score,
                            tags={"component": name},
                        )

                    except Exception as e:
                        await self.safe_log_warning(
                            f"Health check failed for {name}: {e}"
                        )
                        component_health[name] = "unknown"

            # 记录系统整体健康评分
            healthy_count = sum(
                1 for status in component_health.values() if status == "healthy"
            )
            total_count = len(component_health)
            overall_health = healthy_count / total_count if total_count > 0 else 0

            await self.performance_monitor.record_custom_metric(
                "system_health_score", overall_health
            )

        except Exception as e:
            await self.safe_log_error(f"Component health update failed: {e}")

    async def _handle_performance_alert(self, alert: PerformanceAlert):
        """处理性能告警"""
        try:
            await self.safe_log_warning(
                f"Performance alert received: {alert.metric_name} = {alert.current_value:.2f} (threshold: {alert.threshold})"
            )

            # 可以在这里添加额外的告警处理逻辑
            # 例如：发送通知、触发自动缩放、调整策略参数等

        except Exception as e:
            await self.safe_log_error(f"Alert handling failed: {e}")

    async def _handle_performance_recommendation(
        self, recommendation: PerformanceRecommendation
    ):
        """处理性能优化建议"""
        try:
            await self.safe_log_info(
                f"Performance recommendation ({recommendation.priority}): {recommendation.description}"
            )

            # 可以在这里添加自动应用建议的逻辑

        except Exception as e:
            await self.safe_log_error(f"Recommendation handling failed: {e}")

    async def _check_optimization_triggers(self):
        """检查优化触发条件"""
        try:
            # 获取当前性能指标
            current_metrics = await self.performance_monitor.get_current_metrics()

            # 检查是否需要优化
            optimization_needed = (
                current_metrics.cpu_percent > 85
                or current_metrics.memory_percent > 85
                or current_metrics.avg_response_time_ms > 1000
            )

            if optimization_needed:
                await self.safe_log_info(
                    "Performance optimization trigger conditions met"
                )

        except Exception as e:
            await self.safe_log_error(f"Optimization trigger check failed: {e}")

    async def _execute_automatic_optimizations(self):
        """执行自动优化"""
        try:
            # 获取优化建议
            recommendations = await self.performance_monitor.get_recommendations(
                hours=1
            )

            # 执行低风险的自动优化
            for recommendation in recommendations:
                if (
                    recommendation.priority == "high"
                    and recommendation.implementation_effort in ["Low", "Low to Medium"]
                    and "configuration" in recommendation.description.lower()
                ):
                    await self.safe_log_info(
                        f"Applying automatic optimization: {recommendation.description}"
                    )
                    # 这里可以添加具体的优化实现逻辑

        except Exception as e:
            await self.safe_log_error(f"Automatic optimization failed: {e}")

    # 公共接口方法

    async def get_system_performance_summary(self) -> dict[str, Any]:
        """获取系统性能摘要"""
        if not self.performance_monitor:
            return {}

        return await self.performance_monitor.get_system_summary()

    async def get_component_metrics(
        self, component_name: str, hours: int = 1
    ) -> list[dict[str, Any]]:
        """获取组件指标"""
        if not self.performance_monitor:
            return []

        metrics = await self.performance_monitor.get_metric_history(
            f"{component_name.lower()}_health_score", hours
        )
        return [
            {"timestamp": m.timestamp.isoformat(), "value": m.value, "tags": m.tags}
            for m in metrics
        ]

    async def trigger_manual_optimization(self, optimization_type: str) -> bool:
        """触发手动优化"""
        try:
            await self.safe_log_info(
                f"Manual optimization triggered: {optimization_type}"
            )

            if optimization_type == "memory_cleanup":
                # 执行内存清理优化
                await self._execute_memory_cleanup()
            elif optimization_type == "cache_optimization":
                # 执行缓存优化
                await self._execute_cache_optimization()
            else:
                await self.safe_log_warning(
                    f"Unknown optimization type: {optimization_type}"
                )
                return False

            return True

        except Exception as e:
            await self.safe_log_error(f"Manual optimization failed: {e}")
            return False

    async def _execute_memory_cleanup(self):
        """执行内存清理"""
        try:
            # 触发各组件的内存清理
            components = [
                self.event_bus,
                self.data_engine,
                self.strategy_coordinator,
                self.order_manager,
            ]

            for component in components:
                if component and hasattr(component, "cleanup_memory"):
                    try:
                        await component.cleanup_memory()
                    except Exception as e:
                        await self.safe_log_warning(
                            f"Memory cleanup failed for {type(component).__name__}: {e}"
                        )

            await self.safe_log_info("Memory cleanup optimization completed")

        except Exception as e:
            await self.safe_log_error(f"Memory cleanup failed: {e}")

    async def _execute_cache_optimization(self):
        """执行缓存优化"""
        try:
            # 优化各组件的缓存策略
            if self.data_engine and hasattr(self.data_engine, "optimize_cache"):
                await self.data_engine.optimize_cache()

            await self.safe_log_info("Cache optimization completed")

        except Exception as e:
            await self.safe_log_error(f"Cache optimization failed: {e}")
