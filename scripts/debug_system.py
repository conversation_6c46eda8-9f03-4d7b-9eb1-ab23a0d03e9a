#!/usr/bin/env python3
"""
系统调试脚本

检查系统各组件状态，找出数据流转问题
"""

import asyncio
import os
import sys
from pathlib import Path

from dotenv import load_dotenv

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 加载环境变量
load_dotenv()


async def debug_system():
    """调试系统状态"""
    print("🔍 开始系统调试...")

    # 1. 检查环境变量
    print("\n📋 环境变量检查:")
    env_vars = [
        "BINANCE_API_KEY",
        "BINANCE_API_SECRET",
        "DERIBIT_API_KEY",
        "DERIBIT_API_SECRET",
        "DB_HOST",
        "DB_PORT",
        "DB_NAME",
        "DB_USERNAME",
        "ENABLE_BINANCE",
        "ENABLE_DERIBIT",
        "USE_TESTNET",
    ]

    for var in env_vars:
        value = os.getenv(var, "NOT_SET")
        if "KEY" in var or "SECRET" in var or "PASSWORD" in var:
            # 隐藏敏感信息
            display_value = (
                f"{value[:8]}..." if value != "NOT_SET" and len(value) > 8 else value
            )
        else:
            display_value = value
        print(f"  {var}: {display_value}")

    # 2. 检查数据库连接
    print("\n🗄️ 数据库连接检查:")
    try:
        from src.core.config_manager import ConfigManager
        from src.data.timescale_manager import TimescaleDBManager

        config_manager = ConfigManager()
        await config_manager.initialize()

        timescale_manager = TimescaleDBManager(config_manager)
        await timescale_manager.initialize()

        async with timescale_manager.get_connection(read_only=True) as conn:
            # 测试查询
            result = await conn.fetchval("SELECT 1")
            print(f"  ✅ 数据库连接正常: {result}")

            # 检查表结构
            tables = await conn.fetch("""
                SELECT tablename FROM pg_tables
                WHERE schemaname = 'public'
                ORDER BY tablename
            """)
            print(f"  📊 数据库表数量: {len(tables)}")
            for table in tables:
                count = await conn.fetchval(
                    f"SELECT COUNT(*) FROM {table['tablename']}"
                )
                print(f"    {table['tablename']}: {count} 条记录")

    except Exception as e:
        print(f"  ❌ 数据库连接失败: {e}")

    # 3. 测试交易所连接
    print("\n🌐 交易所连接测试:")

    # 测试Binance连接
    try:
        print("  📈 测试Binance连接...")
        from src.core.celery_manager import CeleryTaskManager
        from src.gateways.binance_client import BinanceClient

        # 创建Celery管理器（可选）
        celery_manager = None
        try:
            celery_manager = CeleryTaskManager(config_manager)
            await celery_manager.initialize()
        except Exception:
            print("    ⚠️ CeleryManager初始化失败，继续测试")

        binance_client = BinanceClient(
            api_key=os.getenv("BINANCE_API_KEY"),
            api_secret=os.getenv("BINANCE_API_SECRET"),
            celery_manager=celery_manager,
            config_manager=config_manager,
        )

        # 初始化但不启动WebSocket
        await binance_client.initialize()
        print("    ✅ Binance客户端初始化成功")

        # 测试REST API
        try:
            # 简单的ping测试
            import aiohttp

            async with aiohttp.ClientSession() as session:
                url = "https://api.binance.com/api/v3/ping"
                async with session.get(url) as response:
                    if response.status == 200:
                        print("    ✅ Binance REST API连接正常")
                    else:
                        print(f"    ⚠️ Binance REST API响应异常: {response.status}")
        except Exception as e:
            print(f"    ❌ Binance REST API测试失败: {e}")

    except Exception as e:
        print(f"    ❌ Binance客户端测试失败: {e}")

    # 测试Deribit连接
    try:
        print("  📊 测试Deribit连接...")
        from src.gateways.deribit_client import DeribitClient

        api_key = os.getenv("DERIBIT_API_KEY")
        api_secret = os.getenv("DERIBIT_API_SECRET")

        if api_key and api_secret:
            deribit_client = DeribitClient(
                api_key=api_key, api_secret=api_secret, celery_manager=celery_manager
            )
            await deribit_client.initialize()
            print("    ✅ Deribit客户端初始化成功")
        else:
            print("    ⚠️ Deribit API密钥未配置，尝试公共模式")
            # 测试公共API
            try:
                import aiohttp

                async with aiohttp.ClientSession() as session:
                    url = "https://www.deribit.com/api/v2/public/get_time"
                    async with session.get(url) as response:
                        if response.status == 200:
                            data = await response.json()
                            print(f"    ✅ Deribit公共API连接正常: {data}")
                        else:
                            print(f"    ⚠️ Deribit公共API响应异常: {response.status}")
            except Exception as e:
                print(f"    ❌ Deribit公共API测试失败: {e}")

    except Exception as e:
        print(f"    ❌ Deribit客户端测试失败: {e}")

    # 4. 检查Redis连接
    print("\n🔴 Redis连接检查:")
    try:
        import redis.asyncio as redis

        redis_host = os.getenv("REDIS_HOST", "localhost")
        redis_port = int(os.getenv("REDIS_PORT", 6379))
        redis_db = int(os.getenv("REDIS_DB", 0))

        redis_client = redis.Redis(
            host=redis_host, port=redis_port, db=redis_db, decode_responses=True
        )

        # 测试连接
        await redis_client.ping()
        print(f"  ✅ Redis连接正常: {redis_host}:{redis_port}/{redis_db}")

        # 检查键数量
        key_count = await redis_client.dbsize()
        print(f"  📊 Redis键数量: {key_count}")

        await redis_client.close()

    except Exception as e:
        print(f"  ❌ Redis连接失败: {e}")

    # 5. 检查进程状态
    print("\n🔄 进程状态检查:")
    try:
        import psutil

        # 查找Python进程
        python_processes = []
        for proc in psutil.process_iter(["pid", "name", "cmdline"]):
            try:
                if proc.info["name"] and "python" in proc.info["name"].lower():
                    cmdline = (
                        " ".join(proc.info["cmdline"]) if proc.info["cmdline"] else ""
                    )
                    if "main.py" in cmdline or "btc" in cmdline.lower():
                        python_processes.append(
                            {"pid": proc.info["pid"], "cmdline": cmdline}
                        )
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        if python_processes:
            print(f"  📋 发现 {len(python_processes)} 个相关Python进程:")
            for proc in python_processes:
                print(f"    PID {proc['pid']}: {proc['cmdline']}")
        else:
            print("  ⚠️ 未发现运行中的主程序进程")

    except Exception as e:
        print(f"  ❌ 进程检查失败: {e}")

    print("\n✅ 系统调试完成")


if __name__ == "__main__":
    asyncio.run(debug_system())
