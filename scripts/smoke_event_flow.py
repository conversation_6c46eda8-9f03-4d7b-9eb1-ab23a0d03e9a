#!/usr/bin/env python3
"""
Minimal smoke test for event flow:
- Start EventBus
- Initialize OrderManager to subscribe to DataUpdateEvent
- Publish a DataUpdateEvent
- Print EventBus metrics confirming processing and active subscribers

This test avoids external systems (DB/redis/ws) and runs fast.
"""

import asyncio
import contextlib
import os
import sys
from datetime import UTC, datetime

# Ensure project root on sys.path for `src.*` imports when running as a script
ROOT = os.path.dirname(os.path.dirname(__file__))
if ROOT not in sys.path:
    sys.path.insert(0, ROOT)

from src.core.event_bus import DataUpdateEvent, EventBus  # noqa: E402


async def main() -> int:
    # Create and start EventBus
    bus = EventBus({"worker_count": 2, "max_queue_size": 100, "debug_logs": False})
    ok = await bus.initialize()
    if not ok:
        print("EventBus initialize failed")
        return 1
    ok = await bus.start()
    if not ok:
        print("EventBus start failed")
        return 1

    # Try to initialize core components and inject EventBus
    om = None
    pm = None
    pnl = None
    ex = None
    la = None
    ds = None
    re = None
    try:
        from src.analysis.causal_engine import CausalEngine  # type: ignore
        from src.analysis.liquidity_analyzer import LiquidityAnalyzer  # type: ignore
        from src.analysis.microstructure_signals import (
            MicrostructureSignals,  # type: ignore
        )
        from src.core.config_manager import ConfigManager  # type: ignore
        from src.core.dependency_injector import (  # type: ignore
            DependencyInjector,
            DependencyScope,
        )
        from src.data.cache_manager import CacheManager  # type: ignore
        from src.execution.exercise_handler import ExerciseHandler  # type: ignore
        from src.execution.order_manager import OrderManager  # type: ignore
        from src.execution.pnl_tracker import PnLTracker  # type: ignore
        from src.execution.position_manager import PositionManager  # type: ignore
        from src.gateways.data_synchronizer import DataSynchronizer  # type: ignore
        from src.gateways.deribit_client import DeribitClient  # type: ignore
        from src.risk.risk_engine import RiskEngine  # type: ignore
        from src.services.market_data_service import MarketDataService  # type: ignore
        from src.strategy.branch_strategy import BranchStrategy  # type: ignore
        from src.strategy.option_grid_strategy import OptionGridStrategy  # type: ignore
        from src.strategy.strategy_coordinator import (
            StrategyCoordinator,  # type: ignore
        )

        # OrderManager
        om = OrderManager(config={})
        om.event_bus = bus
        ok = await om.initialize()
        if not ok:
            print("OrderManager initialize failed")
            return 1
        await om.start()
        # PositionManager
        pm = PositionManager(config={})
        pm.event_bus = bus
        await pm.initialize()
        # PnLTracker
        pnl = PnLTracker(config={})
        pnl.event_bus = bus
        await pnl.initialize()
        # ExerciseHandler
        ex = ExerciseHandler(config={})
        ex.event_bus = bus
        await ex.initialize()
        # LiquidityAnalyzer (requires non-empty config)
        la = LiquidityAnalyzer(config={"enabled": True})
        la.event_bus = bus
        await la.initialize()
        # DataSynchronizer (subscribe in start)
        ds = DataSynchronizer()
        ds.set_event_bus(bus)
        await ds.initialize()
        await ds.start()
        # RiskEngine
        re = RiskEngine(config={})
        re.event_bus = bus
        await re.initialize()
        await re.start()
        # CausalEngine + ConfigManager
        cfg = ConfigManager()
        await cfg.initialize()
        # Initialize DI and register MarketDataService backed by cache
        di = DependencyInjector(cfg)
        await di.initialize()
        di.register(
            MarketDataService,
            factory=lambda: MarketDataService(cache),
            scope=DependencyScope.SINGLETON,
        )

        ce = CausalEngine(config_manager=cfg, event_bus=bus, cache_manager=None)
        ce.set_dependency_injector(di)
        await ce.initialize()
        await ce.start()

        # OptionGridStrategy subscribes to SignalEvent and will publish TradingDecisionEvent
        og = OptionGridStrategy(config_manager=cfg)
        og.event_bus = bus
        await og.initialize()
        await og.start()

        # CacheManager (real)
        cache = CacheManager(cfg)
        await cache.initialize()
        await cache.start()

        # MarketDataService using cache
        try:
            mds = MarketDataService(cache)
            await mds.initialize()
        except Exception:
            mds = None

        # MicrostructureSignals (subscribe to DataUpdateEvent)
        ms = MicrostructureSignals(event_bus=bus, cache_manager=cache)
        await ms.initialize()
        await ms.start()

        # DeribitClient (not started; presence required by BranchStrategy)
        deribit = DeribitClient(event_bus=bus, config_manager=cfg)

        # BranchStrategy
        bs = BranchStrategy(config={})
        bs.set_dependencies(
            event_bus=bus,
            causal_engine=ce,
            microstructure_signals=ms,
            cache_manager=cache,
            deribit_client=deribit,
            market_data_service=mds,
            position_manager=pm,
        )
        bs.set_risk_engine(re)
        await bs.initialize()
        await bs.start()

        # StrategyCoordinator (wire strategies; may run in limited mode if some deps missing)
        sc = StrategyCoordinator(config={})
        sc.event_bus = bus
        sc.cache_manager = cache
        sc.main_strategy = og
        sc.branch_strategy = bs
        await sc.initialize()
        await sc.start()
    except Exception as e:
        print("Component import/initialize partially skipped:", e)
        # Fallback: register a simple probe subscriber to confirm event flow
        called = asyncio.Event()

        async def _probe_cb(event):
            called.set()

        await bus.subscribe(
            event_types={"DataUpdateEvent"},
            callback=_probe_cb,
            subscriber_id="smoke_test_probe",
        )

    # Loop: publish a stream of binance/deribit updates to drive CausalEngine
    signal_before = (
        (await bus.get_metrics()).get("event_type_counts", {}).get("SignalEvent", 0)
    )
    branches_before = None
    with contextlib.suppress(Exception):
        branches_before = getattr(bs, "stats", {}).get("total_signals", 0)

    base_price = 50000.0
    base_iv = 0.55
    for i in range(12):
        now = datetime.now(UTC).isoformat()
        # Binance ticker
        await bus.publish(
            DataUpdateEvent(
                data={
                    "symbol": "BTCUSDT",
                    "price": base_price + i * 5.0,
                    "volume": 1.0 + i * 0.1,
                    "timestamp": now,
                },
                data_type="ticker",
                exchange="binance",
                source="binance_spot",
            )
        )
        # Deribit option snapshot
        await bus.publish(
            DataUpdateEvent(
                data={
                    "symbol": "BTC-26SEP25-60000-C",  # for DataSynchronizer compatibility
                    "instrument_name": "BTC-26SEP25-60000-C",
                    "strike": 60000,
                    "mark_price": 2400.0 + i * 10.0,
                    "open_interest": 1500 + i,
                    "bid_price": 2390.0 + i * 10.0,
                    "ask_price": 2410.0 + i * 10.0,
                    "mark_iv": base_iv + (0.001 * ((-1) ** i)),
                    "timestamp": now,
                },
                data_type="option",
                exchange="deribit",
                source="deribit",
            )
        )
        await asyncio.sleep(0.1)
    await asyncio.sleep(1.0)

    # Publish a SignalEvent to exercise analysis -> strategy -> risk path
    try:
        from src.core.event_bus import SignalEvent

        sig = SignalEvent(
            signal_data={
                "signal_type": "momentum",
                "strength": 0.8,
                "confidence": 0.9,
                "direction": "bullish",
            },
            signal_type="momentum",
            confidence=0.9,
            source="CausalEngine",
            timestamp=datetime.now(UTC),
        )
        await bus.publish(sig)
        await asyncio.sleep(0.5)
    except Exception as e:
        print("SignalEvent publish failed:", e)

    # Compute signal deltas from automatic generation
    signal_after = (
        (await bus.get_metrics()).get("event_type_counts", {}).get("SignalEvent", 0)
    )
    if branches_before is not None and "bs" in locals():
        branches_after = getattr(bs, "stats", {}).get("total_signals", 0)
        print("BranchStrategy total_signals delta:", branches_after - branches_before)
    print("SignalEvent count delta:", (signal_after or 0) - (signal_before or 0))

    # Inspect subscribers for TradingDecisionEvent
    try:
        subs = bus.get_subscribers("TradingDecisionEvent")
        print("TDE subscribers:", list(subs.keys()))
    except Exception as e:
        print("Inspect subscribers failed:", e)

    # Note: No manual TradingDecisionEvent. We rely purely on DataUpdateEvent-driven signals.

    # Also directly submit one order via OrderManager API to simulate entry
    # No direct submission; rely on TDE path for entry

    # Fetch and print metrics
    metrics = await bus.get_metrics()
    print(
        "EventBus processed:",
        metrics.get("total_processed"),
        "failed:",
        metrics.get("total_failed"),
        "event_type_counts:",
        metrics.get("event_type_counts"),
        "active_subscribers:",
        metrics.get("active_subscribers"),
    )

    if om is not None:
        print(
            "OrderManager queues:",
            "order_queue=",
            om.order_queue.qsize() if getattr(om, "order_queue", None) else None,
            "priority_queue=",
            om.priority_queue.qsize() if getattr(om, "priority_queue", None) else None,
            "active_orders=",
            len(om.active_orders),
        )
        print("Order history size:", len(om.order_history))

    # RiskEngine rejection test (low confidence)
    if re is not None and om is not None:
        from src.core.event_bus import TradingDecisionEvent

        metrics_before = await bus.get_metrics()
        rej_before = (metrics_before.get("event_type_counts", {}) or {}).get(
            "RiskRejectionEvent", 0
        )
        oh_before = len(om.order_history)

        tde_low_conf = TradingDecisionEvent(
            strategy_name="SmokeStrategy",
            action="buy",
            instruments=["BTCUSDT"],
            risk_params={"max_position_size": 100000, "risk_tolerance": 0.05},
            confidence=0.4,  # below threshold -> should be rejected
            decision_data={"signal_type": "momentum", "quantity": 0.01},
            source="OptionGridStrategy",
            timestamp=datetime.now(UTC),
        )
        await bus.publish(tde_low_conf)
        await asyncio.sleep(0.6)

        metrics_after = await bus.get_metrics()
        rej_after = (metrics_after.get("event_type_counts", {}) or {}).get(
            "RiskRejectionEvent", 0
        )
        oh_after = len(om.order_history)
        print("Risk rejection events delta:", rej_after - rej_before)
        print("Rejection test orders added:", oh_after - oh_before)

    # If probe subscriber was used, verify it fired
    if "called" in locals():
        print("Probe triggered:", called.is_set())

    # Graceful stop
    # Graceful stop components started
    if ds is not None:
        with contextlib.suppress(Exception):
            await ds.stop()
    for comp in (om, pm, pnl, ex, la, re):
        if comp is not None:
            with contextlib.suppress(Exception):
                await comp.stop()
    with contextlib.suppress(Exception):
        await bus.stop()
    return 0


if __name__ == "__main__":
    raise SystemExit(asyncio.run(main()))
