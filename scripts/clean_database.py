#!/usr/bin/env python3
"""
数据库清理脚本

清理所有交易数据，保留数据库结构
"""

import asyncio
import sys
from pathlib import Path

from dotenv import load_dotenv


async def clean_database():
    """清理数据库数据"""
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    # 现在可以安全地导入项目模块
    from src.core.config_manager import ConfigManager
    from src.data.timescale_manager import TimescaleDBManager

    print("🗑️ 开始清理数据库数据...")

    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        await config_manager.initialize()

        # 初始化数据库管理器
        timescale_manager = TimescaleDBManager(config_manager)
        await timescale_manager.initialize()

        # 获取数据库连接
        async with timescale_manager.get_connection() as conn:
            # 获取所有表名
            tables = await conn.fetch("""
                SELECT tablename
                FROM pg_tables
                WHERE schemaname = 'public'
                ORDER BY tablename
            """)

            print(f"📊 发现 {len(tables)} 个表")

            # 清理每个表的数据
            for table in tables:
                table_name = table["tablename"]
                try:
                    # 获取表的记录数
                    count_before = await conn.fetchval(
                        f"SELECT COUNT(*) FROM {table_name}"
                    )

                    if count_before > 0:
                        # 清空表数据
                        await conn.execute(f"TRUNCATE TABLE {table_name} CASCADE")
                        print(f"✅ 清理表 {table_name}: {count_before} 条记录")
                    else:
                        print(f"⚪ 表 {table_name}: 已为空")

                except Exception as e:
                    print(f"❌ 清理表 {table_name} 失败: {e}")

            # 执行VACUUM释放空间
            print("🔧 执行VACUUM释放磁盘空间...")
            await conn.execute("VACUUM")

            print("✅ 数据库清理完成！")

    except Exception as e:
        print(f"❌ 数据库清理失败: {e}")
        return False

    return True


if __name__ == "__main__":
    # 加载环境变量
    load_dotenv()
    success = asyncio.run(clean_database())
    sys.exit(0 if success else 1)
