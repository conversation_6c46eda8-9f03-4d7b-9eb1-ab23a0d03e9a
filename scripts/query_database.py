#!/usr/bin/env python3
"""
数据库查询脚本

查询数据库表结构和数据统计
"""

import asyncio
import sys
from pathlib import Path

from dotenv import load_dotenv


async def query_database():
    """查询数据库状态"""
    # 添加项目根目录到Python路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    # 现在可以安全地导入项目模块
    from src.core.config_manager import ConfigManager
    from src.data.timescale_manager import TimescaleDBManager

    print("📊 查询数据库状态...")

    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        await config_manager.initialize()

        # 初始化数据库管理器
        timescale_manager = TimescaleDBManager(config_manager)
        await timescale_manager.initialize()

        # 获取数据库连接
        async with timescale_manager.get_connection(read_only=True) as conn:
            # 获取数据库大小
            db_size = await conn.fetchval("SELECT pg_database_size(current_database())")
            db_size_mb = db_size / (1024 * 1024)
            print(f"💾 数据库大小: {db_size_mb:.2f} MB")

            # 获取所有表及其记录数
            tables_info = await conn.fetch("""
                SELECT
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                FROM pg_tables
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """)

            print(f"\n📋 数据库表信息 (共 {len(tables_info)} 个表):")
            print("-" * 80)
            print(f"{'表名':<30} {'大小':<15} {'记录数':<15}")
            print("-" * 80)

            total_records = 0
            for table_info in tables_info:
                table_name = table_info["tablename"]
                table_size = table_info["size"]

                try:
                    # 获取记录数
                    count = await conn.fetchval(f"SELECT COUNT(*) FROM {table_name}")
                    total_records += count
                    print(f"{table_name:<30} {table_size:<15} {count:<15,}")
                except Exception:
                    print(f"{table_name:<30} {table_size:<15} {'ERROR':<15}")

            print("-" * 80)
            print(f"{'总计':<30} {'':<15} {total_records:<15,}")

            # 查询最近的数据
            print("\n🕒 最近数据时间:")
            print("-" * 50)

            # 检查常见的时间序列表
            time_tables = [
                "market_data",
                "option_data",
                "risk_metrics",
                "signals",
                "trades",
            ]

            for table_name in time_tables:
                try:
                    # 检查表是否存在
                    exists = await conn.fetchval(
                        """
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables
                            WHERE table_schema = 'public'
                            AND table_name = $1
                        )
                    """,
                        table_name,
                    )

                    if exists:
                        # 获取最新记录时间
                        latest_time = await conn.fetchval(f"""
                            SELECT MAX(time) FROM {table_name}
                            WHERE time IS NOT NULL
                        """)

                        if latest_time:
                            print(f"{table_name:<20}: {latest_time}")
                        else:
                            print(f"{table_name:<20}: 无数据")
                    else:
                        print(f"{table_name:<20}: 表不存在")

                except Exception as e:
                    print(f"{table_name:<20}: 查询失败 - {e}")

            # 查询交易所数据统计
            print("\n🏢 交易所数据统计:")
            print("-" * 50)

            # 检查market_data表中的交易所数据
            try:
                exists = await conn.fetchval("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name = 'market_data'
                    )
                """)

                if exists:
                    exchange_stats = await conn.fetch("""
                        SELECT
                            exchange,
                            COUNT(*) as record_count,
                            MIN(time) as earliest_time,
                            MAX(time) as latest_time
                        FROM market_data
                        GROUP BY exchange
                        ORDER BY record_count DESC
                    """)

                    if exchange_stats:
                        for stat in exchange_stats:
                            print(
                                f"{stat['exchange']:<15}: {stat['record_count']:>8,} 条记录 "
                                f"({stat['earliest_time']} ~ {stat['latest_time']})"
                            )
                    else:
                        print("market_data表中无数据")
                else:
                    print("market_data表不存在")

            except Exception as e:
                print(f"查询交易所数据失败: {e}")

            print("\n✅ 数据库查询完成！")

    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")
        import traceback

        print(f"错误详情: {traceback.format_exc()}")
        return False

    return True


if __name__ == "__main__":
    # 加载环境变量
    load_dotenv()
    success = asyncio.run(query_database())
    sys.exit(0 if success else 1)
