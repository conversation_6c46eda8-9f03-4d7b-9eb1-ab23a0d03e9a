#!/usr/bin/env python3
"""
Celery Worker启动脚本

用于启动Celery worker进程，处理分布式任务
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.celery_manager import get_celery_app  # noqa: E402

# 获取Celery应用实例
app = get_celery_app()

if __name__ == "__main__":
    # 启动worker
    # 使用方法：python scripts/start_celery_worker.py
    # 或者直接使用celery命令：
    # celery -A scripts.start_celery_worker worker --loglevel=info

    print("🚀 启动Celery Worker...")
    print("使用以下命令启动worker:")
    print(
        "celery -A scripts.start_celery_worker worker --loglevel=info --queues=high_priority,medium_priority,low_priority"
    )
    print("或者分别启动不同优先级的worker:")
    print(
        "celery -A scripts.start_celery_worker worker --loglevel=info --queues=high_priority -n high_worker@%h"
    )
    print(
        "celery -A scripts.start_celery_worker worker --loglevel=info --queues=medium_priority -n medium_worker@%h"
    )
    print(
        "celery -A scripts.start_celery_worker worker --loglevel=info --queues=low_priority -n low_worker@%h"
    )
