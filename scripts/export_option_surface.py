#!/usr/bin/env python3
import argparse
import asyncio
import csv
from datetime import UTC, datetime

import asyncpg


async def export_csv(dsn: str, start: str, end: str, out: str):
    conn = await asyncpg.connect(dsn)
    try:
        rows = await conn.fetch(
            """
            SELECT time, bucket_id, iv_vwap, oi_sum, delta_exposure, gamma_exposure,
                   vega_exposure, mark_vwap, d_iv, d_oi, d_gex, d_vega
            FROM option_surface_features
            WHERE time BETWEEN $1 AND $2
            ORDER BY time ASC
            """,
            datetime.fromisoformat(start).replace(tzinfo=UTC),
            datetime.fromisoformat(end).replace(tzinfo=UTC),
        )
        with open(out, "w", newline="") as f:
            w = csv.writer(f)
            w.writerow(
                [
                    "time",
                    "bucket_id",
                    "iv_vwap",
                    "oi_sum",
                    "delta_exposure",
                    "gamma_exposure",
                    "vega_exposure",
                    "mark_vwap",
                    "d_iv",
                    "d_oi",
                    "d_gex",
                    "d_vega",
                ]
            )
            for r in rows:
                w.writerow([r[k] for k in r])
        print(f"Exported {len(rows)} rows to {out}")
    finally:
        await conn.close()


def main():
    ap = argparse.ArgumentParser()
    ap.add_argument(
        "--dsn",
        required=True,
        help="postgres DSN, e.g., ********************************/db",
    )
    ap.add_argument("--start", required=True, help="ISO start time")
    ap.add_argument("--end", required=True, help="ISO end time")
    ap.add_argument("--out", required=True, help="output CSV path")
    args = ap.parse_args()
    asyncio.run(export_csv(args.dsn, args.start, args.end, args.out))


if __name__ == "__main__":
    main()
