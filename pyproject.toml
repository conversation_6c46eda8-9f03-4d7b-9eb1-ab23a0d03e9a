[tool.ruff]
# Ruff 配置
line-length = 88
target-version = "py312"

[tool.ruff.lint]
# 启用的规则
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort imports
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "SIM",  # flake8-simplify
]

# 忽略的规则
ignore = [
    "E501",  # line too long (handled by formatter)
]

[tool.ruff.lint.isort]
# Import 排序配置
known-first-party = ["src"]
section-order = ["future", "standard-library", "third-party", "first-party", "local-folder"]
split-on-trailing-comma = true