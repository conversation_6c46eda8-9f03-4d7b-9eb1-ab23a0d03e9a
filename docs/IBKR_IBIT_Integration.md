# IBKR IBIT 期权交易集成指南

## 📋 概览

本指南详细说明如何配置和使用 BTC 到 IBIT 期权交易的完整映射架构。系统通过分析 Deribit BTC 期权数据生成交易信号，然后映射为 IBIT 期权交易并通过 IBKR 执行。

## 🏗️ 架构概览

```
Deribit BTC 期权数据 → 信号分析 → BTC→IBIT映射 → IBKR执行IBIT期权交易
```

### 核心组件

1. **BranchStrategy** - 主策略引擎，集成映射器和 IBKR 客户端
2. **BTCToIBITMapper** - BTC 信号到 IBIT 期权合约的映射层
3. **IBKRClient** - Interactive Brokers 连接和交易执行
4. **MarketHoursManager** - 美股交易时段控制
5. **PretradeFilters** - 交易前风险控制和流动性检查

## ⚙️ 配置设置

### 基本配置 (config/config.yaml)

```yaml
exchanges:
  ibkr:
    enabled: true
    host: "127.0.0.1"
    port: 4002          # Gateway Paper: 4002, TWS Paper: 7497
    client_id: 1
    paper_trading: true
    api_settings:
      read_only: false  # 必须为 false 才能下单
      enable_api: true

strategy:
  branch_strategy:
    # 市场时段策略
    market_hours_policy: "queue_until_open"  # open_only | prepost_allowed | queue_until_open
    
    # BTC 到 IBIT 映射参数
    btc_to_ibit_mapping:
      regression_a: 0.5      # 回归常数项
      regression_b: 0.0015   # 回归斜率 (需要根据历史数据校准)
      tracking_error_band_bps: 300  # 允许的跟踪误差 (3%)
    
    # 预交易过滤器
    pretrade_filters:
      max_spread_bps: 500           # 最大价差 (5%)
      min_open_interest: 200        # 最小持仓量
      min_volume: 50               # 最小成交量
      daily_csp_notional_cap: 100000  # CSP 日内名义上限
```

### 高级配置

```yaml
strategy:
  branch_strategy:
    # 期权选择参数
    otm_delta_range: [0.15, 0.35]    # OTM Delta 范围
    min_time_to_expiry_days: 7       # 最小到期天数
    max_time_to_expiry_days: 90      # 最大到期天数
    
    # 风险控制
    max_position_size: 10            # 最大持仓数量
    max_holding_hours: 24            # 最大持仓时间
```

## 🚀 启动和运行

### 1. 环境准备

```bash
# 确保 IB Gateway 已启动并正确配置
# 参考 docs/IBKR_Gateway_Setup.md

# 设置环境变量（可选）
export TEST_IBKR_ON_START=true
export TELEGRAM_BOT_TOKEN="your_bot_token"
export TELEGRAM_CHAT_ID="your_chat_id"
```

### 2. 启动系统

```bash
# 启动主程序
python src/main.py
```

### 3. 验证 IBIT 交易路径

启动后检查日志中的关键标识：

```
✅ 成功标识：
[IBKR][SUCCESS] Connected to Gateway
[BranchStrategy] IBIT mapper initialized successfully
[BranchStrategy] Market hours manager initialized
[BranchStrategy] Retrieved IBIT price: $45.23

⚠️ 分析模式标识：
[BranchStrategy] IBKR client not available, analysis-only mode
[BranchStrategy] Signal detected but no IBKR client available: bullish

❌ 错误标识：
[IBKR][ERROR] Connection failed
[BranchStrategy] IBIT option trade rejected by pretrade filters
[BranchStrategy] Market closed, queueing IBIT option trade until open
```

## 📊 交易流程详解

### 1. 信号生成
- 系统监控 Deribit BTC 期权数据
- CausalEngine 和 MicrostructureSignals 分析生成交易信号
- 信号包含方向 (bullish/bearish/neutral) 和强度

### 2. BTC→IBIT 映射
```python
# 映射逻辑示例
btc_price = 60000  # 当前 BTC 价格
ibit_estimated = 0.5 + 0.0015 * btc_price  # = 90.5

# 检查跟踪误差
if abs(ibit_quote - ibit_estimated) / ibit_estimated <= 0.03:
    # 在误差带内，继续交易
    if signal.direction == "bullish":
        action = "LONG_CALL"
        strike = ibit_quote * 1.05  # 5% OTM Call
    else:
        action = "SELL_PUT_CSP" 
        strike = ibit_quote * 0.90  # 10% OTM Put
```

### 3. 预交易检查
- **流动性检查**: 价差 < 5%, OI > 200, Volume > 50
- **资金检查**: CSP 需要足额现金担保
- **市场时段**: 根据 market_hours_policy 决定执行或排队

### 4. 订单执行
- 通过 IBKR 执行 IBIT 期权交易
- 支持 Market 和 Limit 订单类型
- 实时跟踪订单状态和成交情况

## 🛡️ 风险控制

### 预交易过滤器

| 检查项 | 阈值 | 说明 |
|--------|------|------|
| 价差 | < 500 bps | 确保流动性充足 |
| 持仓量 | > 200 | 避免流动性差的合约 |
| 成交量 | > 50 | 当日活跃度检查 |
| 跟踪误差 | < 300 bps | BTC-IBIT 价格偏离控制 |

### 市场时段控制

- **open_only**: 仅在正常交易时段 (9:30-16:00 ET)
- **prepost_allowed**: 允许盘前盘后交易
- **queue_until_open**: 闭市时排队，开盘后执行

### 持仓管理

- 自动跟踪 IBIT 期权持仓
- 支持止盈止损设置
- Assignment/Exercise 事件处理

## 🔧 故障排查

### 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 无法获取 IBIT 价格 | IBKR 连接问题 | 检查 Gateway 状态，重启连接 |
| 预交易检查失败 | 流动性不足 | 调整过滤器阈值或等待更好时机 |
| 订单被拒绝 | Read-Only API | 确保 Gateway 中取消勾选 "Read-Only API" |
| 跟踪误差过大 | 映射参数需校准 | 基于历史数据重新校准 a, b 参数 |

### 日志分析

```bash
# 查看实时日志
tail -f logs/btc_option_grid_bot.log | grep -E "(IBKR|IBIT|BranchStrategy)"

# 查看错误日志
grep -E "(ERROR|FAILED)" logs/btc_option_grid_bot.log | tail -20
```

## 📈 监控和告警

### Telegram 通知

系统会发送以下关键事件通知：
- IBIT 期权交易执行成功/失败
- 预交易检查拒绝
- 市场时段变化
- 连接状态变化

### 健康检查

```python
# 检查系统健康状态
curl http://localhost:8000/health

# 检查 IBKR 连接状态
curl http://localhost:8000/health/ibkr
```

## 🔄 日常维护

### 每日检查项
- [ ] IBKR Gateway 运行状态
- [ ] IBIT 价格获取正常
- [ ] 映射参数跟踪误差在合理范围
- [ ] 预交易过滤器工作正常

### 每周维护
- [ ] 校准 BTC→IBIT 映射参数
- [ ] 检查期权到期日计算准确性
- [ ] 更新市场假日日历
- [ ] 清理历史日志文件

## 📚 相关文档

- [IBKR Gateway 设置指南](IBKR_Gateway_Setup.md)
- [系统架构文档](design.md)
- [风险管理文档](requirements.md#风险控制)
- [API 文档](api.md)

## ⚠️ 重要提醒

1. **Paper Trading**: 建议先在纸上交易环境充分测试
2. **参数校准**: 映射参数需要基于历史数据定期校准
3. **风险控制**: 严格遵守预交易检查，不要绕过风控
4. **市场时段**: 注意美股交易时段，避免闭市时段的意外交易
5. **资金管理**: CSP 策略需要足额现金担保，注意资金占用
