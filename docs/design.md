# BTC智能期权网格策略交易架构 - 设计文档

## 📋 概述

本文档基于需求文档，设计一个完全独立的BTC智能期权交易系统。系统核心是用期权工具实现"分批抄底+分批卖出"策略，通过Sell Put/Call期权替代传统现货网格，在保证金模式下实现多倍资金效率，同时融合跨交易所因果信号分析进行策略增强。

**相关文档**：
- 需求文档：#[[file:requirements.md]]
- 实施任务：#[[file:tasks.md]]
- 实施指南：#[[file:implementation-guide.md]]
- 策略框架：#[[file:智能BTC期权网格策略框架.md]]
- 架构设计：#[[file:BTC智能期权网格交易框架架构设计.md]]

## 🧪 测试规范

### 测试文件组织结构

所有测试文件必须严格按照以下结构组织：

```
btc_option_grid_bot/tests/
├── __init__.py
├── unit/                    # 单元测试
│   ├── __init__.py
│   ├── test_celery_manager.py      # CeleryTaskManager测试
│   ├── test_base_component.py      # BaseComponent测试
│   ├── test_event_bus.py           # EventBus测试
│   ├── test_config_manager.py      # ConfigManager测试
│   ├── test_async_logger.py        # AsyncLogger测试
│   ├── test_binance_client.py      # BinanceClient测试
│   ├── test_deribit_client.py      # DeribitClient测试
│   └── ...                         # 其他组件测试
├── integration/             # 集成测试
│   ├── __init__.py
│   ├── test_data_flow.py           # 数据流集成测试
│   ├── test_strategy_integration.py # 策略集成测试
│   └── test_system_integration.py  # 系统集成测试
└── fixtures/                # 测试数据和工具
    ├── __init__.py
    ├── sample_data.py              # 示例数据
    └── test_helpers.py             # 测试辅助函数
```

### 测试命名规范

- **单元测试文件**: `test_{component_name}.py`
- **测试类**: `Test{ComponentName}`
- **测试方法**: `test_{specific_functionality}`
- **异步测试方法**: 使用 `@pytest.mark.asyncio` 装饰器

### 测试要求

1. **每个组件对应一个测试文件**: 一对一映射关系
2. **测试覆盖率**: 单元测试覆盖率 > 80%
3. **测试独立性**: 每个测试方法必须独立，不依赖其他测试
4. **清理机制**: 测试完成后自动清理资源，不留临时文件
5. **模拟外部依赖**: 使用mock模拟Redis、数据库等外部服务

### 禁止的测试实践

- ❌ 在项目根目录创建临时测试脚本
- ❌ 创建任务完成报告文档
- ❌ 创建演示脚本（demo_*.py）
- ❌ 在tests目录外创建测试相关文件

## 🏗️ 整体架构设计

### 系统架构概览

```mermaid
graph TB
    subgraph "External APIs"
        A[Binance WebSocket API]
        B[Deribit WebSocket API]
        C[Liquidation Data Source]
    end

    subgraph "API Gateway Layer"
        D[BinanceClient]
        E[DeribitClient]
        F[DataSynchronizer]
    end

    subgraph "Data Layer"
        G[DataEngine]
        H[CausalEngine]
        MS[MicrostructureSignals]
        I[CacheManager]
        J[Greeks Calculator]
    end

    subgraph "Strategy Layer"
        K[OptionGridStrategy]
        L[BranchStrategy]
        M[StrategyCoordinator]
        N[MarketDetector]
    end

    subgraph "Risk Management Layer"
        O[RiskEngine]
        P[ExpiryManager]
        Q[MarginCalculator]
        R[AlertManager]
    end

    subgraph "Execution Layer"
        S[OrderManager]
        T[PositionManager]
        U[PnLTracker]
        V[ExerciseHandler]
    end

    subgraph "Infrastructure Layer"
        W[EventBus]
        X[StateManager]
        Y[ConfigManager]
        Z[AsyncLogger]
        CELERY[Celery Worker Pool]
        TELEGRAM[TelegramBotHandler]
    end

    subgraph "Storage Layer"
        AA[Redis Cluster]
        BB[PostgreSQL + TimescaleDB]
    end

    A --> D
    B --> E
    C --> F
    D --> G
    E --> G
    F --> H
    G --> I
    H --> I
    I --> AA
    G --> J
    J --> K
    H --> L
    MS --> L
    K --> M
    L --> M
    M --> O
    O --> S
    S --> T
    T --> U
    O --> P
    P --> V
    W --> X
    X --> Y
    Z --> BB
    AA --> BB
    R --> TELEGRAM
```

### 分层架构详细设计

#### 1. API Gateway Layer（API网关层）
**职责**：统一管理外部API接入，提供数据标准化和错误处理

**核心组件**：
- **BinanceClient**：Binance WebSocket连接管理
- **DeribitClient**：Deribit WebSocket连接管理
- **DataSynchronizer**：多源数据时间同步和一致性保证，集成数据同步缓冲区支持±200ms延迟容忍

- **IBKRClient (USOptionsClient)**：IBKR TWS/Gateway 连接与美股期权/现货执行（支持 IBIT）
- **SignalMapper(BTC→IBIT)**：BTC（Binance/Deribit）→ IBIT 的价格/波动映射与执行过滤器

**设计要点**：
- 实现自动重连机制（指数退避，最大30秒）
- 提供统一的数据格式转换接口
- 支持连接池管理和负载均衡
- 实现数据质量监控和异常检测

#### 2. Data Layer（数据层）
**职责**：数据处理、存储、分析和缓存管理

**核心组件**：
- **DataEngine**：主控制器，协调数据流
- **CausalEngine**：跨交易所因果信号分析
- **MicrostructureSignals**：微观结构信号模块（趋势衰竭识别与精准出场）
- **CacheManager**：Redis缓存策略管理

**设计要点**：
- 实现实时数据流处理管道
- 实现三级数据分层存储：热数据内存（<1秒）+ 温数据Redis（1-300秒）+ 冷数据TimescaleDB（历史数据）
- 支持数据回放和历史查询
- 实现数据质量评分和过滤机制
- 集成Celery分布式任务队列处理计算密集任务

#### 3. Strategy Layer（策略层）
**职责**：交易策略逻辑和决策协调

**核心组件**：
- **OptionGridStrategy**：主策略（期权版分批抄底/卖出）
- **BranchStrategy**：分支策略（因果信号入场 + 微观结构信号出场）
- **StrategyCoordinator**：策略协调和资金分配
- **MarketDetector**：市场状态识别
- **OptionLifecycleManager**：期权生命周期管理（新增）

**设计要点**：
- 实现插件化策略架构，便于扩展
- 提供策略间通信和协调机制
- 支持策略参数动态调整
- 实现策略性能评估和优化
- **新增**：期权行权后的智能策略转换和风险协调

#### 4. Risk Management Layer（风险管理层）
**职责**：实时风险监控和自动化风险控制

**核心组件**：
- **RiskEngine**：核心风险控制引擎
- **ExpiryManager**：期权到期管理
- **MarginCalculator**：保证金计算
- **AlertManager**：告警管理

**设计要点**：
- 实现多维度风险指标实时监控
- 提供自动化风险处理机制
- 支持风险阈值动态调整
- 实现告警分级和多渠道通知

#### 5. Execution Layer（执行层）
**职责**：订单执行、仓位管理和盈亏追踪

**核心组件**：
- **OrderManager**：智能订单管理
- **PositionManager**：实时仓位跟踪
- **PnLTracker**：盈亏计算和追踪
- **ExerciseHandler**：期权行权处理

**设计要点**：
- 实现智能路由和订单优化
- 提供实时仓位同步机制
- 支持复杂期权组合的PnL计算
- 实现自动化行权决策

#### 6. Infrastructure Layer（基础设施层）
**职责**：系统基础服务和运维支撑

**核心组件**：
- **EventBus**：异步事件总线
- **StateManager**：系统状态管理
- **ConfigManager**：配置管理
- **AsyncLogger**：异步日志系统
- **CeleryWorkerPool**：分布式计算任务队列，处理Greeks计算等CPU密集任务
- **TelegramBotHandler**：Telegram查询和通知服务

**设计要点**：
- 实现高性能异步事件处理
- 提供分布式状态同步
- 支持配置热更新和版本管理
- 实现结构化日志和链路追踪
- 通过Celery实现分布式计算，支持Greeks预计算、数据压缩等CPU密集任务
- 集成Telegram Bot实现远程查询和实时告警通知

## 🔧 核心组件详细设计

### 数据模型定义

#### 基础数据类型

```python
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Union, Literal
from datetime import datetime
from decimal import Decimal
from enum import Enum

# 枚举类型定义
class ExchangeType(Enum):
    BINANCE = "binance"
    DERIBIT = "deribit"

class OptionType(Enum):
    CALL = "call"
    PUT = "put"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    PARTIALLY_FILLED = "partially_filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

class StrategyMode(Enum):
    ACCUMULATION = "accumulation"  # 抄底模式
    DISTRIBUTION = "distribution"  # 卖出模式
    SIDEWAYS = "sideways"         # 震荡模式

class RiskLevel(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class DataQuality(Enum):
    HIGH = "high"      # <50ms延迟
    MEDIUM = "medium"  # 50-200ms延迟
    LOW = "low"        # 200-500ms延迟
    STALE = "stale"    # >500ms延迟
```

#### 核心数据模型

```python
@dataclass
class MarketData:
    """统一市场数据模型"""
    symbol: str
    exchange: ExchangeType
    price: Decimal
    volume: Decimal
    timestamp: datetime
    data_quality: DataQuality
    metadata: Dict = field(default_factory=dict)

@dataclass
class Greeks:
    """期权Greeks参数"""
    delta: Decimal
    gamma: Decimal
    theta: Decimal
    vega: Decimal
    rho: Decimal
    timestamp: datetime

    def to_dict(self) -> Dict[str, float]:
        return {
            'delta': float(self.delta),
            'gamma': float(self.gamma),
            'theta': float(self.theta),
            'vega': float(self.vega),
            'rho': float(self.rho)
        }

@dataclass
class OptionData:
    """期权数据模型"""
    symbol: str
    underlying: str = "BTC"
    strike: Decimal
    expiry: datetime
    option_type: OptionType
    mark_price: Decimal
    bid: Decimal
    ask: Decimal
    volume: Decimal
    open_interest: int
    greeks: Greeks
    iv: Decimal  # 隐含波动率
    timestamp: datetime

    @property
    def bid_ask_spread(self) -> Decimal:
        """买卖价差"""
        return self.ask - self.bid

    @property
    def spread_ratio(self) -> Decimal:
        """价差比例"""
        if self.mark_price > 0:
            return self.bid_ask_spread / self.mark_price
        return Decimal('0')

    @property
    def days_to_expiry(self) -> int:
        """距离到期天数"""
        return (self.expiry.date() - datetime.utcnow().date()).days

@dataclass
class Order:
    """订单模型"""
    order_id: str
    symbol: str
    exchange: ExchangeType
    side: OrderSide
    size: Decimal
    price: Decimal
    order_type: str = "limit"
    status: OrderStatus = OrderStatus.PENDING
    filled_size: Decimal = Decimal('0')
    avg_fill_price: Decimal = Decimal('0')
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    strategy_id: str = ""
    metadata: Dict = field(default_factory=dict)

    @property
    def remaining_size(self) -> Decimal:
        return self.size - self.filled_size

    @property
    def is_filled(self) -> bool:
        return self.status == OrderStatus.FILLED

    @property
    def fill_ratio(self) -> Decimal:
        if self.size > 0:
            return self.filled_size / self.size
        return Decimal('0')

@dataclass
class Position:
    """持仓模型"""
    symbol: str
    exchange: ExchangeType
    size: Decimal  # 正数为多头，负数为空头
    avg_price: Decimal
    mark_price: Decimal
    unrealized_pnl: Decimal
    realized_pnl: Decimal
    margin_required: Decimal
    timestamp: datetime
    strategy_id: str = ""

    @property
    def market_value(self) -> Decimal:
        return self.size * self.mark_price

    @property
    def is_long(self) -> bool:
        return self.size > 0

    @property
    def is_short(self) -> bool:
        return self.size < 0

@dataclass
class OptionPosition(Position):
    """期权持仓模型"""
    strike: Decimal
    expiry: datetime
    option_type: OptionType
    greeks: Greeks
    iv: Decimal

    @property
    def is_itm(self) -> bool:
        """是否价内"""
        if self.option_type == OptionType.CALL:
            return self.mark_price > self.strike
        else:
            return self.mark_price < self.strike

    @property
    def intrinsic_value(self) -> Decimal:
        """内在价值"""
        if self.option_type == OptionType.CALL:
            return max(Decimal('0'), self.mark_price - self.strike)
        else:
            return max(Decimal('0'), self.strike - self.mark_price)

    @property
    def time_value(self) -> Decimal:
        """时间价值"""
        return self.mark_price - self.intrinsic_value
```

#### 信号和事件模型

```python
@dataclass
class CausalSignal:
    """因果分析信号"""
    signal_type: Literal["structure_divergence", "volatility_mismatch", "gamma_liquidation_overlap"]
    strength: Decimal  # 0-1之间
    confidence: Decimal  # 0-1之间
    timestamp: datetime
    metadata: Dict = field(default_factory=dict)

    @property
    def is_strong(self) -> bool:
        return self.strength > Decimal('0.7')

    @property
    def is_reliable(self) -> bool:
        return self.confidence > Decimal('0.8')

@dataclass
class MicrostructureSignal:
    """微观结构信号"""
    vwap_lwap_spread: Decimal
    orderbook_depth_1pct: Decimal
    orderbook_turnover: Decimal
    taker_net_pressure: Decimal
    timestamp: datetime

    def should_exit(self) -> bool:
        """判断是否应该出场"""
        return (
            self.vwap_lwap_spread < Decimal('-0.5') and
            self.orderbook_turnover > Decimal('0.4') and
            self.orderbook_depth_1pct < Decimal('0.3') and
            self.taker_net_pressure > Decimal('0')
        )

@dataclass
class RiskMetrics:
    """风险指标"""
    total_delta: Decimal
    total_gamma: Decimal
    total_theta: Decimal
    total_vega: Decimal
    margin_used: Decimal
    margin_available: Decimal
    var_1d: Decimal  # 1日VaR
    max_drawdown: Decimal
    timestamp: datetime

    @property
    def margin_usage_rate(self) -> Decimal:
        total_margin = self.margin_used + self.margin_available
        if total_margin > 0:
            return self.margin_used / total_margin
        return Decimal('0')

    @property
    def is_high_risk(self) -> bool:
        return (
            abs(self.total_delta) > Decimal('0.5') or
            abs(self.total_gamma) > Decimal('0.05') or
            self.margin_usage_rate > Decimal('0.7')
        )

@dataclass
class SystemEvent:
    """系统事件基类"""
    event_id: str
    event_type: str
    timestamp: datetime
    data: Dict = field(default_factory=dict)

@dataclass
class RiskEvent(SystemEvent):
    """风险事件"""
    event_type: str = "risk"
    risk_level: RiskLevel = RiskLevel.LOW
    message: str = ""
    suggested_actions: List[str] = field(default_factory=list)
```

### 异常处理体系

#### 异常类层次结构

```python
from typing import Dict, Optional, Any
from datetime import datetime
from enum import Enum

class ErrorSeverity(Enum):
    """错误严重程度"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"
    FATAL = "fatal"

class ErrorCategory(Enum):
    """错误分类"""
    CONNECTIVITY = "connectivity"
    DATA_QUALITY = "data_quality"
    STRATEGY = "strategy"
    RISK = "risk"
    EXECUTION = "execution"
    SYSTEM = "system"
    CONFIGURATION = "configuration"

class TradingSystemException(Exception):
    """交易系统基础异常类"""

    def __init__(
        self,
        message: str,
        error_code: str = None,
        severity: ErrorSeverity = ErrorSeverity.ERROR,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        context: Dict[str, Any] = None,
        recoverable: bool = True,
        retry_after: Optional[int] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.severity = severity
        self.category = category
        self.context = context or {}
        self.recoverable = recoverable
        self.retry_after = retry_after
        self.timestamp = datetime.utcnow()

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'error_code': self.error_code,
            'message': self.message,
            'severity': self.severity.value,
            'category': self.category.value,
            'context': self.context,
            'recoverable': self.recoverable,
            'retry_after': self.retry_after,
            'timestamp': self.timestamp.isoformat()
        }

class ConnectivityException(TradingSystemException):
    """连接相关异常"""

    def __init__(self, message: str, exchange: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.CONNECTIVITY,
            **kwargs
        )
        self.exchange = exchange
        if exchange:
            self.context['exchange'] = exchange

class DataException(TradingSystemException):
    """数据相关异常"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.DATA_QUALITY,
            **kwargs
        )

class StrategyException(TradingSystemException):
    """策略相关异常"""

    def __init__(self, message: str, strategy_id: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.STRATEGY,
            **kwargs
        )
        if strategy_id:
            self.context['strategy_id'] = strategy_id

class RiskException(TradingSystemException):
    """风险相关异常"""

    def __init__(self, message: str, risk_type: str = None, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.RISK,
            severity=ErrorSeverity.WARNING,
            **kwargs
        )
        if risk_type:
            self.context['risk_type'] = risk_type

class ExecutionException(TradingSystemException):
    """执行相关异常"""

    def __init__(self, message: str, **kwargs):
        super().__init__(
            message,
            category=ErrorCategory.EXECUTION,
            **kwargs
        )
```

#### 异常处理装饰器

```python
import functools
import asyncio
from typing import Callable, Type, Union, List
import logging

logger = logging.getLogger(__name__)

def handle_exceptions(
    exceptions: Union[Type[Exception], List[Type[Exception]]] = None,
    default_return=None,
    log_level: int = logging.ERROR,
    reraise: bool = False,
    max_retries: int = 0,
    retry_delay: float = 1.0
):
    """异常处理装饰器"""

    if exceptions is None:
        exceptions = [Exception]
    elif not isinstance(exceptions, list):
        exceptions = [exceptions]

    def decorator(func: Callable):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    last_exception = e

                    # 记录日志
                    logger.log(
                        log_level,
                        f"Exception in {func.__name__} (attempt {attempt + 1}): {e}",
                        exc_info=True
                    )

                    # 如果是最后一次尝试或不需要重试
                    if attempt == max_retries:
                        if reraise:
                            raise
                        return default_return

                    # 等待重试
                    if retry_delay > 0:
                        await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避

            return default_return

        return async_wrapper if asyncio.iscoroutinefunction(func) else func

    return decorator
```

### 1. DataEngine（数据引擎）

```python
from abc import ABC, abstractmethod
from typing import Protocol

class DataEngineInterface(ABC):
    """数据引擎接口"""

    @abstractmethod
    async def start(self) -> bool:
        """启动数据引擎"""
        pass

    @abstractmethod
    async def stop(self) -> bool:
        """停止数据引擎"""
        pass

    @abstractmethod
    async def process_market_data(self, data: MarketData) -> bool:
        """处理市场数据"""
        pass

    @abstractmethod
    async def get_latest_market_data(self, symbol: str) -> Optional[MarketData]:
        """获取最新市场数据"""
        pass

class DataEngine(DataEngineInterface):
    """
    数据引擎 - 统一数据处理中心
    """

    def __init__(self):
        self.binance_client = BinanceClient()
        self.deribit_client = DeribitClient()
        self.synchronizer = DataSynchronizer()
        self.cache_manager = CacheManager()
        self.greeks_processor = GreeksProcessor()  # 修正：处理而非计算Greeks
        self._is_running = False

    async def start(self) -> bool:
        """启动数据引擎"""
        try:
            await self.binance_client.connect()
            await self.deribit_client.connect()
            await self.synchronizer.start()
            self._is_running = True
            return True
        except Exception as e:
            raise DataException(f"Failed to start data engine: {str(e)}")

    async def stop(self) -> bool:
        """停止数据引擎"""
        try:
            await self.binance_client.disconnect()
            await self.deribit_client.disconnect()
            await self.synchronizer.stop()
            self._is_running = False
            return True
        except Exception as e:
            logger.error(f"Error stopping data engine: {e}")
            return False

    @handle_exceptions(
        exceptions=[DataException],
        max_retries=3,
        retry_delay=1.0
    )
    async def process_market_data(self, data: MarketData) -> bool:
        """处理市场数据"""
        # 数据验证和清洗
        cleaned_data = await self.validate_and_clean(data)

        # 存储到缓存
        await self.cache_manager.store(cleaned_data)

        # 如果是期权数据，处理Greeks
        if isinstance(cleaned_data, OptionData):
            await self.greeks_processor.process_greeks_data(cleaned_data.greeks)

        # 发布数据更新事件
        await self.event_bus.publish(DataUpdateEvent(cleaned_data))

        return True

    async def validate_and_clean(self, data: MarketData) -> MarketData:
        """验证和清洗数据"""
        # 价格合理性检查
        if data.price <= 0:
            raise DataException(
                f"Invalid price: {data.price}",
                context={'symbol': data.symbol, 'price': float(data.price)}
            )

        # 时间戳检查
        now = datetime.utcnow()
        if abs((now - data.timestamp).total_seconds()) > 300:  # 5分钟
            data.data_quality = DataQuality.STALE

        return data

    async def get_latest_market_data(self, symbol: str) -> Optional[MarketData]:
        """获取最新市场数据"""
        return await self.cache_manager.get(f"market_data:{symbol}", "market_data")
```

**关键特性**：
- 支持多数据源并发处理
- 实现数据质量自动评估
- 提供数据血缘追踪
- 支持数据回放和模拟
- 统一的异常处理机制
- 完整的数据验证流程

### 2. CausalEngine（因果分析引擎）

```python
class CausalAnalysisInterface(ABC):
    """因果分析接口"""

    @abstractmethod
    async def analyze_structure_divergence(self, market_data: Dict[str, Any]) -> CausalSignal:
        """分析结构分歧信号"""
        pass

    @abstractmethod
    async def analyze_volatility_mismatch(self, market_data: Dict[str, Any]) -> CausalSignal:
        """分析波动率错配信号"""
        pass

    @abstractmethod
    async def analyze_gamma_liquidation_overlap(self, market_data: Dict[str, Any]) -> CausalSignal:
        """分析Gamma清算重叠信号"""
        pass

    @abstractmethod
    async def get_signal_history(self, signal_type: str, hours: int = 24) -> List[CausalSignal]:
        """获取信号历史"""
        pass

class CausalEngine(CausalAnalysisInterface):
    """
    跨交易所因果信号分析引擎
    """

    def __init__(self):
        self.signal_generators = {
            'structure_divergence': StructureDivergenceAnalyzer(),
            'volatility_mismatch': VolatilityMismatchAnalyzer(),
            'gamma_liquidation_overlap': GammaLiquidationAnalyzer()
        }
        self.thresholds = {
            'structure_divergence': Decimal('0.65'),
            'volatility_mismatch': Decimal('0.60'),
            'gamma_liquidation_overlap': Decimal('0.70')
        }
        self._signal_history = []

    @handle_exceptions(
        exceptions=[DataException, StrategyException],
        max_retries=2,
        retry_delay=0.5
    )
    async def analyze_causal_signals(self, market_data: Dict) -> List[CausalSignal]:
        """实时分析因果信号"""
        signals = []

        try:
            # 并发分析三个信号
            tasks = []
            for signal_name, analyzer in self.signal_generators.items():
                task = asyncio.create_task(
                    self._analyze_single_signal(signal_name, analyzer, market_data)
                )
                tasks.append(task)

            results = await asyncio.gather(*tasks, return_exceptions=True)

            for result in results:
                if isinstance(result, CausalSignal):
                    signals.append(result)
                    # 存储信号历史
                    self._signal_history.append(result)
                elif isinstance(result, Exception):
                    logger.error(f"Signal analysis error: {result}")

            # 清理旧的信号历史（保留24小时）
            cutoff_time = datetime.utcnow() - timedelta(hours=24)
            self._signal_history = [
                s for s in self._signal_history
                if s.timestamp >= cutoff_time
            ]

            # 发布信号事件
            if signals:
                await self.event_bus.publish(SignalEvent(signal_data=signals))

        except Exception as e:
            raise StrategyException(f"Causal analysis failed: {str(e)}")

        return signals

    async def _analyze_single_signal(
        self,
        signal_name: str,
        analyzer,
        market_data: Dict
    ) -> Optional[CausalSignal]:
        """分析单个信号"""
        try:
            signal_strength = await analyzer.calculate(market_data)

            if signal_strength > self.thresholds[signal_name]:
                return CausalSignal(
                    signal_type=signal_name,
                    strength=signal_strength,
                    confidence=await analyzer.get_confidence(),
                    timestamp=datetime.utcnow(),
                    metadata=await analyzer.get_metadata()
                )
        except Exception as e:
            logger.error(f"Error analyzing {signal_name}: {e}")
            return None

    async def analyze_structure_divergence(self, market_data: Dict[str, Any]) -> CausalSignal:
        """分析结构分歧信号"""
        analyzer = self.signal_generators['structure_divergence']
        strength = await analyzer.calculate(market_data)

        return CausalSignal(
            signal_type="structure_divergence",
            strength=strength,
            confidence=await analyzer.get_confidence(),
            timestamp=datetime.utcnow(),
            metadata=await analyzer.get_metadata()
        )

    async def analyze_volatility_mismatch(self, market_data: Dict[str, Any]) -> CausalSignal:
        """分析波动率错配信号"""
        analyzer = self.signal_generators['volatility_mismatch']
        strength = await analyzer.calculate(market_data)

        return CausalSignal(
            signal_type="volatility_mismatch",
            strength=strength,
            confidence=await analyzer.get_confidence(),
            timestamp=datetime.utcnow(),
            metadata=await analyzer.get_metadata()
        )

    async def analyze_gamma_liquidation_overlap(self, market_data: Dict[str, Any]) -> CausalSignal:
        """分析Gamma清算重叠信号"""
        analyzer = self.signal_generators['gamma_liquidation_overlap']
        strength = await analyzer.calculate(market_data)

        return CausalSignal(
            signal_type="gamma_liquidation_overlap",
            strength=strength,
            confidence=await analyzer.get_confidence(),
            timestamp=datetime.utcnow(),
            metadata=await analyzer.get_metadata()
        )

    async def get_signal_history(self, signal_type: str, hours: int = 24) -> List[CausalSignal]:
        """获取信号历史"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        return [
            signal for signal in self._signal_history
            if signal.signal_type == signal_type and signal.timestamp >= cutoff_time
        ]

    async def update_signal_thresholds(self, thresholds: Dict[str, Decimal]) -> bool:
        """更新信号阈值"""
        try:
            for signal_type, threshold in thresholds.items():
                if signal_type in self.thresholds:
                    self.thresholds[signal_type] = threshold
            return True
        except Exception as e:
            logger.error(f"Failed to update thresholds: {e}")
            return False
```

**核心分析器**：

1. **StructureDivergenceAnalyzer**：
   - 分析Binance Taker Flow vs Deribit OI分歧
   - 识别主流资金与期权防御的方向差异
   - 为Iron Condor策略提供入场信号

2. **VolatilityMismatchAnalyzer**：
   - 计算Funding Rate与IV Skew背离度
   - 识别恐慌情绪与实际波动率的错配
   - 为波动率套利提供机会识别

3. **GammaLiquidationAnalyzer**：
   - 分析清算密集区与GEX重叠
   - 预测价格加速突破点
   - 为方向性期权提供精准时机

### 3. MicrostructureSignals（微观结构信号模块）

```python
class MicrostructureSignals:
    """
    微观结构信号模块 - 用于趋势衰竭识别与精准出场
    """

    def __init__(self):
        self.signal_calculators = {
            'vwap_lwap_spread': VWAPLWAPAnalyzer(),
            'orderbook_depth_1pct': OrderBookDepthAnalyzer(),
            'orderbook_turnover': OrderBookTurnoverAnalyzer(),
            'taker_net_pressure': TakerPressureAnalyzer()
        }

    async def calculate_microstructure_signals(self, trade_buffer, orderbook_data):
        """实时计算微观结构信号"""
        signals = {}

        # 1. VWAP vs LWAP Spread
        signals['vwap_lwap_spread'] = await self.calculate_vwap_lwap_spread(trade_buffer)

        # 2. Order Book Depth 1%
        signals['orderbook_depth_1pct'] = await self.calculate_depth_1pct(orderbook_data)

        # 3. Order Book Turnover Rate
        signals['orderbook_turnover'] = await self.calculate_turnover_rate(orderbook_data)

        # 4. Taker Net Pressure
        signals['taker_net_pressure'] = await self.calculate_taker_pressure(trade_buffer)

        return signals

    async def should_exit_due_to_structure_breakdown(self, signals):
        """判断是否应该因结构断裂而出场"""
        return (
            signals['vwap_lwap_spread'] < -0.5 and
            signals['orderbook_turnover'] > 0.4 and
            signals['orderbook_depth_1pct'] < self.depth_low_threshold and
            signals['taker_net_pressure'] > 0 and
            self.price_momentum_divergence_detected()
        )

    async def calculate_vwap_lwap_spread(self, trade_buffer):
        """计算VWAP vs LWAP价差"""
        prices = np.array([float(t['p']) for t in trade_buffer])
        sizes = np.array([float(t['q']) for t in trade_buffer])
        vwap = np.sum(prices * sizes) / np.sum(sizes)
        lwap = np.mean(prices)
        return vwap - lwap
```

**关键特性**：
- **零额外数据源**：完全复用现有Binance WebSocket数据
- **实时计算**：毫秒级响应，无系统负担
- **精准出场**：基于市场微观结构变化的客观出场标准
- **策略适配**：专为分支策略的方向性期权交易优化

### 4. CeleryTaskManager（分布式任务管理器）

```python
class CeleryTaskManager:
    """
    分布式计算任务管理器
    """

    def __init__(self):
        self.celery_app = Celery('btc_option_grid_bot')
        self.celery_app.config_from_object({
            'broker_url': 'redis://localhost:6379/0',
            'result_backend': 'redis://localhost:6379/0',
            'task_serializer': 'json',
            'accept_content': ['json'],
            'result_serializer': 'json',
            'timezone': 'UTC',
            'enable_utc': True,
            'worker_pool': 'threads',
            'worker_concurrency': 8
        })

    @celery_app.task(bind=True)
    def calculate_gex_distribution(self, option_chain_data):
        """基于Deribit实时Greeks计算GEX分布"""
        gex_levels = {}
        for strike, option_data in option_chain_data.items():
            # 直接使用Deribit提供的gamma值
            gex = option_data['greeks']['gamma'] * option_data['open_interest'] * option_data['contract_size']
            gex_levels[strike] = gex
        return gex_levels

    @celery_app.task(bind=True)
    def compress_historical_data(self, data_batch):
        """压缩历史数据存储"""
        compressed_data = self.compress_data(data_batch)
        return compressed_data
```

**关键特性**：
- **异步处理**：GEX分布计算、数据压缩等任务异步执行，不阻塞主流程
- **分布式扩展**：支持多worker横向扩展计算能力
- **任务重试**：失败任务自动重试，提高系统可靠性
- **优先级队列**：关键任务优先处理，保证实时性要求

### 5. TelegramBotHandler（Telegram通知和查询服务）

```python
class TelegramBotInterface(ABC):
    """Telegram Bot接口"""

    @abstractmethod
    async def send_message(self, chat_id: int, message: str, **kwargs) -> bool:
        """发送消息"""
        pass

    @abstractmethod
    async def handle_command(self, command: str, args: List[str], user_id: int) -> str:
        """处理命令"""
        pass

    @abstractmethod
    def is_authorized(self, user_id: int) -> bool:
        """检查用户授权"""
        pass

class TelegramBotHandler(TelegramBotInterface):
    """
    Telegram Bot查询和通知服务
    """

    def __init__(self, bot_token: str, authorized_users: List[int]):
        self.bot = telegram.Bot(token=bot_token)
        self.authorized_users = set(authorized_users)
        self.application = Application.builder().token(bot_token).build()
        self._setup_handlers()

        # 注入依赖的系统组件
        self.position_manager = None
        self.risk_engine = None
        self.causal_engine = None
        self.microstructure_signals = None
        self.strategy_coordinator = None

        # 命令处理器映射
        self.command_handlers = {
            'help': self.handle_help,
            'status': self.handle_status,
            'positions': self.handle_positions,
            'risk': self.handle_risk,
            'pnl': self.handle_pnl,
            'signals': self.handle_signals,
            'pause': self.handle_pause,
            'resume': self.handle_resume,
            'emergency': self.handle_emergency_stop,
            'setrisk': self.handle_set_risk
        }

    def inject_dependencies(self, components: Dict):
        """注入系统组件依赖"""
        self.position_manager = components.get('position_manager')
        self.risk_engine = components.get('risk_engine')
        self.causal_engine = components.get('causal_engine')
        self.microstructure_signals = components.get('microstructure_signals')
        self.strategy_coordinator = components.get('strategy_coordinator')

    def _setup_handlers(self):
        """设置命令处理器"""
        for command, handler in self.command_handlers.items():
            self.application.add_handler(CommandHandler(command, handler))

    def is_authorized(self, user_id: int) -> bool:
        """验证用户授权"""
        return user_id in self.authorized_users

    async def send_message(self, chat_id: int, message: str, **kwargs) -> bool:
        """发送消息"""
        try:
            await self.bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode=kwargs.get('parse_mode', 'Markdown'),
                **kwargs
            )
            return True
        except Exception as e:
            logger.error(f"Failed to send message to {chat_id}: {e}")
            return False

    async def handle_command(self, command: str, args: List[str], user_id: int) -> str:
        """处理命令"""
        if not self.is_authorized(user_id):
            return "❌ 未授权用户"

        handler = self.command_handlers.get(command)
        if handler:
            try:
                return await handler(args, user_id)
            except Exception as e:
                logger.error(f"Command {command} error: {e}")
                return f"❌ 命令执行失败: {str(e)}"
        else:
            return f"❌ 未知命令: {command}"

    async def handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """显示帮助信息"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        help_text = """
🤖 *BTC期权网格交易Bot命令*

📊 *查询命令*:
/status - 系统状态概览
/positions - 持仓信息
/risk - 风险指标
/pnl - 损益统计
/signals - 交易信号状态

⚡ *控制命令*:
/pause - 暂停策略
/resume - 恢复策略
/emergency - 紧急停止

⚙️ *设置命令*:
/setrisk [参数] - 调整风险参数
/help - 显示帮助信息

🔒 仅授权用户可使用
        """
        await update.message.reply_text(help_text, parse_mode='Markdown')

    async def handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询系统状态"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            status_data = await self.get_system_status()
            message = self.format_status_message(status_data)
            await update.message.reply_text(message, parse_mode='Markdown')
        except Exception as e:
            await update.message.reply_text(f"❌ 状态查询失败: {str(e)}")

    async def handle_positions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询持仓信息"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            positions = await self.position_manager.get_positions()
            message = self.format_positions_message(positions)
            await update.message.reply_text(message, parse_mode='Markdown')
        except Exception as e:
            await update.message.reply_text(f"❌ 持仓查询失败: {str(e)}")

    async def handle_risk(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询风险指标"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            positions = await self.position_manager.get_positions()
            risk_metrics = await self.risk_engine.calculate_portfolio_risk(positions)
            message = self.format_risk_message(risk_metrics)
            await update.message.reply_text(message, parse_mode='Markdown')
        except Exception as e:
            await update.message.reply_text(f"❌ 风险查询失败: {str(e)}")

    async def handle_pnl(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询损益统计"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            pnl_data = await self.position_manager.get_pnl_summary()
            message = self.format_pnl_message(pnl_data)
            await update.message.reply_text(message, parse_mode='Markdown')
        except Exception as e:
            await update.message.reply_text(f"❌ 盈亏查询失败: {str(e)}")

    async def handle_signals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询交易信号"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            # 获取因果信号历史
            causal_signals = {}
            for signal_type in ['structure_divergence', 'volatility_mismatch', 'gamma_liquidation_overlap']:
                signals = await self.causal_engine.get_signal_history(signal_type, hours=1)
                if signals:
                    latest_signal = max(signals, key=lambda s: s.timestamp)
                    causal_signals[signal_type] = {
                        'strength': float(latest_signal.strength),
                        'confidence': float(latest_signal.confidence),
                        'timestamp': latest_signal.timestamp
                    }

            # 获取微观结构信号（需要实现获取最新信号的方法）
            microstructure = await self.microstructure_signals.get_latest_signals()

            message = self.format_signals_message(causal_signals, microstructure)
            await update.message.reply_text(message, parse_mode='Markdown')
        except Exception as e:
            await update.message.reply_text(f"❌ 信号查询失败: {str(e)}")

    async def handle_pause(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """暂停策略"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            await self.strategy_coordinator.pause_all_strategies()
            await update.message.reply_text("⏸️ 所有策略已暂停")
        except Exception as e:
            await update.message.reply_text(f"❌ 暂停失败: {str(e)}")

    async def handle_resume(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """恢复策略"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            await self.strategy_coordinator.resume_all_strategies()
            await update.message.reply_text("▶️ 策略已恢复运行")
        except Exception as e:
            await update.message.reply_text(f"❌ 恢复失败: {str(e)}")

    async def handle_emergency_stop(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """紧急停止"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            await self.strategy_coordinator.emergency_stop_all()
            await self.send_alert("EMERGENCY_STOP", "用户触发紧急停止", RiskLevel.CRITICAL)
            await update.message.reply_text("🚨 紧急停止已执行")
        except Exception as e:
            await update.message.reply_text(f"❌ 紧急停止失败: {str(e)}")

    async def handle_set_risk(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """设置风险参数"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("❌ 未授权用户")
            return

        try:
            args = context.args
            if len(args) < 2:
                await update.message.reply_text("❌ 参数不足，格式: /setrisk <参数名> <值>")
                return

            param_name = args[0]
            param_value = Decimal(args[1])

            # 更新风险参数
            success = await self.risk_engine.update_risk_limits({param_name: param_value})

            if success:
                await update.message.reply_text(f"✅ 风险参数已更新: {param_name} = {param_value}")
            else:
                await update.message.reply_text(f"❌ 风险参数更新失败")

        except ValueError:
            await update.message.reply_text("❌ 参数值格式错误")
        except Exception as e:
            await update.message.reply_text(f"❌ 设置失败: {str(e)}")

    async def send_alert(self, alert_type: str, message: str, level: RiskLevel):
        """发送告警通知"""
        alert_message = f"""
🚨 *系统告警*

类型: {alert_type}
级别: {level.value.upper()}
时间: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}

消息: {message}
        """

        for user_id in self.authorized_users:
            try:
                await self.bot.send_message(
                    chat_id=user_id,
                    text=alert_message,
                    parse_mode='Markdown'
                )
            except Exception as e:
                logger.error(f"Failed to send alert to {user_id}: {e}")

    def format_status_message(self, status_data):
        """格式化系统状态消息"""
        return f"""
🖥️ *系统状态*

🔄 *运行状态*: {status_data['status']}
⏰ *运行时间*: {status_data['uptime']}
💾 *内存使用*: {status_data['memory_usage']:.1f}%
🖥️ *CPU使用*: {status_data['cpu_usage']:.1f}%

📡 *连接状态*:
• Binance: {status_data['binance_status']}
• Deribit: {status_data['deribit_status']}

🎯 *策略状态*:
• 主策略: {status_data['main_strategy_status']}
• 分支策略: {status_data['branch_strategy_status']}
        """

    def format_positions_message(self, positions):
        """格式化持仓消息"""
        if not positions:
            return "📊 *当前持仓*\n\n暂无持仓"

        message = "📊 *当前持仓*\n\n"

        total_value = Decimal('0')
        for position in positions:
            message += f"• {position.symbol}: {position.size:+.4f}\n"
            message += f"  均价: ${position.avg_price:.2f} | 市价: ${position.mark_price:.2f}\n"
            message += f"  盈亏: {position.unrealized_pnl:+.2f} USDT\n\n"
            total_value += position.market_value

        message += f"💰 *总市值*: ${total_value:.2f}"
        return message

    def format_pnl_message(self, pnl_data):
        """格式化损益消息"""
        return f"""
📈 *损益统计*

📊 *今日PnL*: {pnl_data['daily_pnl']:+.2f} USDT
📅 *本周PnL*: {pnl_data['weekly_pnl']:+.2f} USDT
📆 *本月PnL*: {pnl_data['monthly_pnl']:+.2f} USDT
💰 *总PnL*: {pnl_data['total_pnl']:+.2f} USDT

🎯 *主策略PnL*: {pnl_data['main_strategy_pnl']:+.2f} USDT
⚡ *分支策略PnL*: {pnl_data['branch_strategy_pnl']:+.2f} USDT

📊 *胜率*: {pnl_data['win_rate']:.1f}%
📈 *收益率*: {pnl_data['return_rate']:+.2f}%
        """

    def format_risk_message(self, risk_metrics: RiskMetrics):
        """格式化风险指标消息"""
        return f"""
🛡️ *风险指标监控*

📊 *保证金状态*:
• 已用: ${risk_metrics.margin_used:.2f}
• 可用: ${risk_metrics.margin_available:.2f}
• 占用率: {risk_metrics.margin_usage_rate:.1%}

⚡ *Greeks风险*:
• Delta: {risk_metrics.total_delta:+.3f}
• Gamma: {risk_metrics.total_gamma:+.3f}
• Vega: {risk_metrics.total_vega:+.2f}
• Theta: {risk_metrics.total_theta:+.2f}

📊 *风险指标*:
• VaR(1日): ${risk_metrics.var_1d:.2f}
• 最大回撤: {risk_metrics.max_drawdown:.1%}
        """

    def format_signals_message(self, causal_signals, microstructure):
        """格式化信号状态消息"""
        message = "🎯 *交易信号状态*\n\n"

        message += "🔍 *因果分析信号*:\n"
        for signal_name, signal_data in causal_signals.items():
            strength = signal_data['strength']
            status = "🟢" if strength > 0.7 else "🟡" if strength > 0.4 else "🔴"
            message += f"{status} {signal_name}: {strength:.2f}\n"

        message += "\n📊 *微观结构信号*:\n"
        for signal_name, value in microstructure.items():
            message += f"• {signal_name}: {value:.3f}\n"

        return message

    async def start_bot(self):
        """启动Telegram Bot"""
        try:
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            logger.info("Telegram Bot started successfully")
        except Exception as e:
            logger.error(f"Failed to start Telegram Bot: {e}")
            raise

    async def stop_bot(self):
        """停止Telegram Bot"""
        try:
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()
            logger.info("Telegram Bot stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping Telegram Bot: {e}")

    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        import psutil

        return {
            'status': 'running',
            'uptime': '2h 30m',  # 实际应该计算运行时间
            'memory_usage': psutil.virtual_memory().percent,
            'cpu_usage': psutil.cpu_percent(),
            'binance_status': 'connected',
            'deribit_status': 'connected',
            'main_strategy_status': 'active',
            'branch_strategy_status': 'active'
        }
```

**关键特性**：
- **命令授权**：仅允许授权用户访问敏感功能
- **实时查询**：支持系统状态、持仓、风险、信号等查询
- **远程控制**：支持策略暂停、恢复、紧急停止等操作
- **格式化消息**：友好的Markdown格式输出
- **告警推送**：集成系统告警，实时推送重要事件

### 6. OptionGridStrategy（主策略引擎）

```python
class OptionGridStrategy:
    """
    期权版分批抄底/卖出策略
    """

    def __init__(self):
        self.modes = {
            'accumulation': AccumulationMode(),    # 下跌抄底模式
            'distribution': DistributionMode(),    # 上涨卖出模式
            'sideways': SidewaysMode()            # 震荡收益模式
        }
        self.current_mode = None

    async def execute_strategy(self, market_state: MarketState):
        """执行策略逻辑"""

        # 根据市场状态选择模式
        target_mode = await self.select_mode(market_state)

        if target_mode != self.current_mode:
            await self.transition_mode(target_mode)

        # 执行当前模式的交易逻辑
        actions = await self.current_mode.generate_actions(market_state)

        for action in actions:
            await self.risk_check(action)
            await self.execute_action(action)

    async def select_mode(self, market_state: MarketState) -> str:
        """智能模式选择"""
        price_trend = market_state.price_momentum
        iv_rank = market_state.iv_rank

        if price_trend < -0.05 and iv_rank > 0.7:
            return 'accumulation'  # 下跌+高IV，卖Put抄底
        elif price_trend > 0.05 and self.has_spot_position():
            return 'distribution'  # 上涨+有现货，卖Call获利
        else:
            return 'sideways'      # 震荡，收获时间价值
```

**策略模式详解**：

1. **AccumulationMode（抄底模式）**：
   ```python
   class AccumulationMode:
       async def generate_actions(self, market_state):
           actions = []

           # 分批卖Put期权
           for level in self.get_accumulation_levels():
               if self.should_sell_put(level, market_state):
                   put_strike = self.calculate_put_strike(level)
                   action = SellPutAction(
                       strike=put_strike,
                       size=self.calculate_size(level),
                       expiry=self.select_expiry()
                   )
                   actions.append(action)

           return actions
   ```

2. **DistributionMode（卖出模式）**：
   ```python
   class DistributionMode:
       async def generate_actions(self, market_state):
           actions = []

           # 针对现货持仓卖Call
           spot_position = await self.get_spot_position()
           if spot_position > 0:
               call_strike = self.calculate_call_strike(market_state)
               action = SellCallAction(
                   strike=call_strike,
                   size=min(spot_position, self.max_call_size),
                   expiry=self.select_expiry()
               )
               actions.append(action)

           return actions
   ```

3. **SidewaysMode（震荡模式）**：
   ```python
   class SidewaysMode:
       async def generate_actions(self, market_state):
           # Short Strangle或Iron Condor
           if market_state.volatility_rank > 0.6:
               return await self.create_short_strangle()
           else:
               return await self.create_iron_condor()
   ```

### 7. RiskEngine（风险控制引擎）

```python
class RiskEngineInterface(ABC):
    """风险引擎接口"""

    @abstractmethod
    async def validate_order(self, order: Order) -> bool:
        """验证订单风险"""
        pass

    @abstractmethod
    async def calculate_portfolio_risk(self, positions: List[Position]) -> RiskMetrics:
        """计算组合风险"""
        pass

    @abstractmethod
    async def check_risk_limits(self, metrics: RiskMetrics) -> List[RiskEvent]:
        """检查风险限制"""
        pass

    @abstractmethod
    async def get_var_calculation(self, confidence: float = 0.95, horizon_days: int = 1) -> Decimal:
        """计算VaR"""
        pass

class RiskEngine(RiskEngineInterface):
    """
    期权专用风险控制引擎
    """

    def __init__(self):
        self.risk_monitors = {
            'margin': MarginRiskMonitor(),
            'greeks': GreeksRiskMonitor(),
            'expiry': ExpiryRiskMonitor(),
            'liquidity': LiquidityRiskMonitor()
        }
        self.risk_limits = {
            'max_delta': Decimal('0.5'),
            'max_gamma': Decimal('0.05'),
            'max_margin_usage': Decimal('0.7'),
            'max_daily_loss': Decimal('0.05'),
            'min_liquidity_score': Decimal('0.2')
        }
        self.alert_manager = AlertManager()

    @handle_exceptions(
        exceptions=[RiskException],
        max_retries=1,
        retry_delay=0.1
    )
    async def validate_order(self, order: Order) -> bool:
        """验证订单风险"""
        try:
            # 检查订单基本参数
            if order.size <= 0:
                raise RiskException(
                    f"Invalid order size: {order.size}",
                    risk_type="order_validation"
                )

            # 检查价格合理性
            if order.price <= 0:
                raise RiskException(
                    f"Invalid order price: {order.price}",
                    risk_type="order_validation"
                )

            # 检查保证金需求
            margin_required = await self._calculate_order_margin(order)
            available_margin = await self._get_available_margin()

            if margin_required > available_margin:
                raise RiskException(
                    f"Insufficient margin: required {margin_required}, available {available_margin}",
                    risk_type="margin_insufficient"
                )

            return True

        except RiskException:
            raise
        except Exception as e:
            raise RiskException(f"Order validation failed: {str(e)}")

    async def calculate_portfolio_risk(self, positions: List[Position]) -> RiskMetrics:
        """计算组合风险"""
        try:
            total_delta = Decimal('0')
            total_gamma = Decimal('0')
            total_theta = Decimal('0')
            total_vega = Decimal('0')
            margin_used = Decimal('0')

            for position in positions:
                if isinstance(position, OptionPosition):
                    # 计算Greeks敞口
                    position_size = abs(position.size)
                    total_delta += position.greeks.delta * position_size
                    total_gamma += position.greeks.gamma * position_size
                    total_theta += position.greeks.theta * position_size
                    total_vega += position.greeks.vega * position_size

                margin_used += position.margin_required

            # 获取可用保证金
            margin_available = await self._get_available_margin()

            # 计算VaR
            var_1d = await self.get_var_calculation()

            # 计算最大回撤
            max_drawdown = await self._calculate_max_drawdown()

            return RiskMetrics(
                total_delta=total_delta,
                total_gamma=total_gamma,
                total_theta=total_theta,
                total_vega=total_vega,
                margin_used=margin_used,
                margin_available=margin_available,
                var_1d=var_1d,
                max_drawdown=max_drawdown,
                timestamp=datetime.utcnow()
            )

        except Exception as e:
            raise RiskException(f"Portfolio risk calculation failed: {str(e)}")

    async def check_risk_limits(self, metrics: RiskMetrics) -> List[RiskEvent]:
        """检查风险限制"""
        risk_events = []

        # 检查Delta敞口
        if abs(metrics.total_delta) > self.risk_limits['max_delta']:
            risk_events.append(RiskEvent(
                event_id=f"delta_limit_{datetime.utcnow().timestamp()}",
                event_type="risk",
                risk_level=RiskLevel.HIGH,
                message=f"Delta exposure {metrics.total_delta} exceeds limit {self.risk_limits['max_delta']}",
                suggested_actions=["Hedge delta exposure", "Reduce position size"],
                timestamp=datetime.utcnow()
            ))

        # 检查Gamma敞口
        if abs(metrics.total_gamma) > self.risk_limits['max_gamma']:
            risk_events.append(RiskEvent(
                event_id=f"gamma_limit_{datetime.utcnow().timestamp()}",
                event_type="risk",
                risk_level=RiskLevel.HIGH,
                message=f"Gamma exposure {metrics.total_gamma} exceeds limit {self.risk_limits['max_gamma']}",
                suggested_actions=["Reduce gamma exposure", "Close high-gamma positions"],
                timestamp=datetime.utcnow()
            ))

        # 检查保证金使用率
        if metrics.margin_usage_rate > self.risk_limits['max_margin_usage']:
            risk_events.append(RiskEvent(
                event_id=f"margin_limit_{datetime.utcnow().timestamp()}",
                event_type="risk",
                risk_level=RiskLevel.CRITICAL,
                message=f"Margin usage {metrics.margin_usage_rate:.2%} exceeds limit {self.risk_limits['max_margin_usage']:.2%}",
                suggested_actions=["Add margin", "Close positions", "Reduce position size"],
                timestamp=datetime.utcnow()
            ))

        return risk_events

    async def get_var_calculation(self, confidence: float = 0.95, horizon_days: int = 1) -> Decimal:
        """计算VaR"""
        try:
            # 简化的VaR计算，实际应该使用历史模拟或蒙特卡洛方法
            portfolio_value = await self._get_portfolio_value()
            volatility = await self._get_portfolio_volatility()

            # 使用正态分布假设计算VaR
            from scipy.stats import norm
            z_score = norm.ppf(confidence)
            var = portfolio_value * volatility * z_score * (horizon_days ** 0.5)

            return Decimal(str(var))

        except Exception as e:
            logger.error(f"VaR calculation error: {e}")
            return Decimal('0')

    async def monitor_risks(self, positions: List[Position]) -> List[RiskEvent]:
        """实时风险监控"""
        risk_events = []

        try:
            # 计算组合风险指标
            metrics = await self.calculate_portfolio_risk(positions)

            # 检查风险限制
            limit_events = await self.check_risk_limits(metrics)
            risk_events.extend(limit_events)

            # 运行各个风险监控器
            for monitor_name, monitor in self.risk_monitors.items():
                try:
                    monitor_events = await monitor.assess_risk(positions)
                    if monitor_events:
                        risk_events.extend(monitor_events)
                except Exception as e:
                    logger.error(f"Risk monitor {monitor_name} error: {e}")

            # 处理风险事件
            if risk_events:
                await self.handle_risk_events(risk_events)

        except Exception as e:
            logger.error(f"Risk monitoring error: {e}")

        return risk_events

    async def handle_risk_events(self, events: List[RiskEvent]):
        """处理风险事件"""
        for event in events:
            try:
                # 发送告警
                await self.alert_manager.send_alert(event)

                # 自动化处理
                if event.risk_level in [RiskLevel.CRITICAL, RiskLevel.HIGH]:
                    await self.execute_emergency_actions(event)

            except Exception as e:
                logger.error(f"Error handling risk event {event.event_id}: {e}")

    async def execute_emergency_actions(self, event: RiskEvent):
        """执行紧急风险处理"""
        try:
            if "margin" in event.message.lower():
                # 保证金不足，强制减仓
                await self._force_position_reduction(0.3)  # 减仓30%
            elif "delta" in event.message.lower():
                # Delta敞口过大，执行对冲
                await self._hedge_delta_exposure()
            elif "gamma" in event.message.lower():
                # Gamma敞口过大，平仓高Gamma仓位
                await self._close_high_gamma_positions()

        except Exception as e:
            logger.error(f"Emergency action execution failed: {e}")

    async def _calculate_order_margin(self, order: Order) -> Decimal:
        """计算订单保证金需求"""
        # 简化计算，实际应该根据交易所规则
        return order.size * order.price * Decimal('0.1')  # 假设10%保证金

    async def _get_available_margin(self) -> Decimal:
        """获取可用保证金"""
        # 从账户管理器获取
        return Decimal('10000')  # 示例值

    async def _get_portfolio_value(self) -> Decimal:
        """获取组合价值"""
        return Decimal('100000')  # 示例值

    async def _get_portfolio_volatility(self) -> Decimal:
        """获取组合波动率"""
        return Decimal('0.02')  # 示例值：2%日波动率

    async def _calculate_max_drawdown(self) -> Decimal:
        """计算最大回撤"""
        return Decimal('0.05')  # 示例值：5%

    async def _force_position_reduction(self, reduction_ratio: float):
        """强制减仓"""
        logger.warning(f"Executing forced position reduction: {reduction_ratio:.1%}")
        # 实现减仓逻辑

    async def _hedge_delta_exposure(self):
        """对冲Delta敞口"""
        logger.warning("Executing delta hedge")
        # 实现Delta对冲逻辑

    async def _close_high_gamma_positions(self):
        """平仓高Gamma仓位"""
        logger.warning("Closing high gamma positions")
        # 实现高Gamma仓位平仓逻辑
```

**风险监控模块**：

1. **MarginRiskMonitor**：
   - 实时监控保证金占用率
   - 预测保证金需求变化
   - 自动触发减仓或补充保证金

2. **GreeksRiskMonitor**：
   - 监控组合Delta/Gamma/Vega敞口
   - 实时计算Greeks风险价值
   - 提供自动对冲建议

3. **ExpiryRiskMonitor**：
   - 跟踪期权到期风险
   - 计算行权概率和资金需求
   - 自动执行到期处理流程

### 8. StrategyCoordinator（策略协调器）

```python
class StrategyCoordinatorInterface(ABC):
    """策略协调器接口"""

    @abstractmethod
    async def register_strategy(self, strategy: StrategyInterface) -> bool:
        """注册策略"""
        pass

    @abstractmethod
    async def allocate_capital(self, allocations: Dict[str, Decimal]) -> bool:
        """分配资金"""
        pass

    @abstractmethod
    async def check_strategy_conflicts(self) -> List[Dict[str, Any]]:
        """检查策略冲突"""
        pass

    @abstractmethod
    async def emergency_stop_all(self) -> bool:
        """紧急停止所有策略"""
        pass

class StrategyCoordinator(StrategyCoordinatorInterface):
    """
    策略协调器 - 管理主分支策略协调和资金分配
    """

    def __init__(self):
        self.strategies: Dict[str, StrategyInterface] = {}
        self.capital_allocations = {
            'main_strategy': Decimal('0.70'),    # 主策略70%
            'branch_strategy': Decimal('0.25'),  # 分支策略25%
            'reserve': Decimal('0.05')           # 预留5%
        }
        self.total_capital = Decimal('100000')  # 总资金
        self.risk_engine = None
        self.position_manager = None
        self._is_emergency_stopped = False

    def inject_dependencies(self, risk_engine, position_manager):
        """注入依赖"""
        self.risk_engine = risk_engine
        self.position_manager = position_manager

    async def register_strategy(self, strategy: StrategyInterface) -> bool:
        """注册策略"""
        try:
            strategy_id = strategy.strategy_id
            if strategy_id in self.strategies:
                logger.warning(f"Strategy {strategy_id} already registered")
                return False

            self.strategies[strategy_id] = strategy
            logger.info(f"Strategy {strategy_id} registered successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to register strategy: {e}")
            return False

    async def unregister_strategy(self, strategy_id: str) -> bool:
        """注销策略"""
        try:
            if strategy_id in self.strategies:
                strategy = self.strategies[strategy_id]
                await strategy.stop()
                del self.strategies[strategy_id]
                logger.info(f"Strategy {strategy_id} unregistered")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to unregister strategy {strategy_id}: {e}")
            return False

    async def allocate_capital(self, allocations: Dict[str, Decimal]) -> bool:
        """分配资金"""
        try:
            # 验证分配比例总和为1
            total_allocation = sum(allocations.values())
            if abs(total_allocation - Decimal('1.0')) > Decimal('0.01'):
                raise StrategyException(f"Invalid allocation total: {total_allocation}")

            # 更新资金分配
            self.capital_allocations.update(allocations)

            # 通知各策略更新资金配置
            for strategy_id, strategy in self.strategies.items():
                if strategy_id in allocations:
                    allocated_capital = self.total_capital * allocations[strategy_id]
                    await strategy.update_config({'allocated_capital': allocated_capital})

            logger.info(f"Capital allocation updated: {allocations}")
            return True

        except Exception as e:
            logger.error(f"Capital allocation failed: {e}")
            return False

    async def check_strategy_conflicts(self) -> List[Dict[str, Any]]:
        """检查策略冲突"""
        conflicts = []

        try:
            # 获取所有策略的持仓
            all_positions = await self.position_manager.get_positions()

            # 计算总Delta敞口
            total_delta = Decimal('0')
            strategy_deltas = {}

            for position in all_positions:
                if isinstance(position, OptionPosition):
                    delta_exposure = position.greeks.delta * position.size
                    total_delta += delta_exposure

                    strategy_id = position.strategy_id
                    if strategy_id not in strategy_deltas:
                        strategy_deltas[strategy_id] = Decimal('0')
                    strategy_deltas[strategy_id] += delta_exposure

            # 检查总Delta敞口是否超限
            if abs(total_delta) > Decimal('0.5'):
                conflicts.append({
                    'type': 'delta_exposure',
                    'severity': 'high',
                    'message': f'Total delta exposure {total_delta} exceeds limit 0.5',
                    'strategy_contributions': strategy_deltas,
                    'suggested_action': 'hedge_delta_exposure'
                })

            # 检查保证金使用冲突
            risk_metrics = await self.risk_engine.calculate_portfolio_risk(all_positions)
            if risk_metrics.margin_usage_rate > Decimal('0.8'):
                conflicts.append({
                    'type': 'margin_conflict',
                    'severity': 'critical',
                    'message': f'Margin usage {risk_metrics.margin_usage_rate:.1%} too high',
                    'suggested_action': 'reduce_positions'
                })

            # 检查策略间的方向性冲突
            directional_conflicts = await self._check_directional_conflicts()
            conflicts.extend(directional_conflicts)

        except Exception as e:
            logger.error(f"Strategy conflict check failed: {e}")

        return conflicts

    async def _check_directional_conflicts(self) -> List[Dict[str, Any]]:
        """检查方向性冲突"""
        conflicts = []

        # 获取主策略和分支策略的方向性偏好
        main_strategy = self.strategies.get('main_strategy')
        branch_strategy = self.strategies.get('branch_strategy')

        if main_strategy and branch_strategy:
            # 检查是否存在相反的方向性偏好
            main_bias = await self._get_strategy_directional_bias(main_strategy)
            branch_bias = await self._get_strategy_directional_bias(branch_strategy)

            if main_bias and branch_bias and main_bias * branch_bias < 0:
                conflicts.append({
                    'type': 'directional_conflict',
                    'severity': 'medium',
                    'message': f'Main strategy bias {main_bias} conflicts with branch strategy bias {branch_bias}',
                    'suggested_action': 'coordinate_directions'
                })

        return conflicts

    async def _get_strategy_directional_bias(self, strategy) -> Optional[Decimal]:
        """获取策略的方向性偏好"""
        try:
            metrics = await strategy.get_performance_metrics()
            return metrics.get('directional_bias', Decimal('0'))
        except:
            return None

    async def rebalance_positions(self) -> List[Order]:
        """重新平衡仓位"""
        rebalance_orders = []

        try:
            # 检查策略冲突
            conflicts = await self.check_strategy_conflicts()

            for conflict in conflicts:
                if conflict['suggested_action'] == 'hedge_delta_exposure':
                    # 生成Delta对冲订单
                    hedge_orders = await self._generate_delta_hedge_orders(conflict)
                    rebalance_orders.extend(hedge_orders)

                elif conflict['suggested_action'] == 'reduce_positions':
                    # 生成减仓订单
                    reduction_orders = await self._generate_position_reduction_orders(conflict)
                    rebalance_orders.extend(reduction_orders)

        except Exception as e:
            logger.error(f"Position rebalancing failed: {e}")

        return rebalance_orders

    async def _generate_delta_hedge_orders(self, conflict: Dict[str, Any]) -> List[Order]:
        """生成Delta对冲订单"""
        orders = []

        try:
            # 计算需要对冲的Delta
            total_delta = conflict.get('strategy_contributions', {})
            net_delta = sum(total_delta.values())

            # 生成对冲订单（简化实现）
            if abs(net_delta) > Decimal('0.1'):
                hedge_size = -net_delta  # 反向对冲

                order = Order(
                    order_id=f"hedge_{datetime.utcnow().timestamp()}",
                    symbol="BTC-PERPETUAL",
                    exchange=ExchangeType.BINANCE,
                    side=OrderSide.BUY if hedge_size > 0 else OrderSide.SELL,
                    size=abs(hedge_size),
                    price=Decimal('0'),  # 市价单
                    order_type="market",
                    strategy_id="hedge"
                )
                orders.append(order)

        except Exception as e:
            logger.error(f"Delta hedge order generation failed: {e}")

        return orders

    async def _generate_position_reduction_orders(self, conflict: Dict[str, Any]) -> List[Order]:
        """生成减仓订单"""
        orders = []

        try:
            # 获取当前持仓
            positions = await self.position_manager.get_positions()

            # 按保证金占用排序，优先减少高保证金仓位
            sorted_positions = sorted(
                positions,
                key=lambda p: p.margin_required,
                reverse=True
            )

            # 减少30%的高保证金仓位
            for position in sorted_positions[:3]:  # 前3个最大仓位
                reduction_size = position.size * Decimal('0.3')

                order = Order(
                    order_id=f"reduce_{position.symbol}_{datetime.utcnow().timestamp()}",
                    symbol=position.symbol,
                    exchange=position.exchange,
                    side=OrderSide.SELL if position.size > 0 else OrderSide.BUY,
                    size=abs(reduction_size),
                    price=position.mark_price,
                    order_type="limit",
                    strategy_id="risk_reduction"
                )
                orders.append(order)

        except Exception as e:
            logger.error(f"Position reduction order generation failed: {e}")

        return orders

    async def pause_all_strategies(self) -> bool:
        """暂停所有策略"""
        try:
            for strategy_id, strategy in self.strategies.items():
                await strategy.pause()
                logger.info(f"Strategy {strategy_id} paused")
            return True
        except Exception as e:
            logger.error(f"Failed to pause strategies: {e}")
            return False

    async def resume_all_strategies(self) -> bool:
        """恢复所有策略"""
        try:
            if self._is_emergency_stopped:
                logger.warning("Cannot resume strategies: emergency stop is active")
                return False

            for strategy_id, strategy in self.strategies.items():
                await strategy.resume()
                logger.info(f"Strategy {strategy_id} resumed")
            return True
        except Exception as e:
            logger.error(f"Failed to resume strategies: {e}")
            return False

    async def emergency_stop_all(self) -> bool:
        """紧急停止所有策略"""
        try:
            self._is_emergency_stopped = True

            # 停止所有策略
            for strategy_id, strategy in self.strategies.items():
                await strategy.stop()
                logger.critical(f"Strategy {strategy_id} emergency stopped")

            # 平仓所有持仓（可选）
            positions = await self.position_manager.get_positions()
            close_orders = []

            for position in positions:
                if abs(position.size) > Decimal('0.001'):  # 忽略极小仓位
                    close_order = Order(
                        order_id=f"emergency_close_{position.symbol}_{datetime.utcnow().timestamp()}",
                        symbol=position.symbol,
                        exchange=position.exchange,
                        side=OrderSide.SELL if position.size > 0 else OrderSide.BUY,
                        size=abs(position.size),
                        price=Decimal('0'),  # 市价单
                        order_type="market",
                        strategy_id="emergency_close"
                    )
                    close_orders.append(close_order)

            # 提交平仓订单
            if close_orders:
                logger.critical(f"Submitting {len(close_orders)} emergency close orders")
                # 这里应该调用订单管理器提交订单

            return True

        except Exception as e:
            logger.critical(f"Emergency stop failed: {e}")
            return False

    async def get_coordination_status(self) -> Dict[str, Any]:
        """获取协调状态"""
        try:
            conflicts = await self.check_strategy_conflicts()

            return {
                'total_strategies': len(self.strategies),
                'active_strategies': sum(1 for s in self.strategies.values() if s.is_active),
                'capital_allocations': {k: float(v) for k, v in self.capital_allocations.items()},
                'conflicts': conflicts,
                'emergency_stopped': self._is_emergency_stopped,
                'last_rebalance': datetime.utcnow().isoformat()
            }

        except Exception as e:
            logger.error(f"Failed to get coordination status: {e}")
            return {'error': str(e)}
```

### 9. OptionLifecycleManager（期权生命周期管理器）

```python
class OptionLifecycleManager:
    """
    期权生命周期管理器 - 处理期权完整生命周期事件
    """

    def __init__(self):
        self.exercise_processor = ExercisePostProcessor()
        self.expiry_processor = ExpiryProcessor()
        self.assignment_processor = AssignmentProcessor()
        self.event_dispatcher = EventDispatcher()

        # 注入系统依赖
        self.strategy_coordinator = None
        self.risk_engine = None
        self.data_engine = None

    def inject_dependencies(self, components: Dict):
        """注入系统组件依赖"""
        self.strategy_coordinator = components.get('strategy_coordinator')
        self.risk_engine = components.get('risk_engine')
        self.data_engine = components.get('data_engine')
        self.exercise_processor.inject_dependencies(components)

    async def handle_lifecycle_event(self, event_type: str, event_data: Dict):
        """统一处理期权生命周期事件"""

        lifecycle_events = {
            'EXERCISE': self.handle_exercise_event,
            'EXPIRY': self.handle_expiry_event,
            'ASSIGNMENT': self.handle_assignment_event,
            'EARLY_EXERCISE': self.handle_early_exercise_event
        }

        handler = lifecycle_events.get(event_type)
        if handler:
            return await handler(event_data)
        else:
            raise ValueError(f"Unsupported lifecycle event: {event_type}")

    async def handle_exercise_event(self, event_data: Dict):
        """处理期权行权事件"""

        # Step 1: 事件验证和丰富
        validated_event = await self.validate_and_enrich_event(event_data)

        # Step 2: 风险预评估
        risk_assessment = await self.risk_engine.assess_exercise_impact(validated_event)

        if not risk_assessment.is_acceptable:
            return {
                'status': 'REJECTED',
                'reason': risk_assessment.rejection_reason,
                'risk_metrics': risk_assessment.metrics
            }

        # Step 3: 生成后续策略
        post_exercise_strategy = await self.exercise_processor.process_exercise(
            validated_event,
            await self.get_current_portfolio_state()
        )

        # Step 4: 策略协调
        coordination_result = await self.strategy_coordinator.coordinate_exercise_strategy(
            post_exercise_strategy
        )

        if coordination_result.approved:
            # Step 5: 执行策略
            execution_result = await self.execute_exercise_strategy(post_exercise_strategy)

            # Step 6: 更新状态和通知
            await self.update_system_state(execution_result)
            await self.notify_stakeholders(execution_result)

            return {
                'status': 'SUCCESS',
                'strategy': post_exercise_strategy,
                'execution_result': execution_result
            }
        else:
            return {
                'status': 'COORDINATION_FAILED',
                'reason': coordination_result.rejection_reason,
                'suggested_adjustments': coordination_result.adjustments
            }

    async def validate_and_enrich_event(self, event_data: Dict):
        """验证并丰富事件数据"""

        # 基础验证
        required_fields = ['option_type', 'strike', 'size', 'expiry', 'exercise_price']
        for field in required_fields:
            if field not in event_data:
                raise ValueError(f"Missing required field: {field}")

        # 数据丰富
        enriched_event = {
            **event_data,
            'timestamp': datetime.utcnow(),
            'market_price': await self.data_engine.get_current_btc_price(),
            'iv_environment': await self.data_engine.get_iv_analysis(),
            'portfolio_state': await self.get_current_portfolio_state()
        }

        return enriched_event
```

#### ExercisePostProcessor（期权行权后处理器）

```python
class ExercisePostProcessor:
    """
    期权行权后智能处理器 - 核心创新模块

    设计理念：将行权获得的现货智能转换为新的期权策略，
    保持资金效率和期权属性，避免退化为低效现货操作
    """

    def __init__(self):
        # 策略参数配置
        self.strategy_config = {
            'iv_thresholds': {
                'high': 0.6,        # 高IV阈值
                'normal': 0.4,      # 正常IV阈值
                'low': 0.2          # 低IV阈值
            },
            'profit_thresholds': {
                'high': 0.10,       # 高盈利阈值 (10%)
                'medium': 0.05,     # 中等盈利阈值 (5%)
                'low': 0.02         # 低盈利阈值 (2%)
            },
            'otm_ratios': {
                'aggressive': 0.20, # 激进OTM (20%)
                'standard': 0.15,   # 标准OTM (15%)
                'conservative': 0.08 # 保守OTM (8%)
            }
        }

        # 系统组件（通过依赖注入）
        self.market_analyzer = None
        self.iv_analyzer = None
        self.order_manager = None
        self.position_manager = None
        self.risk_engine = None

    def inject_dependencies(self, components: Dict):
        """注入系统组件依赖"""
        self.market_analyzer = components.get('market_analyzer')
        self.iv_analyzer = components.get('iv_analyzer')
        self.order_manager = components.get('order_manager')
        self.position_manager = components.get('position_manager')
        self.risk_engine = components.get('risk_engine')

    async def process_exercise(self, exercise_event: Dict, portfolio_state: Dict):
        """处理期权行权的主入口"""

        if exercise_event['option_type'] == 'PUT':
            return await self.handle_put_exercise(exercise_event, portfolio_state)
        elif exercise_event['option_type'] == 'CALL':
            return await self.handle_call_exercise(exercise_event, portfolio_state)
        else:
            raise ValueError(f"Unknown option type: {exercise_event['option_type']}")

    async def handle_put_exercise(self, exercise_event: Dict, portfolio_state: Dict):
        """Put被行权：智能现货转换策略"""

        # 基础数据提取
        spot_position = exercise_event['size']
        current_price = exercise_event['market_price']
        exercise_price = exercise_event['exercise_price']
        profit_ratio = (current_price - exercise_price) / exercise_price

        # 市场环境分析
        iv_analysis = await self.iv_analyzer.analyze_call_iv_environment(current_price)
        market_regime = await self.market_analyzer.determine_market_regime()

        # 策略决策树
        strategy = await self.determine_post_exercise_strategy(
            spot_position, current_price, profit_ratio, iv_analysis, market_regime
        )

        return strategy

    async def determine_post_exercise_strategy(self, spot_position, current_price, profit_ratio, iv_analysis, market_regime):
        """核心策略决策逻辑"""

        strategy = {
            'type': 'COVERED_CALL_COMPOSITE',
            'spot_size': spot_position,
            'actions': [],
            'risk_metrics': {},
            'expected_outcome': {}
        }

        # 决策逻辑：基于盈利情况和IV环境
        if profit_ratio > self.strategy_config['profit_thresholds']['high']:
            # 高盈利情况 (>10%)
            if iv_analysis.iv_rank > self.strategy_config['iv_thresholds']['high']:
                # 高盈利 + 高IV：激进策略
                strategy = await self.build_aggressive_covered_call_strategy(
                    spot_position, current_price, iv_analysis
                )
            else:
                # 高盈利 + 正常IV：标准策略
                strategy = await self.build_standard_covered_call_strategy(
                    spot_position, current_price, iv_analysis
                )

        elif profit_ratio > self.strategy_config['profit_thresholds']['medium']:
            # 中等盈利情况 (5-10%)
            strategy = await self.build_conservative_covered_call_strategy(
                spot_position, current_price, iv_analysis
            )

        else:
            # 低盈利或持平情况 (<5%)
            strategy = await self.build_holding_strategy(
                spot_position, current_price, iv_analysis
            )

        # 风险评估和调整
        risk_assessment = await self.risk_engine.assess_strategy_risk(strategy)
        if risk_assessment.requires_adjustment:
            strategy = await self.adjust_strategy_for_risk(strategy, risk_assessment)

        return strategy

    async def build_aggressive_covered_call_strategy(self, spot_position, current_price, iv_analysis):
        """构建激进Covered Call策略（高盈利 + 高IV）"""

        # 激进参数：深OTM + 较大仓位比例
        otm_ratio = self.strategy_config['otm_ratios']['aggressive']  # 20% OTM
        call_strike = current_price * (1 + otm_ratio)
        covered_call_ratio = 0.5  # 50%仓位做Covered Call

        strategy = {
            'type': 'AGGRESSIVE_COVERED_CALL',
            'spot_size': spot_position,
            'actions': [
                {
                    'action_type': 'SELL_CALL',
                    'strike': call_strike,
                    'size': spot_position * covered_call_ratio,
                    'expiry': await self.select_optimal_expiry(iv_analysis, 'short_term'),
                    'reasoning': f'高IV({iv_analysis.iv_rank:.2f})环境下卖深OTM Call获得丰厚时间价值'
                },
                {
                    'action_type': 'SETUP_MICRO_GRID',
                    'size': spot_position * (1 - covered_call_ratio),
                    'grid_range': 0.05,  # 5%网格范围
                    'reasoning': '剩余现货设置微网格策略，捕捉小幅波动'
                }
            ],
            'expected_outcome': {
                'max_profit_scenario': 'Call未被行权，获得全部权利金收益',
                'moderate_scenario': 'Call被行权，确认部分现货收益',
                'risk_scenario': '现货价格大幅上涨，错失部分收益'
            }
        }

        return strategy

    async def select_optimal_expiry(self, iv_analysis, term_preference):
        """选择最优到期日"""

        expiry_strategy = {
            'short_term': await self.get_weekly_expiry,      # 周度到期
            'medium_term': await self.get_bi_weekly_expiry,  # 双周到期
            'long_term': await self.get_monthly_expiry       # 月度到期
        }

        # 根据IV趋势调整期限偏好
        if iv_analysis.trend == 'RISING' and term_preference != 'short_term':
            # IV上升期偏向短期限
            return await expiry_strategy['short_term']()
        elif iv_analysis.trend == 'FALLING' and term_preference != 'long_term':
            # IV下降期偏向长期限
            return await expiry_strategy['long_term']()
        else:
            return await expiry_strategy[term_preference]()
```

**关键特性**：
- **事件驱动架构**：基于期权生命周期事件的处理机制
- **智能策略转换**：IV敏感的策略选择和参数优化
- **风险感知协调**：与现有策略的风险协调和冲突解决
- **模块化设计**：可扩展的处理器架构，便于添加新的生命周期事件
- **依赖注入模式**：松耦合的组件关系，便于测试和维护

### 系统集成点

```python
# 在主系统启动时的组件装配
async def initialize_system():
    """系统初始化时的组件装配"""

    # 创建核心组件
    lifecycle_manager = OptionLifecycleManager()
    strategy_coordinator = StrategyCoordinator()

    # 组件依赖注入
    system_components = {
        'strategy_coordinator': strategy_coordinator,
        'risk_engine': risk_engine,
        'data_engine': data_engine,
        'market_analyzer': market_analyzer,
        'iv_analyzer': iv_analyzer,
        'order_manager': order_manager,
        'position_manager': position_manager
    }

    lifecycle_manager.inject_dependencies(system_components)
    strategy_coordinator.set_lifecycle_manager(lifecycle_manager)

    # 注册事件监听器
    expiry_manager.register_event_handler('OPTION_EXERCISED', lifecycle_manager.handle_lifecycle_event)
```

## 📊 数据流设计

### 实时数据处理流程

```mermaid
sequenceDiagram
    participant B as Binance API
    participant D as Deribit API
    participant G as DataEngine
    participant C as CausalEngine
    participant S as StrategyEngine
    participant R as RiskEngine
    participant E as ExecutionEngine

    B->>G: Market Data Stream
    D->>G: Option Chain Data
    G->>G: Data Validation & Sync
    G->>C: Processed Data
    C->>C: Causal Analysis
    C->>S: Signal Events
    S->>S: Strategy Logic
    S->>R: Risk Check
    R->>R: Risk Assessment
    R->>E: Approved Actions
    E->>D: Execute Orders
```

### 缓存策略设计

```python
CACHE_STRATEGY = {
    # 实时市场数据
    'market_data': {
        'type': 'hash',
        'ttl': 300,  # 5分钟，保持不变
        'compression': True,
        'storage_tier': 'warm'  # Redis温数据
    },

    # 期权链数据
    'option_chain': {
        'type': 'sorted_set',
        'ttl': 240,  # 4分钟，从300s优化
        'index': ['expiry', 'strike', 'type'],
        'storage_tier': 'warm'
    },

    # Greeks数据 - 直接来自Deribit WebSocket
    'greeks': {
        'type': 'hash',
        'ttl': 120,  # 2分钟，从300s优化
        'fields': ['delta', 'gamma', 'theta', 'vega', 'rho'],
        'source': 'deribit_ticker',  # 数据来源：Deribit ticker订阅
        'storage_tier': 'warm'
    },

    # 仓位数据
    'positions': {
        'type': 'json',
        'ttl': 60,  # 1分钟，保持不变
        'real_time_update': True,
        'storage_tier': 'hot'  # 内存热数据
    },

    # 因果信号
    'causal_signals': {
        'type': 'json',
        'ttl': 300,
        'history_retention': 86400,  # 24小时
        'storage_tier': 'warm'
    },

    # 微观结构信号 - 新增
    'microstructure': {
        'type': 'json',
        'ttl': 30,  # 30秒，高频更新
        'storage_tier': 'hot',  # 内存热数据
        'fields': ['vwap_lwap_spread', 'depth_1pct', 'turnover_rate', 'taker_pressure']
    },

    # 历史数据归档策略
    'historical_data': {
        'storage_tier': 'cold',  # TimescaleDB冷数据
        'retention_policy': '30days',
        'compression': True
    }
}
```

## 🔐 安全设计

### API密钥管理
```python
class SecureCredentialManager:
    """安全凭证管理"""

    def __init__(self):
        self.encryption_key = self.load_master_key()
        self.credentials = {}

    def store_credential(self, exchange: str, api_key: str, secret: str):
        """加密存储API凭证"""
        encrypted_key = self.encrypt(api_key)
        encrypted_secret = self.encrypt(secret)

        self.credentials[exchange] = {
            'api_key': encrypted_key,
            'secret': encrypted_secret,
            'created_at': datetime.utcnow()
        }

    def get_credential(self, exchange: str) -> Dict:
        """获取解密的凭证"""
        if exchange not in self.credentials:
            raise CredentialNotFoundError(f"No credentials for {exchange}")

        encrypted_cred = self.credentials[exchange]
        return {
            'api_key': self.decrypt(encrypted_cred['api_key']),
            'secret': self.decrypt(encrypted_cred['secret'])
        }
```

### 交易安全控制
```python
class TradingSecurityController:
    """交易安全控制"""

    def __init__(self):
        self.daily_loss_limit = 0.05  # 5%
        self.single_trade_limit = 0.02  # 2%
        self.ip_whitelist = set()

    async def validate_trade(self, trade_request: TradeRequest) -> bool:
        """交易请求安全验证"""

        # 检查交易限额
        if not await self.check_trade_limits(trade_request):
            return False

        # 检查IP白名单
        if not self.check_ip_whitelist(trade_request.source_ip):
            return False

        # 检查异常模式
        if await self.detect_anomaly(trade_request):
            return False

        return True
```

## 📈 性能优化设计

### 异步处理架构
```python
class AsyncTaskManager:
    """异步任务管理器"""

    def __init__(self):
        self.task_queue = asyncio.Queue()
        self.workers = []
        self.max_workers = 10

    async def start(self):
        """启动工作协程池"""
        for i in range(self.max_workers):
            worker = asyncio.create_task(self.worker())
            self.workers.append(worker)

    async def worker(self):
        """工作协程"""
        while True:
            task = await self.task_queue.get()
            try:
                await task.execute()
            except Exception as e:
                await self.handle_error(task, e)
            finally:
                self.task_queue.task_done()
```

### 内存优化策略
```python
class MemoryOptimizer:
    """内存优化管理"""

    def __init__(self):
        self.max_memory_usage = 3 * 1024 * 1024 * 1024  # 3GB (考虑双WebSocket数据流和实时计算负载)
        self.cleanup_threshold = 0.8

    async def monitor_memory(self):
        """内存使用监控"""
        while True:
            usage = psutil.virtual_memory().percent
            if usage > self.cleanup_threshold:
                await self.cleanup_memory()
            await asyncio.sleep(60)

    async def cleanup_memory(self):
        """内存清理"""
        # 清理过期缓存
        await self.cache_manager.cleanup_expired()

        # 清理历史数据
        await self.data_engine.cleanup_old_data()

        # 触发垃圾回收
        gc.collect()
```

## 📦 依赖管理

### Python依赖包 (requirements.txt)
```txt
# 异步框架和Web服务
asyncio
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# 数据处理和计算
numpy==1.24.3
pandas==2.0.3
aiohttp==3.8.5

# 数据存储和缓存
redis==5.0.1
asyncpg==0.29.0
sqlalchemy==2.0.23
timescaledb==0.0.13

# 分布式任务队列
celery[redis]==5.3.4
kombu==5.3.4

# 交易所API连接
websocket-client==1.6.4
ccxt==4.1.25

# 监控和日志
prometheus-client==0.19.0
loguru==0.7.2
psutil==5.9.6

# Telegram Bot
python-telegram-bot==20.7
telegram==0.0.1

# 配置管理
pydantic==2.5.0
python-dotenv==1.0.0
PyYAML==6.0.1

# 测试框架
pytest==7.4.3
pytest-asyncio==0.21.1
```

## 🚀 部署架构

### Docker容器化部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖（优化架构，保留实时计算能力）
RUN apt-get update && apt-get install -y \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/

# 设置环境变量
ENV PYTHONPATH=/app/src
ENV LOG_LEVEL=INFO

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "src/main.py"]
```

### Kubernetes部署配置
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: btc-option-grid-bot
spec:
  replicas: 2
  selector:
    matchLabels:
      app: btc-option-grid-bot
  template:
    metadata:
      labels:
        app: btc-option-grid-bot
    spec:
      containers:
      - name: trading-engine
        image: btc-option-grid-bot:latest
        resources:
          requests:
            memory: "2Gi"  # 双WebSocket数据流 + 实时计算负载
            cpu: "800m"    # 微观结构信号、因果分析等实时计算
          limits:
            memory: "3Gi"  # 预留缓冲空间处理数据高峰
            cpu: "1500m"   # 保证计算性能
        env:
        - name: REDIS_URL
          value: "redis://redis-cluster:6379"
        - name: POSTGRES_URL
          value: "************************************/trading"
```

## 📊 监控与告警

### Prometheus指标收集
```python
# 业务指标定义
METRICS = {
    'trading_pnl_total': Counter('trading_pnl_total', 'Total P&L'),
    'orders_executed_total': Counter('orders_executed_total', 'Orders executed'),
    'risk_alerts_total': Counter('risk_alerts_total', 'Risk alerts', ['severity']),
    'data_latency_seconds': Histogram('data_latency_seconds', 'Data processing latency'),
    'greeks_risk_gauge': Gauge('greeks_risk_gauge', 'Greeks risk level', ['type'])
}
```

### 告警规则配置
```yaml
groups:
- name: trading_alerts
  rules:
  - alert: HighDataLatency
    expr: data_latency_seconds > 5
    for: 1m
    annotations:
      summary: "Data processing latency too high"

  - alert: CriticalRiskLevel
    expr: greeks_risk_gauge{type="gamma"} > 0.05
    for: 30s
    annotations:
      summary: "Critical Gamma risk detected"

  - alert: TradingSystemDown
    expr: up{job="btc-option-grid-bot"} == 0
    for: 30s
    annotations:
      summary: "Trading system is down"
```

## 🔧 接口设计

### REST API接口
```python
@app.route('/api/v1/strategy/status', methods=['GET'])
async def get_strategy_status():
    """获取策略状态"""
    return {
        'main_strategy': await main_strategy.get_status(),
        'branch_strategy': await branch_strategy.get_status(),
        'risk_metrics': await risk_engine.get_metrics(),
        'positions': await position_manager.get_summary()
    }

@app.route('/api/v1/risk/override', methods=['POST'])
async def override_risk_settings():
    """临时调整风险参数"""
    data = await request.get_json()
    return await risk_engine.update_settings(data)
```

### Telegram Bot接口
```python
class TelegramBotHandler:
    """Telegram Bot查询和通知服务"""

    def __init__(self, bot_token: str, authorized_users: List[int]):
        self.bot = telegram.Bot(token=bot_token)
        self.authorized_users = set(authorized_users)
        self.application = Application.builder().token(bot_token).build()
        self.setup_handlers()

    def setup_handlers(self):
        """设置命令处理器"""
        # 查询命令
        self.application.add_handler(CommandHandler("status", self.handle_status))
        self.application.add_handler(CommandHandler("positions", self.handle_positions))
        self.application.add_handler(CommandHandler("risk", self.handle_risk))
        self.application.add_handler(CommandHandler("pnl", self.handle_pnl))
        self.application.add_handler(CommandHandler("signals", self.handle_signals))

        # 控制命令
        self.application.add_handler(CommandHandler("pause", self.handle_pause))
        self.application.add_handler(CommandHandler("resume", self.handle_resume))
        self.application.add_handler(CommandHandler("emergency", self.handle_emergency_stop))

        # 设置命令
        self.application.add_handler(CommandHandler("setrisk", self.handle_set_risk))
        self.application.add_handler(CommandHandler("help", self.handle_help))

    async def handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询系统状态"""
        if not self.is_authorized(update.effective_user.id):
            await update.message.reply_text("⚠️ 未授权访问")
            return

        try:
            status_data = await self.get_system_status()
            message = self.format_status_message(status_data)
            await update.message.reply_text(message, parse_mode='Markdown')
        except Exception as e:
            await update.message.reply_text(f"❌ 状态查询失败: {str(e)}")

    async def handle_positions(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询持仓信息"""
        if not self.is_authorized(update.effective_user.id):
            return

        positions = await self.position_manager.get_summary()
        message = self.format_positions_message(positions)
        await update.message.reply_text(message, parse_mode='Markdown')

    async def handle_risk(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询风险指标"""
        if not self.is_authorized(update.effective_user.id):
            return

        risk_metrics = await self.risk_engine.get_metrics()
        message = self.format_risk_message(risk_metrics)
        await update.message.reply_text(message, parse_mode='Markdown')

    async def handle_signals(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """查询交易信号"""
        if not self.is_authorized(update.effective_user.id):
            return

        signals = await self.causal_engine.get_current_signals()
        microstructure = await self.microstructure_signals.get_current_signals()
        message = self.format_signals_message(signals, microstructure)
        await update.message.reply_text(message, parse_mode='Markdown')

    async def send_alert(self, alert_type: str, message: str, severity: str = "INFO"):
        """发送告警通知"""
        severity_icons = {
            "CRITICAL": "🚨",
            "HIGH": "⚠️",
            "MEDIUM": "⚡",
            "LOW": "ℹ️",
            "INFO": "📊"
        }

        icon = severity_icons.get(severity, "📊")
        timestamp = datetime.now().strftime("%H:%M:%S")

        formatted_message = f"{icon} *{alert_type}* ({timestamp})\n\n{message}"

        for user_id in self.authorized_users:
            try:
                await self.bot.send_message(
                    chat_id=user_id,
                    text=formatted_message,
                    parse_mode='Markdown'
                )
            except Exception as e:
                logger.error(f"Failed to send alert to {user_id}: {e}")

    def format_status_message(self, status_data):
        """格式化系统状态消息"""
        return f"""
📊 *系统状态概览*

🔄 *运行状态*: {status_data['system_status']}
⏰ *运行时间*: {status_data['uptime']}
💾 *内存使用*: {status_data['memory_usage']:.1f}%
⚡ *CPU使用*: {status_data['cpu_usage']:.1f}%

📈 *策略状态*:
• 主策略: {status_data['main_strategy_status']}
• 分支策略: {status_data['branch_strategy_status']}

🎯 *今日PnL*: {status_data['daily_pnl']:+.2f} USDT
💰 *总PnL*: {status_data['total_pnl']:+.2f} USDT

⚡ *活跃信号*: {len(status_data['active_signals'])}
⚠️ *风险告警*: {len(status_data['active_alerts'])}
"""

    def format_positions_message(self, positions):
        """格式化持仓信息"""
        message = "📋 *持仓概览*\n\n"

        if positions['spot_positions']:
            message += "💎 *现货持仓*:\n"
            for symbol, pos in positions['spot_positions'].items():
                message += f"• {symbol}: {pos['quantity']:+.4f} (${pos['value']:.2f})\n"
            message += "\n"

        if positions['option_positions']:
            message += "📊 *期权持仓*:\n"
            for pos in positions['option_positions']:
                message += f"• {pos['symbol']} {pos['type']} ${pos['strike']}\n"
                message += f"  数量: {pos['quantity']:+.0f} | PnL: {pos['pnl']:+.2f}\n"
            message += "\n"

        message += f"💰 *组合价值*: ${positions['total_value']:.2f}\n"
        message += f"📊 *保证金占用*: ${positions['margin_used']:.2f}\n"
        message += f"🛡️ *可用保证金*: ${positions['margin_available']:.2f}"

        return message
```

### WebSocket推送接口
```python
class WebSocketHandler:
    """WebSocket实时数据推送"""

    def __init__(self):
        self.connected_clients = set()
        self.client_subscriptions = {}
        self.heartbeat_interval = 30  # 30秒心跳
        self.max_message_size = 1024 * 1024  # 1MB最大消息

    async def handle_connection(self, websocket, path):
        """处理WebSocket连接"""
        client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"

        try:
            # 添加到连接池
            self.connected_clients.add(websocket)
            self.client_subscriptions[client_id] = set()

            # 发送连接确认
            await websocket.send(json.dumps({
                'type': 'connection_established',
                'client_id': client_id,
                'timestamp': datetime.utcnow().isoformat(),
                'server_time': time.time()
            }))

            # 启动心跳任务
            heartbeat_task = asyncio.create_task(self.heartbeat_loop(websocket, client_id))

            # 处理消息
            async for message in websocket:
                try:
                    await self.handle_message(websocket, client_id, message)
                except json.JSONDecodeError:
                    await websocket.send(json.dumps({
                        'type': 'error',
                        'message': 'Invalid JSON format'
                    }))
                except Exception as e:
                    logger.error(f"Message handling error for {client_id}: {e}")

        except websockets.exceptions.ConnectionClosed:
            logger.info(f"Client {client_id} disconnected")
        except Exception as e:
            logger.error(f"WebSocket error for {client_id}: {e}")
        finally:
            # 清理连接
            heartbeat_task.cancel()
            self.connected_clients.discard(websocket)
            self.client_subscriptions.pop(client_id, None)

    async def handle_message(self, websocket, client_id: str, message: str):
        """处理客户端消息"""
        if len(message) > self.max_message_size:
            await websocket.send(json.dumps({
                'type': 'error',
                'message': 'Message too large'
            }))
            return

        data = json.loads(message)
        msg_type = data.get('type')

        if msg_type == 'subscribe':
            # 订阅特定数据流
            channels = data.get('channels', [])
            self.client_subscriptions[client_id].update(channels)
            await websocket.send(json.dumps({
                'type': 'subscription_confirmed',
                'channels': list(self.client_subscriptions[client_id])
            }))

        elif msg_type == 'unsubscribe':
            # 取消订阅
            channels = data.get('channels', [])
            self.client_subscriptions[client_id].difference_update(channels)
            await websocket.send(json.dumps({
                'type': 'unsubscription_confirmed',
                'channels': list(self.client_subscriptions[client_id])
            }))

        elif msg_type == 'ping':
            # 心跳响应
            await websocket.send(json.dumps({
                'type': 'pong',
                'timestamp': datetime.utcnow().isoformat()
            }))

    async def heartbeat_loop(self, websocket, client_id: str):
        """心跳循环"""
        try:
            while True:
                await asyncio.sleep(self.heartbeat_interval)
                await websocket.send(json.dumps({
                    'type': 'heartbeat',
                    'timestamp': datetime.utcnow().isoformat()
                }))
        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Heartbeat error for {client_id}: {e}")

    async def broadcast_update(self, update_type: str, data: Dict, channel: str = None):
        """广播更新消息"""
        message = {
            'type': update_type,
            'data': data,
            'channel': channel,
            'timestamp': datetime.utcnow().isoformat(),
            'server_time': time.time()
        }

        message_json = json.dumps(message)
        disconnected_clients = set()

        # 发送给订阅了该频道的客户端
        for websocket in self.connected_clients.copy():
            try:
                client_id = f"{websocket.remote_address[0]}:{websocket.remote_address[1]}"

                # 检查客户端是否订阅了该频道
                if channel and channel not in self.client_subscriptions.get(client_id, set()):
                    continue

                await websocket.send(message_json)

            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.add(websocket)
            except Exception as e:
                logger.error(f"Broadcast error: {e}")
                disconnected_clients.add(websocket)

        # 清理断开的连接
        for websocket in disconnected_clients:
            self.connected_clients.discard(websocket)
```

### 10. LiquidityAnalyzer（流动性分析器）

```python
class LiquidityAnalyzer:
    """
    流动性分析器 - 实时评估期权流动性状况
    """

    def __init__(self):
        self.spread_monitor = BidAskSpreadMonitor()
        self.volume_analyzer = VolumeAnalyzer()
        self.depth_analyzer = MarketDepthAnalyzer()
        self.liquidity_scorer = LiquidityScorer()

    async def analyze_liquidity(self, option_data: Dict) -> LiquidityMetrics:
        """综合流动性分析"""

        # 1. Bid-Ask价差分析
        spread_metrics = await self.spread_monitor.analyze_spread(option_data)

        # 2. 成交量分析
        volume_metrics = await self.volume_analyzer.analyze_volume(option_data)

        # 3. 市场深度分析
        depth_metrics = await self.depth_analyzer.analyze_depth(option_data)

        # 4. 综合流动性评分
        liquidity_score = await self.liquidity_scorer.calculate_score(
            spread_metrics, volume_metrics, depth_metrics
        )

        return LiquidityMetrics(
            spread=spread_metrics,
            volume=volume_metrics,
            depth=depth_metrics,
            overall_score=liquidity_score,
            risk_level=self._determine_risk_level(liquidity_score)
        )

    def _determine_risk_level(self, score: float) -> str:
        """确定流动性风险等级"""
        if score < 20:
            return 'CRITICAL'
        elif score < 40:
            return 'HIGH'
        elif score < 60:
            return 'MEDIUM'
        else:
            return 'LOW'
```

**关键特性**：
- **实时监控**：Bid-Ask价差、成交量、市场深度的实时分析
- **综合评分**：0-100分的流动性评分系统
- **风险预警**：多级别流动性风险告警
- **自动控制**：流动性不足时自动暂停交易或减少仓位

### 11. BacktestEngine（回测引擎）

```python
class BacktestEngine:
    """
    回测引擎主控制器
    """

    def __init__(self):
        self.data_provider = HistoricalDataProvider()
        self.strategy_runner = StrategyRunner()
        self.portfolio_tracker = PortfolioTracker()
        self.performance_analyzer = PerformanceAnalyzer()
        self.risk_analyzer = RiskAnalyzer()
        self.report_generator = ReportGenerator()

    async def run_backtest(self, config: BacktestConfig) -> BacktestResult:
        """执行回测"""

        # 1. 初始化回测环境
        await self.initialize_backtest(config)

        # 2. 数据回放
        async for market_data in self.data_provider.replay_data(
            config.start_date, config.end_date, config.symbols
        ):
            # 3. 策略执行
            actions = await self.strategy_runner.process_market_data(market_data)

            # 4. 组合更新
            await self.portfolio_tracker.process_actions(actions, market_data)

            # 5. 风险监控
            await self.risk_analyzer.update_risk_metrics(
                self.portfolio_tracker.get_current_portfolio()
            )

        # 6. 性能分析
        performance_metrics = await self.performance_analyzer.calculate_metrics(
            self.portfolio_tracker.get_trade_history()
        )

        # 7. 生成报告
        report = await self.report_generator.generate_report(
            performance_metrics, self.risk_analyzer.get_risk_history()
        )

        return BacktestResult(
            performance=performance_metrics,
            risk_metrics=self.risk_analyzer.get_summary(),
            trades=self.portfolio_tracker.get_trade_history(),
            report=report
        )
```

**关键特性**：
- **高精度回放**：秒级历史数据回放
- **模拟交易所**：支持无滑点和真实滑点模式
- **性能分析**：完整的性能指标计算
- **参数优化**：支持策略参数敏感性分析

### 12. MonitoringDashboard（监控面板）

```python
class MonitoringAPI:
    """
    监控面板API服务
    """

    def __init__(self):
        self.app = FastAPI(title="BTC期权交易监控API")
        self.websocket_manager = WebSocketManager()
        self.data_aggregator = DataAggregator()

    def setup_routes(self):
        """设置API路由"""

        @self.app.get("/api/system/status")
        async def get_system_status():
            """获取系统状态"""
            return await self.data_aggregator.get_system_status()

        @self.app.get("/api/positions/summary")
        async def get_positions_summary():
            """获取持仓概览"""
            return await self.position_manager.get_positions_summary()

        @self.app.websocket("/ws/realtime")
        async def websocket_endpoint(websocket: WebSocket):
            """实时数据WebSocket连接"""
            await self.websocket_manager.connect(websocket)
```

**前端组件设计**：
```typescript
// 主仪表板组件
const Dashboard: React.FC = () => {
  const [realTimeData, setRealTimeData] = useState<RealTimeData>();

  // WebSocket连接
  useEffect(() => {
    const ws = new WebSocket('ws://localhost:8000/ws/realtime');

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setRealTimeData(data);
    };

    return () => ws.close();
  }, []);

  return (
    <Layout className="dashboard-layout">
      <Header className="dashboard-header">
        <SystemStatusIndicator />
        <UserControls />
      </Header>

      <Content className="dashboard-content">
        <Row gutter={[16, 16]}>
          <Col span={24}>
            <SystemOverviewPanel data={realTimeData?.system} />
          </Col>
          <Col span={12}>
            <PositionsPanel data={realTimeData?.positions} />
          </Col>
          <Col span={12}>
            <RiskPanel data={realTimeData?.risk} />
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};
```

**关键特性**：
- **实时更新**：WebSocket实时数据推送
- **响应式设计**：支持桌面和移动端
- **交互控制**：策略启停、参数调整
- **可视化图表**：PnL曲线、风险指标展示

### 13. DatabaseManager（数据库管理器）

```python
class DatabaseManager:
    """
    数据库管理器 - 统一数据访问接口
    """

    def __init__(self):
        self.pool = None
        self.connection_string = self._build_connection_string()

    async def initialize(self):
        """初始化连接池"""
        self.pool = await asyncpg.create_pool(
            self.connection_string,
            min_size=10,
            max_size=50,
            command_timeout=60
        )

    async def insert_market_data(self, data: List[Dict]):
        """批量插入市场数据"""
        query = """
        INSERT INTO market_data (
            timestamp, symbol, exchange, price, volume,
            taker_buy_ratio, funding_rate, open_interest
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (timestamp, symbol, exchange) DO UPDATE SET
            price = EXCLUDED.price,
            volume = EXCLUDED.volume,
            updated_at = NOW()
        """

        async with self.pool.acquire() as conn:
            await conn.executemany(query, [
                (
                    item['timestamp'], item['symbol'], item['exchange'],
                    item['price'], item['volume'], item.get('taker_buy_ratio'),
                    item.get('funding_rate'), item.get('open_interest')
                )
                for item in data
            ])
```

**数据库设计要点**：
- **时序优化**：TimescaleDB超表，按天自动分区
- **索引策略**：针对查询模式优化的复合索引
- **数据压缩**：7天后自动压缩，90天后归档
- **性能监控**：慢查询检测和性能优化

## 📊 系统集成架构

### 组件交互图

```mermaid
graph LR
    A[LiquidityAnalyzer] --> B[RiskEngine]
    C[BacktestEngine] --> D[StrategyCoordinator]
    E[MonitoringDashboard] --> F[WebSocketManager]
    G[DatabaseManager] --> H[DataEngine]

    B --> I[AlertManager]
    D --> J[OrderManager]
    F --> K[TelegramBotHandler]
    H --> L[CacheManager]
```

### 数据流集成

1. **实时数据流**：市场数据 → 流动性分析 → 风险评估 → 交易决策
2. **历史数据流**：数据库 → 回测引擎 → 策略验证 → 参数优化
3. **监控数据流**：系统状态 → 监控面板 → 实时展示 → 用户交互

## 🚨 错误处理和异常管理

### 异常分类体系

```python
class TradingSystemException(Exception):
    """交易系统基础异常类"""

    def __init__(self, message: str, error_code: str = None, context: Dict = None):
        super().__init__(message)
        self.error_code = error_code
        self.context = context or {}
        self.timestamp = datetime.utcnow()

class DataException(TradingSystemException):
    """数据相关异常"""
    pass

class ConnectivityException(DataException):
    """连接异常"""
    pass

class DataQualityException(DataException):
    """数据质量异常"""
    pass

class StrategyException(TradingSystemException):
    """策略相关异常"""
    pass

class RiskException(TradingSystemException):
    """风险管理异常"""
    pass

class ExecutionException(TradingSystemException):
    """执行相关异常"""
    pass

class InsufficientMarginException(RiskException):
    """保证金不足异常"""
    pass

class LiquidityException(ExecutionException):
    """流动性异常"""
    pass
```

### 全局异常处理器

```python
class GlobalExceptionHandler:
    """全局异常处理器"""

    def __init__(self):
        self.alert_manager = AlertManager()
        self.logger = AsyncLogger()
        self.recovery_manager = RecoveryManager()

    async def handle_exception(self, exception: Exception, context: Dict = None):
        """统一异常处理"""

        # 记录异常
        await self.logger.error(
            f"Exception occurred: {type(exception).__name__}",
            extra={
                'exception': str(exception),
                'context': context,
                'traceback': traceback.format_exc()
            }
        )

        # 根据异常类型采取不同处理策略
        if isinstance(exception, ConnectivityException):
            await self.handle_connectivity_exception(exception, context)
        elif isinstance(exception, DataQualityException):
            await self.handle_data_quality_exception(exception, context)
        elif isinstance(exception, InsufficientMarginException):
            await self.handle_margin_exception(exception, context)
        elif isinstance(exception, LiquidityException):
            await self.handle_liquidity_exception(exception, context)
        else:
            await self.handle_generic_exception(exception, context)

    async def handle_connectivity_exception(self, exception: ConnectivityException, context: Dict):
        """处理连接异常"""
        # 1. 发送告警
        await self.alert_manager.send_alert(
            "CONNECTIVITY_ERROR",
            f"Connection lost: {exception}",
            severity="HIGH"
        )

        # 2. 启动重连机制
        await self.recovery_manager.initiate_reconnection(context.get('source'))

        # 3. 切换到降级模式
        await self.recovery_manager.enable_degraded_mode()

    async def handle_data_quality_exception(self, exception: DataQualityException, context: Dict):
        """处理数据质量异常"""
        # 1. 标记数据源为不可用
        await self.recovery_manager.mark_data_source_unavailable(
            context.get('data_source')
        )

        # 2. 切换到备用数据源
        await self.recovery_manager.switch_to_backup_data_source()

        # 3. 发送数据质量告警
        await self.alert_manager.send_alert(
            "DATA_QUALITY_ERROR",
            f"Data quality issue: {exception}",
            severity="MEDIUM"
        )

    async def handle_margin_exception(self, exception: InsufficientMarginException, context: Dict):
        """处理保证金不足异常"""
        # 1. 立即停止新开仓
        await self.recovery_manager.halt_new_positions()

        # 2. 评估是否需要强制平仓
        margin_status = await self.recovery_manager.assess_margin_status()
        if margin_status.critical:
            await self.recovery_manager.force_liquidation()

        # 3. 发送紧急告警
        await self.alert_manager.send_alert(
            "MARGIN_INSUFFICIENT",
            f"Insufficient margin: {exception}",
            severity="CRITICAL"
        )
```

### 恢复管理器

```python
class RecoveryManager:
    """系统恢复管理器"""

    def __init__(self):
        self.recovery_strategies = {
            'connectivity': ConnectivityRecovery(),
            'data_quality': DataQualityRecovery(),
            'margin': MarginRecovery(),
            'liquidity': LiquidityRecovery()
        }

    async def initiate_recovery(self, recovery_type: str, context: Dict):
        """启动恢复流程"""
        strategy = self.recovery_strategies.get(recovery_type)
        if strategy:
            return await strategy.execute_recovery(context)

    async def health_check(self) -> Dict:
        """系统健康检查"""
        health_status = {
            'overall': 'healthy',
            'components': {},
            'timestamp': datetime.utcnow().isoformat()
        }

        # 检查各个组件
        components = [
            'data_engine', 'strategy_engine', 'risk_engine',
            'execution_engine', 'cache_manager', 'database'
        ]

        for component in components:
            try:
                status = await self.check_component_health(component)
                health_status['components'][component] = status

                if status['status'] != 'healthy':
                    health_status['overall'] = 'degraded'

            except Exception as e:
                health_status['components'][component] = {
                    'status': 'error',
                    'error': str(e)
                }
                health_status['overall'] = 'error'

        return health_status
```

## 🔄 断路器模式实现

```python
class CircuitBreaker:
    """断路器模式实现"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

    async def call(self, func, *args, **kwargs):
        """通过断路器调用函数"""

        if self.state == 'OPEN':
            if self._should_attempt_reset():
                self.state = 'HALF_OPEN'
            else:
                raise CircuitBreakerOpenException("Circuit breaker is OPEN")

        try:
            result = await func(*args, **kwargs)
            await self._on_success()
            return result

        except Exception as e:
            await self._on_failure()
            raise e

    async def _on_success(self):
        """成功时的处理"""
        self.failure_count = 0
        self.state = 'CLOSED'

    async def _on_failure(self):
        """失败时的处理"""
        self.failure_count += 1
        self.last_failure_time = time.time()

        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'

    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        return (
            self.last_failure_time and
            time.time() - self.last_failure_time >= self.recovery_timeout
        )
```

## 📝 总结

本设计文档提供了BTC智能期权网格策略交易架构的完整技术设计方案，主要特点：

1. **分层架构**：6层清晰分离，职责明确，便于维护和扩展
2. **实时处理**：基于事件驱动的实时数据处理和风险控制
3. **高性能**：异步处理、内存优化、缓存策略等性能优化措施
4. **安全可靠**：多层次安全控制、容错机制、监控告警
5. **可扩展性**：插件化架构、容器化部署、微服务架构
6. **完整工具链**：流动性分析、回测验证、监控面板、数据管理
7. **健壮性**：完整的异常处理体系、断路器模式、自动恢复机制

系统将期权交易策略与现代软件架构最佳实践相结合，通过创新的期权网格策略、跨交易所因果分析和微观结构信号，实现比传统方法更高的资金效率和更精准的交易决策，为高频金融交易提供稳定可靠的技术支撑。
## 📊 系统配置
管理

### 配置接口定义

```python
class ConfigManagerInterface(ABC):
    """配置管理器接口"""

    @abstractmethod
    async def load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置"""
        pass

    @abstractmethod
    async def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        pass

    @abstractmethod
    async def set_config(self, key: str, value: Any) -> bool:
        """设置配置项"""
        pass

    @abstractmethod
    async def reload_config(self) -> bool:
        """重新加载配置"""
        pass

class ConfigManager(ConfigManagerInterface):
    """配置管理器实现"""

    def __init__(self, config_path: str = "config.yaml"):
        self.config_path = config_path
        self.config_data: Dict[str, Any] = {}
        self.watchers: List[Callable] = []
        self._file_watcher = None

    async def load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载配置"""
        try:
            path = config_path or self.config_path

            # 验证配置文件存在
            if not os.path.exists(path):
                raise ConfigurationException(f"Config file not found: {path}")

            # 加载YAML配置
            import yaml
            with open(path, 'r', encoding='utf-8') as f:
                raw_config = yaml.safe_load(f)

            # 环境变量替换
            self.config_data = self._substitute_env_vars(raw_config)

            # 验证配置
            await self._validate_config(self.config_data)

            logger.info(f"Configuration loaded from {path}")
            return self.config_data

        except Exception as e:
            raise ConfigurationException(f"Failed to load config: {str(e)}")

    async def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        try:
            keys = key.split('.')
            value = self.config_data

            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default

            return value

        except Exception as e:
            logger.error(f"Error getting config {key}: {e}")
            return default

    async def set_config(self, key: str, value: Any) -> bool:
        """设置配置项"""
        try:
            keys = key.split('.')
            config = self.config_data

            # 导航到父级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]

            # 设置值
            config[keys[-1]] = value

            logger.info(f"Config updated: {key} = {value}")
            return True

        except Exception as e:
            logger.error(f"Error setting config {key}: {e}")
            return False

    async def reload_config(self) -> bool:
        """重新加载配置"""
        try:
            await self.load_config()
            logger.info("Configuration reloaded")
            return True
        except Exception as e:
            logger.error(f"Config reload failed: {e}")
            return False

    def watch_config_changes(self, callback: Callable) -> str:
        """监听配置变化"""
        watcher_id = f"watcher_{len(self.watchers)}_{datetime.utcnow().timestamp()}"
        self.watchers.append({
            'id': watcher_id,
            'callback': callback
        })
        return watcher_id

    def _substitute_env_vars(self, config_data: Dict) -> Dict:
        """替换环境变量"""
        import os
        import re

        def substitute_value(value):
            if isinstance(value, str):
                # 替换 ${VAR_NAME} 格式的环境变量
                pattern = r'\$\{([^}]+)\}'
                matches = re.findall(pattern, value)
                for match in matches:
                    env_value = os.getenv(match, '')
                    value = value.replace(f'${{{match}}}', env_value)
                return value
            elif isinstance(value, dict):
                return {k: substitute_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [substitute_value(item) for item in value]
            else:
                return value

        return substitute_value(config_data)

    async def _validate_config(self, config: Dict[str, Any]):
        """验证配置"""
        required_sections = ['exchanges', 'redis', 'database', 'strategies', 'risk_limits']

        for section in required_sections:
            if section not in config:
                raise ConfigurationException(f"Missing required config section: {section}")

        # 验证交易所配置
        for exchange_name, exchange_config in config.get('exchanges', {}).items():
            if not exchange_config.get('api_key'):
                raise ConfigurationException(f"Missing API key for {exchange_name}")
            if not exchange_config.get('api_secret'):
                raise ConfigurationException(f"Missing API secret for {exchange_name}")

        # 验证策略配置
        total_allocation = Decimal('0')
        for strategy_name, strategy_config in config.get('strategies', {}).items():
            allocation = Decimal(str(strategy_config.get('capital_allocation', 0)))
            total_allocation += allocation

        if abs(total_allocation - Decimal('1.0')) > Decimal('0.01'):
            raise ConfigurationException(f"Strategy allocations sum to {total_allocation}, should be 1.0")
```

### 配置文件结构

```yaml
# config.yaml - 主配置文件
system:
  environment: "production"  # development, testing, production
  log_level: "INFO"
  max_workers: 8

exchanges:
  binance:
    api_key: "${BINANCE_API_KEY}"
    api_secret: "${BINANCE_API_SECRET}"
    testnet: false
    rate_limits:
      orders_per_second: 10
      requests_per_minute: 1200
    endpoints:
      spot_ws: "wss://stream.binance.com:9443/ws"
      futures_ws: "wss://fstream.binance.com/ws"

  deribit:
    api_key: "${DERIBIT_API_KEY}"
    api_secret: "${DERIBIT_API_SECRET}"
    testnet: false
    endpoints:
      ws: "wss://www.deribit.com/ws/api/v2"

redis:
  host: "localhost"
  port: 6379
  db: 0
  password: "${REDIS_PASSWORD}"
  cluster_mode: true

database:
  host: "localhost"
  port: 5432
  database: "btc_option_grid_bot"
  username: "${DB_USERNAME}"
  password: "${DB_PASSWORD}"

strategies:
  option_grid:
    type: "OptionGridStrategy"
    capital_allocation: 0.70
    is_active: true
    mode: "accumulation"
    parameters:
      delta_range: [0.1, 0.2]
      strike_range: [-0.20, -0.08]
      iv_threshold: 0.6
    risk_limits:
      max_position_size: 1000
      max_daily_trades: 50

  branch_strategy:
    type: "BranchStrategy"
    capital_allocation: 0.25
    is_active: true
    parameters:
      signal_thresholds:
        structure_divergence: 0.65
        volatility_mismatch: 0.60
        gamma_liquidation_overlap: 0.70
      max_position_hold_time: 3600  # 1小时
    risk_limits:
      max_position_size: 500

risk_limits:
  max_delta: 0.5
  max_gamma: 0.05
  max_margin_usage: 0.70
  max_daily_loss: 0.05
  min_liquidity_score: 0.20

telegram:
  bot_token: "${TELEGRAM_BOT_TOKEN}"
  authorized_users: [123456789, 987654321]

monitoring:
  prometheus_port: 9090
  grafana_port: 3000
  alert_webhook: "${ALERT_WEBHOOK_URL}"

logging:
  level: "INFO"
  format: "json"
  file: "logs/btc_option_grid_bot.log"
  max_size: "100MB"
  backup_count: 5
```

## 🧪 测试框架设计

### 测试接口定义

```python
class TestableInterface(Protocol):
    """可测试接口协议"""

    async def setup_test_environment(self) -> bool:
        """设置测试环境"""
        ...

    async def cleanup_test_environment(self) -> bool:
        """清理测试环境"""
        ...

    async def get_test_data(self) -> Dict[str, Any]:
        """获取测试数据"""
        ...

class MockExchange:
    """模拟交易所"""

    def __init__(self):
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.market_data: Dict[str, MarketData] = {}
        self.latency_ms = 50  # 模拟延迟

    async def place_order(self, order: Order) -> str:
        """模拟下单"""
        await asyncio.sleep(self.latency_ms / 1000)

        # 模拟订单执行
        order.status = OrderStatus.FILLED
        order.filled_size = order.size
        order.avg_fill_price = order.price

        self.orders[order.order_id] = order

        # 更新持仓
        await self._update_position(order)

        return order.order_id

    async def _update_position(self, order: Order):
        """更新持仓"""
        symbol = order.symbol

        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                exchange=order.exchange,
                size=Decimal('0'),
                avg_price=Decimal('0'),
                mark_price=order.price,
                unrealized_pnl=Decimal('0'),
                realized_pnl=Decimal('0'),
                margin_required=Decimal('0'),
                timestamp=datetime.utcnow()
            )

        position = self.positions[symbol]

        # 计算新的持仓
        if order.side == OrderSide.BUY:
            new_size = position.size + order.filled_size
        else:
            new_size = position.size - order.filled_size

        # 更新平均价格
        if new_size != 0:
            total_cost = position.size * position.avg_price + order.filled_size * order.avg_fill_price
            position.avg_price = total_cost / new_size

        position.size = new_size
        position.timestamp = datetime.utcnow()

class TestDataGenerator:
    """测试数据生成器"""

    @staticmethod
    def generate_market_data(
        symbol: str,
        start_price: Decimal,
        volatility: float = 0.02,
        count: int = 1000
    ) -> List[MarketData]:
        """生成模拟市场数据"""
        import random
        import math

        data = []
        current_price = start_price

        for i in range(count):
            # 使用几何布朗运动模拟价格
            dt = 1.0 / (24 * 60)  # 1分钟
            drift = 0.0  # 无漂移
            shock = random.gauss(0, 1)

            price_change = drift * dt + volatility * math.sqrt(dt) * shock
            current_price *= (1 + price_change)

            market_data = MarketData(
                symbol=symbol,
                exchange=ExchangeType.BINANCE,
                price=current_price,
                volume=Decimal(str(random.uniform(100, 1000))),
                timestamp=datetime.utcnow() + timedelta(minutes=i),
                data_quality=DataQuality.HIGH
            )
            data.append(market_data)

        return data

    @staticmethod
    def generate_option_data(
        underlying_price: Decimal,
        strike: Decimal,
        expiry_days: int,
        option_type: OptionType,
        iv: float = 0.8
    ) -> OptionData:
        """生成期权数据"""
        from scipy.stats import norm
        import math

        # Black-Scholes计算
        S = float(underlying_price)
        K = float(strike)
        T = expiry_days / 365.0
        r = 0.05  # 无风险利率
        sigma = iv

        d1 = (math.log(S/K) + (r + 0.5*sigma**2)*T) / (sigma*math.sqrt(T))
        d2 = d1 - sigma*math.sqrt(T)

        if option_type == OptionType.CALL:
            price = S*norm.cdf(d1) - K*math.exp(-r*T)*norm.cdf(d2)
            delta = norm.cdf(d1)
        else:
            price = K*math.exp(-r*T)*norm.cdf(-d2) - S*norm.cdf(-d1)
            delta = -norm.cdf(-d1)

        gamma = norm.pdf(d1) / (S*sigma*math.sqrt(T))
        theta = -(S*norm.pdf(d1)*sigma/(2*math.sqrt(T)) + r*K*math.exp(-r*T)*norm.cdf(d2 if option_type == OptionType.CALL else -d2))
        vega = S*norm.pdf(d1)*math.sqrt(T)
        rho = K*T*math.exp(-r*T)*norm.cdf(d2 if option_type == OptionType.CALL else -d2)

        greeks = Greeks(
            delta=Decimal(str(delta)),
            gamma=Decimal(str(gamma)),
            theta=Decimal(str(theta/365)),  # 日Theta
            vega=Decimal(str(vega/100)),    # 1%波动率变化的Vega
            rho=Decimal(str(rho/100)),      # 1%利率变化的Rho
            timestamp=datetime.utcnow()
        )

        return OptionData(
            symbol=f"BTC-{expiry_days}D-{strike}-{option_type.value.upper()}",
            strike=strike,
            expiry=datetime.utcnow() + timedelta(days=expiry_days),
            option_type=option_type,
            mark_price=Decimal(str(price)),
            bid=Decimal(str(price * 0.98)),
            ask=Decimal(str(price * 1.02)),
            volume=Decimal(str(random.uniform(10, 100))),
            open_interest=random.randint(100, 1000),
            greeks=greeks,
            iv=Decimal(str(iv)),
            timestamp=datetime.utcnow()
        )

# 测试用例示例
import pytest

class TestOptionGridStrategy:
    """期权网格策略测试"""

    @pytest.fixture
    async def strategy(self):
        """策略fixture"""
        strategy = OptionGridStrategy()
        config = StrategyConfig(
            strategy_id="test_grid",
            strategy_type="OptionGridStrategy",
            mode=StrategyMode.ACCUMULATION,
            capital_allocation=Decimal('0.7'),
            risk_limits={'max_delta': Decimal('0.5')},
            parameters={'delta_range': [0.1, 0.2]}
        )
        await strategy.initialize(config)
        yield strategy
        await strategy.stop()

    @pytest.fixture
    def mock_exchange(self):
        """模拟交易所fixture"""
        return MockExchange()

    async def test_strategy_initialization(self, strategy):
        """测试策略初始化"""
        assert strategy.strategy_id == "test_grid"
        assert strategy.is_active is False  # 初始化后未启动

    async def test_mode_switching(self, strategy):
        """测试模式切换"""
        # 测试切换到分发模式
        result = await strategy.switch_mode(StrategyMode.DISTRIBUTION)
        assert result is True

        current_mode = await strategy.get_current_mode()
        assert current_mode == StrategyMode.DISTRIBUTION

    async def test_grid_level_calculation(self, strategy):
        """测试网格价位计算"""
        market_data = MarketData(
            symbol="BTC-USD",
            exchange=ExchangeType.BINANCE,
            price=Decimal('50000'),
            volume=Decimal('100'),
            timestamp=datetime.utcnow(),
            data_quality=DataQuality.HIGH
        )

        levels = await strategy.calculate_grid_levels(market_data)

        assert len(levels) > 0
        assert all(isinstance(level, Decimal) for level in levels)
        assert all(level > 0 for level in levels)

    async def test_option_order_generation(self, strategy, mock_exchange):
        """测试期权订单生成"""
        levels = [Decimal('45000'), Decimal('46000'), Decimal('47000')]

        orders = await strategy.generate_option_orders(StrategyMode.ACCUMULATION, levels)

        assert len(orders) > 0

        for order in orders:
            assert isinstance(order, Order)
            assert order.size > 0
            assert order.price > 0

            # 模拟执行订单
            order_id = await mock_exchange.place_order(order)
            assert order_id == order.order_id

class TestRiskEngine:
    """风险引擎测试"""

    @pytest.fixture
    async def risk_engine(self):
        """风险引擎fixture"""
        engine = RiskEngine()
        yield engine

    async def test_order_validation(self, risk_engine):
        """测试订单验证"""
        valid_order = Order(
            order_id="test_order_1",
            symbol="BTC-USD",
            exchange=ExchangeType.BINANCE,
            side=OrderSide.BUY,
            size=Decimal('1'),
            price=Decimal('50000')
        )

        result = await risk_engine.validate_order(valid_order)
        assert result is True

        # 测试无效订单
        invalid_order = Order(
            order_id="test_order_2",
            symbol="BTC-USD",
            exchange=ExchangeType.BINANCE,
            side=OrderSide.BUY,
            size=Decimal('0'),  # 无效大小
            price=Decimal('50000')
        )

        with pytest.raises(RiskException):
            await risk_engine.validate_order(invalid_order)

    async def test_portfolio_risk_calculation(self, risk_engine):
        """测试组合风险计算"""
        # 创建测试持仓
        positions = [
            TestDataGenerator.generate_option_position(),
            TestDataGenerator.generate_option_position()
        ]

        risk_metrics = await risk_engine.calculate_portfolio_risk(positions)

        assert isinstance(risk_metrics, RiskMetrics)
        assert risk_metrics.total_delta is not None
        assert risk_metrics.total_gamma is not None
        assert risk_metrics.margin_used >= 0

    async def test_risk_limit_checking(self, risk_engine):
        """测试风险限制检查"""
        # 创建高风险指标
        high_risk_metrics = RiskMetrics(
            total_delta=Decimal('0.8'),  # 超过限制
            total_gamma=Decimal('0.02'),
            total_theta=Decimal('-10'),
            total_vega=Decimal('50'),
            margin_used=Decimal('8000'),
            margin_available=Decimal('2000'),
            var_1d=Decimal('1000'),
            max_drawdown=Decimal('0.1'),
            timestamp=datetime.utcnow()
        )

        risk_events = await risk_engine.check_risk_limits(high_risk_metrics)

        assert len(risk_events) > 0
        assert any(event.risk_level == RiskLevel.HIGH for event in risk_events)

# 集成测试
class TestSystemIntegration:
    """系统集成测试"""

    @pytest.fixture
    async def trading_system(self):
        """完整交易系统fixture"""
        # 这里应该初始化完整的系统
        # 包括所有组件的集成
        pass

    async def test_end_to_end_trading_flow(self, trading_system):
        """端到端交易流程测试"""
        # 1. 系统启动
        # 2. 数据接收
        # 3. 信号生成
        # 4. 策略执行
        # 5. 订单提交
        # 6. 风险检查
        # 7. 持仓更新
        pass

    async def test_system_recovery(self, trading_system):
        """系统恢复测试"""
        # 测试系统从故障中恢复的能力
        pass
```

## 📈 性能基准和优化

### 性能指标定义

```python
@dataclass
class PerformanceMetrics:
    """性能指标"""

    # 延迟指标
    data_processing_latency_ms: float
    signal_generation_latency_ms: float
    order_execution_latency_ms: float

    # 吞吐量指标
    messages_per_second: int
    orders_per_second: int

    # 资源使用指标
    cpu_usage_percent: float
    memory_usage_mb: float
    network_io_mbps: float

    # 可靠性指标
    uptime_percent: float
    error_rate_percent: float

    # 业务指标
    strategy_win_rate: float
    sharpe_ratio: float
    max_drawdown: float

    timestamp: datetime = field(default_factory=datetime.utcnow)

class PerformanceBenchmark:
    """性能基准测试"""

    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []

    async def run_latency_benchmark(self) -> Dict[str, float]:
        """运行延迟基准测试"""
        results = {}

        # 数据处理延迟测试
        start_time = time.perf_counter()
        await self._simulate_data_processing()
        results['data_processing_ms'] = (time.perf_counter() - start_time) * 1000

        # 信号生成延迟测试
        start_time = time.perf_counter()
        await self._simulate_signal_generation()
        results['signal_generation_ms'] = (time.perf_counter() - start_time) * 1000

        # 订单执行延迟测试
        start_time = time.perf_counter()
        await self._simulate_order_execution()
        results['order_execution_ms'] = (time.perf_counter() - start_time) * 1000

        return results

    async def run_throughput_benchmark(self) -> Dict[str, int]:
        """运行吞吐量基准测试"""
        results = {}

        # 消息处理吞吐量
        start_time = time.time()
        message_count = await self._simulate_message_processing(duration_seconds=10)
        elapsed = time.time() - start_time
        results['messages_per_second'] = int(message_count / elapsed)

        # 订单处理吞吐量
        start_time = time.time()
        order_count = await self._simulate_order_processing(duration_seconds=10)
        elapsed = time.time() - start_time
        results['orders_per_second'] = int(order_count / elapsed)

        return results

    async def _simulate_data_processing(self):
        """模拟数据处理"""
        # 模拟数据验证、转换、存储等操作
        await asyncio.sleep(0.001)  # 1ms模拟处理时间

    async def _simulate_signal_generation(self):
        """模拟信号生成"""
        # 模拟因果分析、微观结构分析等
        await asyncio.sleep(0.005)  # 5ms模拟处理时间

    async def _simulate_order_execution(self):
        """模拟订单执行"""
        # 模拟订单验证、提交、确认等
        await asyncio.sleep(0.002)  # 2ms模拟处理时间
```

---

**总结**: 现在design.md文档已经包含了完整的系统设计细节，包括：

1. ✅ **完整的数据模型定义** - 所有核心数据类型和枚举
2. ✅ **异常处理体系** - 分层异常类和处理机制
3. ✅ **详细的接口定义** - 所有核心组件的接口规范
4. ✅ **核心组件实现** - 包含具体的代码实现框架
5. ✅ **配置管理系统** - 完整的配置加载和管理机制
6. ✅ **测试框架设计** - 单元测试、集成测试和性能测试
7. ✅ **性能基准定义** - 性能指标和基准测试框架

这样，开发者只需要查看一个完整的design.md文档就能获得所有必要的设计信息，避免了信息分散的问题。