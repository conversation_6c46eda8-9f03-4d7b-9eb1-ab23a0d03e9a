
## 📊 微观结构信号模块说明（用于趋势衰竭识别与期权策略切换）

本模块基于 Binance 和 Deribit 的 WebSocket 数据，构建轻量级的实时微观结构信号系统，主要用于：

- ✅ 判断趋势是否正在结构性减弱
- ✅ 辅助方向性期权策略的止盈出场
- ✅ 触发做空波动率类策略（如 Iron Condor）的切换时机

---

### 🧠 核心信号列表（共4项）

| 信号名称 | 用途 | 数据源 | 更新频率 |
|----------|------|---------|-----------|
| VWAP vs LWAP Spread | 判断大单/小单是否一致方向推动 | Binance trades | 每10秒 |
| Order Book Depth 1% | 检测市场承接力是否塌陷 | Binance/Deribit depth | 实时 / 滚动窗口 |
| Order Book Turnover Rate | 判断市场是否进入挂单混乱/高频主导状态 | Binance/Deribit orderbook | 每10秒快照对比 |
| Taker Net Pressure | 判断主动成交行为是否与价格一致 | Binance aggTrade | 每5~10秒 |

---

### ✅ 信号 1：VWAP vs LWAP Spread

#### 📌 定义：
- **VWAP (Volume-Weighted Avg Price)**: ∑(price × size) / ∑size
- **LWAP (Linear-Weighted Avg Price)**: ∑price / n
- **Spread = VWAP − LWAP**

#### 🔍 解读：
- Spread > 0：大单在高位成交，趋势健康
- Spread < -0.5：大单在砸盘，小单追涨，结构反转风险高

#### 🔧 Python计算逻辑：
```python
prices = np.array([float(t['p']) for t in trade_buffer])
sizes = np.array([float(t['q']) for t in trade_buffer])
vwap = np.sum(prices * sizes) / np.sum(sizes)
lwap = np.mean(prices)
spread = vwap - lwap
```

---

### ✅ 信号 2：Order Book Depth 1%

#### 📌 定义：
- Bid Volume（≥ 99% mark price）+ Ask Volume（≤ 101% mark price）之和

#### 🔍 解读：
- 买盘变薄，bid_volume 显著下降 → 市场失去支撑

#### 🔧 Python计算逻辑：
```python
bid_volume = sum(size for price, size in bids if price >= mark_price * 0.99)
ask_volume = sum(size for price, size in asks if price <= mark_price * 1.01)
```

---

### ✅ 信号 3：Order Book Turnover Rate

#### 📌 定义：
- 挂单更新频率 = 改变的价格档位 / 总档位数量（每10秒对比两次快照）

#### 🔍 解读：
- Turnover Rate > 0.4 表示流动性极不稳定（高频刷单主导）

#### 🔧 Python计算逻辑：
```python
changed_levels = prev_prices.symmetric_difference(curr_prices)
turnover_rate = len(changed_levels) / len(curr_prices.union(prev_prices))
```

---

### ✅ 信号 4：Taker Net Pressure

#### 📌 定义：
- Buy Taker Volume − Sell Taker Volume

#### 🔍 解读：
- 买盘主导却无涨幅，或卖压不再推动下跌 → 趋势失效

#### 🔧 Python计算逻辑：
```python
buy_volume = sum(float(t['q']) for t in trade_buffer if not t['m'])
sell_volume = sum(float(t['q']) for t in trade_buffer if t['m'])
net_pressure = buy_volume - sell_volume
```

---

### 🎯 模块触发逻辑示例：结构断裂出场

```python
def should_exit_due_to_structure_breakdown(signals):
    return (
        signals['vwap_lwap_spread'] < -0.5 and
        signals['order_turnover'] > 0.4 and
        signals['depth_1pct_bid'] < DEPTH_LOW_THRESHOLD and
        signals['taker_net_pressure'] > 0 and
        price_does_not_move_up()
    )
```

---

### ✅ 总结

该模块将作为你现有因果预测系统的“趋势衰竭检测器”，在方向策略高收益阶段识别风险信号，并自动提示：

- 是否应提前止盈 Buy OTM 策略
- 是否应切换为 Short Vega 策略（Iron Condor 等）
- 是否应空仓避险

该系统轻量、稳定，部署后能显著提升你策略的收尾质量与风控结构。
