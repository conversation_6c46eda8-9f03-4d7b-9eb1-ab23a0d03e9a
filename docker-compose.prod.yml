version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: btc_option_grid_bot_redis_prod
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

  # PostgreSQL + TimescaleDB服务
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: btc_option_grid_bot_postgres_prod
    environment:
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_USER: ${DB_USERNAME}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USERNAME}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: btc_option_grid_bot_prometheus_prod
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=7d'
      - '--web.enable-lifecycle'
      - '--storage.tsdb.retention.size=1GB'
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: btc_option_grid_bot_grafana_prod
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: "false"
      GF_SECURITY_COOKIE_SECURE: "true"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    depends_on:
      prometheus:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M

  # 主应用服务（生产环境）
  app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: btc_option_grid_bot_app_prod
    environment:
      - ENVIRONMENT=production
      - REDIS_HOST=redis
      - DB_HOST=postgres
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    # 移除端口暴露，这是后台交易程序而非Web服务
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    healthcheck:
      test: ["CMD-SHELL", "pgrep -f 'python src/main.py' > /dev/null || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery Worker (生产环境)
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: btc_option_grid_bot_celery_worker_prod
    command: celery -A src.core.celery_manager worker --loglevel=info --concurrency=4
    environment:
      - ENVIRONMENT=production
      - REDIS_HOST=redis
      - DB_HOST=postgres
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Celery Beat (定时任务 - 生产环境)
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile.prod
    container_name: btc_option_grid_bot_celery_beat_prod
    command: celery -A src.core.celery_manager beat --loglevel=info
    environment:
      - ENVIRONMENT=production
      - REDIS_HOST=redis
      - DB_HOST=postgres
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      redis:
        condition: service_healthy
      postgres:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # Nginx反向代理 (可选) - 用于代理Grafana和Prometheus Web界面
  nginx:
    image: nginx:alpine
    container_name: btc_option_grid_bot_nginx_prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./config/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - grafana
      - prometheus
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    profiles:
      - nginx

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  btc_option_grid_bot_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16