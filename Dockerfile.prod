# 生产环境Dockerfile
FROM python:3.9-slim as base

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# 创建非root用户
RUN groupadd -r app && useradd -r -g app app

# 安装Poetry
RUN pip install --no-cache-dir poetry

# 复制Poetry配置文件
COPY pyproject.toml poetry.lock ./

# 配置Poetry
RUN poetry config virtualenvs.create false

# 安装Python依赖 (生产环境)
RUN poetry install --only=main --no-dev

# 复制应用代码
COPY src/ ./src/
COPY config/ ./config/

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV ENVIRONMENT=production

# 设置权限
RUN chown -R app:app /app
USER app

# 健康检查 - 检查主进程是否运行
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pgrep -f "python src/main.py" > /dev/null || exit 1

# 启动命令 - 运行主程序
CMD ["python", "src/main.py"]