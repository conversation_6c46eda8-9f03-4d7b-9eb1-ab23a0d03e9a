# 性能监控配置
performance_monitor:
  # 指标收集间隔（秒）
  collection_interval: 5.0
  
  # 指标保留时间（小时）
  metric_retention_hours: 24
  
  # 告警阈值配置
  alert_thresholds:
    cpu_percent: 80.0              # CPU使用率阈值 (%)
    memory_percent: 85.0           # 内存使用率阈值 (%)
    error_rate_percent: 5.0        # 错误率阈值 (%)
    avg_response_time_ms: 1000.0   # 平均响应时间阈值 (ms)
    data_feed_latency_ms: 500.0    # 数据延迟阈值 (ms)
    event_queue_size: 1000         # 事件队列大小阈值
    network_latency_ms: 200.0      # 网络延迟阈值 (ms)
  
  # 性能基准配置
  performance_benchmarks:
    excellent:
      cpu: 50                      # CPU使用率 < 50%
      memory: 60                   # 内存使用率 < 60%
      response_time: 100           # 响应时间 < 100ms
      throughput: 1000             # 吞吐量 > 1000 req/s
    good:
      cpu: 70
      memory: 75
      response_time: 300
      throughput: 500
    average:
      cpu: 85
      memory: 85
      response_time: 500
      throughput: 200
    poor:
      cpu: 95
      memory: 95
      response_time: 1000
      throughput: 100

# 系统指标集成配置
system_metrics_integration:
  # 集成间隔（秒）
  integration_interval: 10.0
  
  # 是否启用自动优化
  auto_optimization: true
  
  # 优化策略配置
  optimization_strategies:
    memory_cleanup:
      enabled: true
      trigger_threshold: 90.0      # 内存使用率达到90%时触发
      cleanup_interval: 300        # 清理间隔（秒）
    
    cache_optimization:
      enabled: true
      trigger_threshold: 85.0      # 缓存命中率低于85%时触发
      optimization_interval: 600   # 优化间隔（秒）
    
    connection_pooling:
      enabled: true
      max_connections: 100         # 最大连接数
      idle_timeout: 300            # 空闲连接超时（秒）
    
    batch_processing:
      enabled: true
      batch_size_optimization: true
      dynamic_batching: true

# 告警配置
alerting:
  # 告警通道
  channels:
    telegram:
      enabled: true
      critical_only: false        # 是否仅发送严重告警
    
    email:
      enabled: false
      recipients:
        - <EMAIL>
    
    webhook:
      enabled: false
      url: "https://hooks.example.com/alerts"
  
  # 告警规则
  rules:
    system_critical:
      conditions:
        - metric: cpu_percent
          operator: ">"
          value: 95.0
        - metric: memory_percent
          operator: ">"
          value: 95.0
      severity: critical
      cooldown: 300              # 告警冷却时间（秒）
    
    performance_degraded:
      conditions:
        - metric: avg_response_time_ms
          operator: ">"
          value: 2000.0
        - metric: error_rate_percent
          operator: ">"
          value: 10.0
      severity: high
      cooldown: 600
    
    trading_issues:
      conditions:
        - metric: order_success_rate
          operator: "<"
          value: 95.0
        - metric: data_feed_latency_ms
          operator: ">"
          value: 1000.0
      severity: high
      cooldown: 180

# 报告配置
reporting:
  # 自动报告生成
  auto_reports:
    enabled: true
    
    # 日报
    daily:
      enabled: true
      time: "09:00"              # 每天09:00生成
      recipients:
        - telegram
      include:
        - system_performance
        - trading_summary
        - alerts_summary
    
    # 周报
    weekly:
      enabled: true
      day: "monday"
      time: "10:00"
      recipients:
        - telegram
        - email
      include:
        - performance_trends
        - optimization_recommendations
        - system_health_analysis
  
  # 报告格式
  formats:
    charts: true                 # 包含图表
    detailed_metrics: true       # 详细指标
    recommendations: true        # 包含优化建议
    historical_comparison: true  # 历史对比

# 存储配置
storage:
  # 指标存储
  metrics:
    backend: "memory"            # memory, redis, influxdb
    retention_policy:
      high_frequency: "1h"       # 高频指标保留1小时
      medium_frequency: "24h"    # 中频指标保留24小时
      low_frequency: "7d"        # 低频指标保留7天
  
  # 告警历史存储
  alerts:
    backend: "memory"
    retention_days: 30
  
  # 报告存储
  reports:
    backend: "file"
    path: "reports/"
    retention_days: 90

# 高级配置
advanced:
  # 性能采样
  sampling:
    enabled: true
    rate: 0.1                    # 采样率 10%
    adaptive: true               # 自适应采样
  
  # 异常检测
  anomaly_detection:
    enabled: true
    algorithm: "isolation_forest" # isolation_forest, zscore
    sensitivity: 0.1             # 敏感度
    min_samples: 100             # 最小样本数
  
  # 预测分析
  forecasting:
    enabled: false
    horizon: 3600                # 预测时间窗口（秒）
    models: ["linear", "arima"]  # 预测模型
  
  # 分布式监控
  distributed:
    enabled: false
    cluster_mode: false
    node_id: "node-1"
    coordinator: "127.0.0.1:8080"