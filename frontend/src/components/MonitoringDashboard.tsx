/**
 * Web监控面板主组件
 * 
 * 提供实时系统状态监控、持仓信息、风险指标和性能分析
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout,
  Card,
  Row,
  Col,
  Statistic,
  Table,
  Alert,
  Badge,
  Button,
  Switch,
  Tabs,
  Progress,
  notification,
  Space,
  Typography,
  Divider
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Toolt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell
} from 'recharts';
import {
  DashboardOutlined,
  TrendingUpOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  StopOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';

const { Header, Content, Sider } = Layout;
const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 接口定义
interface SystemMetrics {
  timestamp: string;
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  active_orders: number;
  total_positions: number;
  current_pnl: number;
  risk_score: number;
  order_latency_ms: number;
  position_sync_latency_ms: number;
  event_processing_rate: number;
  error_count_1h: number;
  warning_count_1h: number;
  exchange_connections: Record<string, boolean>;
  database_connection: boolean;
  cache_connection: boolean;
}

interface Alert {
  id: string;
  level: string;
  title: string;
  message: string;
  timestamp: string;
  component: string;
  resolved: boolean;
}

interface Position {
  symbol: string;
  quantity: number;
  avg_price: number;
  market_value: number;
  unrealized_pnl: number;
  pnl_percentage: number;
}

interface Trade {
  timestamp: string;
  symbol: string;
  side: string;
  quantity: number;
  price: number;
  pnl: number;
}

// WebSocket连接管理
class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 5000;

  constructor(
    private url: string,
    private onMessage: (data: any) => void,
    private onError: (error: Event) => void
  ) {}

  connect() {
    try {
      this.ws = new WebSocket(this.url);
      
      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.onMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.reconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.onError(error);
      };
    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      this.reconnect();
    }
  }

  private reconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`Reconnecting... Attempt ${this.reconnectAttempts}`);
        this.connect();
      }, this.reconnectInterval);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  send(data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data));
    }
  }
}

// API服务
class ApiService {
  private baseUrl: string;

  constructor(baseUrl: string = '/api') {
    this.baseUrl = baseUrl;
  }

  async get(endpoint: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}${endpoint}`);
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }
    return response.json();
  }

  async post(endpoint: string, data: any): Promise<any> {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`API request failed: ${response.statusText}`);
    }
    return response.json();
  }
}

// 主监控面板组件
const MonitoringDashboard: React.FC = () => {
  // 状态管理
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [recentTrades, setRecentTrades] = useState<Trade[]>([]);
  const [metricsHistory, setMetricsHistory] = useState<SystemMetrics[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [strategyRunning, setStrategyRunning] = useState(false);
  const [loading, setLoading] = useState(true);

  // 服务实例
  const apiService = new ApiService();
  const [wsManager, setWsManager] = useState<WebSocketManager | null>(null);

  // WebSocket消息处理
  const handleWebSocketMessage = useCallback((data: any) => {
    switch (data.type) {
      case 'metrics_update':
        setSystemMetrics(data.data);
        setMetricsHistory(prev => [...prev.slice(-99), data.data]); // 保留最近100个数据点
        break;
      case 'new_alerts':
        setAlerts(prev => [...prev, ...data.data]);
        // 显示新告警通知
        data.data.forEach((alert: Alert) => {
          notification[alert.level === 'critical' ? 'error' : 'warning']({
            message: alert.title,
            description: alert.message,
            duration: alert.level === 'critical' ? 0 : 4.5,
          });
        });
        break;
      case 'position_update':
        setPositions(data.data);
        break;
      case 'trade_update':
        setRecentTrades(prev => [data.data, ...prev.slice(0, 49)]); // 保留最近50笔交易
        break;
    }
  }, []);

  const handleWebSocketError = useCallback((error: Event) => {
    setIsConnected(false);
    notification.error({
      message: 'Connection Error',
      description: 'WebSocket connection failed. Trying to reconnect...',
    });
  }, []);

  // 初始化连接
  useEffect(() => {
    const wsUrl = `ws://${window.location.host}/ws`;
    const manager = new WebSocketManager(wsUrl, handleWebSocketMessage, handleWebSocketError);
    manager.connect();
    setWsManager(manager);
    setIsConnected(true);

    // 初始数据加载
    loadInitialData();

    return () => {
      manager.disconnect();
    };
  }, [handleWebSocketMessage, handleWebSocketError]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      
      // 并行加载初始数据
      const [metricsRes, alertsRes, healthRes] = await Promise.all([
        apiService.get('/metrics'),
        apiService.get('/alerts'),
        apiService.get('/health')
      ]);

      setSystemMetrics(metricsRes);
      setAlerts(alertsRes);
      
      // 模拟仓位和交易数据
      setPositions([
        {
          symbol: 'BTC-07JAN25-45000-C',
          quantity: 5,
          avg_price: 2500,
          market_value: 12750,
          unrealized_pnl: 250,
          pnl_percentage: 2.0
        },
        {
          symbol: 'BTC-07JAN25-45000-P',
          quantity: 3,
          avg_price: 1800,
          market_value: 5100,
          unrealized_pnl: -150,
          pnl_percentage: -2.9
        }
      ]);

    } catch (error) {
      console.error('Failed to load initial data:', error);
      notification.error({
        message: 'Data Loading Error',
        description: 'Failed to load initial dashboard data.',
      });
    } finally {
      setLoading(false);
    }
  };

  // 策略控制
  const handleStrategyToggle = async (running: boolean) => {
    try {
      await apiService.post('/strategy/control', { action: running ? 'start' : 'stop' });
      setStrategyRunning(running);
      notification.success({
        message: 'Strategy Control',
        description: `Strategy ${running ? 'started' : 'stopped'} successfully.`,
      });
    } catch (error) {
      notification.error({
        message: 'Strategy Control Error',
        description: 'Failed to control strategy.',
      });
    }
  };

  // 告警解决
  const handleResolveAlert = async (alertId: string) => {
    try {
      await apiService.post(`/alerts/${alertId}/resolve`, {});
      setAlerts(prev => prev.map(alert => 
        alert.id === alertId ? { ...alert, resolved: true } : alert
      ));
      notification.success({
        message: 'Alert Resolved',
        description: 'Alert has been marked as resolved.',
      });
    } catch (error) {
      notification.error({
        message: 'Error',
        description: 'Failed to resolve alert.',
      });
    }
  };

  // 系统健康检查
  const handleHealthCheck = async () => {
    try {
      const health = await apiService.get('/health');
      notification.info({
        message: 'Health Check',
        description: `System status: ${health.overall_status}`,
      });
    } catch (error) {
      notification.error({
        message: 'Health Check Failed',
        description: 'Unable to perform health check.',
      });
    }
  };

  // 渲染系统状态卡片
  const renderSystemStatus = () => (
    <Card title="System Status" extra={
      <Space>
        <Badge status={isConnected ? "success" : "error"} text={isConnected ? "Connected" : "Disconnected"} />
        <Button icon={<ReloadOutlined />} onClick={handleHealthCheck} size="small">
          Health Check
        </Button>
      </Space>
    }>
      <Row gutter={16}>
        <Col span={6}>
          <Statistic
            title="CPU Usage"
            value={systemMetrics?.cpu_usage || 0}
            suffix="%"
            valueStyle={{ color: (systemMetrics?.cpu_usage || 0) > 80 ? '#cf1322' : '#3f8600' }}
          />
          <Progress percent={systemMetrics?.cpu_usage || 0} size="small" />
        </Col>
        <Col span={6}>
          <Statistic
            title="Memory Usage"
            value={systemMetrics?.memory_usage || 0}
            suffix="%"
            valueStyle={{ color: (systemMetrics?.memory_usage || 0) > 85 ? '#cf1322' : '#3f8600' }}
          />
          <Progress percent={systemMetrics?.memory_usage || 0} size="small" />
        </Col>
        <Col span={6}>
          <Statistic
            title="Active Orders"
            value={systemMetrics?.active_orders || 0}
            valueStyle={{ color: '#1890ff' }}
          />
        </Col>
        <Col span={6}>
          <Statistic
            title="Risk Score"
            value={systemMetrics?.risk_score || 0}
            precision={2}
            valueStyle={{ color: (systemMetrics?.risk_score || 0) > 0.8 ? '#cf1322' : '#3f8600' }}
          />
          <Progress 
            percent={(systemMetrics?.risk_score || 0) * 100} 
            size="small"
            strokeColor={(systemMetrics?.risk_score || 0) > 0.8 ? '#cf1322' : '#52c41a'}
          />
        </Col>
      </Row>
    </Card>
  );

  // 渲染策略控制
  const renderStrategyControl = () => (
    <Card title="Strategy Control" size="small">
      <Space>
        <Text>Strategy Status:</Text>
        <Badge status={strategyRunning ? "processing" : "default"} text={strategyRunning ? "Running" : "Stopped"} />
        <Switch
          checked={strategyRunning}
          onChange={handleStrategyToggle}
          checkedChildren={<PlayCircleOutlined />}
          unCheckedChildren={<StopOutlined />}
        />
      </Space>
    </Card>
  );

  // 渲染告警列表
  const renderAlerts = () => {
    const activeAlerts = alerts.filter(alert => !alert.resolved);
    
    return (
      <Card title={`Active Alerts (${activeAlerts.length})`} size="small">
        {activeAlerts.length === 0 ? (
          <Text type="secondary">No active alerts</Text>
        ) : (
          activeAlerts.slice(0, 5).map(alert => (
            <Alert
              key={alert.id}
              message={alert.title}
              description={alert.message}
              type={alert.level === 'critical' ? 'error' : alert.level === 'high' ? 'warning' : 'info'}
              showIcon
              closable
              onClose={() => handleResolveAlert(alert.id)}
              style={{ marginBottom: 8 }}
            />
          ))
        )}
      </Card>
    );
  };

  // 渲染仓位表格
  const renderPositions = () => {
    const columns = [
      {
        title: 'Symbol',
        dataIndex: 'symbol',
        key: 'symbol',
      },
      {
        title: 'Quantity',
        dataIndex: 'quantity',
        key: 'quantity',
        render: (value: number) => value.toFixed(2),
      },
      {
        title: 'Avg Price',
        dataIndex: 'avg_price',
        key: 'avg_price',
        render: (value: number) => `$${value.toFixed(2)}`,
      },
      {
        title: 'Market Value',
        dataIndex: 'market_value',
        key: 'market_value',
        render: (value: number) => `$${value.toFixed(2)}`,
      },
      {
        title: 'Unrealized PnL',
        dataIndex: 'unrealized_pnl',
        key: 'unrealized_pnl',
        render: (value: number, record: Position) => (
          <Text type={value >= 0 ? 'success' : 'danger'}>
            ${value.toFixed(2)} ({record.pnl_percentage.toFixed(2)}%)
          </Text>
        ),
      },
    ];

    return (
      <Card title="Positions" size="small">
        <Table
          columns={columns}
          dataSource={positions}
          rowKey="symbol"
          size="small"
          pagination={false}
        />
      </Card>
    );
  };

  // 渲染性能图表
  const renderPerformanceChart = () => {
    const chartData = metricsHistory.map((metrics, index) => ({
      time: index,
      pnl: metrics.current_pnl,
      risk: metrics.risk_score * 100,
    }));

    return (
      <Card title="Performance Chart" size="small">
        <ResponsiveContainer width="100%" height={300}>
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="time" />
            <YAxis yAxisId="left" />
            <YAxis yAxisId="right" orientation="right" />
            <Tooltip />
            <Legend />
            <Line yAxisId="left" type="monotone" dataKey="pnl" stroke="#8884d8" name="PnL" />
            <Line yAxisId="right" type="monotone" dataKey="risk" stroke="#82ca9d" name="Risk Score" />
          </LineChart>
        </ResponsiveContainer>
      </Card>
    );
  };

  if (loading) {
    return (
      <Layout style={{ minHeight: '100vh' }}>
        <Content style={{ padding: '50px', textAlign: 'center' }}>
          <Title level={3}>Loading Dashboard...</Title>
        </Content>
      </Layout>
    );
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Header style={{ background: '#fff', padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={3} style={{ margin: 0 }}>
              <DashboardOutlined /> BTC Options Grid Trading Monitor
            </Title>
          </Col>
          <Col>
            {renderStrategyControl()}
          </Col>
        </Row>
      </Header>
      
      <Content style={{ padding: '24px' }}>
        <Row gutter={[16, 16]}>
          <Col span={24}>
            {renderSystemStatus()}
          </Col>
          
          <Col span={12}>
            {renderAlerts()}
          </Col>
          
          <Col span={12}>
            {renderPositions()}
          </Col>
          
          <Col span={24}>
            {renderPerformanceChart()}
          </Col>
        </Row>
      </Content>
    </Layout>
  );
};

export default MonitoringDashboard;

// 额外的工具组件和功能

// 实时数据更新Hook
export const useRealTimeData = (wsUrl: string) => {
  const [data, setData] = useState<any>(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    const ws = new WebSocket(wsUrl);

    ws.onopen = () => setConnected(true);
    ws.onclose = () => setConnected(false);
    ws.onmessage = (event) => {
      try {
        const parsedData = JSON.parse(event.data);
        setData(parsedData);
      } catch (error) {
        console.error('Failed to parse WebSocket data:', error);
      }
    };

    return () => ws.close();
  }, [wsUrl]);

  return { data, connected };
};

// 响应式设计Hook
export const useResponsive = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  return { isMobile };
};
