import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import MonitoringDashboard from './components/MonitoringDashboard';
import './App.css';

// Ant Design主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff',
    borderRadius: 6,
    wireframe: false,
  },
};

const App: React.FC = () => {
  return (
    <ConfigProvider theme={theme}>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<MonitoringDashboard />} />
            <Route path="/dashboard" element={<MonitoringDashboard />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  );
};

export default App;
