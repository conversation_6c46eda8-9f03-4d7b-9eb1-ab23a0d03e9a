version: '3.8'

services:
  # Redis服务
  redis:
    image: redis:7-alpine
    container_name: btc_option_grid_bot_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network

  # PostgreSQL + TimescaleDB服务
  postgres:
    image: timescale/timescaledb:latest-pg15
    container_name: btc_option_grid_bot_postgres
    environment:
      POSTGRES_DB: btc_option_grid_bot
      POSTGRES_USER: btc_option_grid_bot
      POSTGRES_PASSWORD: ${DB_PASSWORD:-btc_option_grid_bot_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: btc_option_grid_bot_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./config/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network

  # Grafana仪表板
  grafana:
    image: grafana/grafana:latest
    container_name: btc_option_grid_bot_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./config/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./config/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    depends_on:
      - prometheus

  # 主应用服务（开发环境）
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: btc_option_grid_bot_app
    environment:
      - ENVIRONMENT=development
      - REDIS_HOST=redis
      - DB_HOST=postgres
    volumes:
      - .:/app
      - /app/__pycache__
    # 移除端口暴露，这是后台交易程序而非Web服务
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    profiles:
      - dev

  # Celery Worker
  celery_worker:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: btc_option_grid_bot_celery_worker
    command: celery -A src.core.celery_manager worker --loglevel=info
    environment:
      - ENVIRONMENT=development
      - REDIS_HOST=redis
      - DB_HOST=postgres
    volumes:
      - .:/app
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    profiles:
      - dev

  # Celery Beat (定时任务)
  celery_beat:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: btc_option_grid_bot_celery_beat
    command: celery -A src.core.celery_manager beat --loglevel=info
    environment:
      - ENVIRONMENT=development
      - REDIS_HOST=redis
      - DB_HOST=postgres
    volumes:
      - .:/app
    depends_on:
      - redis
      - postgres
    restart: unless-stopped
    networks:
      - btc_option_grid_bot_network
    profiles:
      - dev

volumes:
  redis_data:
  postgres_data:
  prometheus_data:
  grafana_data:

networks:
  btc_option_grid_bot_network:
    driver: bridge